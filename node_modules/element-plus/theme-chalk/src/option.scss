@use 'sass:map';

@use 'mixins/mixins' as *;
@use 'common/var' as *;

@include b(select-dropdown) {
  @include e(item) {
    font-size: map.get($select, 'font-size');
    // 20 as the padding of option item, 12 as the size of ✓ icon size
    padding: 0 #{20 + 12}px 0 20px;
    position: relative;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: map.get($select-option, 'text-color');
    height: map.get($select-option, 'height');
    line-height: map.get($select-option, 'height');
    box-sizing: border-box;
    cursor: pointer;

    @include when(disabled) {
      color: map.get($select-option, 'disabled-color');
      cursor: not-allowed;
    }

    &.hover,
    &:hover {
      background-color: map.get($select-option, 'hover-background');
    }

    &.selected {
      color: map.get($select-option, 'selected-text-color');
      font-weight: bold;
    }
  }
}
