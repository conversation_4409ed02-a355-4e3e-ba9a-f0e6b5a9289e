@use 'sass:map';

@use 'mixins/mixins' as *;
@use 'mixins/var' as *;
@use 'common/var' as *;
@use 'common/popup' as *;

@include b(message-box) {
  @include set-component-css-var('messagebox', $messagebox);
}

@include b(message-box) {
  display: inline-block;
  max-width: getCssVar('messagebox-width');
  width: 100%;
  padding-bottom: 10px;
  vertical-align: middle;
  background-color: getCssVar('bg-color');
  border-radius: getCssVar('messagebox-border-radius');
  border: 1px solid getCssVar('border-color-lighter');
  font-size: getCssVar('messagebox-font-size');
  box-shadow: getCssVar('box-shadow-light');
  text-align: left;
  overflow: hidden;
  backface-visibility: hidden;
  // To avoid small screen overflowing, see #11919
  box-sizing: border-box;

  &:focus {
    outline: none !important;
  }

  @at-root .#{$namespace}-overlay.is-message-box {
    .#{$namespace}-overlay-message-box {
      text-align: center;
      position: fixed;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
      padding: 16px;
      overflow: auto;

      &::after {
        content: '';
        display: inline-block;
        height: 100%;
        width: 0;
        vertical-align: middle;
      }
    }
  }

  @include when(draggable) {
    @include e(header) {
      cursor: move;
      user-select: none;
    }
  }

  @include e(header) {
    position: relative;
    padding: getCssVar('messagebox-padding-primary');
    padding-bottom: 10px;
  }

  @include e(title) {
    padding-left: 0;
    margin-bottom: 0;
    font-size: getCssVar('messagebox-font-size');
    line-height: 1;
    color: getCssVar('messagebox-title-color');
  }

  @include e(headerbtn) {
    position: absolute;
    top: getCssVar('messagebox-padding-primary');
    right: getCssVar('messagebox-padding-primary');
    padding: 0;
    border: none;
    outline: none;
    background: transparent;
    font-size: var(
      #{getCssVarName('message-close-size')},
      map.get($message, 'close-size')
    );
    cursor: pointer;

    .#{$namespace}-message-box__close {
      color: getCssVar('color-info');
      font-size: inherit;
    }

    &:focus,
    &:hover {
      .#{$namespace}-message-box__close {
        color: getCssVar('color-primary');
      }
    }
  }

  @include e(content) {
    padding: 10px getCssVar('messagebox-padding-primary');
    color: getCssVar('messagebox-content-color');
    font-size: getCssVar('messagebox-content-font-size');
  }

  @include e(container) {
    position: relative;
  }

  @include e(input) {
    padding-top: 15px;

    & div.invalid > input {
      border-color: getCssVar('color-error');

      &:focus {
        border-color: getCssVar('color-error');
      }
    }
  }

  @include e(status) {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    font-size: 24px !important;

    &::before {
      // 防止图标切割
      padding-left: 1px;
    }

    &.#{$namespace}-icon {
      // 防止 el-icon 的position样式覆盖
      position: absolute;
    }

    + .#{$namespace}-message-box__message {
      padding-left: 36px;
      padding-right: 12px;
      word-break: break-word;
    }

    @each $type in (success, info, warning, error) {
      &.#{$namespace}-message-box-icon--#{$type} {
        @include css-var-from-global(('messagebox', 'color'), ('color', $type));
        color: getCssVar('messagebox-color');
      }
    }
  }

  @include e(message) {
    margin: 0;

    & p {
      margin: 0;
      line-height: 24px;
    }
  }

  @include e(errormsg) {
    color: getCssVar('color-error');
    font-size: getCssVar('messagebox-error-font-size');
    min-height: 18px;
    margin-top: 2px;
  }

  @include e(btns) {
    padding: 5px 15px 0;
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-end;
    align-items: center;

    & button:nth-child(2) {
      margin-left: 10px;
    }
  }

  @include e(btns-reverse) {
    flex-direction: row-reverse;
  }

  // centerAlign 布局
  @include m(center) {
    @include e(title) {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    @include e(status) {
      position: relative;
      top: auto;
      padding-right: 5px;
      text-align: center;
      transform: translateY(-1px);
    }

    @include e(message) {
      margin-left: 0;
    }

    @include e(btns) {
      justify-content: center;
    }

    @include e(content) {
      $padding-horizontal: calc(
        #{getCssVar('messagebox', 'padding-primary')} + 12px
      );

      padding-left: $padding-horizontal;
      padding-right: $padding-horizontal;

      text-align: center;
    }
  }
}

.fade-in-linear-enter-active {
  .#{$namespace}-overlay-message-box {
    animation: msgbox-fade-in getCssVar('transition-duration');
  }
}

.fade-in-linear-leave-active {
  .#{$namespace}-overlay-message-box {
    animation: msgbox-fade-in getCssVar('transition-duration') reverse;
  }
}

@keyframes msgbox-fade-in {
  0% {
    transform: translate3d(0, -20px, 0);
    opacity: 0;
  }

  100% {
    transform: translate3d(0, 0, 0);
    opacity: 1;
  }
}
