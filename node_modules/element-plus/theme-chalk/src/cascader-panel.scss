@use 'mixins/mixins' as *;
@use 'mixins/var' as *;
@use 'common/var' as *;

@include b(cascader-panel) {
  @include set-component-css-var('cascader', $cascader);
}

@include b(cascader-panel) {
  display: flex;
  border-radius: getCssVar('cascader-menu', 'radius');
  font-size: getCssVar('cascader-menu', 'font-size');

  @include when(bordered) {
    border: getCssVar('cascader-menu', 'border');
    border-radius: getCssVar('cascader-menu', 'radius');
  }
}

@include b(cascader-menu) {
  min-width: 180px;
  box-sizing: border-box;
  color: getCssVar('cascader-menu', 'text-color');
  border-right: getCssVar('cascader-menu', 'border');

  &:last-child {
    border-right: none;

    .#{$namespace}-cascader-node {
      padding-right: 20px;
    }
  }

  @include e(wrap) {
    // Raise the priority
    &.#{$namespace}-scrollbar__wrap {
      height: 204px;
    }
  }

  @include e(list) {
    position: relative;
    min-height: 100%;
    margin: 0;
    padding: 6px 0;
    list-style: none;
    box-sizing: border-box;
  }

  @include e(hover-zone) {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
  }

  @include e(empty-text) {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    align-items: center;
    color: getCssVar('cascader', 'color-empty');

    .is-loading {
      margin-right: 2px;
    }
  }
}

@include b(cascader-node) {
  position: relative;
  display: flex;
  align-items: center;
  padding: 0 30px 0 20px;
  height: 34px;
  line-height: 34px;
  outline: none;

  &.is-selectable.in-active-path {
    color: getCssVar('cascader-menu', 'text-color');
  }

  &.in-active-path,
  &.is-selectable.in-checked-path,
  &.is-active {
    color: getCssVar('cascader-menu', 'selected-text-color');
    font-weight: bold;
  }

  &:not(.is-disabled) {
    cursor: pointer;

    &:hover,
    &:focus {
      background: getCssVar('cascader-node', 'background-hover');
    }
  }

  @include when(disabled) {
    color: getCssVar('cascader-node', 'color-disabled');
    cursor: not-allowed;
  }

  @include e(prefix) {
    position: absolute;
    left: 10px;
  }

  @include e(postfix) {
    position: absolute;
    right: 10px;
  }

  @include e(label) {
    flex: 1;
    text-align: left;
    padding: 0 8px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  > .#{$namespace}-checkbox {
    margin-right: 0;
  }

  > .#{$namespace}-radio {
    margin-right: 0;

    .#{$namespace}-radio__label {
      padding-left: 0;
    }
  }
}
