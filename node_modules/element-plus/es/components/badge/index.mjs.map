{"version": 3, "file": "index.mjs", "sources": ["../../../../../packages/components/badge/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\n\nimport Badge from './src/badge.vue'\n\nexport const ElBadge = withInstall(Badge)\nexport default ElBadge\n\nexport * from './src/badge'\nexport type { BadgeInstance } from './src/instance'\n"], "names": [], "mappings": ";;;;;AAEY,MAAC,OAAO,GAAG,WAAW,CAAC,KAAK;;;;"}