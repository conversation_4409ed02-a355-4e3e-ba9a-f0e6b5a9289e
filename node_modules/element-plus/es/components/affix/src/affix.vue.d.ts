import type { CSSProperties } from 'vue';
declare const _default: import("vue").DefineComponent<{
    readonly zIndex: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => import("csstype").ZIndexProperty & {}) | (() => import("csstype").ZIndexProperty) | ((new (...args: any[]) => import("csstype").ZIndexProperty & {}) | (() => import("csstype").ZIndexProperty))[], unknown, unknown, 100, boolean>;
    readonly target: import("element-plus/es/utils").EpPropFinalized<StringConstructor, unknown, unknown, "", boolean>;
    readonly offset: import("element-plus/es/utils").EpPropFinalized<NumberConstructor, unknown, unknown, 0, boolean>;
    readonly position: import("element-plus/es/utils").EpPropFinalized<StringConstructor, "top" | "bottom", unknown, "top", boolean>;
}, {
    COMPONENT_NAME: string;
    props: Readonly<import("@vue/shared").LooseRequired<Readonly<import("vue").ExtractPropTypes<{
        readonly zIndex: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => import("csstype").ZIndexProperty & {}) | (() => import("csstype").ZIndexProperty) | ((new (...args: any[]) => import("csstype").ZIndexProperty & {}) | (() => import("csstype").ZIndexProperty))[], unknown, unknown, 100, boolean>;
        readonly target: import("element-plus/es/utils").EpPropFinalized<StringConstructor, unknown, unknown, "", boolean>;
        readonly offset: import("element-plus/es/utils").EpPropFinalized<NumberConstructor, unknown, unknown, 0, boolean>;
        readonly position: import("element-plus/es/utils").EpPropFinalized<StringConstructor, "top" | "bottom", unknown, "top", boolean>;
    }>> & {
        onChange?: ((fixed: boolean) => any) | undefined;
        onScroll?: ((args_0: {
            scrollTop: number;
            fixed: boolean;
        }) => any) | undefined;
    }>>;
    emit: ((event: "change", fixed: boolean) => void) & ((event: "scroll", args_0: {
        scrollTop: number;
        fixed: boolean;
    }) => void);
    ns: {
        namespace: import("vue").ComputedRef<string>;
        b: (blockSuffix?: string) => string;
        e: (element?: string | undefined) => string;
        m: (modifier?: string | undefined) => string;
        be: (blockSuffix?: string | undefined, element?: string | undefined) => string;
        em: (element?: string | undefined, modifier?: string | undefined) => string;
        bm: (blockSuffix?: string | undefined, modifier?: string | undefined) => string;
        bem: (blockSuffix?: string | undefined, element?: string | undefined, modifier?: string | undefined) => string;
        is: {
            (name: string, state: boolean | undefined): string;
            (name: string): string;
        };
        cssVar: (object: Record<string, string>) => Record<string, string>;
        cssVarName: (name: string) => string;
        cssVarBlock: (object: Record<string, string>) => Record<string, string>;
        cssVarBlockName: (name: string) => string;
    };
    target: import("vue").ShallowRef<HTMLElement | undefined>;
    root: import("vue").ShallowRef<HTMLDivElement | undefined>;
    scrollContainer: import("vue").ShallowRef<Window | HTMLElement | undefined>;
    windowHeight: import("vue").Ref<number>;
    rootHeight: import("vue").Ref<number>;
    rootWidth: import("vue").Ref<number>;
    rootTop: import("vue").Ref<number>;
    rootBottom: import("vue").Ref<number>;
    updateRoot: () => void;
    targetRect: {
        height: import("vue").Ref<number>;
        bottom: import("vue").Ref<number>;
        left: import("vue").Ref<number>;
        right: import("vue").Ref<number>;
        top: import("vue").Ref<number>;
        width: import("vue").Ref<number>;
        x: import("vue").Ref<number>;
        y: import("vue").Ref<number>;
        update: () => void;
    };
    fixed: import("vue").Ref<boolean>;
    scrollTop: import("vue").Ref<number>;
    transform: import("vue").Ref<number>;
    rootStyle: import("vue").ComputedRef<CSSProperties>;
    affixStyle: import("vue").ComputedRef<CSSProperties>;
    update: () => void;
    handleScroll: () => void;
}, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
    scroll: ({ scrollTop, fixed }: {
        scrollTop: number;
        fixed: boolean;
    }) => boolean;
    change: (fixed: boolean) => boolean;
}, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    readonly zIndex: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => import("csstype").ZIndexProperty & {}) | (() => import("csstype").ZIndexProperty) | ((new (...args: any[]) => import("csstype").ZIndexProperty & {}) | (() => import("csstype").ZIndexProperty))[], unknown, unknown, 100, boolean>;
    readonly target: import("element-plus/es/utils").EpPropFinalized<StringConstructor, unknown, unknown, "", boolean>;
    readonly offset: import("element-plus/es/utils").EpPropFinalized<NumberConstructor, unknown, unknown, 0, boolean>;
    readonly position: import("element-plus/es/utils").EpPropFinalized<StringConstructor, "top" | "bottom", unknown, "top", boolean>;
}>> & {
    onChange?: ((fixed: boolean) => any) | undefined;
    onScroll?: ((args_0: {
        scrollTop: number;
        fixed: boolean;
    }) => any) | undefined;
}, {
    readonly zIndex: import("element-plus/es/utils").EpPropMergeType<(new (...args: any[]) => import("csstype").ZIndexProperty & {}) | (() => import("csstype").ZIndexProperty) | ((new (...args: any[]) => import("csstype").ZIndexProperty & {}) | (() => import("csstype").ZIndexProperty))[], unknown, unknown>;
    readonly target: string;
    readonly offset: number;
    readonly position: import("element-plus/es/utils").EpPropMergeType<StringConstructor, "top" | "bottom", unknown>;
}>;
export default _default;
