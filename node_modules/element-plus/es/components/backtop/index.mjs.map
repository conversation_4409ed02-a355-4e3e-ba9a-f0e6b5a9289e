{"version": 3, "file": "index.mjs", "sources": ["../../../../../packages/components/backtop/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\n\nimport Backtop from './src/backtop.vue'\n\nexport const ElBacktop = withInstall(Backtop)\nexport default ElBacktop\n\nexport * from './src/backtop'\nexport type { BacktopInstance } from './src/instance'\n"], "names": [], "mappings": ";;;;;AAEY,MAAC,SAAS,GAAG,WAAW,CAAC,OAAO;;;;"}