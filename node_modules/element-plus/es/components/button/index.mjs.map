{"version": 3, "file": "index.mjs", "sources": ["../../../../../packages/components/button/index.ts"], "sourcesContent": ["import { with<PERSON><PERSON><PERSON>, with<PERSON>oop<PERSON>nstall } from '@element-plus/utils'\nimport Button from './src/button.vue'\nimport ButtonGroup from './src/button-group.vue'\n\nexport const ElButton = withInstall(Button, {\n  ButtonGroup,\n})\nexport const ElButtonGroup = with<PERSON><PERSON>Install(ButtonGroup)\nexport default ElButton\n\nexport * from './src/button'\nexport * from './src/constants'\nexport type { ButtonInstance, ButtonGroupInstance } from './src/instance'\n"], "names": [], "mappings": ";;;;;;;AAGY,MAAC,QAAQ,GAAG,WAAW,CAAC,MAAM,EAAE;AAC5C,EAAE,WAAW;AACb,CAAC,EAAE;AACS,MAAC,aAAa,GAAG,eAAe,CAAC,WAAW;;;;"}