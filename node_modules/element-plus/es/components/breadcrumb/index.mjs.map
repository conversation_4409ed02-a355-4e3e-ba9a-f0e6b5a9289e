{"version": 3, "file": "index.mjs", "sources": ["../../../../../packages/components/breadcrumb/index.ts"], "sourcesContent": ["import { with<PERSON><PERSON><PERSON>, with<PERSON><PERSON><PERSON>nstall } from '@element-plus/utils'\n\nimport Breadcrumb from './src/breadcrumb.vue'\nimport BreadcrumbItem from './src/breadcrumb-item.vue'\n\nexport const ElBreadcrumb = withInstall(Breadcrumb, {\n  BreadcrumbItem,\n})\nexport const ElBreadcrumbItem = withNoopInstall(BreadcrumbItem)\nexport default ElBreadcrumb\n\nexport * from './src/breadcrumb'\nexport * from './src/breadcrumb-item'\nexport * from './src/constants'\nexport type {\n  BreadcrumbInstance,\n  BreadcrumbItemInstance,\n} from './src/instances'\n"], "names": [], "mappings": ";;;;;;;;AAGY,MAAC,YAAY,GAAG,WAAW,CAAC,UAAU,EAAE;AACpD,EAAE,cAAc;AAChB,CAAC,EAAE;AACS,MAAC,gBAAgB,GAAG,eAAe,CAAC,cAAc;;;;"}