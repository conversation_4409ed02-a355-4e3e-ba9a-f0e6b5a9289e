{"version": 3, "file": "index.mjs", "sources": ["../../../../../packages/components/alert/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\n\nimport Alert from './src/alert.vue'\n\nexport const ElAlert = withInstall(Alert)\nexport default ElAlert\n\nexport * from './src/alert'\nexport type { AlertInstance } from './src/instance'\n"], "names": [], "mappings": ";;;;;AAEY,MAAC,OAAO,GAAG,WAAW,CAAC,KAAK;;;;"}