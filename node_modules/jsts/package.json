{"name": "jsts", "description": "A JavaScript library of spatial predicates and functions for processing geometry", "version": "2.7.1", "author": "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "keywords": ["JSTS", "JavaScript", "JTS", "Java", "Topology", "Geometry"], "license": "(EDL-1.0 OR EPL-1.0)", "devDependencies": {"@babel/core": "7.14.3", "@babel/preset-env": "7.14.4", "@babel/register": "7.13.16", "@babel/eslint-parser": "7.14.4", "@rollup/plugin-replace": "2.4.2", "@rollup/plugin-babel": "5.3.0", "chai": "4.3.4", "cheerio": "0.22.0", "codecov": "3.8.2", "eslint": "7.27.0", "esm": "3.2.25", "expect.js": "0.3.1", "git-rev-sync": "3.0.1", "jsdoc": "3.6.7", "mocha": "8.4.0", "mocha-lcov-reporter": "1.3.0", "nyc": "15.1.0", "rollup": "2.50.6", "terser": "5.7.0"}, "main": "dist/jsts.min.js", "engines": {"node": ">= 12"}, "repository": {"type": "git", "url": "git://github.com/bjornharrtell/jsts.git"}, "standard": {"globals": ["describe", "it", "jsts", "ol"]}, "scripts": {"lint": "eslint src", "test-manual": "NODE_PATH=src mocha --timeout 10s -r esm --recursive test/manual", "test": "NODE_PATH=src nyc mocha --timeout 10s -r esm --recursive test/auto/node test/manual", "test-perf": "NODE_PATH=src 0x -- node -r esm $(which _mocha) --timeout 10s --recursive test/auto/node test/manual", "coverage": "nyc report --reporter=text-lcov > coverage.lcov && codecov", "build-jsdoc": "jsdoc -c doc/jsdoc.json", "build": "rollup -c rollup.config.js -o dist/jsts.js && terser dist/jsts.js --source-map content=dist/jsts.js.map,filename=dist/jsts.min.js.map,url=jsts.min.js.map --comments -c -m -o dist/jsts.min.js", "build-es6": "rollup -c rollup.config.es6.js -o dist/jsts.es6.js && terser dist/jsts.es6.js --module --source-map content=dist/jsts.es6.js.map,filename=dist/jsts.es6.min.js.map,url=jsts.es6.min.js.map --comments -c -m -o dist/jsts.es6.min.js"}}