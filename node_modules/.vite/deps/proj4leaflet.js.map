{"version": 3, "sources": ["../../proj4leaflet/src/proj4leaflet.js"], "sourcesContent": ["(function (factory) {\r\n\tvar L, proj4;\r\n\tif (typeof define === 'function' && define.amd) {\r\n\t\t// AMD\r\n\t\tdefine(['leaflet', 'proj4'], factory);\r\n\t} else if (typeof module === 'object' && typeof module.exports === \"object\") {\r\n\t\t// Node/CommonJS\r\n\t\tL = require('leaflet');\r\n\t\tproj4 = require('proj4');\r\n\t\tmodule.exports = factory(L, proj4);\r\n\t} else {\r\n\t\t// Browser globals\r\n\t\tif (typeof window.L === 'undefined' || typeof window.proj4 === 'undefined')\r\n\t\t\tthrow 'Leaflet and proj4 must be loaded first';\r\n\t\tfactory(window.L, window.proj4);\r\n\t}\r\n}(function (L, proj4) {\r\n\tif (proj4.__esModule && proj4.default) {\r\n\t\t// If proj4 was bundled as an ES6 module, unwrap it to get\r\n\t\t// to the actual main proj4 object.\r\n\t\t// See discussion in https://github.com/kartena/Proj4Leaflet/pull/147\r\n\t\tproj4 = proj4.default;\r\n\t}\r\n \r\n\tL.Proj = {};\r\n\r\n\tL.Proj._isProj4Obj = function(a) {\r\n\t\treturn (typeof a.inverse !== 'undefined' &&\r\n\t\t\ttypeof a.forward !== 'undefined');\r\n\t};\r\n\r\n\tL.Proj.Projection = L.Class.extend({\r\n\t\tinitialize: function(code, def, bounds) {\r\n\t\t\tvar isP4 = L.Proj._isProj4Obj(code);\r\n\t\t\tthis._proj = isP4 ? code : this._projFromCodeDef(code, def);\r\n\t\t\tthis.bounds = isP4 ? def : bounds;\r\n\t\t},\r\n\r\n\t\tproject: function (latlng) {\r\n\t\t\tvar point = this._proj.forward([latlng.lng, latlng.lat]);\r\n\t\t\treturn new L.Point(point[0], point[1]);\r\n\t\t},\r\n\r\n\t\tunproject: function (point, unbounded) {\r\n\t\t\tvar point2 = this._proj.inverse([point.x, point.y]);\r\n\t\t\treturn new L.LatLng(point2[1], point2[0], unbounded);\r\n\t\t},\r\n\r\n\t\t_projFromCodeDef: function(code, def) {\r\n\t\t\tif (def) {\r\n\t\t\t\tproj4.defs(code, def);\r\n\t\t\t} else if (proj4.defs[code] === undefined) {\r\n\t\t\t\tvar urn = code.split(':');\r\n\t\t\t\tif (urn.length > 3) {\r\n\t\t\t\t\tcode = urn[urn.length - 3] + ':' + urn[urn.length - 1];\r\n\t\t\t\t}\r\n\t\t\t\tif (proj4.defs[code] === undefined) {\r\n\t\t\t\t\tthrow 'No projection definition for code ' + code;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\treturn proj4(code);\r\n\t\t}\r\n\t});\r\n\r\n\tL.Proj.CRS = L.Class.extend({\r\n\t\tincludes: L.CRS,\r\n\r\n\t\toptions: {\r\n\t\t\ttransformation: new L.Transformation(1, 0, -1, 0)\r\n\t\t},\r\n\r\n\t\tinitialize: function(a, b, c) {\r\n\t\t\tvar code,\r\n\t\t\t    proj,\r\n\t\t\t    def,\r\n\t\t\t    options;\r\n\r\n\t\t\tif (L.Proj._isProj4Obj(a)) {\r\n\t\t\t\tproj = a;\r\n\t\t\t\tcode = proj.srsCode;\r\n\t\t\t\toptions = b || {};\r\n\r\n\t\t\t\tthis.projection = new L.Proj.Projection(proj, options.bounds);\r\n\t\t\t} else {\r\n\t\t\t\tcode = a;\r\n\t\t\t\tdef = b;\r\n\t\t\t\toptions = c || {};\r\n\t\t\t\tthis.projection = new L.Proj.Projection(code, def, options.bounds);\r\n\t\t\t}\r\n\r\n\t\t\tL.Util.setOptions(this, options);\r\n\t\t\tthis.code = code;\r\n\t\t\tthis.transformation = this.options.transformation;\r\n\r\n\t\t\tif (this.options.origin) {\r\n\t\t\t\tthis.transformation =\r\n\t\t\t\t\tnew L.Transformation(1, -this.options.origin[0],\r\n\t\t\t\t\t\t-1, this.options.origin[1]);\r\n\t\t\t}\r\n\r\n\t\t\tif (this.options.scales) {\r\n\t\t\t\tthis._scales = this.options.scales;\r\n\t\t\t} else if (this.options.resolutions) {\r\n\t\t\t\tthis._scales = [];\r\n\t\t\t\tfor (var i = this.options.resolutions.length - 1; i >= 0; i--) {\r\n\t\t\t\t\tif (this.options.resolutions[i]) {\r\n\t\t\t\t\t\tthis._scales[i] = 1 / this.options.resolutions[i];\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\tthis.infinite = !this.options.bounds;\r\n\r\n\t\t},\r\n\r\n\t\tscale: function(zoom) {\r\n\t\t\tvar iZoom = Math.floor(zoom),\r\n\t\t\t\tbaseScale,\r\n\t\t\t\tnextScale,\r\n\t\t\t\tscaleDiff,\r\n\t\t\t\tzDiff;\r\n\t\t\tif (zoom === iZoom) {\r\n\t\t\t\treturn this._scales[zoom];\r\n\t\t\t} else {\r\n\t\t\t\t// Non-integer zoom, interpolate\r\n\t\t\t\tbaseScale = this._scales[iZoom];\r\n\t\t\t\tnextScale = this._scales[iZoom + 1];\r\n\t\t\t\tscaleDiff = nextScale - baseScale;\r\n\t\t\t\tzDiff = (zoom - iZoom);\r\n\t\t\t\treturn baseScale + scaleDiff * zDiff;\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\tzoom: function(scale) {\r\n\t\t\t// Find closest number in this._scales, down\r\n\t\t\tvar downScale = this._closestElement(this._scales, scale),\r\n\t\t\t\tdownZoom = this._scales.indexOf(downScale),\r\n\t\t\t\tnextScale,\r\n\t\t\t\tnextZoom,\r\n\t\t\t\tscaleDiff;\r\n\t\t\t// Check if scale is downScale => return array index\r\n\t\t\tif (scale === downScale) {\r\n\t\t\t\treturn downZoom;\r\n\t\t\t}\r\n\t\t\tif (downScale === undefined) {\r\n\t\t\t\treturn -Infinity;\r\n\t\t\t}\r\n\t\t\t// Interpolate\r\n\t\t\tnextZoom = downZoom + 1;\r\n\t\t\tnextScale = this._scales[nextZoom];\r\n\t\t\tif (nextScale === undefined) {\r\n\t\t\t\treturn Infinity;\r\n\t\t\t}\r\n\t\t\tscaleDiff = nextScale - downScale;\r\n\t\t\treturn (scale - downScale) / scaleDiff + downZoom;\r\n\t\t},\r\n\r\n\t\tdistance: L.CRS.Earth.distance,\r\n\r\n\t\tR: L.CRS.Earth.R,\r\n\r\n\t\t/* Get the closest lowest element in an array */\r\n\t\t_closestElement: function(array, element) {\r\n\t\t\tvar low;\r\n\t\t\tfor (var i = array.length; i--;) {\r\n\t\t\t\tif (array[i] <= element && (low === undefined || low < array[i])) {\r\n\t\t\t\t\tlow = array[i];\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\treturn low;\r\n\t\t}\r\n\t});\r\n\r\n\tL.Proj.GeoJSON = L.GeoJSON.extend({\r\n\t\tinitialize: function(geojson, options) {\r\n\t\t\tthis._callLevel = 0;\r\n\t\t\tL.GeoJSON.prototype.initialize.call(this, geojson, options);\r\n\t\t},\r\n\r\n\t\taddData: function(geojson) {\r\n\t\t\tvar crs;\r\n\r\n\t\t\tif (geojson) {\r\n\t\t\t\tif (geojson.crs && geojson.crs.type === 'name') {\r\n\t\t\t\t\tcrs = new L.Proj.CRS(geojson.crs.properties.name);\r\n\t\t\t\t} else if (geojson.crs && geojson.crs.type) {\r\n\t\t\t\t\tcrs = new L.Proj.CRS(geojson.crs.type + ':' + geojson.crs.properties.code);\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (crs !== undefined) {\r\n\t\t\t\t\tthis.options.coordsToLatLng = function(coords) {\r\n\t\t\t\t\t\tvar point = L.point(coords[0], coords[1]);\r\n\t\t\t\t\t\treturn crs.projection.unproject(point);\r\n\t\t\t\t\t};\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t// Base class' addData might call us recursively, but\r\n\t\t\t// CRS shouldn't be cleared in that case, since CRS applies\r\n\t\t\t// to the whole GeoJSON, inluding sub-features.\r\n\t\t\tthis._callLevel++;\r\n\t\t\ttry {\r\n\t\t\t\tL.GeoJSON.prototype.addData.call(this, geojson);\r\n\t\t\t} finally {\r\n\t\t\t\tthis._callLevel--;\r\n\t\t\t\tif (this._callLevel === 0) {\r\n\t\t\t\t\tdelete this.options.coordsToLatLng;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t});\r\n\r\n\tL.Proj.geoJson = function(geojson, options) {\r\n\t\treturn new L.Proj.GeoJSON(geojson, options);\r\n\t};\r\n\r\n\tL.Proj.ImageOverlay = L.ImageOverlay.extend({\r\n\t\tinitialize: function (url, bounds, options) {\r\n\t\t\tL.ImageOverlay.prototype.initialize.call(this, url, null, options);\r\n\t\t\tthis._projectedBounds = bounds;\r\n\t\t},\r\n\r\n\t\t// Danger ahead: Overriding internal methods in Leaflet.\r\n\t\t// Decided to do this rather than making a copy of L.ImageOverlay\r\n\t\t// and doing very tiny modifications to it.\r\n\t\t// Future will tell if this was wise or not.\r\n\t\t_animateZoom: function (event) {\r\n\t\t\tvar scale = this._map.getZoomScale(event.zoom);\r\n\t\t\tvar northWest = L.point(this._projectedBounds.min.x, this._projectedBounds.max.y);\r\n\t\t\tvar offset = this._projectedToNewLayerPoint(northWest, event.zoom, event.center);\r\n\r\n\t\t\tL.DomUtil.setTransform(this._image, offset, scale);\r\n\t\t},\r\n\r\n\t\t_reset: function () {\r\n\t\t\tvar zoom = this._map.getZoom();\r\n\t\t\tvar pixelOrigin = this._map.getPixelOrigin();\r\n\t\t\tvar bounds = L.bounds(\r\n\t\t\t\tthis._transform(this._projectedBounds.min, zoom)._subtract(pixelOrigin),\r\n\t\t\t\tthis._transform(this._projectedBounds.max, zoom)._subtract(pixelOrigin)\r\n\t\t\t);\r\n\t\t\tvar size = bounds.getSize();\r\n\r\n\t\t\tL.DomUtil.setPosition(this._image, bounds.min);\r\n\t\t\tthis._image.style.width = size.x + 'px';\r\n\t\t\tthis._image.style.height = size.y + 'px';\r\n\t\t},\r\n\r\n\t\t_projectedToNewLayerPoint: function (point, zoom, center) {\r\n\t\t\tvar viewHalf = this._map.getSize()._divideBy(2);\r\n\t\t\tvar newTopLeft = this._map.project(center, zoom)._subtract(viewHalf)._round();\r\n\t\t\tvar topLeft = newTopLeft.add(this._map._getMapPanePos());\r\n\r\n\t\t\treturn this._transform(point, zoom)._subtract(topLeft);\r\n\t\t},\r\n\r\n\t\t_transform: function (point, zoom) {\r\n\t\t\tvar crs = this._map.options.crs;\r\n\t\t\tvar transformation = crs.transformation;\r\n\t\t\tvar scale = crs.scale(zoom);\r\n\r\n\t\t\treturn transformation.transform(point, scale);\r\n\t\t}\r\n\t});\r\n\r\n\tL.Proj.imageOverlay = function (url, bounds, options) {\r\n\t\treturn new L.Proj.ImageOverlay(url, bounds, options);\r\n\t};\r\n\r\n\treturn L.Proj;\r\n}));\r\n"], "mappings": ";;;;;;;;;;;;;AAAA;AAAA;AAAA,KAAC,SAAU,SAAS;AACnB,UAAI,GAAG;AACP,UAAI,OAAO,WAAW,cAAc,OAAO,KAAK;AAE/C,eAAO,CAAC,WAAW,OAAO,GAAG,OAAO;AAAA,MACrC,WAAW,OAAO,WAAW,YAAY,OAAO,OAAO,YAAY,UAAU;AAE5E,YAAI;AACJ,gBAAQ;AACR,eAAO,UAAU,QAAQ,GAAG,KAAK;AAAA,MAClC,OAAO;AAEN,YAAI,OAAO,OAAO,MAAM,eAAe,OAAO,OAAO,UAAU;AAC9D,gBAAM;AACP,gBAAQ,OAAO,GAAG,OAAO,KAAK;AAAA,MAC/B;AAAA,IACD,GAAE,SAAU,GAAG,OAAO;AACrB,UAAI,MAAM,cAAc,MAAM,SAAS;AAItC,gBAAQ,MAAM;AAAA,MACf;AAEA,QAAE,OAAO,CAAC;AAEV,QAAE,KAAK,cAAc,SAAS,GAAG;AAChC,eAAQ,OAAO,EAAE,YAAY,eAC5B,OAAO,EAAE,YAAY;AAAA,MACvB;AAEA,QAAE,KAAK,aAAa,EAAE,MAAM,OAAO;AAAA,QAClC,YAAY,SAAS,MAAM,KAAK,QAAQ;AACvC,cAAI,OAAO,EAAE,KAAK,YAAY,IAAI;AAClC,eAAK,QAAQ,OAAO,OAAO,KAAK,iBAAiB,MAAM,GAAG;AAC1D,eAAK,SAAS,OAAO,MAAM;AAAA,QAC5B;AAAA,QAEA,SAAS,SAAU,QAAQ;AAC1B,cAAI,QAAQ,KAAK,MAAM,QAAQ,CAAC,OAAO,KAAK,OAAO,GAAG,CAAC;AACvD,iBAAO,IAAI,EAAE,MAAM,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC;AAAA,QACtC;AAAA,QAEA,WAAW,SAAU,OAAO,WAAW;AACtC,cAAI,SAAS,KAAK,MAAM,QAAQ,CAAC,MAAM,GAAG,MAAM,CAAC,CAAC;AAClD,iBAAO,IAAI,EAAE,OAAO,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,SAAS;AAAA,QACpD;AAAA,QAEA,kBAAkB,SAAS,MAAM,KAAK;AACrC,cAAI,KAAK;AACR,kBAAM,KAAK,MAAM,GAAG;AAAA,UACrB,WAAW,MAAM,KAAK,IAAI,MAAM,QAAW;AAC1C,gBAAI,MAAM,KAAK,MAAM,GAAG;AACxB,gBAAI,IAAI,SAAS,GAAG;AACnB,qBAAO,IAAI,IAAI,SAAS,CAAC,IAAI,MAAM,IAAI,IAAI,SAAS,CAAC;AAAA,YACtD;AACA,gBAAI,MAAM,KAAK,IAAI,MAAM,QAAW;AACnC,oBAAM,uCAAuC;AAAA,YAC9C;AAAA,UACD;AAEA,iBAAO,MAAM,IAAI;AAAA,QAClB;AAAA,MACD,CAAC;AAED,QAAE,KAAK,MAAM,EAAE,MAAM,OAAO;AAAA,QAC3B,UAAU,EAAE;AAAA,QAEZ,SAAS;AAAA,UACR,gBAAgB,IAAI,EAAE,eAAe,GAAG,GAAG,IAAI,CAAC;AAAA,QACjD;AAAA,QAEA,YAAY,SAAS,GAAG,GAAG,GAAG;AAC7B,cAAI,MACA,MACA,KACA;AAEJ,cAAI,EAAE,KAAK,YAAY,CAAC,GAAG;AAC1B,mBAAO;AACP,mBAAO,KAAK;AACZ,sBAAU,KAAK,CAAC;AAEhB,iBAAK,aAAa,IAAI,EAAE,KAAK,WAAW,MAAM,QAAQ,MAAM;AAAA,UAC7D,OAAO;AACN,mBAAO;AACP,kBAAM;AACN,sBAAU,KAAK,CAAC;AAChB,iBAAK,aAAa,IAAI,EAAE,KAAK,WAAW,MAAM,KAAK,QAAQ,MAAM;AAAA,UAClE;AAEA,YAAE,KAAK,WAAW,MAAM,OAAO;AAC/B,eAAK,OAAO;AACZ,eAAK,iBAAiB,KAAK,QAAQ;AAEnC,cAAI,KAAK,QAAQ,QAAQ;AACxB,iBAAK,iBACJ,IAAI,EAAE;AAAA,cAAe;AAAA,cAAG,CAAC,KAAK,QAAQ,OAAO,CAAC;AAAA,cAC7C;AAAA,cAAI,KAAK,QAAQ,OAAO,CAAC;AAAA,YAAC;AAAA,UAC7B;AAEA,cAAI,KAAK,QAAQ,QAAQ;AACxB,iBAAK,UAAU,KAAK,QAAQ;AAAA,UAC7B,WAAW,KAAK,QAAQ,aAAa;AACpC,iBAAK,UAAU,CAAC;AAChB,qBAAS,IAAI,KAAK,QAAQ,YAAY,SAAS,GAAG,KAAK,GAAG,KAAK;AAC9D,kBAAI,KAAK,QAAQ,YAAY,CAAC,GAAG;AAChC,qBAAK,QAAQ,CAAC,IAAI,IAAI,KAAK,QAAQ,YAAY,CAAC;AAAA,cACjD;AAAA,YACD;AAAA,UACD;AAEA,eAAK,WAAW,CAAC,KAAK,QAAQ;AAAA,QAE/B;AAAA,QAEA,OAAO,SAAS,MAAM;AACrB,cAAI,QAAQ,KAAK,MAAM,IAAI,GAC1B,WACA,WACA,WACA;AACD,cAAI,SAAS,OAAO;AACnB,mBAAO,KAAK,QAAQ,IAAI;AAAA,UACzB,OAAO;AAEN,wBAAY,KAAK,QAAQ,KAAK;AAC9B,wBAAY,KAAK,QAAQ,QAAQ,CAAC;AAClC,wBAAY,YAAY;AACxB,oBAAS,OAAO;AAChB,mBAAO,YAAY,YAAY;AAAA,UAChC;AAAA,QACD;AAAA,QAEA,MAAM,SAAS,OAAO;AAErB,cAAI,YAAY,KAAK,gBAAgB,KAAK,SAAS,KAAK,GACvD,WAAW,KAAK,QAAQ,QAAQ,SAAS,GACzC,WACA,UACA;AAED,cAAI,UAAU,WAAW;AACxB,mBAAO;AAAA,UACR;AACA,cAAI,cAAc,QAAW;AAC5B,mBAAO;AAAA,UACR;AAEA,qBAAW,WAAW;AACtB,sBAAY,KAAK,QAAQ,QAAQ;AACjC,cAAI,cAAc,QAAW;AAC5B,mBAAO;AAAA,UACR;AACA,sBAAY,YAAY;AACxB,kBAAQ,QAAQ,aAAa,YAAY;AAAA,QAC1C;AAAA,QAEA,UAAU,EAAE,IAAI,MAAM;AAAA,QAEtB,GAAG,EAAE,IAAI,MAAM;AAAA;AAAA,QAGf,iBAAiB,SAAS,OAAO,SAAS;AACzC,cAAI;AACJ,mBAAS,IAAI,MAAM,QAAQ,OAAM;AAChC,gBAAI,MAAM,CAAC,KAAK,YAAY,QAAQ,UAAa,MAAM,MAAM,CAAC,IAAI;AACjE,oBAAM,MAAM,CAAC;AAAA,YACd;AAAA,UACD;AACA,iBAAO;AAAA,QACR;AAAA,MACD,CAAC;AAED,QAAE,KAAK,UAAU,EAAE,QAAQ,OAAO;AAAA,QACjC,YAAY,SAAS,SAAS,SAAS;AACtC,eAAK,aAAa;AAClB,YAAE,QAAQ,UAAU,WAAW,KAAK,MAAM,SAAS,OAAO;AAAA,QAC3D;AAAA,QAEA,SAAS,SAAS,SAAS;AAC1B,cAAI;AAEJ,cAAI,SAAS;AACZ,gBAAI,QAAQ,OAAO,QAAQ,IAAI,SAAS,QAAQ;AAC/C,oBAAM,IAAI,EAAE,KAAK,IAAI,QAAQ,IAAI,WAAW,IAAI;AAAA,YACjD,WAAW,QAAQ,OAAO,QAAQ,IAAI,MAAM;AAC3C,oBAAM,IAAI,EAAE,KAAK,IAAI,QAAQ,IAAI,OAAO,MAAM,QAAQ,IAAI,WAAW,IAAI;AAAA,YAC1E;AAEA,gBAAI,QAAQ,QAAW;AACtB,mBAAK,QAAQ,iBAAiB,SAAS,QAAQ;AAC9C,oBAAI,QAAQ,EAAE,MAAM,OAAO,CAAC,GAAG,OAAO,CAAC,CAAC;AACxC,uBAAO,IAAI,WAAW,UAAU,KAAK;AAAA,cACtC;AAAA,YACD;AAAA,UACD;AAKA,eAAK;AACL,cAAI;AACH,cAAE,QAAQ,UAAU,QAAQ,KAAK,MAAM,OAAO;AAAA,UAC/C,UAAE;AACD,iBAAK;AACL,gBAAI,KAAK,eAAe,GAAG;AAC1B,qBAAO,KAAK,QAAQ;AAAA,YACrB;AAAA,UACD;AAAA,QACD;AAAA,MACD,CAAC;AAED,QAAE,KAAK,UAAU,SAAS,SAAS,SAAS;AAC3C,eAAO,IAAI,EAAE,KAAK,QAAQ,SAAS,OAAO;AAAA,MAC3C;AAEA,QAAE,KAAK,eAAe,EAAE,aAAa,OAAO;AAAA,QAC3C,YAAY,SAAU,KAAK,QAAQ,SAAS;AAC3C,YAAE,aAAa,UAAU,WAAW,KAAK,MAAM,KAAK,MAAM,OAAO;AACjE,eAAK,mBAAmB;AAAA,QACzB;AAAA;AAAA;AAAA;AAAA;AAAA,QAMA,cAAc,SAAU,OAAO;AAC9B,cAAI,QAAQ,KAAK,KAAK,aAAa,MAAM,IAAI;AAC7C,cAAI,YAAY,EAAE,MAAM,KAAK,iBAAiB,IAAI,GAAG,KAAK,iBAAiB,IAAI,CAAC;AAChF,cAAI,SAAS,KAAK,0BAA0B,WAAW,MAAM,MAAM,MAAM,MAAM;AAE/E,YAAE,QAAQ,aAAa,KAAK,QAAQ,QAAQ,KAAK;AAAA,QAClD;AAAA,QAEA,QAAQ,WAAY;AACnB,cAAI,OAAO,KAAK,KAAK,QAAQ;AAC7B,cAAI,cAAc,KAAK,KAAK,eAAe;AAC3C,cAAI,SAAS,EAAE;AAAA,YACd,KAAK,WAAW,KAAK,iBAAiB,KAAK,IAAI,EAAE,UAAU,WAAW;AAAA,YACtE,KAAK,WAAW,KAAK,iBAAiB,KAAK,IAAI,EAAE,UAAU,WAAW;AAAA,UACvE;AACA,cAAI,OAAO,OAAO,QAAQ;AAE1B,YAAE,QAAQ,YAAY,KAAK,QAAQ,OAAO,GAAG;AAC7C,eAAK,OAAO,MAAM,QAAQ,KAAK,IAAI;AACnC,eAAK,OAAO,MAAM,SAAS,KAAK,IAAI;AAAA,QACrC;AAAA,QAEA,2BAA2B,SAAU,OAAO,MAAM,QAAQ;AACzD,cAAI,WAAW,KAAK,KAAK,QAAQ,EAAE,UAAU,CAAC;AAC9C,cAAI,aAAa,KAAK,KAAK,QAAQ,QAAQ,IAAI,EAAE,UAAU,QAAQ,EAAE,OAAO;AAC5E,cAAI,UAAU,WAAW,IAAI,KAAK,KAAK,eAAe,CAAC;AAEvD,iBAAO,KAAK,WAAW,OAAO,IAAI,EAAE,UAAU,OAAO;AAAA,QACtD;AAAA,QAEA,YAAY,SAAU,OAAO,MAAM;AAClC,cAAI,MAAM,KAAK,KAAK,QAAQ;AAC5B,cAAI,iBAAiB,IAAI;AACzB,cAAI,QAAQ,IAAI,MAAM,IAAI;AAE1B,iBAAO,eAAe,UAAU,OAAO,KAAK;AAAA,QAC7C;AAAA,MACD,CAAC;AAED,QAAE,KAAK,eAAe,SAAU,KAAK,QAAQ,SAAS;AACrD,eAAO,IAAI,EAAE,KAAK,aAAa,KAAK,QAAQ,OAAO;AAAA,MACpD;AAEA,aAAO,EAAE;AAAA,IACV,CAAC;AAAA;AAAA;", "names": []}