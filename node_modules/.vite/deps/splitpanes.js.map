{"version": 3, "sources": ["../../splitpanes/dist/splitpanes.es.js"], "sourcesContent": ["import { h as c, openBlock as m, createElementBlock as p, normalizeStyle as z, renderSlot as f } from \"vue\";\nconst M = {\n  name: \"splitpanes\",\n  emits: [\"ready\", \"resize\", \"resized\", \"pane-click\", \"pane-maximize\", \"pane-add\", \"pane-remove\", \"splitter-click\"],\n  props: {\n    horizontal: { type: Boolean },\n    pushOtherPanes: { type: Boolean, default: !0 },\n    dblClickSplitter: { type: Boolean, default: !0 },\n    rtl: { type: Boolean, default: !1 },\n    firstSplitter: { type: Boolean }\n  },\n  provide() {\n    return {\n      requestUpdate: this.requestUpdate,\n      onPaneAdd: this.onPaneAdd,\n      onPaneRemove: this.onPaneRemove,\n      onPaneClick: this.onPaneClick\n    };\n  },\n  data: () => ({\n    container: null,\n    ready: !1,\n    panes: [],\n    touch: {\n      mouseDown: !1,\n      dragging: !1,\n      activeSplitter: null\n    },\n    splitterTaps: {\n      splitter: null,\n      timeoutId: null\n    }\n  }),\n  computed: {\n    panesCount() {\n      return this.panes.length;\n    },\n    indexedPanes() {\n      return this.panes.reduce((e, i) => (e[i.id] = i) && e, {});\n    }\n  },\n  methods: {\n    updatePaneComponents() {\n      this.panes.forEach((e) => {\n        e.update && e.update({\n          [this.horizontal ? \"height\" : \"width\"]: `${this.indexedPanes[e.id].size}%`\n        });\n      });\n    },\n    bindEvents() {\n      document.addEventListener(\"mousemove\", this.onMouseMove, { passive: !1 }), document.addEventListener(\"mouseup\", this.onMouseUp), \"ontouchstart\" in window && (document.addEventListener(\"touchmove\", this.onMouseMove, { passive: !1 }), document.addEventListener(\"touchend\", this.onMouseUp));\n    },\n    unbindEvents() {\n      document.removeEventListener(\"mousemove\", this.onMouseMove, { passive: !1 }), document.removeEventListener(\"mouseup\", this.onMouseUp), \"ontouchstart\" in window && (document.removeEventListener(\"touchmove\", this.onMouseMove, { passive: !1 }), document.removeEventListener(\"touchend\", this.onMouseUp));\n    },\n    onMouseDown(e, i) {\n      this.bindEvents(), this.touch.mouseDown = !0, this.touch.activeSplitter = i;\n    },\n    onMouseMove(e) {\n      this.touch.mouseDown && (e.preventDefault(), this.touch.dragging = !0, this.calculatePanesSize(this.getCurrentMouseDrag(e)), this.$emit(\"resize\", this.panes.map((i) => ({ min: i.min, max: i.max, size: i.size }))));\n    },\n    onMouseUp() {\n      this.touch.dragging && this.$emit(\"resized\", this.panes.map((e) => ({ min: e.min, max: e.max, size: e.size }))), this.touch.mouseDown = !1, setTimeout(() => {\n        this.touch.dragging = !1, this.unbindEvents();\n      }, 100);\n    },\n    onSplitterClick(e, i) {\n      \"ontouchstart\" in window && (e.preventDefault(), this.dblClickSplitter && (this.splitterTaps.splitter === i ? (clearTimeout(this.splitterTaps.timeoutId), this.splitterTaps.timeoutId = null, this.onSplitterDblClick(e, i), this.splitterTaps.splitter = null) : (this.splitterTaps.splitter = i, this.splitterTaps.timeoutId = setTimeout(() => {\n        this.splitterTaps.splitter = null;\n      }, 500)))), this.touch.dragging || this.$emit(\"splitter-click\", this.panes[i]);\n    },\n    onSplitterDblClick(e, i) {\n      let s = 0;\n      this.panes = this.panes.map((n, t) => (n.size = t === i ? n.max : n.min, t !== i && (s += n.min), n)), this.panes[i].size -= s, this.$emit(\"pane-maximize\", this.panes[i]), this.$emit(\"resized\", this.panes.map((n) => ({ min: n.min, max: n.max, size: n.size })));\n    },\n    onPaneClick(e, i) {\n      this.$emit(\"pane-click\", this.indexedPanes[i]);\n    },\n    getCurrentMouseDrag(e) {\n      const i = this.container.getBoundingClientRect(), { clientX: s, clientY: n } = \"ontouchstart\" in window && e.touches ? e.touches[0] : e;\n      return {\n        x: s - i.left,\n        y: n - i.top\n      };\n    },\n    getCurrentDragPercentage(e) {\n      e = e[this.horizontal ? \"y\" : \"x\"];\n      const i = this.container[this.horizontal ? \"clientHeight\" : \"clientWidth\"];\n      return this.rtl && !this.horizontal && (e = i - e), e * 100 / i;\n    },\n    calculatePanesSize(e) {\n      const i = this.touch.activeSplitter;\n      let s = {\n        prevPanesSize: this.sumPrevPanesSize(i),\n        nextPanesSize: this.sumNextPanesSize(i),\n        prevReachedMinPanes: 0,\n        nextReachedMinPanes: 0\n      };\n      const n = 0 + (this.pushOtherPanes ? 0 : s.prevPanesSize), t = 100 - (this.pushOtherPanes ? 0 : s.nextPanesSize), a = Math.max(Math.min(this.getCurrentDragPercentage(e), t), n);\n      let r = [i, i + 1], o = this.panes[r[0]] || null, h = this.panes[r[1]] || null;\n      const l = o.max < 100 && a >= o.max + s.prevPanesSize, u = h.max < 100 && a <= 100 - (h.max + this.sumNextPanesSize(i + 1));\n      if (l || u) {\n        l ? (o.size = o.max, h.size = Math.max(100 - o.max - s.prevPanesSize - s.nextPanesSize, 0)) : (o.size = Math.max(100 - h.max - s.prevPanesSize - this.sumNextPanesSize(i + 1), 0), h.size = h.max);\n        return;\n      }\n      if (this.pushOtherPanes) {\n        const d = this.doPushOtherPanes(s, a);\n        if (!d)\n          return;\n        ({ sums: s, panesToResize: r } = d), o = this.panes[r[0]] || null, h = this.panes[r[1]] || null;\n      }\n      o !== null && (o.size = Math.min(Math.max(a - s.prevPanesSize - s.prevReachedMinPanes, o.min), o.max)), h !== null && (h.size = Math.min(Math.max(100 - a - s.nextPanesSize - s.nextReachedMinPanes, h.min), h.max));\n    },\n    doPushOtherPanes(e, i) {\n      const s = this.touch.activeSplitter, n = [s, s + 1];\n      return i < e.prevPanesSize + this.panes[n[0]].min && (n[0] = this.findPrevExpandedPane(s).index, e.prevReachedMinPanes = 0, n[0] < s && this.panes.forEach((t, a) => {\n        a > n[0] && a <= s && (t.size = t.min, e.prevReachedMinPanes += t.min);\n      }), e.prevPanesSize = this.sumPrevPanesSize(n[0]), n[0] === void 0) ? (e.prevReachedMinPanes = 0, this.panes[0].size = this.panes[0].min, this.panes.forEach((t, a) => {\n        a > 0 && a <= s && (t.size = t.min, e.prevReachedMinPanes += t.min);\n      }), this.panes[n[1]].size = 100 - e.prevReachedMinPanes - this.panes[0].min - e.prevPanesSize - e.nextPanesSize, null) : i > 100 - e.nextPanesSize - this.panes[n[1]].min && (n[1] = this.findNextExpandedPane(s).index, e.nextReachedMinPanes = 0, n[1] > s + 1 && this.panes.forEach((t, a) => {\n        a > s && a < n[1] && (t.size = t.min, e.nextReachedMinPanes += t.min);\n      }), e.nextPanesSize = this.sumNextPanesSize(n[1] - 1), n[1] === void 0) ? (e.nextReachedMinPanes = 0, this.panes[this.panesCount - 1].size = this.panes[this.panesCount - 1].min, this.panes.forEach((t, a) => {\n        a < this.panesCount - 1 && a >= s + 1 && (t.size = t.min, e.nextReachedMinPanes += t.min);\n      }), this.panes[n[0]].size = 100 - e.prevPanesSize - e.nextReachedMinPanes - this.panes[this.panesCount - 1].min - e.nextPanesSize, null) : { sums: e, panesToResize: n };\n    },\n    sumPrevPanesSize(e) {\n      return this.panes.reduce((i, s, n) => i + (n < e ? s.size : 0), 0);\n    },\n    sumNextPanesSize(e) {\n      return this.panes.reduce((i, s, n) => i + (n > e + 1 ? s.size : 0), 0);\n    },\n    findPrevExpandedPane(e) {\n      return [...this.panes].reverse().find((s) => s.index < e && s.size > s.min) || {};\n    },\n    findNextExpandedPane(e) {\n      return this.panes.find((s) => s.index > e + 1 && s.size > s.min) || {};\n    },\n    checkSplitpanesNodes() {\n      Array.from(this.container.children).forEach((i) => {\n        const s = i.classList.contains(\"splitpanes__pane\"), n = i.classList.contains(\"splitpanes__splitter\");\n        !s && !n && (i.parentNode.removeChild(i), console.warn(\"Splitpanes: Only <pane> elements are allowed at the root of <splitpanes>. One of your DOM nodes was removed.\"));\n      });\n    },\n    addSplitter(e, i, s = !1) {\n      const n = e - 1, t = document.createElement(\"div\");\n      t.classList.add(\"splitpanes__splitter\"), s || (t.onmousedown = (a) => this.onMouseDown(a, n), typeof window < \"u\" && \"ontouchstart\" in window && (t.ontouchstart = (a) => this.onMouseDown(a, n)), t.onclick = (a) => this.onSplitterClick(a, n + 1)), this.dblClickSplitter && (t.ondblclick = (a) => this.onSplitterDblClick(a, n + 1)), i.parentNode.insertBefore(t, i);\n    },\n    removeSplitter(e) {\n      e.onmousedown = void 0, e.onclick = void 0, e.ondblclick = void 0, e.parentNode.removeChild(e);\n    },\n    redoSplitters() {\n      const e = Array.from(this.container.children);\n      e.forEach((s) => {\n        s.className.includes(\"splitpanes__splitter\") && this.removeSplitter(s);\n      });\n      let i = 0;\n      e.forEach((s) => {\n        s.className.includes(\"splitpanes__pane\") && (!i && this.firstSplitter ? this.addSplitter(i, s, !0) : i && this.addSplitter(i, s), i++);\n      });\n    },\n    requestUpdate({ target: e, ...i }) {\n      const s = this.indexedPanes[e._.uid];\n      Object.entries(i).forEach(([n, t]) => s[n] = t);\n    },\n    onPaneAdd(e) {\n      let i = -1;\n      Array.from(e.$el.parentNode.children).some((t) => (t.className.includes(\"splitpanes__pane\") && i++, t === e.$el));\n      const s = parseFloat(e.minSize), n = parseFloat(e.maxSize);\n      this.panes.splice(i, 0, {\n        id: e._.uid,\n        index: i,\n        min: isNaN(s) ? 0 : s,\n        max: isNaN(n) ? 100 : n,\n        size: e.size === null ? null : parseFloat(e.size),\n        givenSize: e.size,\n        update: e.update\n      }), this.panes.forEach((t, a) => t.index = a), this.ready && this.$nextTick(() => {\n        this.redoSplitters(), this.resetPaneSizes({ addedPane: this.panes[i] }), this.$emit(\"pane-add\", { index: i, panes: this.panes.map((t) => ({ min: t.min, max: t.max, size: t.size })) });\n      });\n    },\n    onPaneRemove(e) {\n      const i = this.panes.findIndex((n) => n.id === e._.uid), s = this.panes.splice(i, 1)[0];\n      this.panes.forEach((n, t) => n.index = t), this.$nextTick(() => {\n        this.redoSplitters(), this.resetPaneSizes({ removedPane: { ...s, index: i } }), this.$emit(\"pane-remove\", { removed: s, panes: this.panes.map((n) => ({ min: n.min, max: n.max, size: n.size })) });\n      });\n    },\n    resetPaneSizes(e = {}) {\n      !e.addedPane && !e.removedPane ? this.initialPanesSizing() : this.panes.some((i) => i.givenSize !== null || i.min || i.max < 100) ? this.equalizeAfterAddOrRemove(e) : this.equalize(), this.ready && this.$emit(\"resized\", this.panes.map((i) => ({ min: i.min, max: i.max, size: i.size })));\n    },\n    equalize() {\n      const e = 100 / this.panesCount;\n      let i = 0;\n      const s = [], n = [];\n      this.panes.forEach((t) => {\n        t.size = Math.max(Math.min(e, t.max), t.min), i -= t.size, t.size >= t.max && s.push(t.id), t.size <= t.min && n.push(t.id);\n      }), i > 0.1 && this.readjustSizes(i, s, n);\n    },\n    initialPanesSizing() {\n      let e = 100;\n      const i = [], s = [];\n      let n = 0;\n      this.panes.forEach((a) => {\n        e -= a.size, a.size !== null && n++, a.size >= a.max && i.push(a.id), a.size <= a.min && s.push(a.id);\n      });\n      let t = 100;\n      e > 0.1 && (this.panes.forEach((a) => {\n        a.size === null && (a.size = Math.max(Math.min(e / (this.panesCount - n), a.max), a.min)), t -= a.size;\n      }), t > 0.1 && this.readjustSizes(e, i, s));\n    },\n    equalizeAfterAddOrRemove({ addedPane: e, removedPane: i } = {}) {\n      let s = 100 / this.panesCount, n = 0;\n      const t = [], a = [];\n      e && e.givenSize !== null && (s = (100 - e.givenSize) / (this.panesCount - 1)), this.panes.forEach((r) => {\n        n -= r.size, r.size >= r.max && t.push(r.id), r.size <= r.min && a.push(r.id);\n      }), !(Math.abs(n) < 0.1) && (this.panes.forEach((r) => {\n        e && e.givenSize !== null && e.id === r.id || (r.size = Math.max(Math.min(s, r.max), r.min)), n -= r.size, r.size >= r.max && t.push(r.id), r.size <= r.min && a.push(r.id);\n      }), n > 0.1 && this.readjustSizes(n, t, a));\n    },\n    readjustSizes(e, i, s) {\n      let n;\n      e > 0 ? n = e / (this.panesCount - i.length) : n = e / (this.panesCount - s.length), this.panes.forEach((t, a) => {\n        if (e > 0 && !i.includes(t.id)) {\n          const r = Math.max(Math.min(t.size + n, t.max), t.min), o = r - t.size;\n          e -= o, t.size = r;\n        } else if (!s.includes(t.id)) {\n          const r = Math.max(Math.min(t.size + n, t.max), t.min), o = r - t.size;\n          e -= o, t.size = r;\n        }\n        t.update({\n          [this.horizontal ? \"height\" : \"width\"]: `${this.indexedPanes[t.id].size}%`\n        });\n      }), Math.abs(e) > 0.1 && this.$nextTick(() => {\n        this.ready && console.warn(\"Splitpanes: Could not resize panes correctly due to their constraints.\");\n      });\n    }\n  },\n  watch: {\n    panes: {\n      deep: !0,\n      immediate: !1,\n      handler() {\n        this.updatePaneComponents();\n      }\n    },\n    horizontal() {\n      this.updatePaneComponents();\n    },\n    firstSplitter() {\n      this.redoSplitters();\n    },\n    dblClickSplitter(e) {\n      [...this.container.querySelectorAll(\".splitpanes__splitter\")].forEach((s, n) => {\n        s.ondblclick = e ? (t) => this.onSplitterDblClick(t, n) : void 0;\n      });\n    }\n  },\n  beforeUnmount() {\n    this.ready = !1;\n  },\n  mounted() {\n    this.container = this.$refs.container, this.checkSplitpanesNodes(), this.redoSplitters(), this.resetPaneSizes(), this.$emit(\"ready\"), this.ready = !0;\n  },\n  render() {\n    return c(\n      \"div\",\n      {\n        ref: \"container\",\n        class: [\n          \"splitpanes\",\n          `splitpanes--${this.horizontal ? \"horizontal\" : \"vertical\"}`,\n          {\n            \"splitpanes--dragging\": this.touch.dragging\n          }\n        ]\n      },\n      this.$slots.default()\n    );\n  }\n}, S = (e, i) => {\n  const s = e.__vccOpts || e;\n  for (const [n, t] of i)\n    s[n] = t;\n  return s;\n}, x = {\n  name: \"pane\",\n  inject: [\"requestUpdate\", \"onPaneAdd\", \"onPaneRemove\", \"onPaneClick\"],\n  props: {\n    size: { type: [Number, String], default: null },\n    minSize: { type: [Number, String], default: 0 },\n    maxSize: { type: [Number, String], default: 100 }\n  },\n  data: () => ({\n    style: {}\n  }),\n  mounted() {\n    this.onPaneAdd(this);\n  },\n  beforeUnmount() {\n    this.onPaneRemove(this);\n  },\n  methods: {\n    update(e) {\n      this.style = e;\n    }\n  },\n  computed: {\n    sizeNumber() {\n      return this.size || this.size === 0 ? parseFloat(this.size) : null;\n    },\n    minSizeNumber() {\n      return parseFloat(this.minSize);\n    },\n    maxSizeNumber() {\n      return parseFloat(this.maxSize);\n    }\n  },\n  watch: {\n    sizeNumber(e) {\n      this.requestUpdate({ target: this, size: e });\n    },\n    minSizeNumber(e) {\n      this.requestUpdate({ target: this, min: e });\n    },\n    maxSizeNumber(e) {\n      this.requestUpdate({ target: this, max: e });\n    }\n  }\n};\nfunction P(e, i, s, n, t, a) {\n  return m(), p(\"div\", {\n    class: \"splitpanes__pane\",\n    onClick: i[0] || (i[0] = (r) => a.onPaneClick(r, e._.uid)),\n    style: z(e.style)\n  }, [\n    f(e.$slots, \"default\")\n  ], 4);\n}\nconst g = /* @__PURE__ */ S(x, [[\"render\", P]]);\nexport {\n  g as Pane,\n  M as Splitpanes\n};\n"], "mappings": ";;;;;;;;;;;AACA,IAAM,IAAI;AAAA,EACR,MAAM;AAAA,EACN,OAAO,CAAC,SAAS,UAAU,WAAW,cAAc,iBAAiB,YAAY,eAAe,gBAAgB;AAAA,EAChH,OAAO;AAAA,IACL,YAAY,EAAE,MAAM,QAAQ;AAAA,IAC5B,gBAAgB,EAAE,MAAM,SAAS,SAAS,KAAG;AAAA,IAC7C,kBAAkB,EAAE,MAAM,SAAS,SAAS,KAAG;AAAA,IAC/C,KAAK,EAAE,MAAM,SAAS,SAAS,MAAG;AAAA,IAClC,eAAe,EAAE,MAAM,QAAQ;AAAA,EACjC;AAAA,EACA,UAAU;AACR,WAAO;AAAA,MACL,eAAe,KAAK;AAAA,MACpB,WAAW,KAAK;AAAA,MAChB,cAAc,KAAK;AAAA,MACnB,aAAa,KAAK;AAAA,IACpB;AAAA,EACF;AAAA,EACA,MAAM,OAAO;AAAA,IACX,WAAW;AAAA,IACX,OAAO;AAAA,IACP,OAAO,CAAC;AAAA,IACR,OAAO;AAAA,MACL,WAAW;AAAA,MACX,UAAU;AAAA,MACV,gBAAgB;AAAA,IAClB;AAAA,IACA,cAAc;AAAA,MACZ,UAAU;AAAA,MACV,WAAW;AAAA,IACb;AAAA,EACF;AAAA,EACA,UAAU;AAAA,IACR,aAAa;AACX,aAAO,KAAK,MAAM;AAAA,IACpB;AAAA,IACA,eAAe;AACb,aAAO,KAAK,MAAM,OAAO,CAAC,GAAG,OAAO,EAAE,EAAE,EAAE,IAAI,MAAM,GAAG,CAAC,CAAC;AAAA,IAC3D;AAAA,EACF;AAAA,EACA,SAAS;AAAA,IACP,uBAAuB;AACrB,WAAK,MAAM,QAAQ,CAAC,MAAM;AACxB,UAAE,UAAU,EAAE,OAAO;AAAA,UACnB,CAAC,KAAK,aAAa,WAAW,OAAO,GAAG,GAAG,KAAK,aAAa,EAAE,EAAE,EAAE,IAAI;AAAA,QACzE,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AAAA,IACA,aAAa;AACX,eAAS,iBAAiB,aAAa,KAAK,aAAa,EAAE,SAAS,MAAG,CAAC,GAAG,SAAS,iBAAiB,WAAW,KAAK,SAAS,GAAG,kBAAkB,WAAW,SAAS,iBAAiB,aAAa,KAAK,aAAa,EAAE,SAAS,MAAG,CAAC,GAAG,SAAS,iBAAiB,YAAY,KAAK,SAAS;AAAA,IAC/R;AAAA,IACA,eAAe;AACb,eAAS,oBAAoB,aAAa,KAAK,aAAa,EAAE,SAAS,MAAG,CAAC,GAAG,SAAS,oBAAoB,WAAW,KAAK,SAAS,GAAG,kBAAkB,WAAW,SAAS,oBAAoB,aAAa,KAAK,aAAa,EAAE,SAAS,MAAG,CAAC,GAAG,SAAS,oBAAoB,YAAY,KAAK,SAAS;AAAA,IAC3S;AAAA,IACA,YAAY,GAAG,GAAG;AAChB,WAAK,WAAW,GAAG,KAAK,MAAM,YAAY,MAAI,KAAK,MAAM,iBAAiB;AAAA,IAC5E;AAAA,IACA,YAAY,GAAG;AACb,WAAK,MAAM,cAAc,EAAE,eAAe,GAAG,KAAK,MAAM,WAAW,MAAI,KAAK,mBAAmB,KAAK,oBAAoB,CAAC,CAAC,GAAG,KAAK,MAAM,UAAU,KAAK,MAAM,IAAI,CAAC,OAAO,EAAE,KAAK,EAAE,KAAK,KAAK,EAAE,KAAK,MAAM,EAAE,KAAK,EAAE,CAAC;AAAA,IACrN;AAAA,IACA,YAAY;AACV,WAAK,MAAM,YAAY,KAAK,MAAM,WAAW,KAAK,MAAM,IAAI,CAAC,OAAO,EAAE,KAAK,EAAE,KAAK,KAAK,EAAE,KAAK,MAAM,EAAE,KAAK,EAAE,CAAC,GAAG,KAAK,MAAM,YAAY,OAAI,WAAW,MAAM;AAC3J,aAAK,MAAM,WAAW,OAAI,KAAK,aAAa;AAAA,MAC9C,GAAG,GAAG;AAAA,IACR;AAAA,IACA,gBAAgB,GAAG,GAAG;AACpB,wBAAkB,WAAW,EAAE,eAAe,GAAG,KAAK,qBAAqB,KAAK,aAAa,aAAa,KAAK,aAAa,KAAK,aAAa,SAAS,GAAG,KAAK,aAAa,YAAY,MAAM,KAAK,mBAAmB,GAAG,CAAC,GAAG,KAAK,aAAa,WAAW,SAAS,KAAK,aAAa,WAAW,GAAG,KAAK,aAAa,YAAY,WAAW,MAAM;AAChV,aAAK,aAAa,WAAW;AAAA,MAC/B,GAAG,GAAG,MAAM,KAAK,MAAM,YAAY,KAAK,MAAM,kBAAkB,KAAK,MAAM,CAAC,CAAC;AAAA,IAC/E;AAAA,IACA,mBAAmB,GAAG,GAAG;AACvB,UAAI,IAAI;AACR,WAAK,QAAQ,KAAK,MAAM,IAAI,CAAC,GAAG,OAAO,EAAE,OAAO,MAAM,IAAI,EAAE,MAAM,EAAE,KAAK,MAAM,MAAM,KAAK,EAAE,MAAM,EAAE,GAAG,KAAK,MAAM,CAAC,EAAE,QAAQ,GAAG,KAAK,MAAM,iBAAiB,KAAK,MAAM,CAAC,CAAC,GAAG,KAAK,MAAM,WAAW,KAAK,MAAM,IAAI,CAAC,OAAO,EAAE,KAAK,EAAE,KAAK,KAAK,EAAE,KAAK,MAAM,EAAE,KAAK,EAAE,CAAC;AAAA,IACrQ;AAAA,IACA,YAAY,GAAG,GAAG;AAChB,WAAK,MAAM,cAAc,KAAK,aAAa,CAAC,CAAC;AAAA,IAC/C;AAAA,IACA,oBAAoB,GAAG;AACrB,YAAM,IAAI,KAAK,UAAU,sBAAsB,GAAG,EAAE,SAAS,GAAG,SAAS,EAAE,IAAI,kBAAkB,UAAU,EAAE,UAAU,EAAE,QAAQ,CAAC,IAAI;AACtI,aAAO;AAAA,QACL,GAAG,IAAI,EAAE;AAAA,QACT,GAAG,IAAI,EAAE;AAAA,MACX;AAAA,IACF;AAAA,IACA,yBAAyB,GAAG;AAC1B,UAAI,EAAE,KAAK,aAAa,MAAM,GAAG;AACjC,YAAM,IAAI,KAAK,UAAU,KAAK,aAAa,iBAAiB,aAAa;AACzE,aAAO,KAAK,OAAO,CAAC,KAAK,eAAe,IAAI,IAAI,IAAI,IAAI,MAAM;AAAA,IAChE;AAAA,IACA,mBAAmB,GAAG;AACpB,YAAM,IAAI,KAAK,MAAM;AACrB,UAAI,IAAI;AAAA,QACN,eAAe,KAAK,iBAAiB,CAAC;AAAA,QACtC,eAAe,KAAK,iBAAiB,CAAC;AAAA,QACtC,qBAAqB;AAAA,QACrB,qBAAqB;AAAA,MACvB;AACA,YAAM,IAAI,KAAK,KAAK,iBAAiB,IAAI,EAAE,gBAAgB,IAAI,OAAO,KAAK,iBAAiB,IAAI,EAAE,gBAAgB,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,yBAAyB,CAAC,GAAG,CAAC,GAAG,CAAC;AAC/K,UAAI,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,KAAK,MAAM,EAAE,CAAC,CAAC,KAAK,MAAMA,KAAI,KAAK,MAAM,EAAE,CAAC,CAAC,KAAK;AAC1E,YAAM,IAAI,EAAE,MAAM,OAAO,KAAK,EAAE,MAAM,EAAE,eAAe,IAAIA,GAAE,MAAM,OAAO,KAAK,OAAOA,GAAE,MAAM,KAAK,iBAAiB,IAAI,CAAC;AACzH,UAAI,KAAK,GAAG;AACV,aAAK,EAAE,OAAO,EAAE,KAAKA,GAAE,OAAO,KAAK,IAAI,MAAM,EAAE,MAAM,EAAE,gBAAgB,EAAE,eAAe,CAAC,MAAM,EAAE,OAAO,KAAK,IAAI,MAAMA,GAAE,MAAM,EAAE,gBAAgB,KAAK,iBAAiB,IAAI,CAAC,GAAG,CAAC,GAAGA,GAAE,OAAOA,GAAE;AAC9L;AAAA,MACF;AACA,UAAI,KAAK,gBAAgB;AACvB,cAAM,IAAI,KAAK,iBAAiB,GAAG,CAAC;AACpC,YAAI,CAAC;AACH;AACF,SAAC,EAAE,MAAM,GAAG,eAAe,EAAE,IAAI,IAAI,IAAI,KAAK,MAAM,EAAE,CAAC,CAAC,KAAK,MAAMA,KAAI,KAAK,MAAM,EAAE,CAAC,CAAC,KAAK;AAAA,MAC7F;AACA,YAAM,SAAS,EAAE,OAAO,KAAK,IAAI,KAAK,IAAI,IAAI,EAAE,gBAAgB,EAAE,qBAAqB,EAAE,GAAG,GAAG,EAAE,GAAG,IAAIA,OAAM,SAASA,GAAE,OAAO,KAAK,IAAI,KAAK,IAAI,MAAM,IAAI,EAAE,gBAAgB,EAAE,qBAAqBA,GAAE,GAAG,GAAGA,GAAE,GAAG;AAAA,IACpN;AAAA,IACA,iBAAiB,GAAG,GAAG;AACrB,YAAM,IAAI,KAAK,MAAM,gBAAgB,IAAI,CAAC,GAAG,IAAI,CAAC;AAClD,aAAO,IAAI,EAAE,gBAAgB,KAAK,MAAM,EAAE,CAAC,CAAC,EAAE,QAAQ,EAAE,CAAC,IAAI,KAAK,qBAAqB,CAAC,EAAE,OAAO,EAAE,sBAAsB,GAAG,EAAE,CAAC,IAAI,KAAK,KAAK,MAAM,QAAQ,CAAC,GAAG,MAAM;AACnK,YAAI,EAAE,CAAC,KAAK,KAAK,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,uBAAuB,EAAE;AAAA,MACpE,CAAC,GAAG,EAAE,gBAAgB,KAAK,iBAAiB,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,MAAM,WAAW,EAAE,sBAAsB,GAAG,KAAK,MAAM,CAAC,EAAE,OAAO,KAAK,MAAM,CAAC,EAAE,KAAK,KAAK,MAAM,QAAQ,CAAC,GAAG,MAAM;AACrK,YAAI,KAAK,KAAK,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,uBAAuB,EAAE;AAAA,MACjE,CAAC,GAAG,KAAK,MAAM,EAAE,CAAC,CAAC,EAAE,OAAO,MAAM,EAAE,sBAAsB,KAAK,MAAM,CAAC,EAAE,MAAM,EAAE,gBAAgB,EAAE,eAAe,QAAQ,IAAI,MAAM,EAAE,gBAAgB,KAAK,MAAM,EAAE,CAAC,CAAC,EAAE,QAAQ,EAAE,CAAC,IAAI,KAAK,qBAAqB,CAAC,EAAE,OAAO,EAAE,sBAAsB,GAAG,EAAE,CAAC,IAAI,IAAI,KAAK,KAAK,MAAM,QAAQ,CAAC,GAAG,MAAM;AAC/R,YAAI,KAAK,IAAI,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,uBAAuB,EAAE;AAAA,MACnE,CAAC,GAAG,EAAE,gBAAgB,KAAK,iBAAiB,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,MAAM,WAAW,EAAE,sBAAsB,GAAG,KAAK,MAAM,KAAK,aAAa,CAAC,EAAE,OAAO,KAAK,MAAM,KAAK,aAAa,CAAC,EAAE,KAAK,KAAK,MAAM,QAAQ,CAAC,GAAG,MAAM;AAC7M,YAAI,KAAK,aAAa,KAAK,KAAK,IAAI,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,uBAAuB,EAAE;AAAA,MACvF,CAAC,GAAG,KAAK,MAAM,EAAE,CAAC,CAAC,EAAE,OAAO,MAAM,EAAE,gBAAgB,EAAE,sBAAsB,KAAK,MAAM,KAAK,aAAa,CAAC,EAAE,MAAM,EAAE,eAAe,QAAQ,EAAE,MAAM,GAAG,eAAe,EAAE;AAAA,IACzK;AAAA,IACA,iBAAiB,GAAG;AAClB,aAAO,KAAK,MAAM,OAAO,CAAC,GAAG,GAAG,MAAM,KAAK,IAAI,IAAI,EAAE,OAAO,IAAI,CAAC;AAAA,IACnE;AAAA,IACA,iBAAiB,GAAG;AAClB,aAAO,KAAK,MAAM,OAAO,CAAC,GAAG,GAAG,MAAM,KAAK,IAAI,IAAI,IAAI,EAAE,OAAO,IAAI,CAAC;AAAA,IACvE;AAAA,IACA,qBAAqB,GAAG;AACtB,aAAO,CAAC,GAAG,KAAK,KAAK,EAAE,QAAQ,EAAE,KAAK,CAAC,MAAM,EAAE,QAAQ,KAAK,EAAE,OAAO,EAAE,GAAG,KAAK,CAAC;AAAA,IAClF;AAAA,IACA,qBAAqB,GAAG;AACtB,aAAO,KAAK,MAAM,KAAK,CAAC,MAAM,EAAE,QAAQ,IAAI,KAAK,EAAE,OAAO,EAAE,GAAG,KAAK,CAAC;AAAA,IACvE;AAAA,IACA,uBAAuB;AACrB,YAAM,KAAK,KAAK,UAAU,QAAQ,EAAE,QAAQ,CAAC,MAAM;AACjD,cAAM,IAAI,EAAE,UAAU,SAAS,kBAAkB,GAAG,IAAI,EAAE,UAAU,SAAS,sBAAsB;AACnG,SAAC,KAAK,CAAC,MAAM,EAAE,WAAW,YAAY,CAAC,GAAG,QAAQ,KAAK,8GAA8G;AAAA,MACvK,CAAC;AAAA,IACH;AAAA,IACA,YAAY,GAAG,GAAG,IAAI,OAAI;AACxB,YAAM,IAAI,IAAI,GAAG,IAAI,SAAS,cAAc,KAAK;AACjD,QAAE,UAAU,IAAI,sBAAsB,GAAG,MAAM,EAAE,cAAc,CAAC,MAAM,KAAK,YAAY,GAAG,CAAC,GAAG,OAAO,SAAS,OAAO,kBAAkB,WAAW,EAAE,eAAe,CAAC,MAAM,KAAK,YAAY,GAAG,CAAC,IAAI,EAAE,UAAU,CAAC,MAAM,KAAK,gBAAgB,GAAG,IAAI,CAAC,IAAI,KAAK,qBAAqB,EAAE,aAAa,CAAC,MAAM,KAAK,mBAAmB,GAAG,IAAI,CAAC,IAAI,EAAE,WAAW,aAAa,GAAG,CAAC;AAAA,IAC3W;AAAA,IACA,eAAe,GAAG;AAChB,QAAE,cAAc,QAAQ,EAAE,UAAU,QAAQ,EAAE,aAAa,QAAQ,EAAE,WAAW,YAAY,CAAC;AAAA,IAC/F;AAAA,IACA,gBAAgB;AACd,YAAM,IAAI,MAAM,KAAK,KAAK,UAAU,QAAQ;AAC5C,QAAE,QAAQ,CAAC,MAAM;AACf,UAAE,UAAU,SAAS,sBAAsB,KAAK,KAAK,eAAe,CAAC;AAAA,MACvE,CAAC;AACD,UAAI,IAAI;AACR,QAAE,QAAQ,CAAC,MAAM;AACf,UAAE,UAAU,SAAS,kBAAkB,MAAM,CAAC,KAAK,KAAK,gBAAgB,KAAK,YAAY,GAAG,GAAG,IAAE,IAAI,KAAK,KAAK,YAAY,GAAG,CAAC,GAAG;AAAA,MACpI,CAAC;AAAA,IACH;AAAA,IACA,cAAc,EAAE,QAAQ,GAAG,GAAG,EAAE,GAAG;AACjC,YAAM,IAAI,KAAK,aAAa,EAAE,EAAE,GAAG;AACnC,aAAO,QAAQ,CAAC,EAAE,QAAQ,CAAC,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC;AAAA,IAChD;AAAA,IACA,UAAU,GAAG;AACX,UAAI,IAAI;AACR,YAAM,KAAK,EAAE,IAAI,WAAW,QAAQ,EAAE,KAAK,CAAC,OAAO,EAAE,UAAU,SAAS,kBAAkB,KAAK,KAAK,MAAM,EAAE,IAAI;AAChH,YAAM,IAAI,WAAW,EAAE,OAAO,GAAG,IAAI,WAAW,EAAE,OAAO;AACzD,WAAK,MAAM,OAAO,GAAG,GAAG;AAAA,QACtB,IAAI,EAAE,EAAE;AAAA,QACR,OAAO;AAAA,QACP,KAAK,MAAM,CAAC,IAAI,IAAI;AAAA,QACpB,KAAK,MAAM,CAAC,IAAI,MAAM;AAAA,QACtB,MAAM,EAAE,SAAS,OAAO,OAAO,WAAW,EAAE,IAAI;AAAA,QAChD,WAAW,EAAE;AAAA,QACb,QAAQ,EAAE;AAAA,MACZ,CAAC,GAAG,KAAK,MAAM,QAAQ,CAAC,GAAG,MAAM,EAAE,QAAQ,CAAC,GAAG,KAAK,SAAS,KAAK,UAAU,MAAM;AAChF,aAAK,cAAc,GAAG,KAAK,eAAe,EAAE,WAAW,KAAK,MAAM,CAAC,EAAE,CAAC,GAAG,KAAK,MAAM,YAAY,EAAE,OAAO,GAAG,OAAO,KAAK,MAAM,IAAI,CAAC,OAAO,EAAE,KAAK,EAAE,KAAK,KAAK,EAAE,KAAK,MAAM,EAAE,KAAK,EAAE,EAAE,CAAC;AAAA,MACxL,CAAC;AAAA,IACH;AAAA,IACA,aAAa,GAAG;AACd,YAAM,IAAI,KAAK,MAAM,UAAU,CAAC,MAAM,EAAE,OAAO,EAAE,EAAE,GAAG,GAAG,IAAI,KAAK,MAAM,OAAO,GAAG,CAAC,EAAE,CAAC;AACtF,WAAK,MAAM,QAAQ,CAAC,GAAG,MAAM,EAAE,QAAQ,CAAC,GAAG,KAAK,UAAU,MAAM;AAC9D,aAAK,cAAc,GAAG,KAAK,eAAe,EAAE,aAAa,EAAE,GAAG,GAAG,OAAO,EAAE,EAAE,CAAC,GAAG,KAAK,MAAM,eAAe,EAAE,SAAS,GAAG,OAAO,KAAK,MAAM,IAAI,CAAC,OAAO,EAAE,KAAK,EAAE,KAAK,KAAK,EAAE,KAAK,MAAM,EAAE,KAAK,EAAE,EAAE,CAAC;AAAA,MACpM,CAAC;AAAA,IACH;AAAA,IACA,eAAe,IAAI,CAAC,GAAG;AACrB,OAAC,EAAE,aAAa,CAAC,EAAE,cAAc,KAAK,mBAAmB,IAAI,KAAK,MAAM,KAAK,CAAC,MAAM,EAAE,cAAc,QAAQ,EAAE,OAAO,EAAE,MAAM,GAAG,IAAI,KAAK,yBAAyB,CAAC,IAAI,KAAK,SAAS,GAAG,KAAK,SAAS,KAAK,MAAM,WAAW,KAAK,MAAM,IAAI,CAAC,OAAO,EAAE,KAAK,EAAE,KAAK,KAAK,EAAE,KAAK,MAAM,EAAE,KAAK,EAAE,CAAC;AAAA,IAC/R;AAAA,IACA,WAAW;AACT,YAAM,IAAI,MAAM,KAAK;AACrB,UAAI,IAAI;AACR,YAAM,IAAI,CAAC,GAAG,IAAI,CAAC;AACnB,WAAK,MAAM,QAAQ,CAAC,MAAM;AACxB,UAAE,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,KAAK,EAAE,EAAE,GAAG,EAAE,QAAQ,EAAE,OAAO,EAAE,KAAK,EAAE,EAAE;AAAA,MAC5H,CAAC,GAAG,IAAI,OAAO,KAAK,cAAc,GAAG,GAAG,CAAC;AAAA,IAC3C;AAAA,IACA,qBAAqB;AACnB,UAAI,IAAI;AACR,YAAM,IAAI,CAAC,GAAG,IAAI,CAAC;AACnB,UAAI,IAAI;AACR,WAAK,MAAM,QAAQ,CAAC,MAAM;AACxB,aAAK,EAAE,MAAM,EAAE,SAAS,QAAQ,KAAK,EAAE,QAAQ,EAAE,OAAO,EAAE,KAAK,EAAE,EAAE,GAAG,EAAE,QAAQ,EAAE,OAAO,EAAE,KAAK,EAAE,EAAE;AAAA,MACtG,CAAC;AACD,UAAI,IAAI;AACR,UAAI,QAAQ,KAAK,MAAM,QAAQ,CAAC,MAAM;AACpC,UAAE,SAAS,SAAS,EAAE,OAAO,KAAK,IAAI,KAAK,IAAI,KAAK,KAAK,aAAa,IAAI,EAAE,GAAG,GAAG,EAAE,GAAG,IAAI,KAAK,EAAE;AAAA,MACpG,CAAC,GAAG,IAAI,OAAO,KAAK,cAAc,GAAG,GAAG,CAAC;AAAA,IAC3C;AAAA,IACA,yBAAyB,EAAE,WAAW,GAAG,aAAa,EAAE,IAAI,CAAC,GAAG;AAC9D,UAAI,IAAI,MAAM,KAAK,YAAY,IAAI;AACnC,YAAM,IAAI,CAAC,GAAG,IAAI,CAAC;AACnB,WAAK,EAAE,cAAc,SAAS,KAAK,MAAM,EAAE,cAAc,KAAK,aAAa,KAAK,KAAK,MAAM,QAAQ,CAAC,MAAM;AACxG,aAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,KAAK,EAAE,EAAE,GAAG,EAAE,QAAQ,EAAE,OAAO,EAAE,KAAK,EAAE,EAAE;AAAA,MAC9E,CAAC,GAAG,EAAE,KAAK,IAAI,CAAC,IAAI,SAAS,KAAK,MAAM,QAAQ,CAAC,MAAM;AACrD,aAAK,EAAE,cAAc,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,EAAE,GAAG,GAAG,EAAE,GAAG,IAAI,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,KAAK,EAAE,EAAE,GAAG,EAAE,QAAQ,EAAE,OAAO,EAAE,KAAK,EAAE,EAAE;AAAA,MAC5K,CAAC,GAAG,IAAI,OAAO,KAAK,cAAc,GAAG,GAAG,CAAC;AAAA,IAC3C;AAAA,IACA,cAAc,GAAG,GAAG,GAAG;AACrB,UAAI;AACJ,UAAI,IAAI,IAAI,KAAK,KAAK,aAAa,EAAE,UAAU,IAAI,KAAK,KAAK,aAAa,EAAE,SAAS,KAAK,MAAM,QAAQ,CAAC,GAAG,MAAM;AAChH,YAAI,IAAI,KAAK,CAAC,EAAE,SAAS,EAAE,EAAE,GAAG;AAC9B,gBAAM,IAAI,KAAK,IAAI,KAAK,IAAI,EAAE,OAAO,GAAG,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,IAAI,IAAI,EAAE;AAClE,eAAK,GAAG,EAAE,OAAO;AAAA,QACnB,WAAW,CAAC,EAAE,SAAS,EAAE,EAAE,GAAG;AAC5B,gBAAM,IAAI,KAAK,IAAI,KAAK,IAAI,EAAE,OAAO,GAAG,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,IAAI,IAAI,EAAE;AAClE,eAAK,GAAG,EAAE,OAAO;AAAA,QACnB;AACA,UAAE,OAAO;AAAA,UACP,CAAC,KAAK,aAAa,WAAW,OAAO,GAAG,GAAG,KAAK,aAAa,EAAE,EAAE,EAAE,IAAI;AAAA,QACzE,CAAC;AAAA,MACH,CAAC,GAAG,KAAK,IAAI,CAAC,IAAI,OAAO,KAAK,UAAU,MAAM;AAC5C,aAAK,SAAS,QAAQ,KAAK,wEAAwE;AAAA,MACrG,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,OAAO;AAAA,IACL,OAAO;AAAA,MACL,MAAM;AAAA,MACN,WAAW;AAAA,MACX,UAAU;AACR,aAAK,qBAAqB;AAAA,MAC5B;AAAA,IACF;AAAA,IACA,aAAa;AACX,WAAK,qBAAqB;AAAA,IAC5B;AAAA,IACA,gBAAgB;AACd,WAAK,cAAc;AAAA,IACrB;AAAA,IACA,iBAAiB,GAAG;AAClB,OAAC,GAAG,KAAK,UAAU,iBAAiB,uBAAuB,CAAC,EAAE,QAAQ,CAAC,GAAG,MAAM;AAC9E,UAAE,aAAa,IAAI,CAAC,MAAM,KAAK,mBAAmB,GAAG,CAAC,IAAI;AAAA,MAC5D,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,gBAAgB;AACd,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,UAAU;AACR,SAAK,YAAY,KAAK,MAAM,WAAW,KAAK,qBAAqB,GAAG,KAAK,cAAc,GAAG,KAAK,eAAe,GAAG,KAAK,MAAM,OAAO,GAAG,KAAK,QAAQ;AAAA,EACrJ;AAAA,EACA,SAAS;AACP,WAAO;AAAA,MACL;AAAA,MACA;AAAA,QACE,KAAK;AAAA,QACL,OAAO;AAAA,UACL;AAAA,UACA,eAAe,KAAK,aAAa,eAAe,UAAU;AAAA,UAC1D;AAAA,YACE,wBAAwB,KAAK,MAAM;AAAA,UACrC;AAAA,QACF;AAAA,MACF;AAAA,MACA,KAAK,OAAO,QAAQ;AAAA,IACtB;AAAA,EACF;AACF;AArRA,IAqRG,IAAI,CAAC,GAAG,MAAM;AACf,QAAM,IAAI,EAAE,aAAa;AACzB,aAAW,CAAC,GAAG,CAAC,KAAK;AACnB,MAAE,CAAC,IAAI;AACT,SAAO;AACT;AA1RA,IA0RG,IAAI;AAAA,EACL,MAAM;AAAA,EACN,QAAQ,CAAC,iBAAiB,aAAa,gBAAgB,aAAa;AAAA,EACpE,OAAO;AAAA,IACL,MAAM,EAAE,MAAM,CAAC,QAAQ,MAAM,GAAG,SAAS,KAAK;AAAA,IAC9C,SAAS,EAAE,MAAM,CAAC,QAAQ,MAAM,GAAG,SAAS,EAAE;AAAA,IAC9C,SAAS,EAAE,MAAM,CAAC,QAAQ,MAAM,GAAG,SAAS,IAAI;AAAA,EAClD;AAAA,EACA,MAAM,OAAO;AAAA,IACX,OAAO,CAAC;AAAA,EACV;AAAA,EACA,UAAU;AACR,SAAK,UAAU,IAAI;AAAA,EACrB;AAAA,EACA,gBAAgB;AACd,SAAK,aAAa,IAAI;AAAA,EACxB;AAAA,EACA,SAAS;AAAA,IACP,OAAO,GAAG;AACR,WAAK,QAAQ;AAAA,IACf;AAAA,EACF;AAAA,EACA,UAAU;AAAA,IACR,aAAa;AACX,aAAO,KAAK,QAAQ,KAAK,SAAS,IAAI,WAAW,KAAK,IAAI,IAAI;AAAA,IAChE;AAAA,IACA,gBAAgB;AACd,aAAO,WAAW,KAAK,OAAO;AAAA,IAChC;AAAA,IACA,gBAAgB;AACd,aAAO,WAAW,KAAK,OAAO;AAAA,IAChC;AAAA,EACF;AAAA,EACA,OAAO;AAAA,IACL,WAAW,GAAG;AACZ,WAAK,cAAc,EAAE,QAAQ,MAAM,MAAM,EAAE,CAAC;AAAA,IAC9C;AAAA,IACA,cAAc,GAAG;AACf,WAAK,cAAc,EAAE,QAAQ,MAAM,KAAK,EAAE,CAAC;AAAA,IAC7C;AAAA,IACA,cAAc,GAAG;AACf,WAAK,cAAc,EAAE,QAAQ,MAAM,KAAK,EAAE,CAAC;AAAA,IAC7C;AAAA,EACF;AACF;AACA,SAAS,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG;AAC3B,SAAO,UAAE,GAAG,mBAAE,OAAO;AAAA,IACnB,OAAO;AAAA,IACP,SAAS,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,MAAM,EAAE,YAAY,GAAG,EAAE,EAAE,GAAG;AAAA,IACxD,OAAO,eAAE,EAAE,KAAK;AAAA,EAClB,GAAG;AAAA,IACD,WAAE,EAAE,QAAQ,SAAS;AAAA,EACvB,GAAG,CAAC;AACN;AACA,IAAM,IAAoB,EAAE,GAAG,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC;", "names": ["h"]}