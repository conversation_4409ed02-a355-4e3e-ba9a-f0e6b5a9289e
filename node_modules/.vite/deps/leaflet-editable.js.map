{"version": 3, "sources": ["../../leaflet-editable/src/Leaflet.Editable.js"], "sourcesContent": [";((factory, window) => {\n  /*globals define, module, require*/\n\n  // define an AMD module that relies on 'leaflet'\n  if (typeof define === 'function' && define.amd) {\n    define(['leaflet'], factory)\n\n    // define a Common JS module that relies on 'leaflet'\n  } else if (typeof exports === 'object') {\n    module.exports = factory(require('leaflet'))\n  }\n\n  // attach your plugin to the global 'L' variable\n  if (typeof window !== 'undefined' && window.L) {\n    factory(window.L)\n  }\n})((L) => {\n  // 🍂miniclass CancelableEvent (Event objects)\n  // 🍂method cancel()\n  // Cancel any subsequent action.\n\n  // 🍂miniclass VertexEvent (Event objects)\n  // 🍂property vertex: VertexMarker\n  // The vertex that fires the event.\n\n  // 🍂miniclass ShapeEvent (Event objects)\n  // 🍂property shape: Array\n  // The shape (LatLngs array) subject of the action.\n\n  // 🍂miniclass CancelableVertexEvent (Event objects)\n  // 🍂inherits VertexEvent\n  // 🍂inherits CancelableEvent\n\n  // 🍂miniclass CancelableShapeEvent (Event objects)\n  // 🍂inherits ShapeEvent\n  // 🍂inherits CancelableEvent\n\n  // 🍂miniclass LayerEvent (Event objects)\n  // 🍂property layer: object\n  // The Layer (Marker, <PERSON>yline…) subject of the action.\n\n  // 🍂namespace Editable; 🍂class Editable; 🍂aka L.Editable\n  // Main edition handler. By default, it is attached to the map\n  // as `map.editTools` property.\n  // Leaflet.Editable is made to be fully extendable. You have three ways to customize\n  // the behaviour: using options, listening to events, or extending.\n  L.Editable = L.Evented.extend({\n    statics: {\n      FORWARD: 1,\n      BACKWARD: -1,\n    },\n\n    options: {\n      // You can pass them when creating a map using the `editOptions` key.\n      // 🍂option zIndex: int = 1000\n      // The default zIndex of the editing tools.\n      zIndex: 1000,\n\n      // 🍂option polygonClass: class = L.Polygon\n      // Class to be used when creating a new Polygon.\n      polygonClass: L.Polygon,\n\n      // 🍂option polylineClass: class = L.Polyline\n      // Class to be used when creating a new Polyline.\n      polylineClass: L.Polyline,\n\n      // 🍂option markerClass: class = L.Marker\n      // Class to be used when creating a new Marker.\n      markerClass: L.Marker,\n\n      // 🍂option circleMarkerClass: class = L.CircleMarker\n      // Class to be used when creating a new CircleMarker.\n      circleMarkerClass: L.CircleMarker,\n\n      // 🍂option rectangleClass: class = L.Rectangle\n      // Class to be used when creating a new Rectangle.\n      rectangleClass: L.Rectangle,\n\n      // 🍂option circleClass: class = L.Circle\n      // Class to be used when creating a new Circle.\n      circleClass: L.Circle,\n\n      // 🍂option drawingCSSClass: string = 'leaflet-editable-drawing'\n      // CSS class to be added to the map container while drawing.\n      drawingCSSClass: 'leaflet-editable-drawing',\n\n      // 🍂option drawingCursor: const = 'crosshair'\n      // Cursor mode set to the map while drawing.\n      drawingCursor: 'crosshair',\n\n      // 🍂option editLayer: Layer = new L.LayerGroup()\n      // Layer used to store edit tools (vertex, line guide…).\n      editLayer: undefined,\n\n      // 🍂option featuresLayer: Layer = new L.LayerGroup()\n      // Default layer used to store drawn features (Marker, Polyline…).\n      featuresLayer: undefined,\n\n      // 🍂option polylineEditorClass: class = PolylineEditor\n      // Class to be used as Polyline editor.\n      polylineEditorClass: undefined,\n\n      // 🍂option polygonEditorClass: class = PolygonEditor\n      // Class to be used as Polygon editor.\n      polygonEditorClass: undefined,\n\n      // 🍂option markerEditorClass: class = MarkerEditor\n      // Class to be used as Marker editor.\n      markerEditorClass: undefined,\n\n      // 🍂option circleMarkerEditorClass: class = CircleMarkerEditor\n      // Class to be used as CircleMarker editor.\n      circleMarkerEditorClass: undefined,\n\n      // 🍂option rectangleEditorClass: class = RectangleEditor\n      // Class to be used as Rectangle editor.\n      rectangleEditorClass: undefined,\n\n      // 🍂option circleEditorClass: class = CircleEditor\n      // Class to be used as Circle editor.\n      circleEditorClass: undefined,\n\n      // 🍂option lineGuideOptions: hash = {}\n      // Options to be passed to the line guides.\n      lineGuideOptions: {},\n\n      // 🍂option skipMiddleMarkers: boolean = false\n      // Set this to true if you don't want middle markers.\n      skipMiddleMarkers: false,\n    },\n\n    initialize: function (map, options) {\n      L.setOptions(this, options)\n      this._lastZIndex = this.options.zIndex\n      this.map = map\n      this.editLayer = this.createEditLayer()\n      this.featuresLayer = this.createFeaturesLayer()\n      this.forwardLineGuide = this.createLineGuide()\n      this.backwardLineGuide = this.createLineGuide()\n    },\n\n    fireAndForward: function (type, e) {\n      e = e || {}\n      e.editTools = this\n      this.fire(type, e)\n      this.map.fire(type, e)\n    },\n\n    createLineGuide: function () {\n      const options = L.extend(\n        { dashArray: '5,10', weight: 1, interactive: false },\n        this.options.lineGuideOptions\n      )\n      return L.polyline([], options)\n    },\n\n    createVertexIcon: (options) =>\n      L.Browser.mobile && L.Browser.touch\n        ? new L.Editable.TouchVertexIcon(options)\n        : new L.Editable.VertexIcon(options),\n\n    createEditLayer: function () {\n      return this.options.editLayer || new L.LayerGroup().addTo(this.map)\n    },\n\n    createFeaturesLayer: function () {\n      return this.options.featuresLayer || new L.LayerGroup().addTo(this.map)\n    },\n\n    moveForwardLineGuide: function (latlng) {\n      if (this.forwardLineGuide._latlngs.length) {\n        this.forwardLineGuide._latlngs[1] = latlng\n        this.forwardLineGuide._bounds.extend(latlng)\n        this.forwardLineGuide.redraw()\n      }\n    },\n\n    moveBackwardLineGuide: function (latlng) {\n      if (this.backwardLineGuide._latlngs.length) {\n        this.backwardLineGuide._latlngs[1] = latlng\n        this.backwardLineGuide._bounds.extend(latlng)\n        this.backwardLineGuide.redraw()\n      }\n    },\n\n    anchorForwardLineGuide: function (latlng) {\n      this.forwardLineGuide._latlngs[0] = latlng\n      this.forwardLineGuide._bounds.extend(latlng)\n      this.forwardLineGuide.redraw()\n    },\n\n    anchorBackwardLineGuide: function (latlng) {\n      this.backwardLineGuide._latlngs[0] = latlng\n      this.backwardLineGuide._bounds.extend(latlng)\n      this.backwardLineGuide.redraw()\n    },\n\n    attachForwardLineGuide: function () {\n      this.editLayer.addLayer(this.forwardLineGuide)\n    },\n\n    attachBackwardLineGuide: function () {\n      this.editLayer.addLayer(this.backwardLineGuide)\n    },\n\n    detachForwardLineGuide: function () {\n      this.forwardLineGuide.setLatLngs([])\n      this.editLayer.removeLayer(this.forwardLineGuide)\n    },\n\n    detachBackwardLineGuide: function () {\n      this.backwardLineGuide.setLatLngs([])\n      this.editLayer.removeLayer(this.backwardLineGuide)\n    },\n\n    blockEvents: function () {\n      // Hack: force map not to listen to other layers events while drawing.\n      if (!this._oldTargets) {\n        this._oldTargets = this.map._targets\n        this.map._targets = {}\n      }\n    },\n\n    unblockEvents: function () {\n      if (this._oldTargets) {\n        // Reset, but keep targets created while drawing.\n        this.map._targets = L.extend(this.map._targets, this._oldTargets)\n        delete this._oldTargets\n      }\n    },\n\n    registerForDrawing: function (editor) {\n      if (this._drawingEditor) this.unregisterForDrawing(this._drawingEditor)\n      this.blockEvents()\n      editor.reset() // Make sure editor tools still receive events.\n      this._drawingEditor = editor\n      this.map.on('mousemove touchmove', editor.onDrawingMouseMove, editor)\n      this.map.on('mousedown', this.onMousedown, this)\n      this.map.on('mouseup', this.onMouseup, this)\n      L.DomUtil.addClass(this.map._container, this.options.drawingCSSClass)\n      this.defaultMapCursor = this.map._container.style.cursor\n      this.map._container.style.cursor = this.options.drawingCursor\n    },\n\n    unregisterForDrawing: function (editor) {\n      this.unblockEvents()\n      L.DomUtil.removeClass(this.map._container, this.options.drawingCSSClass)\n      this.map._container.style.cursor = this.defaultMapCursor\n      editor = editor || this._drawingEditor\n      if (!editor) return\n      this.map.off('mousemove touchmove', editor.onDrawingMouseMove, editor)\n      this.map.off('mousedown', this.onMousedown, this)\n      this.map.off('mouseup', this.onMouseup, this)\n      if (editor !== this._drawingEditor) return\n      delete this._drawingEditor\n      if (editor._drawing) editor.cancelDrawing()\n    },\n\n    onMousedown: function (e) {\n      if (e.originalEvent.which != 1) return\n      this._mouseDown = e\n      this._drawingEditor.onDrawingMouseDown(e)\n    },\n\n    onMouseup: function (e) {\n      if (this._mouseDown) {\n        const editor = this._drawingEditor\n        const mouseDown = this._mouseDown\n        this._mouseDown = null\n        editor.onDrawingMouseUp(e)\n        if (this._drawingEditor !== editor) return // onDrawingMouseUp may call unregisterFromDrawing.\n        const origin = L.point(\n          mouseDown.originalEvent.clientX,\n          mouseDown.originalEvent.clientY\n        )\n        const distance = L.point(\n          e.originalEvent.clientX,\n          e.originalEvent.clientY\n        ).distanceTo(origin)\n        if (Math.abs(distance) < 9 * (window.devicePixelRatio || 1))\n          this._drawingEditor.onDrawingClick(e)\n      }\n    },\n\n    // 🍂section Public methods\n    // You will generally access them by the `map.editTools`\n    // instance:\n    //\n    // `map.editTools.startPolyline();`\n\n    // 🍂method drawing(): boolean\n    // Return true if any drawing action is ongoing.\n    drawing: function () {\n      return this._drawingEditor?.drawing()\n    },\n\n    // 🍂method stopDrawing()\n    // When you need to stop any ongoing drawing, without needing to know which editor is active.\n    stopDrawing: function () {\n      this.unregisterForDrawing()\n    },\n\n    // 🍂method commitDrawing()\n    // When you need to commit any ongoing drawing, without needing to know which editor is active.\n    commitDrawing: function (e) {\n      if (!this._drawingEditor) return\n      this._drawingEditor.commitDrawing(e)\n    },\n\n    connectCreatedToMap: function (layer) {\n      return this.featuresLayer.addLayer(layer)\n    },\n\n    // 🍂method startPolyline(latlng: L.LatLng, options: hash): L.Polyline\n    // Start drawing a Polyline. If `latlng` is given, a first point will be added. In any case, continuing on user click.\n    // If `options` is given, it will be passed to the Polyline class constructor.\n    startPolyline: function (latlng, options) {\n      const line = this.createPolyline([], options)\n      line.enableEdit(this.map).newShape(latlng)\n      return line\n    },\n\n    // 🍂method startPolygon(latlng: L.LatLng, options: hash): L.Polygon\n    // Start drawing a Polygon. If `latlng` is given, a first point will be added. In any case, continuing on user click.\n    // If `options` is given, it will be passed to the Polygon class constructor.\n    startPolygon: function (latlng, options) {\n      const polygon = this.createPolygon([], options)\n      polygon.enableEdit(this.map).newShape(latlng)\n      return polygon\n    },\n\n    // 🍂method startMarker(latlng: L.LatLng, options: hash): L.Marker\n    // Start adding a Marker. If `latlng` is given, the Marker will be shown first at this point.\n    // In any case, it will follow the user mouse, and will have a final `latlng` on next click (or touch).\n    // If `options` is given, it will be passed to the Marker class constructor.\n    startMarker: function (latlng, options) {\n      latlng = latlng || this.map.getCenter().clone()\n      const marker = this.createMarker(latlng, options)\n      marker.enableEdit(this.map).startDrawing()\n      return marker\n    },\n\n    // 🍂method startCircleMarker(latlng: L.LatLng, options: hash): L.CircleMarker\n    // Start adding a CircleMarker. If `latlng` is given, the CircleMarker will be shown first at this point.\n    // In any case, it will follow the user mouse, and will have a final `latlng` on next click (or touch).\n    // If `options` is given, it will be passed to the CircleMarker class constructor.\n    startCircleMarker: function (latlng, options) {\n      latlng = latlng || this.map.getCenter().clone()\n      const marker = this.createCircleMarker(latlng, options)\n      marker.enableEdit(this.map).startDrawing()\n      return marker\n    },\n\n    // 🍂method startRectangle(latlng: L.LatLng, options: hash): L.Rectangle\n    // Start drawing a Rectangle. If `latlng` is given, the Rectangle anchor will be added. In any case, continuing on user drag.\n    // If `options` is given, it will be passed to the Rectangle class constructor.\n    startRectangle: function (latlng, options) {\n      const corner = latlng || L.latLng([0, 0])\n      const bounds = new L.LatLngBounds(corner, corner)\n      const rectangle = this.createRectangle(bounds, options)\n      rectangle.enableEdit(this.map).startDrawing()\n      return rectangle\n    },\n\n    // 🍂method startCircle(latlng: L.LatLng, options: hash): L.Circle\n    // Start drawing a Circle. If `latlng` is given, the Circle anchor will be added. In any case, continuing on user drag.\n    // If `options` is given, it will be passed to the Circle class constructor.\n    startCircle: function (latlng, options) {\n      latlng = latlng || this.map.getCenter().clone()\n      const circle = this.createCircle(latlng, options)\n      circle.enableEdit(this.map).startDrawing()\n      return circle\n    },\n\n    startHole: (editor, latlng) => {\n      editor.newHole(latlng)\n    },\n\n    createLayer: function (klass, latlngs, options) {\n      options = L.Util.extend({ editOptions: { editTools: this } }, options)\n      const layer = new klass(latlngs, options)\n      // 🍂namespace Editable\n      // 🍂event editable:created: LayerEvent\n      // Fired when a new feature (Marker, Polyline…) is created.\n      this.fireAndForward('editable:created', { layer: layer })\n      return layer\n    },\n\n    createPolyline: function (latlngs, options) {\n      return this.createLayer(\n        options?.polylineClass || this.options.polylineClass,\n        latlngs,\n        options\n      )\n    },\n\n    createPolygon: function (latlngs, options) {\n      return this.createLayer(\n        options?.polygonClass || this.options.polygonClass,\n        latlngs,\n        options\n      )\n    },\n\n    createMarker: function (latlng, options) {\n      return this.createLayer(\n        options?.markerClass || this.options.markerClass,\n        latlng,\n        options\n      )\n    },\n\n    createCircleMarker: function (latlng, options) {\n      return this.createLayer(\n        options?.circleMarkerClass || this.options.circleMarkerClass,\n        latlng,\n        options\n      )\n    },\n\n    createRectangle: function (bounds, options) {\n      return this.createLayer(\n        options?.rectangleClass || this.options.rectangleClass,\n        bounds,\n        options\n      )\n    },\n\n    createCircle: function (latlng, options) {\n      return this.createLayer(\n        options?.circleClass || this.options.circleClass,\n        latlng,\n        options\n      )\n    },\n  })\n\n  L.extend(L.Editable, {\n    makeCancellable: (e) => {\n      e.cancel = () => {\n        e._cancelled = true\n      }\n    },\n  })\n\n  // 🍂namespace Map; 🍂class Map\n  // Leaflet.Editable add options and events to the `L.Map` object.\n  // See `Editable` events for the list of events fired on the Map.\n  // 🍂example\n  //\n  // ```js\n  // var map = L.map('map', {\n  //  editable: true,\n  //  editOptions: {\n  //    …\n  // }\n  // });\n  // ```\n  // 🍂section Editable Map Options\n  L.Map.mergeOptions({\n    // 🍂namespace Map\n    // 🍂section Map Options\n    // 🍂option editToolsClass: class = L.Editable\n    // Class to be used as vertex, for path editing.\n    editToolsClass: L.Editable,\n\n    // 🍂option editable: boolean = false\n    // Whether to create a L.Editable instance at map init.\n    editable: false,\n\n    // 🍂option editOptions: hash = {}\n    // Options to pass to L.Editable when instantiating.\n    editOptions: {},\n  })\n\n  L.Map.addInitHook(function () {\n    this.whenReady(function () {\n      if (this.options.editable) {\n        this.editTools = new this.options.editToolsClass(this, this.options.editOptions)\n      }\n    })\n  })\n\n  L.Editable.VertexIcon = L.DivIcon.extend({\n    options: {\n      iconSize: new L.Point(8, 8),\n    },\n  })\n\n  L.Editable.TouchVertexIcon = L.Editable.VertexIcon.extend({\n    options: {\n      iconSize: new L.Point(20, 20),\n    },\n  })\n\n  // 🍂namespace Editable; 🍂class VertexMarker; Handler for dragging path vertices.\n  L.Editable.VertexMarker = L.Marker.extend({\n    options: {\n      draggable: true,\n      className: 'leaflet-div-icon leaflet-vertex-icon',\n    },\n\n    // 🍂section Public methods\n    // The marker used to handle path vertex. You will usually interact with a `VertexMarker`\n    // instance when listening for events like `editable:vertex:ctrlclick`.\n\n    initialize: function (latlng, latlngs, editor, options) {\n      // We don't use this._latlng, because on drag Leaflet replace it while\n      // we want to keep reference.\n      this.latlng = latlng\n      this.latlngs = latlngs\n      this.editor = editor\n      L.Marker.prototype.initialize.call(this, latlng, options)\n      this.options.icon = this.editor.tools.createVertexIcon({\n        className: this.options.className,\n      })\n      this.latlng.__vertex = this\n      this.connect()\n      this.setZIndexOffset(editor.tools._lastZIndex + 1)\n    },\n\n    connect: function () {\n      this.editor.editLayer.addLayer(this)\n    },\n\n    onAdd: function (map) {\n      L.Marker.prototype.onAdd.call(this, map)\n      this.on('drag', this.onDrag)\n      this.on('dragstart', this.onDragStart)\n      this.on('dragend', this.onDragEnd)\n      this.on('mouseup', this.onMouseup)\n      this.on('click', this.onClick)\n      this.on('contextmenu', this.onContextMenu)\n      this.on('mousedown touchstart', this.onMouseDown)\n      this.on('mouseover', this.onMouseOver)\n      this.on('mouseout', this.onMouseOut)\n      this.addMiddleMarkers()\n    },\n\n    onRemove: function (map) {\n      if (this.middleMarker) this.middleMarker.delete()\n      delete this.latlng.__vertex\n      this.off('drag', this.onDrag)\n      this.off('dragstart', this.onDragStart)\n      this.off('dragend', this.onDragEnd)\n      this.off('mouseup', this.onMouseup)\n      this.off('click', this.onClick)\n      this.off('contextmenu', this.onContextMenu)\n      this.off('mousedown touchstart', this.onMouseDown)\n      this.off('mouseover', this.onMouseOver)\n      this.off('mouseout', this.onMouseOut)\n      L.Marker.prototype.onRemove.call(this, map)\n    },\n\n    onDrag: function (e) {\n      e.vertex = this\n      this.editor.onVertexMarkerDrag(e)\n      const iconPos = L.DomUtil.getPosition(this._icon)\n      const latlng = this._map.layerPointToLatLng(iconPos)\n      this.latlng.update(latlng)\n      this._latlng = this.latlng // Push back to Leaflet our reference.\n      this.editor.refresh()\n      if (this.middleMarker) this.middleMarker.updateLatLng()\n      const next = this.getNext()\n      if (next?.middleMarker) next.middleMarker.updateLatLng()\n    },\n\n    onDragStart: function (e) {\n      e.vertex = this\n      this.editor.onVertexMarkerDragStart(e)\n    },\n\n    onDragEnd: function (e) {\n      e.vertex = this\n      this.editor.onVertexMarkerDragEnd(e)\n    },\n\n    onClick: function (e) {\n      e.vertex = this\n      this.editor.onVertexMarkerClick(e)\n    },\n\n    onMouseup: function (e) {\n      L.DomEvent.stop(e)\n      e.vertex = this\n      this.editor.map.fire('mouseup', e)\n    },\n\n    onContextMenu: function (e) {\n      e.vertex = this\n      this.editor.onVertexMarkerContextMenu(e)\n    },\n\n    onMouseDown: function (e) {\n      e.vertex = this\n      this.editor.onVertexMarkerMouseDown(e)\n    },\n\n    onMouseOver: function (e) {\n      e.vertex = this\n      this.editor.onVertexMarkerMouseOver(e)\n    },\n\n    onMouseOut: function (e) {\n      e.vertex = this\n      this.editor.onVertexMarkerMouseOut(e)\n    },\n\n    // 🍂method delete()\n    // Delete a vertex and the related LatLng.\n    delete: function () {\n      const next = this.getNext() // Compute before changing latlng\n      this.latlngs.splice(this.getIndex(), 1)\n      this.editor.editLayer.removeLayer(this)\n      this.editor.onVertexDeleted({ latlng: this.latlng, vertex: this })\n      if (!this.latlngs.length) this.editor.deleteShape(this.latlngs)\n      if (next) next.resetMiddleMarker()\n      this.editor.refresh()\n    },\n\n    // 🍂method getIndex(): int\n    // Get the index of the current vertex among others of the same LatLngs group.\n    getIndex: function () {\n      return this.latlngs.indexOf(this.latlng)\n    },\n\n    // 🍂method getLastIndex(): int\n    // Get last vertex index of the LatLngs group of the current vertex.\n    getLastIndex: function () {\n      return this.latlngs.length - 1\n    },\n\n    // 🍂method getPrevious(): VertexMarker\n    // Get the previous VertexMarker in the same LatLngs group.\n    getPrevious: function () {\n      if (this.latlngs.length < 2) return\n      const index = this.getIndex()\n      let previousIndex = index - 1\n      if (index === 0 && this.editor.CLOSED) previousIndex = this.getLastIndex()\n      const previous = this.latlngs[previousIndex]\n      if (previous) return previous.__vertex\n    },\n\n    // 🍂method getNext(): VertexMarker\n    // Get the next VertexMarker in the same LatLngs group.\n    getNext: function () {\n      if (this.latlngs.length < 2) return\n      const index = this.getIndex()\n      let nextIndex = index + 1\n      if (index === this.getLastIndex() && this.editor.CLOSED) nextIndex = 0\n      const next = this.latlngs[nextIndex]\n      if (next) return next.__vertex\n    },\n\n    addMiddleMarker: function (previous) {\n      if (!this.editor.hasMiddleMarkers()) return\n      previous = previous || this.getPrevious()\n      if (previous && !this.middleMarker)\n        this.middleMarker = this.editor.addMiddleMarker(\n          previous,\n          this,\n          this.latlngs,\n          this.editor\n        )\n    },\n\n    addMiddleMarkers: function () {\n      if (!this.editor.hasMiddleMarkers()) return\n      const previous = this.getPrevious()\n      if (previous) this.addMiddleMarker(previous)\n      const next = this.getNext()\n      if (next) next.resetMiddleMarker()\n    },\n\n    resetMiddleMarker: function () {\n      if (this.middleMarker) this.middleMarker.delete()\n      this.addMiddleMarker()\n    },\n\n    // 🍂method split()\n    // Split the vertex LatLngs group at its index, if possible.\n    split: function () {\n      if (!this.editor.splitShape) return // Only for PolylineEditor\n      this.editor.splitShape(this.latlngs, this.getIndex())\n    },\n\n    // 🍂method continue()\n    // Continue the vertex LatLngs from this vertex. Only active for first and last vertices of a Polyline.\n    continue: function () {\n      if (!this.editor.continueBackward) return // Only for PolylineEditor\n      const index = this.getIndex()\n      if (index === 0) this.editor.continueBackward(this.latlngs)\n      else if (index === this.getLastIndex()) this.editor.continueForward(this.latlngs)\n    },\n  })\n\n  L.Editable.mergeOptions({\n    // 🍂namespace Editable\n    // 🍂option vertexMarkerClass: class = VertexMarker\n    // Class to be used as vertex, for path editing.\n    vertexMarkerClass: L.Editable.VertexMarker,\n  })\n\n  L.Editable.MiddleMarker = L.Marker.extend({\n    options: {\n      opacity: 0.5,\n      className: 'leaflet-div-icon leaflet-middle-icon',\n      draggable: true,\n    },\n\n    initialize: function (left, right, latlngs, editor, options) {\n      this.left = left\n      this.right = right\n      this.editor = editor\n      this.latlngs = latlngs\n      L.Marker.prototype.initialize.call(this, this.computeLatLng(), options)\n      this._opacity = this.options.opacity\n      this.options.icon = this.editor.tools.createVertexIcon({\n        className: this.options.className,\n      })\n      this.editor.editLayer.addLayer(this)\n      this.setVisibility()\n    },\n\n    setVisibility: function () {\n      const leftPoint = this._map.latLngToContainerPoint(this.left.latlng)\n      const rightPoint = this._map.latLngToContainerPoint(this.right.latlng)\n      const size = L.point(this.options.icon.options.iconSize)\n      if (leftPoint.distanceTo(rightPoint) < size.x * 3) this.hide()\n      else this.show()\n    },\n\n    show: function () {\n      this.setOpacity(this._opacity)\n    },\n\n    hide: function () {\n      this.setOpacity(0)\n    },\n\n    updateLatLng: function () {\n      this.setLatLng(this.computeLatLng())\n      this.setVisibility()\n    },\n\n    computeLatLng: function () {\n      const leftPoint = this.editor.map.latLngToContainerPoint(this.left.latlng)\n      const rightPoint = this.editor.map.latLngToContainerPoint(this.right.latlng)\n      const y = (leftPoint.y + rightPoint.y) / 2\n      const x = (leftPoint.x + rightPoint.x) / 2\n      return this.editor.map.containerPointToLatLng([x, y])\n    },\n\n    onAdd: function (map) {\n      L.Marker.prototype.onAdd.call(this, map)\n      L.DomEvent.on(this._icon, 'mousedown touchstart', this.onMouseDown, this)\n      map.on('zoomend', this.setVisibility, this)\n    },\n\n    onRemove: function (map) {\n      delete this.right.middleMarker\n      L.DomEvent.off(this._icon, 'mousedown touchstart', this.onMouseDown, this)\n      map.off('zoomend', this.setVisibility, this)\n      L.Marker.prototype.onRemove.call(this, map)\n    },\n\n    onMouseDown: function (e) {\n      const iconPos = L.DomUtil.getPosition(this._icon)\n      const latlng = this.editor.map.layerPointToLatLng(iconPos)\n      e = {\n        originalEvent: e,\n        latlng: latlng,\n      }\n      if (this.options.opacity === 0) return\n      L.Editable.makeCancellable(e)\n      this.editor.onMiddleMarkerMouseDown(e)\n      if (e._cancelled) return\n      this.latlngs.splice(this.index(), 0, e.latlng)\n      this.editor.refresh()\n      const icon = this._icon\n      const marker = this.editor.addVertexMarker(e.latlng, this.latlngs)\n      this.editor.onNewVertex(marker)\n      /* Hack to workaround browser not firing touchend when element is no more on DOM */\n      const parent = marker._icon.parentNode\n      parent.removeChild(marker._icon)\n      marker._icon = icon\n      parent.appendChild(marker._icon)\n      marker._initIcon()\n      marker._initInteraction()\n      marker.setOpacity(1)\n      /* End hack */\n      // Transfer ongoing dragging to real marker\n      L.Draggable._dragging = false\n      marker.dragging._draggable._onDown(e.originalEvent)\n      this.delete()\n    },\n\n    delete: function () {\n      this.editor.editLayer.removeLayer(this)\n    },\n\n    index: function () {\n      return this.latlngs.indexOf(this.right.latlng)\n    },\n  })\n\n  L.Editable.mergeOptions({\n    // 🍂namespace Editable\n    // 🍂option middleMarkerClass: class = VertexMarker\n    // Class to be used as middle vertex, pulled by the user to create a new point in the middle of a path.\n    middleMarkerClass: L.Editable.MiddleMarker,\n  })\n\n  // 🍂namespace Editable; 🍂class BaseEditor; 🍂aka L.Editable.BaseEditor\n  // When editing a feature (Marker, Polyline…), an editor is attached to it. This\n  // editor basically knows how to handle the edition.\n  L.Editable.BaseEditor = L.Handler.extend({\n    initialize: function (map, feature, options) {\n      L.setOptions(this, options)\n      this.map = map\n      this.feature = feature\n      this.feature.editor = this\n      this.editLayer = new L.LayerGroup()\n      this.tools = this.options.editTools || map.editTools\n    },\n\n    // 🍂method enable(): this\n    // Set up the drawing tools for the feature to be editable.\n    addHooks: function () {\n      if (this.isConnected()) this.onFeatureAdd()\n      else this.feature.once('add', this.onFeatureAdd, this)\n      this.onEnable()\n      this.feature.on(this._getEvents(), this)\n    },\n\n    // 🍂method disable(): this\n    // Remove the drawing tools for the feature.\n    removeHooks: function () {\n      this.feature.off(this._getEvents(), this)\n      if (this.feature.dragging) this.feature.dragging.disable()\n      this.editLayer.clearLayers()\n      this.tools.editLayer.removeLayer(this.editLayer)\n      this.onDisable()\n      if (this._drawing) this.cancelDrawing()\n    },\n\n    // 🍂method drawing(): boolean\n    // Return true if any drawing action is ongoing with this editor.\n    drawing: function () {\n      return !!this._drawing\n    },\n\n    reset: () => {},\n\n    onFeatureAdd: function () {\n      this.tools.editLayer.addLayer(this.editLayer)\n      if (this.feature.dragging) this.feature.dragging.enable()\n    },\n\n    hasMiddleMarkers: function () {\n      return !this.options.skipMiddleMarkers && !this.tools.options.skipMiddleMarkers\n    },\n\n    fireAndForward: function (type, e) {\n      e = e || {}\n      e.layer = this.feature\n      this.feature.fire(type, e)\n      this.tools.fireAndForward(type, e)\n    },\n\n    onEnable: function () {\n      // 🍂namespace Editable\n      // 🍂event editable:enable: Event\n      // Fired when an existing feature is ready to be edited.\n      this.fireAndForward('editable:enable')\n    },\n\n    onDisable: function () {\n      // 🍂namespace Editable\n      // 🍂event editable:disable: Event\n      // Fired when an existing feature is not ready anymore to be edited.\n      this.fireAndForward('editable:disable')\n    },\n\n    onEditing: function () {\n      // 🍂namespace Editable\n      // 🍂event editable:editing: Event\n      // Fired as soon as any change is made to the feature geometry.\n      this.fireAndForward('editable:editing')\n    },\n\n    onEdited: function () {\n      // 🍂namespace Editable\n      // 🍂event editable:edited: Event\n      // Fired after any change is made to the feature geometry.\n      this.fireAndForward('editable:edited')\n    },\n\n    onStartDrawing: function () {\n      // 🍂namespace Editable\n      // 🍂section Drawing events\n      // 🍂event editable:drawing:start: Event\n      // Fired when a feature is to be drawn.\n      this.fireAndForward('editable:drawing:start')\n    },\n\n    onEndDrawing: function () {\n      // 🍂namespace Editable\n      // 🍂section Drawing events\n      // 🍂event editable:drawing:end: Event\n      // Fired when a feature is not drawn anymore.\n      this.fireAndForward('editable:drawing:end')\n    },\n\n    onCancelDrawing: function () {\n      // 🍂namespace Editable\n      // 🍂section Drawing events\n      // 🍂event editable:drawing:cancel: Event\n      // Fired when user cancel drawing while a feature is being drawn.\n      this.fireAndForward('editable:drawing:cancel')\n    },\n\n    onCommitDrawing: function (e) {\n      // 🍂namespace Editable\n      // 🍂section Drawing events\n      // 🍂event editable:drawing:commit: Event\n      // Fired when user finish drawing a feature.\n      this.fireAndForward('editable:drawing:commit', e)\n      this.onEdited()\n    },\n\n    onDrawingMouseDown: function (e) {\n      // 🍂namespace Editable\n      // 🍂section Drawing events\n      // 🍂event editable:drawing:mousedown: Event\n      // Fired when user `mousedown` while drawing.\n      this.fireAndForward('editable:drawing:mousedown', e)\n    },\n\n    onDrawingMouseUp: function (e) {\n      // 🍂namespace Editable\n      // 🍂section Drawing events\n      // 🍂event editable:drawing:mouseup: Event\n      // Fired when user `mouseup` while drawing.\n      this.fireAndForward('editable:drawing:mouseup', e)\n    },\n\n    startDrawing: function () {\n      if (!this._drawing) this._drawing = L.Editable.FORWARD\n      this.tools.registerForDrawing(this)\n      this.onStartDrawing()\n    },\n\n    commitDrawing: function (e) {\n      this.onCommitDrawing(e)\n      this.endDrawing()\n    },\n\n    cancelDrawing: function () {\n      // If called during a vertex drag, the vertex will be removed before\n      // the mouseup fires on it. This is a workaround. Maybe better fix is\n      // To have L.Draggable reset it's status on disable (Leaflet side).\n      L.Draggable._dragging = false\n      this.onCancelDrawing()\n      this.endDrawing()\n    },\n\n    endDrawing: function () {\n      this._drawing = false\n      this.tools.unregisterForDrawing(this)\n      this.onEndDrawing()\n    },\n\n    onDrawingClick: function (e) {\n      if (!this.drawing()) return\n      L.Editable.makeCancellable(e)\n      // 🍂namespace Editable\n      // 🍂section Drawing events\n      // 🍂event editable:drawing:click: CancelableEvent\n      // Fired when user `click` while drawing, before any internal action is being processed.\n      this.fireAndForward('editable:drawing:click', e)\n      if (e._cancelled) return\n      if (!this.isConnected()) this.connect(e)\n      this.processDrawingClick(e)\n    },\n\n    isConnected: function () {\n      return this.map.hasLayer(this.feature)\n    },\n\n    connect: function () {\n      this.tools.connectCreatedToMap(this.feature)\n      this.tools.editLayer.addLayer(this.editLayer)\n    },\n\n    onMove: function (e) {\n      // 🍂namespace Editable\n      // 🍂section Drawing events\n      // 🍂event editable:drawing:move: Event\n      // Fired when `move` mouse while drawing, while dragging a marker, and while dragging a vertex.\n      this.fireAndForward('editable:drawing:move', e)\n    },\n\n    onDrawingMouseMove: function (e) {\n      this.onMove(e)\n    },\n\n    _getEvents: function () {\n      return {\n        dragstart: this.onDragStart,\n        drag: this.onDrag,\n        dragend: this.onDragEnd,\n        remove: this.disable,\n      }\n    },\n\n    onDragStart: function (e) {\n      this.onEditing()\n      // 🍂namespace Editable\n      // 🍂event editable:dragstart: Event\n      // Fired before a path feature is dragged.\n      this.fireAndForward('editable:dragstart', e)\n    },\n\n    onDrag: function (e) {\n      this.onMove(e)\n      // 🍂namespace Editable\n      // 🍂event editable:drag: Event\n      // Fired when a path feature is being dragged.\n      this.fireAndForward('editable:drag', e)\n    },\n\n    onDragEnd: function (e) {\n      // 🍂namespace Editable\n      // 🍂event editable:dragend: Event\n      // Fired after a path feature has been dragged.\n      this.fireAndForward('editable:dragend', e)\n      this.onEdited()\n    },\n  })\n\n  // 🍂namespace Editable; 🍂class MarkerEditor; 🍂aka L.Editable.MarkerEditor\n  // 🍂inherits BaseEditor\n  // Editor for Marker.\n  L.Editable.MarkerEditor = L.Editable.BaseEditor.extend({\n    onDrawingMouseMove: function (e) {\n      L.Editable.BaseEditor.prototype.onDrawingMouseMove.call(this, e)\n      if (this._drawing) this.feature.setLatLng(e.latlng)\n    },\n\n    processDrawingClick: function (e) {\n      // 🍂namespace Editable\n      // 🍂section Drawing events\n      // 🍂event editable:drawing:clicked: Event\n      // Fired when user `click` while drawing, after all internal actions.\n      this.fireAndForward('editable:drawing:clicked', e)\n      this.commitDrawing(e)\n    },\n\n    connect: function (e) {\n      // On touch, the latlng has not been updated because there is\n      // no mousemove.\n      if (e) this.feature._latlng = e.latlng\n      L.Editable.BaseEditor.prototype.connect.call(this, e)\n    },\n  })\n\n  // 🍂namespace Editable; 🍂class CircleMarkerEditor; 🍂aka L.Editable.CircleMarkerEditor\n  // 🍂inherits BaseEditor\n  // Editor for CircleMarker.\n  L.Editable.CircleMarkerEditor = L.Editable.BaseEditor.extend({\n    onDrawingMouseMove: function (e) {\n      L.Editable.BaseEditor.prototype.onDrawingMouseMove.call(this, e)\n      if (this._drawing) this.feature.setLatLng(e.latlng)\n    },\n\n    processDrawingClick: function (e) {\n      // 🍂namespace Editable\n      // 🍂section Drawing events\n      // 🍂event editable:drawing:clicked: Event\n      // Fired when user `click` while drawing, after all internal actions.\n      this.fireAndForward('editable:drawing:clicked', e)\n      this.commitDrawing(e)\n    },\n\n    connect: function (e) {\n      // On touch, the latlng has not been updated because there is\n      // no mousemove.\n      if (e) this.feature._latlng = e.latlng\n      L.Editable.BaseEditor.prototype.connect.call(this, e)\n    },\n  })\n\n  // 🍂namespace Editable; 🍂class PathEditor; 🍂aka L.Editable.PathEditor\n  // 🍂inherits BaseEditor\n  // Base class for all path editors.\n  L.Editable.PathEditor = L.Editable.BaseEditor.extend({\n    CLOSED: false,\n    MIN_VERTEX: 2,\n\n    addHooks: function () {\n      L.Editable.BaseEditor.prototype.addHooks.call(this)\n      if (this.feature) {\n        this.initVertexMarkers()\n        this.map.on('moveend', this.onMoveEnd, this)\n      }\n      return this\n    },\n\n    removeHooks: function () {\n      L.Editable.BaseEditor.prototype.removeHooks.call(this)\n      if (this.feature) {\n        this.map.off('moveend', this.onMoveEnd, this)\n      }\n    },\n\n    onMoveEnd: function () {\n      this.initVertexMarkers()\n    },\n\n    initVertexMarkers: function (latlngs) {\n      if (!this.enabled()) return\n      latlngs = latlngs || this.getLatLngs()\n      if (isFlat(latlngs)) {\n        this.addVertexMarkers(latlngs)\n      } else {\n        for (const member of latlngs) {\n          this.initVertexMarkers(member)\n        }\n      }\n    },\n\n    getLatLngs: function () {\n      return this.feature.getLatLngs()\n    },\n\n    // 🍂method reset()\n    // Rebuild edit elements (Vertex, MiddleMarker, etc.).\n    reset: function () {\n      this.editLayer.clearLayers()\n      this.initVertexMarkers()\n    },\n\n    addVertexMarker: function (latlng, latlngs) {\n      if (latlng.__vertex) {\n        latlng.__vertex.connect()\n        return latlng.__vertex\n      }\n      return new this.tools.options.vertexMarkerClass(latlng, latlngs, this)\n    },\n\n    onNewVertex: function (vertex) {\n      // 🍂namespace Editable\n      // 🍂section Vertex events\n      // 🍂event editable:vertex:new: VertexEvent\n      // Fired when a new vertex is created.\n      this.fireAndForward('editable:vertex:new', {\n        latlng: vertex.latlng,\n        vertex: vertex,\n      })\n    },\n\n    addVertexMarkers: function (latlngs) {\n      const bounds = this.map.getBounds()\n      for (const latlng of latlngs) {\n        if (!bounds.contains(latlng)) continue\n        this.addVertexMarker(latlng, latlngs)\n      }\n    },\n\n    refreshVertexMarkers: function (latlngs) {\n      latlngs = latlngs || this.getDefaultLatLngs()\n      for (const latlng of latlngs) {\n        latlng.__vertex.update()\n      }\n    },\n\n    addMiddleMarker: function (left, right, latlngs) {\n      return new this.tools.options.middleMarkerClass(left, right, latlngs, this)\n    },\n\n    onVertexMarkerClick: function (e) {\n      L.Editable.makeCancellable(e)\n      // 🍂namespace Editable\n      // 🍂section Vertex events\n      // 🍂event editable:vertex:click: CancelableVertexEvent\n      // Fired when a `click` is issued on a vertex, before any internal action is being processed.\n      this.fireAndForward('editable:vertex:click', e)\n      if (e._cancelled) return\n      if (this.tools.drawing() && this.tools._drawingEditor !== this) return\n      const index = e.vertex.getIndex()\n      let commit\n      if (e.originalEvent.ctrlKey) {\n        this.onVertexMarkerCtrlClick(e)\n      } else if (e.originalEvent.altKey) {\n        this.onVertexMarkerAltClick(e)\n      } else if (e.originalEvent.shiftKey) {\n        this.onVertexMarkerShiftClick(e)\n      } else if (e.originalEvent.metaKey) {\n        this.onVertexMarkerMetaKeyClick(e)\n      } else if (\n        index === e.vertex.getLastIndex() &&\n        this._drawing === L.Editable.FORWARD\n      ) {\n        if (index >= this.MIN_VERTEX - 1) commit = true\n      } else if (\n        index === 0 &&\n        this._drawing === L.Editable.BACKWARD &&\n        this._drawnLatLngs.length >= this.MIN_VERTEX\n      ) {\n        commit = true\n      } else if (\n        index === 0 &&\n        this._drawing === L.Editable.FORWARD &&\n        this._drawnLatLngs.length >= this.MIN_VERTEX &&\n        this.CLOSED\n      ) {\n        commit = true // Allow to close on first point also for polygons\n      } else {\n        this.onVertexRawMarkerClick(e)\n      }\n      // 🍂namespace Editable\n      // 🍂section Vertex events\n      // 🍂event editable:vertex:clicked: VertexEvent\n      // Fired when a `click` is issued on a vertex, after all internal actions.\n      this.fireAndForward('editable:vertex:clicked', e)\n      if (commit) this.commitDrawing(e)\n    },\n\n    onVertexRawMarkerClick: function (e) {\n      // 🍂namespace Editable\n      // 🍂section Vertex events\n      // 🍂event editable:vertex:rawclick: CancelableVertexEvent\n      // Fired when a `click` is issued on a vertex without any special key and without being in drawing mode.\n      this.fireAndForward('editable:vertex:rawclick', e)\n      if (e._cancelled) return\n      if (!this.vertexCanBeDeleted(e.vertex)) return\n      e.vertex.delete()\n    },\n\n    vertexCanBeDeleted: function (vertex) {\n      return vertex.latlngs.length > this.MIN_VERTEX\n    },\n\n    onVertexDeleted: function (e) {\n      // 🍂namespace Editable\n      // 🍂section Vertex events\n      // 🍂event editable:vertex:deleted: VertexEvent\n      // Fired after a vertex has been deleted by user.\n      this.fireAndForward('editable:vertex:deleted', e)\n      this.onEdited()\n    },\n\n    onVertexMarkerCtrlClick: function (e) {\n      // 🍂namespace Editable\n      // 🍂section Vertex events\n      // 🍂event editable:vertex:ctrlclick: VertexEvent\n      // Fired when a `click` with `ctrlKey` is issued on a vertex.\n      this.fireAndForward('editable:vertex:ctrlclick', e)\n    },\n\n    onVertexMarkerShiftClick: function (e) {\n      // 🍂namespace Editable\n      // 🍂section Vertex events\n      // 🍂event editable:vertex:shiftclick: VertexEvent\n      // Fired when a `click` with `shiftKey` is issued on a vertex.\n      this.fireAndForward('editable:vertex:shiftclick', e)\n    },\n\n    onVertexMarkerMetaKeyClick: function (e) {\n      // 🍂namespace Editable\n      // 🍂section Vertex events\n      // 🍂event editable:vertex:metakeyclick: VertexEvent\n      // Fired when a `click` with `metaKey` is issued on a vertex.\n      this.fireAndForward('editable:vertex:metakeyclick', e)\n    },\n\n    onVertexMarkerAltClick: function (e) {\n      // 🍂namespace Editable\n      // 🍂section Vertex events\n      // 🍂event editable:vertex:altclick: VertexEvent\n      // Fired when a `click` with `altKey` is issued on a vertex.\n      this.fireAndForward('editable:vertex:altclick', e)\n    },\n\n    onVertexMarkerContextMenu: function (e) {\n      // 🍂namespace Editable\n      // 🍂section Vertex events\n      // 🍂event editable:vertex:contextmenu: VertexEvent\n      // Fired when a `contextmenu` is issued on a vertex.\n      this.fireAndForward('editable:vertex:contextmenu', e)\n    },\n\n    onVertexMarkerMouseDown: function (e) {\n      // 🍂namespace Editable\n      // 🍂section Vertex events\n      // 🍂event editable:vertex:mousedown: VertexEvent\n      // Fired when user `mousedown` a vertex.\n      this.fireAndForward('editable:vertex:mousedown', e)\n    },\n\n    onVertexMarkerMouseOver: function (e) {\n      // 🍂namespace Editable\n      // 🍂section Vertex events\n      // 🍂event editable:vertex:mouseover: VertexEvent\n      // Fired when a user's mouse enters the vertex\n      this.fireAndForward('editable:vertex:mouseover', e)\n    },\n\n    onVertexMarkerMouseOut: function (e) {\n      // 🍂namespace Editable\n      // 🍂section Vertex events\n      // 🍂event editable:vertex:mouseout: VertexEvent\n      // Fired when a user's mouse leaves the vertex\n      this.fireAndForward('editable:vertex:mouseout', e)\n    },\n\n    onMiddleMarkerMouseDown: function (e) {\n      // 🍂namespace Editable\n      // 🍂section MiddleMarker events\n      // 🍂event editable:middlemarker:mousedown: VertexEvent\n      // Fired when user `mousedown` a middle marker.\n      this.fireAndForward('editable:middlemarker:mousedown', e)\n    },\n\n    onVertexMarkerDrag: function (e) {\n      this.onMove(e)\n      if (this.feature._bounds) this.extendBounds(e)\n      // 🍂namespace Editable\n      // 🍂section Vertex events\n      // 🍂event editable:vertex:drag: VertexEvent\n      // Fired when a vertex is dragged by user.\n      this.fireAndForward('editable:vertex:drag', e)\n    },\n\n    onVertexMarkerDragStart: function (e) {\n      // 🍂namespace Editable\n      // 🍂section Vertex events\n      // 🍂event editable:vertex:dragstart: VertexEvent\n      // Fired before a vertex is dragged by user.\n      this.fireAndForward('editable:vertex:dragstart', e)\n    },\n\n    onVertexMarkerDragEnd: function (e) {\n      // 🍂namespace Editable\n      // 🍂section Vertex events\n      // 🍂event editable:vertex:dragend: VertexEvent\n      // Fired after a vertex is dragged by user.\n      this.fireAndForward('editable:vertex:dragend', e)\n      this.onEdited()\n    },\n\n    setDrawnLatLngs: function (latlngs) {\n      this._drawnLatLngs = latlngs || this.getDefaultLatLngs()\n    },\n\n    startDrawing: function () {\n      if (!this._drawnLatLngs) this.setDrawnLatLngs()\n      L.Editable.BaseEditor.prototype.startDrawing.call(this)\n    },\n\n    startDrawingForward: function () {\n      this.startDrawing()\n    },\n\n    endDrawing: function () {\n      this.tools.detachForwardLineGuide()\n      this.tools.detachBackwardLineGuide()\n      if (this._drawnLatLngs && this._drawnLatLngs.length < this.MIN_VERTEX)\n        this.deleteShape(this._drawnLatLngs)\n      L.Editable.BaseEditor.prototype.endDrawing.call(this)\n      delete this._drawnLatLngs\n    },\n\n    addLatLng: function (latlng) {\n      if (this._drawing === L.Editable.FORWARD) this._drawnLatLngs.push(latlng)\n      else this._drawnLatLngs.unshift(latlng)\n      this.feature._bounds.extend(latlng)\n      const vertex = this.addVertexMarker(latlng, this._drawnLatLngs)\n      this.onNewVertex(vertex)\n      this.refresh()\n    },\n\n    newPointForward: function (latlng) {\n      this.addLatLng(latlng)\n      this.tools.attachForwardLineGuide()\n      this.tools.anchorForwardLineGuide(latlng)\n    },\n\n    newPointBackward: function (latlng) {\n      this.addLatLng(latlng)\n      this.tools.anchorBackwardLineGuide(latlng)\n    },\n\n    // 🍂namespace PathEditor\n    // 🍂method push()\n    // Programmatically add a point while drawing.\n    push: function (latlng) {\n      if (!latlng)\n        return console.error(\n          'L.Editable.PathEditor.push expect a valid latlng as parameter'\n        )\n      if (this._drawing === L.Editable.FORWARD) this.newPointForward(latlng)\n      else this.newPointBackward(latlng)\n    },\n\n    removeLatLng: function (latlng) {\n      latlng.__vertex.delete()\n      this.refresh()\n    },\n\n    // 🍂method pop(): L.LatLng or null\n    // Programmatically remove last point (if any) while drawing.\n    pop: function () {\n      if (this._drawnLatLngs.length <= 1) return\n      let latlng\n      if (this._drawing === L.Editable.FORWARD) {\n        latlng = this._drawnLatLngs[this._drawnLatLngs.length - 1]\n      } else {\n        latlng = this._drawnLatLngs[0]\n      }\n      this.removeLatLng(latlng)\n      if (this._drawing === L.Editable.FORWARD) {\n        this.tools.anchorForwardLineGuide(\n          this._drawnLatLngs[this._drawnLatLngs.length - 1]\n        )\n      } else {\n        this.tools.anchorForwardLineGuide(this._drawnLatLngs[0])\n      }\n      return latlng\n    },\n\n    processDrawingClick: function (e) {\n      if (e.vertex && e.vertex.editor === this) return\n      if (this._drawing === L.Editable.FORWARD) this.newPointForward(e.latlng)\n      else this.newPointBackward(e.latlng)\n      this.fireAndForward('editable:drawing:clicked', e)\n    },\n\n    onDrawingMouseMove: function (e) {\n      L.Editable.BaseEditor.prototype.onDrawingMouseMove.call(this, e)\n      if (this._drawing) {\n        this.tools.moveForwardLineGuide(e.latlng)\n        this.tools.moveBackwardLineGuide(e.latlng)\n      }\n    },\n\n    refresh: function () {\n      this.feature.redraw()\n      this.onEditing()\n    },\n\n    // 🍂namespace PathEditor\n    // 🍂method newShape(latlng?: L.LatLng)\n    // Add a new shape (Polyline, Polygon) in a multi, and setup up drawing tools to draw it;\n    // if optional `latlng` is given, start a path at this point.\n    newShape: function (latlng) {\n      const shape = this.addNewEmptyShape()\n      if (!shape) return\n      this.setDrawnLatLngs(shape[0] || shape) // Polygon or polyline\n      this.startDrawingForward()\n      // 🍂namespace Editable\n      // 🍂section Shape events\n      // 🍂event editable:shape:new: ShapeEvent\n      // Fired when a new shape is created in a multi (Polygon or Polyline).\n      this.fireAndForward('editable:shape:new', { shape: shape })\n      if (latlng) this.newPointForward(latlng)\n    },\n\n    deleteShape: function (shape, latlngs) {\n      const e = { shape: shape }\n      L.Editable.makeCancellable(e)\n      // 🍂namespace Editable\n      // 🍂section Shape events\n      // 🍂event editable:shape:delete: CancelableShapeEvent\n      // Fired before a new shape is deleted in a multi (Polygon or Polyline).\n      this.fireAndForward('editable:shape:delete', e)\n      if (e._cancelled) return\n      shape = this._deleteShape(shape, latlngs)\n      if (this.ensureNotFlat) this.ensureNotFlat() // Polygon.\n      this.feature.setLatLngs(this.getLatLngs()) // Force bounds reset.\n      this.refresh()\n      this.reset()\n      // 🍂namespace Editable\n      // 🍂section Shape events\n      // 🍂event editable:shape:deleted: ShapeEvent\n      // Fired after a new shape is deleted in a multi (Polygon or Polyline).\n      this.fireAndForward('editable:shape:deleted', { shape: shape })\n      this.onEdited()\n      return shape\n    },\n\n    _deleteShape: function (shape, latlngs) {\n      latlngs = latlngs || this.getLatLngs()\n      if (!latlngs.length) return\n      const inplaceDelete = (latlngs, shape) => {\n        // Called when deleting a flat latlngs\n        return latlngs.splice(0, Number.MAX_VALUE)\n      }\n      const spliceDelete = (latlngs, shape) => {\n        // Called when removing a latlngs inside an array\n        latlngs.splice(latlngs.indexOf(shape), 1)\n        if (!latlngs.length) this._deleteShape(latlngs)\n        return shape\n      }\n      if (latlngs === shape) return inplaceDelete(latlngs, shape)\n      for (const member of latlngs) {\n        if (member === shape) return spliceDelete(latlngs, shape)\n        if (member.indexOf(shape) !== -1) return spliceDelete(member, shape)\n      }\n    },\n\n    // 🍂namespace PathEditor\n    // 🍂method deleteShapeAt(latlng: L.LatLng): Array\n    // Remove a path shape at the given `latlng`.\n    deleteShapeAt: function (latlng) {\n      const shape = this.feature.shapeAt(latlng)\n      if (shape) return this.deleteShape(shape)\n    },\n\n    // 🍂method appendShape(shape: Array)\n    // Append a new shape to the Polygon or Polyline.\n    appendShape: function (shape) {\n      this.insertShape(shape)\n    },\n\n    // 🍂method prependShape(shape: Array)\n    // Prepend a new shape to the Polygon or Polyline.\n    prependShape: function (shape) {\n      this.insertShape(shape, 0)\n    },\n\n    // 🍂method insertShape(shape: Array, index: int)\n    // Insert a new shape to the Polygon or Polyline at given index (default is to append).\n    insertShape: function (shape, index) {\n      this.ensureMulti()\n      shape = this.formatShape(shape)\n      if (index === undefined) index = this.feature._latlngs.length\n      this.feature._latlngs.splice(index, 0, shape)\n      this.feature.redraw()\n      if (this._enabled) this.reset()\n    },\n\n    extendBounds: function (e) {\n      this.feature._bounds.extend(e.vertex.latlng)\n    },\n\n    onDragStart: function (e) {\n      this.editLayer.clearLayers()\n      L.Editable.BaseEditor.prototype.onDragStart.call(this, e)\n    },\n\n    onDragEnd: function (e) {\n      this.initVertexMarkers()\n      L.Editable.BaseEditor.prototype.onDragEnd.call(this, e)\n    },\n  })\n\n  // 🍂namespace Editable; 🍂class PolylineEditor; 🍂aka L.Editable.PolylineEditor\n  // 🍂inherits PathEditor\n  L.Editable.PolylineEditor = L.Editable.PathEditor.extend({\n    startDrawingBackward: function () {\n      this._drawing = L.Editable.BACKWARD\n      this.startDrawing()\n    },\n\n    // 🍂method continueBackward(latlngs?: Array)\n    // Set up drawing tools to continue the line backward.\n    continueBackward: function (latlngs) {\n      if (this.drawing()) return\n      latlngs = latlngs || this.getDefaultLatLngs()\n      this.setDrawnLatLngs(latlngs)\n      if (latlngs.length > 0) {\n        this.tools.attachBackwardLineGuide()\n        this.tools.anchorBackwardLineGuide(latlngs[0])\n      }\n      this.startDrawingBackward()\n    },\n\n    // 🍂method continueForward(latlngs?: Array)\n    // Set up drawing tools to continue the line forward.\n    continueForward: function (latlngs) {\n      if (this.drawing()) return\n      latlngs = latlngs || this.getDefaultLatLngs()\n      this.setDrawnLatLngs(latlngs)\n      if (latlngs.length > 0) {\n        this.tools.attachForwardLineGuide()\n        this.tools.anchorForwardLineGuide(latlngs[latlngs.length - 1])\n      }\n      this.startDrawingForward()\n    },\n\n    getDefaultLatLngs: function (latlngs) {\n      latlngs = latlngs || this.feature._latlngs\n      if (!latlngs.length || latlngs[0] instanceof L.LatLng) return latlngs\n      return this.getDefaultLatLngs(latlngs[0])\n    },\n\n    ensureMulti: function () {\n      if (this.feature._latlngs.length && isFlat(this.feature._latlngs)) {\n        this.feature._latlngs = [this.feature._latlngs]\n      }\n    },\n\n    addNewEmptyShape: function () {\n      if (this.feature._latlngs.length) {\n        const shape = []\n        this.appendShape(shape)\n        return shape\n      }\n      return this.feature._latlngs\n    },\n\n    formatShape: function (shape) {\n      if (isFlat(shape)) return shape\n      if (shape[0]) return this.formatShape(shape[0])\n    },\n\n    // 🍂method splitShape(latlngs?: Array, index: int)\n    // Split the given `latlngs` shape at index `index` and integrate new shape in instance `latlngs`.\n    splitShape: function (shape, index) {\n      if (!index || index >= shape.length - 1) return\n      this.ensureMulti()\n      const shapeIndex = this.feature._latlngs.indexOf(shape)\n      if (shapeIndex === -1) return\n      const first = shape.slice(0, index + 1)\n      const second = shape.slice(index)\n      // We deal with reference, we don't want twice the same latlng around.\n      second[0] = L.latLng(second[0].lat, second[0].lng, second[0].alt)\n      this.feature._latlngs.splice(shapeIndex, 1, first, second)\n      this.refresh()\n      this.reset()\n      this.onEdited()\n    },\n  })\n\n  // 🍂namespace Editable; 🍂class PolygonEditor; 🍂aka L.Editable.PolygonEditor\n  // 🍂inherits PathEditor\n  L.Editable.PolygonEditor = L.Editable.PathEditor.extend({\n    CLOSED: true,\n    MIN_VERTEX: 3,\n\n    newPointForward: function (latlng) {\n      L.Editable.PathEditor.prototype.newPointForward.call(this, latlng)\n      if (!this.tools.backwardLineGuide._latlngs.length)\n        this.tools.anchorBackwardLineGuide(latlng)\n      if (this._drawnLatLngs.length === 2) this.tools.attachBackwardLineGuide()\n    },\n\n    addNewEmptyHole: function (latlng) {\n      this.ensureNotFlat()\n      const latlngs = this.feature.shapeAt(latlng)\n      if (!latlngs) return\n      const holes = []\n      latlngs.push(holes)\n      return holes\n    },\n\n    // 🍂method newHole(latlng?: L.LatLng, index: int)\n    // Set up drawing tools for creating a new hole on the Polygon. If the `latlng` param is given, a first point is created.\n    newHole: function (latlng) {\n      const holes = this.addNewEmptyHole(latlng)\n      if (!holes) return\n      this.setDrawnLatLngs(holes)\n      this.startDrawingForward()\n      if (latlng) this.newPointForward(latlng)\n    },\n\n    addNewEmptyShape: function () {\n      if (this.feature._latlngs.length && this.feature._latlngs[0].length) {\n        const shape = []\n        this.appendShape(shape)\n        return shape\n      }\n      return this.feature._latlngs\n    },\n\n    ensureMulti: function () {\n      if (this.feature._latlngs.length && isFlat(this.feature._latlngs[0])) {\n        this.feature._latlngs = [this.feature._latlngs]\n      }\n    },\n\n    ensureNotFlat: function () {\n      if (!this.feature._latlngs.length || isFlat(this.feature._latlngs))\n        this.feature._latlngs = [this.feature._latlngs]\n    },\n\n    vertexCanBeDeleted: function (vertex) {\n      const parent = this.feature.parentShape(vertex.latlngs)\n      const idx = L.Util.indexOf(parent, vertex.latlngs)\n      if (idx > 0) return true // Holes can be totally deleted without removing the layer itself.\n      return L.Editable.PathEditor.prototype.vertexCanBeDeleted.call(this, vertex)\n    },\n\n    getDefaultLatLngs: function () {\n      if (!this.feature._latlngs.length) this.feature._latlngs.push([])\n      return this.feature._latlngs[0]\n    },\n\n    formatShape: (shape) => {\n      // [[1, 2], [3, 4]] => must be nested\n      // [] => must be nested\n      // [[]] => is already nested\n      if (isFlat(shape) && (!shape[0] || shape[0].length !== 0)) return [shape]\n      return shape\n    },\n  })\n\n  // 🍂namespace Editable; 🍂class RectangleEditor; 🍂aka L.Editable.RectangleEditor\n  // 🍂inherits PathEditor\n  L.Editable.RectangleEditor = L.Editable.PathEditor.extend({\n    CLOSED: true,\n    MIN_VERTEX: 4,\n\n    options: {\n      skipMiddleMarkers: true,\n    },\n\n    extendBounds: function (e) {\n      const index = e.vertex.getIndex()\n      const next = e.vertex.getNext()\n      const previous = e.vertex.getPrevious()\n      const oppositeIndex = (index + 2) % 4\n      const opposite = e.vertex.latlngs[oppositeIndex]\n      const bounds = new L.LatLngBounds(e.latlng, opposite)\n      // Update latlngs by hand to preserve order.\n      previous.latlng.update([e.latlng.lat, opposite.lng])\n      next.latlng.update([opposite.lat, e.latlng.lng])\n      this.updateBounds(bounds)\n      this.refreshVertexMarkers()\n    },\n\n    onDrawingMouseDown: function (e) {\n      L.Editable.PathEditor.prototype.onDrawingMouseDown.call(this, e)\n      this.connect()\n      const latlngs = this.getDefaultLatLngs()\n      // L.Polygon._convertLatLngs removes last latlng if it equals first point,\n      // which is the case here as all latlngs are [0, 0]\n      if (latlngs.length === 3) latlngs.push(e.latlng)\n      const bounds = new L.LatLngBounds(e.latlng, e.latlng)\n      this.updateBounds(bounds)\n      this.updateLatLngs(bounds)\n      this.refresh()\n      this.reset()\n      // Stop dragging map.\n      // L.Draggable has two workflows:\n      // - mousedown => mousemove => mouseup\n      // - touchstart => touchmove => touchend\n      // Problem: L.Map.Tap does not allow us to listen to touchstart, so we only\n      // can deal with mousedown, but then when in a touch device, we are dealing with\n      // simulated events (actually simulated by L.Map.Tap), which are no more taken\n      // into account by L.Draggable.\n      // Ref.: https://github.com/Leaflet/Leaflet.Editable/issues/103\n      e.originalEvent._simulated = false\n      this.map.dragging._draggable._onUp(e.originalEvent)\n      // Now transfer ongoing drag action to the bottom right corner.\n      // Should we refine which corner will handle the drag according to\n      // drag direction?\n      latlngs[3].__vertex.dragging._draggable._onDown(e.originalEvent)\n    },\n\n    onDrawingMouseUp: function (e) {\n      this.commitDrawing(e)\n      e.originalEvent._simulated = false\n      L.Editable.PathEditor.prototype.onDrawingMouseUp.call(this, e)\n    },\n\n    onDrawingMouseMove: function (e) {\n      e.originalEvent._simulated = false\n      L.Editable.PathEditor.prototype.onDrawingMouseMove.call(this, e)\n    },\n\n    getDefaultLatLngs: function (latlngs) {\n      return latlngs || this.feature._latlngs[0]\n    },\n\n    updateBounds: function (bounds) {\n      this.feature._bounds = bounds\n    },\n\n    updateLatLngs: function (bounds) {\n      const latlngs = this.getDefaultLatLngs()\n      const newLatlngs = this.feature._boundsToLatLngs(bounds)\n      // Keep references.\n      for (let i = 0; i < latlngs.length; i++) {\n        latlngs[i].update(newLatlngs[i])\n      }\n    },\n  })\n\n  // 🍂namespace Editable; 🍂class CircleEditor; 🍂aka L.Editable.CircleEditor\n  // 🍂inherits PathEditor\n  L.Editable.CircleEditor = L.Editable.PathEditor.extend({\n    MIN_VERTEX: 2,\n\n    options: {\n      skipMiddleMarkers: true,\n    },\n\n    initialize: function (map, feature, options) {\n      L.Editable.PathEditor.prototype.initialize.call(this, map, feature, options)\n      this._resizeLatLng = this.computeResizeLatLng()\n    },\n\n    computeResizeLatLng: function () {\n      // While circle is not added to the map, _radius is not set.\n      const delta =\n        (this.feature._radius || this.feature._mRadius) * Math.cos(Math.PI / 4)\n      const point = this.map.project(this.feature._latlng)\n      return this.map.unproject([point.x + delta, point.y - delta])\n    },\n\n    updateResizeLatLng: function () {\n      this._resizeLatLng.update(this.computeResizeLatLng())\n      this._resizeLatLng.__vertex.update()\n    },\n\n    getLatLngs: function () {\n      return [this.feature._latlng, this._resizeLatLng]\n    },\n\n    getDefaultLatLngs: function () {\n      return this.getLatLngs()\n    },\n\n    onVertexMarkerDrag: function (e) {\n      if (e.vertex.getIndex() === 1) this.resize(e)\n      else this.updateResizeLatLng(e)\n      L.Editable.PathEditor.prototype.onVertexMarkerDrag.call(this, e)\n    },\n\n    resize: function (e) {\n      let radius\n      if (this.map.options.crs) {\n        radius = this.map.options.crs.distance(this.feature._latlng, e.latlng)\n      } else {\n        radius = this.feature._latlng.distanceTo(e.latlng)\n      }\n      this.feature.setRadius(radius)\n    },\n\n    onDrawingMouseDown: function (e) {\n      L.Editable.PathEditor.prototype.onDrawingMouseDown.call(this, e)\n      this._resizeLatLng.update(e.latlng)\n      this.feature._latlng.update(e.latlng)\n      this.connect()\n      // Stop dragging map.\n      e.originalEvent._simulated = false\n      this.map.dragging._draggable._onUp(e.originalEvent)\n      // Now transfer ongoing drag action to the radius handler.\n      this._resizeLatLng.__vertex.dragging._draggable._onDown(e.originalEvent)\n    },\n\n    onDrawingMouseUp: function (e) {\n      this.commitDrawing(e)\n      e.originalEvent._simulated = false\n      L.Editable.PathEditor.prototype.onDrawingMouseUp.call(this, e)\n    },\n\n    onDrawingMouseMove: function (e) {\n      e.originalEvent._simulated = false\n      L.Editable.PathEditor.prototype.onDrawingMouseMove.call(this, e)\n    },\n\n    onDrag: function (e) {\n      L.Editable.PathEditor.prototype.onDrag.call(this, e)\n      this.feature.dragging.updateLatLng(this._resizeLatLng)\n    },\n  })\n\n  // 🍂namespace Editable; 🍂class EditableMixin\n  // `EditableMixin` is included to `L.Polyline`, `L.Polygon`, `L.Rectangle`, `L.Circle`\n  // and `L.Marker`. It adds some methods to them.\n  // *When editing is enabled, the editor is accessible on the instance with the\n  // `editor` property.*\n  const EditableMixin = {\n    createEditor: function (map) {\n      map = map || this._map\n      const tools = this.options.editOptions?.editTools || map.editTools\n      if (!tools) throw Error('Unable to detect Editable instance.')\n      const Klass = this.options.editorClass || this.getEditorClass(tools)\n      return new Klass(map, this, this.options.editOptions)\n    },\n\n    // 🍂method enableEdit(map?: L.Map): this.editor\n    // Enable editing, by creating an editor if not existing, and then calling `enable` on it.\n    enableEdit: function (map) {\n      if (!this.editor) this.createEditor(map)\n      this.editor.enable()\n      return this.editor\n    },\n\n    // 🍂method editEnabled(): boolean\n    // Return true if current instance has an editor attached, and this editor is enabled.\n    editEnabled: function () {\n      return this.editor?.enabled()\n    },\n\n    // 🍂method disableEdit()\n    // Disable editing, also remove the editor property reference.\n    disableEdit: function () {\n      if (this.editor) {\n        this.editor.disable()\n        delete this.editor\n      }\n    },\n\n    // 🍂method toggleEdit()\n    // Enable or disable editing, according to current status.\n    toggleEdit: function () {\n      if (this.editEnabled()) this.disableEdit()\n      else this.enableEdit()\n    },\n\n    _onEditableAdd: function () {\n      if (this.editor) this.enableEdit()\n    },\n  }\n\n  const PolylineMixin = {\n    getEditorClass: (tools) => {\n      return tools?.options?.polylineEditorClass || L.Editable.PolylineEditor\n    },\n\n    shapeAt: function (latlng, latlngs) {\n      // We can have those cases:\n      // - latlngs are just a flat array of latlngs, use this\n      // - latlngs is an array of arrays of latlngs, loop over\n      let shape = null\n      latlngs = latlngs || this._latlngs\n      if (!latlngs.length) return shape\n      if (isFlat(latlngs) && this.isInLatLngs(latlng, latlngs)) shape = latlngs\n      else {\n        for (const member of latlngs) {\n          if (this.isInLatLngs(latlng, member)) return member\n        }\n      }\n      return shape\n    },\n\n    isInLatLngs: function (l, latlngs) {\n      if (!latlngs) return false\n      let i\n      let k\n      let len\n      let part = []\n      let p\n      const w = this._clickTolerance()\n      this._projectLatlngs(latlngs, part, this._pxBounds)\n      part = part[0]\n      p = this._map.latLngToLayerPoint(l)\n\n      if (!this._pxBounds.contains(p)) {\n        return false\n      }\n      for (i = 1, len = part.length, k = 0; i < len; k = i++) {\n        if (L.LineUtil.pointToSegmentDistance(p, part[k], part[i]) <= w) {\n          return true\n        }\n      }\n      return false\n    },\n  }\n\n  const PolygonMixin = {\n    getEditorClass: (tools) => {\n      return tools?.options?.polygonEditorClass || L.Editable.PolygonEditor\n    },\n\n    shapeAt: function (latlng, latlngs) {\n      // We can have those cases:\n      // - latlngs are just a flat array of latlngs, use this\n      // - latlngs is an array of arrays of latlngs, this is a simple polygon (maybe with holes), use the first\n      // - latlngs is an array of arrays of arrays, this is a multi, loop over\n      let shape = null\n      latlngs = latlngs || this._latlngs\n      if (!latlngs.length) return shape\n      if (isFlat(latlngs) && this.isInLatLngs(latlng, latlngs)) shape = latlngs\n      if (isFlat(latlngs[0]) && this.isInLatLngs(latlng, latlngs[0])) {\n        shape = latlngs\n      } else {\n        for (const member of latlngs) {\n          if (this.isInLatLngs(latlng, member[0])) return member\n        }\n      }\n      return shape\n    },\n\n    isInLatLngs: (l, latlngs) => {\n      let inside = false\n      let l1\n      let l2\n      let j\n      let k\n      let len2\n\n      for (j = 0, len2 = latlngs.length, k = len2 - 1; j < len2; k = j++) {\n        l1 = latlngs[j]\n        l2 = latlngs[k]\n\n        if (\n          l1.lat > l.lat !== l2.lat > l.lat &&\n          l.lng < ((l2.lng - l1.lng) * (l.lat - l1.lat)) / (l2.lat - l1.lat) + l1.lng\n        ) {\n          inside = !inside\n        }\n      }\n\n      return inside\n    },\n\n    parentShape: function (shape, latlngs) {\n      latlngs = latlngs || this._latlngs\n      if (!latlngs) return\n      let idx = L.Util.indexOf(latlngs, shape)\n      if (idx !== -1) return latlngs\n      for (const member of latlngs) {\n        idx = L.Util.indexOf(member, shape)\n        if (idx !== -1) return member\n      }\n    },\n  }\n\n  const MarkerMixin = {\n    getEditorClass: (tools) => {\n      return tools?.options?.markerEditorClass || L.Editable.MarkerEditor\n    },\n  }\n\n  const CircleMarkerMixin = {\n    getEditorClass: (tools) => {\n      return tools?.options?.circleMarkerEditorClass || L.Editable.CircleMarkerEditor\n    },\n  }\n\n  const RectangleMixin = {\n    getEditorClass: (tools) => {\n      return tools?.options?.rectangleEditorClass || L.Editable.RectangleEditor\n    },\n  }\n\n  const CircleMixin = {\n    getEditorClass: (tools) => {\n      return tools?.options?.circleEditorClass || L.Editable.CircleEditor\n    },\n  }\n\n  const keepEditable = function () {\n    // Make sure you can remove/readd an editable layer.\n    this.on('add', this._onEditableAdd)\n  }\n\n  const isFlat = L.LineUtil.isFlat || L.LineUtil._flat || L.Polyline._flat // <=> 1.1 compat.\n\n  if (L.Polyline) {\n    L.Polyline.include(EditableMixin)\n    L.Polyline.include(PolylineMixin)\n    L.Polyline.addInitHook(keepEditable)\n  }\n  if (L.Polygon) {\n    L.Polygon.include(EditableMixin)\n    L.Polygon.include(PolygonMixin)\n  }\n  if (L.Marker) {\n    L.Marker.include(EditableMixin)\n    L.Marker.include(MarkerMixin)\n    L.Marker.addInitHook(keepEditable)\n  }\n  if (L.CircleMarker) {\n    L.CircleMarker.include(EditableMixin)\n    L.CircleMarker.include(CircleMarkerMixin)\n    L.CircleMarker.addInitHook(keepEditable)\n  }\n  if (L.Rectangle) {\n    L.Rectangle.include(EditableMixin)\n    L.Rectangle.include(RectangleMixin)\n  }\n  if (L.Circle) {\n    L.Circle.include(EditableMixin)\n    L.Circle.include(CircleMixin)\n  }\n\n  L.LatLng.prototype.update = function (latlng) {\n    latlng = L.latLng(latlng)\n    this.lat = latlng.lat\n    this.lng = latlng.lng\n  }\n}, window)\n"], "mappings": ";;;;;;;;AAAA;AAAA;AAAC,KAAC,CAAC,SAASA,YAAW;AAIrB,UAAI,OAAO,WAAW,cAAc,OAAO,KAAK;AAC9C,eAAO,CAAC,SAAS,GAAG,OAAO;AAAA,MAG7B,WAAW,OAAO,YAAY,UAAU;AACtC,eAAO,UAAU,QAAQ,qBAAkB;AAAA,MAC7C;AAGA,UAAI,OAAOA,YAAW,eAAeA,QAAO,GAAG;AAC7C,gBAAQA,QAAO,CAAC;AAAA,MAClB;AAAA,IACF,GAAG,CAAC,MAAM;AA8BR,QAAE,WAAW,EAAE,QAAQ,OAAO;AAAA,QAC5B,SAAS;AAAA,UACP,SAAS;AAAA,UACT,UAAU;AAAA,QACZ;AAAA,QAEA,SAAS;AAAA;AAAA;AAAA;AAAA,UAIP,QAAQ;AAAA;AAAA;AAAA,UAIR,cAAc,EAAE;AAAA;AAAA;AAAA,UAIhB,eAAe,EAAE;AAAA;AAAA;AAAA,UAIjB,aAAa,EAAE;AAAA;AAAA;AAAA,UAIf,mBAAmB,EAAE;AAAA;AAAA;AAAA,UAIrB,gBAAgB,EAAE;AAAA;AAAA;AAAA,UAIlB,aAAa,EAAE;AAAA;AAAA;AAAA,UAIf,iBAAiB;AAAA;AAAA;AAAA,UAIjB,eAAe;AAAA;AAAA;AAAA,UAIf,WAAW;AAAA;AAAA;AAAA,UAIX,eAAe;AAAA;AAAA;AAAA,UAIf,qBAAqB;AAAA;AAAA;AAAA,UAIrB,oBAAoB;AAAA;AAAA;AAAA,UAIpB,mBAAmB;AAAA;AAAA;AAAA,UAInB,yBAAyB;AAAA;AAAA;AAAA,UAIzB,sBAAsB;AAAA;AAAA;AAAA,UAItB,mBAAmB;AAAA;AAAA;AAAA,UAInB,kBAAkB,CAAC;AAAA;AAAA;AAAA,UAInB,mBAAmB;AAAA,QACrB;AAAA,QAEA,YAAY,SAAU,KAAK,SAAS;AAClC,YAAE,WAAW,MAAM,OAAO;AAC1B,eAAK,cAAc,KAAK,QAAQ;AAChC,eAAK,MAAM;AACX,eAAK,YAAY,KAAK,gBAAgB;AACtC,eAAK,gBAAgB,KAAK,oBAAoB;AAC9C,eAAK,mBAAmB,KAAK,gBAAgB;AAC7C,eAAK,oBAAoB,KAAK,gBAAgB;AAAA,QAChD;AAAA,QAEA,gBAAgB,SAAU,MAAM,GAAG;AACjC,cAAI,KAAK,CAAC;AACV,YAAE,YAAY;AACd,eAAK,KAAK,MAAM,CAAC;AACjB,eAAK,IAAI,KAAK,MAAM,CAAC;AAAA,QACvB;AAAA,QAEA,iBAAiB,WAAY;AAC3B,gBAAM,UAAU,EAAE;AAAA,YAChB,EAAE,WAAW,QAAQ,QAAQ,GAAG,aAAa,MAAM;AAAA,YACnD,KAAK,QAAQ;AAAA,UACf;AACA,iBAAO,EAAE,SAAS,CAAC,GAAG,OAAO;AAAA,QAC/B;AAAA,QAEA,kBAAkB,CAAC,YACjB,EAAE,QAAQ,UAAU,EAAE,QAAQ,QAC1B,IAAI,EAAE,SAAS,gBAAgB,OAAO,IACtC,IAAI,EAAE,SAAS,WAAW,OAAO;AAAA,QAEvC,iBAAiB,WAAY;AAC3B,iBAAO,KAAK,QAAQ,aAAa,IAAI,EAAE,WAAW,EAAE,MAAM,KAAK,GAAG;AAAA,QACpE;AAAA,QAEA,qBAAqB,WAAY;AAC/B,iBAAO,KAAK,QAAQ,iBAAiB,IAAI,EAAE,WAAW,EAAE,MAAM,KAAK,GAAG;AAAA,QACxE;AAAA,QAEA,sBAAsB,SAAU,QAAQ;AACtC,cAAI,KAAK,iBAAiB,SAAS,QAAQ;AACzC,iBAAK,iBAAiB,SAAS,CAAC,IAAI;AACpC,iBAAK,iBAAiB,QAAQ,OAAO,MAAM;AAC3C,iBAAK,iBAAiB,OAAO;AAAA,UAC/B;AAAA,QACF;AAAA,QAEA,uBAAuB,SAAU,QAAQ;AACvC,cAAI,KAAK,kBAAkB,SAAS,QAAQ;AAC1C,iBAAK,kBAAkB,SAAS,CAAC,IAAI;AACrC,iBAAK,kBAAkB,QAAQ,OAAO,MAAM;AAC5C,iBAAK,kBAAkB,OAAO;AAAA,UAChC;AAAA,QACF;AAAA,QAEA,wBAAwB,SAAU,QAAQ;AACxC,eAAK,iBAAiB,SAAS,CAAC,IAAI;AACpC,eAAK,iBAAiB,QAAQ,OAAO,MAAM;AAC3C,eAAK,iBAAiB,OAAO;AAAA,QAC/B;AAAA,QAEA,yBAAyB,SAAU,QAAQ;AACzC,eAAK,kBAAkB,SAAS,CAAC,IAAI;AACrC,eAAK,kBAAkB,QAAQ,OAAO,MAAM;AAC5C,eAAK,kBAAkB,OAAO;AAAA,QAChC;AAAA,QAEA,wBAAwB,WAAY;AAClC,eAAK,UAAU,SAAS,KAAK,gBAAgB;AAAA,QAC/C;AAAA,QAEA,yBAAyB,WAAY;AACnC,eAAK,UAAU,SAAS,KAAK,iBAAiB;AAAA,QAChD;AAAA,QAEA,wBAAwB,WAAY;AAClC,eAAK,iBAAiB,WAAW,CAAC,CAAC;AACnC,eAAK,UAAU,YAAY,KAAK,gBAAgB;AAAA,QAClD;AAAA,QAEA,yBAAyB,WAAY;AACnC,eAAK,kBAAkB,WAAW,CAAC,CAAC;AACpC,eAAK,UAAU,YAAY,KAAK,iBAAiB;AAAA,QACnD;AAAA,QAEA,aAAa,WAAY;AAEvB,cAAI,CAAC,KAAK,aAAa;AACrB,iBAAK,cAAc,KAAK,IAAI;AAC5B,iBAAK,IAAI,WAAW,CAAC;AAAA,UACvB;AAAA,QACF;AAAA,QAEA,eAAe,WAAY;AACzB,cAAI,KAAK,aAAa;AAEpB,iBAAK,IAAI,WAAW,EAAE,OAAO,KAAK,IAAI,UAAU,KAAK,WAAW;AAChE,mBAAO,KAAK;AAAA,UACd;AAAA,QACF;AAAA,QAEA,oBAAoB,SAAU,QAAQ;AACpC,cAAI,KAAK;AAAgB,iBAAK,qBAAqB,KAAK,cAAc;AACtE,eAAK,YAAY;AACjB,iBAAO,MAAM;AACb,eAAK,iBAAiB;AACtB,eAAK,IAAI,GAAG,uBAAuB,OAAO,oBAAoB,MAAM;AACpE,eAAK,IAAI,GAAG,aAAa,KAAK,aAAa,IAAI;AAC/C,eAAK,IAAI,GAAG,WAAW,KAAK,WAAW,IAAI;AAC3C,YAAE,QAAQ,SAAS,KAAK,IAAI,YAAY,KAAK,QAAQ,eAAe;AACpE,eAAK,mBAAmB,KAAK,IAAI,WAAW,MAAM;AAClD,eAAK,IAAI,WAAW,MAAM,SAAS,KAAK,QAAQ;AAAA,QAClD;AAAA,QAEA,sBAAsB,SAAU,QAAQ;AACtC,eAAK,cAAc;AACnB,YAAE,QAAQ,YAAY,KAAK,IAAI,YAAY,KAAK,QAAQ,eAAe;AACvE,eAAK,IAAI,WAAW,MAAM,SAAS,KAAK;AACxC,mBAAS,UAAU,KAAK;AACxB,cAAI,CAAC;AAAQ;AACb,eAAK,IAAI,IAAI,uBAAuB,OAAO,oBAAoB,MAAM;AACrE,eAAK,IAAI,IAAI,aAAa,KAAK,aAAa,IAAI;AAChD,eAAK,IAAI,IAAI,WAAW,KAAK,WAAW,IAAI;AAC5C,cAAI,WAAW,KAAK;AAAgB;AACpC,iBAAO,KAAK;AACZ,cAAI,OAAO;AAAU,mBAAO,cAAc;AAAA,QAC5C;AAAA,QAEA,aAAa,SAAU,GAAG;AACxB,cAAI,EAAE,cAAc,SAAS;AAAG;AAChC,eAAK,aAAa;AAClB,eAAK,eAAe,mBAAmB,CAAC;AAAA,QAC1C;AAAA,QAEA,WAAW,SAAU,GAAG;AACtB,cAAI,KAAK,YAAY;AACnB,kBAAM,SAAS,KAAK;AACpB,kBAAM,YAAY,KAAK;AACvB,iBAAK,aAAa;AAClB,mBAAO,iBAAiB,CAAC;AACzB,gBAAI,KAAK,mBAAmB;AAAQ;AACpC,kBAAM,SAAS,EAAE;AAAA,cACf,UAAU,cAAc;AAAA,cACxB,UAAU,cAAc;AAAA,YAC1B;AACA,kBAAM,WAAW,EAAE;AAAA,cACjB,EAAE,cAAc;AAAA,cAChB,EAAE,cAAc;AAAA,YAClB,EAAE,WAAW,MAAM;AACnB,gBAAI,KAAK,IAAI,QAAQ,IAAI,KAAK,OAAO,oBAAoB;AACvD,mBAAK,eAAe,eAAe,CAAC;AAAA,UACxC;AAAA,QACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAUA,SAAS,WAAY;AApSzB;AAqSM,kBAAO,UAAK,mBAAL,mBAAqB;AAAA,QAC9B;AAAA;AAAA;AAAA,QAIA,aAAa,WAAY;AACvB,eAAK,qBAAqB;AAAA,QAC5B;AAAA;AAAA;AAAA,QAIA,eAAe,SAAU,GAAG;AAC1B,cAAI,CAAC,KAAK;AAAgB;AAC1B,eAAK,eAAe,cAAc,CAAC;AAAA,QACrC;AAAA,QAEA,qBAAqB,SAAU,OAAO;AACpC,iBAAO,KAAK,cAAc,SAAS,KAAK;AAAA,QAC1C;AAAA;AAAA;AAAA;AAAA,QAKA,eAAe,SAAU,QAAQ,SAAS;AACxC,gBAAM,OAAO,KAAK,eAAe,CAAC,GAAG,OAAO;AAC5C,eAAK,WAAW,KAAK,GAAG,EAAE,SAAS,MAAM;AACzC,iBAAO;AAAA,QACT;AAAA;AAAA;AAAA;AAAA,QAKA,cAAc,SAAU,QAAQ,SAAS;AACvC,gBAAM,UAAU,KAAK,cAAc,CAAC,GAAG,OAAO;AAC9C,kBAAQ,WAAW,KAAK,GAAG,EAAE,SAAS,MAAM;AAC5C,iBAAO;AAAA,QACT;AAAA;AAAA;AAAA;AAAA;AAAA,QAMA,aAAa,SAAU,QAAQ,SAAS;AACtC,mBAAS,UAAU,KAAK,IAAI,UAAU,EAAE,MAAM;AAC9C,gBAAM,SAAS,KAAK,aAAa,QAAQ,OAAO;AAChD,iBAAO,WAAW,KAAK,GAAG,EAAE,aAAa;AACzC,iBAAO;AAAA,QACT;AAAA;AAAA;AAAA;AAAA;AAAA,QAMA,mBAAmB,SAAU,QAAQ,SAAS;AAC5C,mBAAS,UAAU,KAAK,IAAI,UAAU,EAAE,MAAM;AAC9C,gBAAM,SAAS,KAAK,mBAAmB,QAAQ,OAAO;AACtD,iBAAO,WAAW,KAAK,GAAG,EAAE,aAAa;AACzC,iBAAO;AAAA,QACT;AAAA;AAAA;AAAA;AAAA,QAKA,gBAAgB,SAAU,QAAQ,SAAS;AACzC,gBAAM,SAAS,UAAU,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;AACxC,gBAAM,SAAS,IAAI,EAAE,aAAa,QAAQ,MAAM;AAChD,gBAAM,YAAY,KAAK,gBAAgB,QAAQ,OAAO;AACtD,oBAAU,WAAW,KAAK,GAAG,EAAE,aAAa;AAC5C,iBAAO;AAAA,QACT;AAAA;AAAA;AAAA;AAAA,QAKA,aAAa,SAAU,QAAQ,SAAS;AACtC,mBAAS,UAAU,KAAK,IAAI,UAAU,EAAE,MAAM;AAC9C,gBAAM,SAAS,KAAK,aAAa,QAAQ,OAAO;AAChD,iBAAO,WAAW,KAAK,GAAG,EAAE,aAAa;AACzC,iBAAO;AAAA,QACT;AAAA,QAEA,WAAW,CAAC,QAAQ,WAAW;AAC7B,iBAAO,QAAQ,MAAM;AAAA,QACvB;AAAA,QAEA,aAAa,SAAU,OAAO,SAAS,SAAS;AAC9C,oBAAU,EAAE,KAAK,OAAO,EAAE,aAAa,EAAE,WAAW,KAAK,EAAE,GAAG,OAAO;AACrE,gBAAM,QAAQ,IAAI,MAAM,SAAS,OAAO;AAIxC,eAAK,eAAe,oBAAoB,EAAE,MAAa,CAAC;AACxD,iBAAO;AAAA,QACT;AAAA,QAEA,gBAAgB,SAAU,SAAS,SAAS;AAC1C,iBAAO,KAAK;AAAA,aACV,mCAAS,kBAAiB,KAAK,QAAQ;AAAA,YACvC;AAAA,YACA;AAAA,UACF;AAAA,QACF;AAAA,QAEA,eAAe,SAAU,SAAS,SAAS;AACzC,iBAAO,KAAK;AAAA,aACV,mCAAS,iBAAgB,KAAK,QAAQ;AAAA,YACtC;AAAA,YACA;AAAA,UACF;AAAA,QACF;AAAA,QAEA,cAAc,SAAU,QAAQ,SAAS;AACvC,iBAAO,KAAK;AAAA,aACV,mCAAS,gBAAe,KAAK,QAAQ;AAAA,YACrC;AAAA,YACA;AAAA,UACF;AAAA,QACF;AAAA,QAEA,oBAAoB,SAAU,QAAQ,SAAS;AAC7C,iBAAO,KAAK;AAAA,aACV,mCAAS,sBAAqB,KAAK,QAAQ;AAAA,YAC3C;AAAA,YACA;AAAA,UACF;AAAA,QACF;AAAA,QAEA,iBAAiB,SAAU,QAAQ,SAAS;AAC1C,iBAAO,KAAK;AAAA,aACV,mCAAS,mBAAkB,KAAK,QAAQ;AAAA,YACxC;AAAA,YACA;AAAA,UACF;AAAA,QACF;AAAA,QAEA,cAAc,SAAU,QAAQ,SAAS;AACvC,iBAAO,KAAK;AAAA,aACV,mCAAS,gBAAe,KAAK,QAAQ;AAAA,YACrC;AAAA,YACA;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAC;AAED,QAAE,OAAO,EAAE,UAAU;AAAA,QACnB,iBAAiB,CAAC,MAAM;AACtB,YAAE,SAAS,MAAM;AACf,cAAE,aAAa;AAAA,UACjB;AAAA,QACF;AAAA,MACF,CAAC;AAgBD,QAAE,IAAI,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA,QAKjB,gBAAgB,EAAE;AAAA;AAAA;AAAA,QAIlB,UAAU;AAAA;AAAA;AAAA,QAIV,aAAa,CAAC;AAAA,MAChB,CAAC;AAED,QAAE,IAAI,YAAY,WAAY;AAC5B,aAAK,UAAU,WAAY;AACzB,cAAI,KAAK,QAAQ,UAAU;AACzB,iBAAK,YAAY,IAAI,KAAK,QAAQ,eAAe,MAAM,KAAK,QAAQ,WAAW;AAAA,UACjF;AAAA,QACF,CAAC;AAAA,MACH,CAAC;AAED,QAAE,SAAS,aAAa,EAAE,QAAQ,OAAO;AAAA,QACvC,SAAS;AAAA,UACP,UAAU,IAAI,EAAE,MAAM,GAAG,CAAC;AAAA,QAC5B;AAAA,MACF,CAAC;AAED,QAAE,SAAS,kBAAkB,EAAE,SAAS,WAAW,OAAO;AAAA,QACxD,SAAS;AAAA,UACP,UAAU,IAAI,EAAE,MAAM,IAAI,EAAE;AAAA,QAC9B;AAAA,MACF,CAAC;AAGD,QAAE,SAAS,eAAe,EAAE,OAAO,OAAO;AAAA,QACxC,SAAS;AAAA,UACP,WAAW;AAAA,UACX,WAAW;AAAA,QACb;AAAA;AAAA;AAAA;AAAA,QAMA,YAAY,SAAU,QAAQ,SAAS,QAAQ,SAAS;AAGtD,eAAK,SAAS;AACd,eAAK,UAAU;AACf,eAAK,SAAS;AACd,YAAE,OAAO,UAAU,WAAW,KAAK,MAAM,QAAQ,OAAO;AACxD,eAAK,QAAQ,OAAO,KAAK,OAAO,MAAM,iBAAiB;AAAA,YACrD,WAAW,KAAK,QAAQ;AAAA,UAC1B,CAAC;AACD,eAAK,OAAO,WAAW;AACvB,eAAK,QAAQ;AACb,eAAK,gBAAgB,OAAO,MAAM,cAAc,CAAC;AAAA,QACnD;AAAA,QAEA,SAAS,WAAY;AACnB,eAAK,OAAO,UAAU,SAAS,IAAI;AAAA,QACrC;AAAA,QAEA,OAAO,SAAU,KAAK;AACpB,YAAE,OAAO,UAAU,MAAM,KAAK,MAAM,GAAG;AACvC,eAAK,GAAG,QAAQ,KAAK,MAAM;AAC3B,eAAK,GAAG,aAAa,KAAK,WAAW;AACrC,eAAK,GAAG,WAAW,KAAK,SAAS;AACjC,eAAK,GAAG,WAAW,KAAK,SAAS;AACjC,eAAK,GAAG,SAAS,KAAK,OAAO;AAC7B,eAAK,GAAG,eAAe,KAAK,aAAa;AACzC,eAAK,GAAG,wBAAwB,KAAK,WAAW;AAChD,eAAK,GAAG,aAAa,KAAK,WAAW;AACrC,eAAK,GAAG,YAAY,KAAK,UAAU;AACnC,eAAK,iBAAiB;AAAA,QACxB;AAAA,QAEA,UAAU,SAAU,KAAK;AACvB,cAAI,KAAK;AAAc,iBAAK,aAAa,OAAO;AAChD,iBAAO,KAAK,OAAO;AACnB,eAAK,IAAI,QAAQ,KAAK,MAAM;AAC5B,eAAK,IAAI,aAAa,KAAK,WAAW;AACtC,eAAK,IAAI,WAAW,KAAK,SAAS;AAClC,eAAK,IAAI,WAAW,KAAK,SAAS;AAClC,eAAK,IAAI,SAAS,KAAK,OAAO;AAC9B,eAAK,IAAI,eAAe,KAAK,aAAa;AAC1C,eAAK,IAAI,wBAAwB,KAAK,WAAW;AACjD,eAAK,IAAI,aAAa,KAAK,WAAW;AACtC,eAAK,IAAI,YAAY,KAAK,UAAU;AACpC,YAAE,OAAO,UAAU,SAAS,KAAK,MAAM,GAAG;AAAA,QAC5C;AAAA,QAEA,QAAQ,SAAU,GAAG;AACnB,YAAE,SAAS;AACX,eAAK,OAAO,mBAAmB,CAAC;AAChC,gBAAM,UAAU,EAAE,QAAQ,YAAY,KAAK,KAAK;AAChD,gBAAM,SAAS,KAAK,KAAK,mBAAmB,OAAO;AACnD,eAAK,OAAO,OAAO,MAAM;AACzB,eAAK,UAAU,KAAK;AACpB,eAAK,OAAO,QAAQ;AACpB,cAAI,KAAK;AAAc,iBAAK,aAAa,aAAa;AACtD,gBAAM,OAAO,KAAK,QAAQ;AAC1B,cAAI,6BAAM;AAAc,iBAAK,aAAa,aAAa;AAAA,QACzD;AAAA,QAEA,aAAa,SAAU,GAAG;AACxB,YAAE,SAAS;AACX,eAAK,OAAO,wBAAwB,CAAC;AAAA,QACvC;AAAA,QAEA,WAAW,SAAU,GAAG;AACtB,YAAE,SAAS;AACX,eAAK,OAAO,sBAAsB,CAAC;AAAA,QACrC;AAAA,QAEA,SAAS,SAAU,GAAG;AACpB,YAAE,SAAS;AACX,eAAK,OAAO,oBAAoB,CAAC;AAAA,QACnC;AAAA,QAEA,WAAW,SAAU,GAAG;AACtB,YAAE,SAAS,KAAK,CAAC;AACjB,YAAE,SAAS;AACX,eAAK,OAAO,IAAI,KAAK,WAAW,CAAC;AAAA,QACnC;AAAA,QAEA,eAAe,SAAU,GAAG;AAC1B,YAAE,SAAS;AACX,eAAK,OAAO,0BAA0B,CAAC;AAAA,QACzC;AAAA,QAEA,aAAa,SAAU,GAAG;AACxB,YAAE,SAAS;AACX,eAAK,OAAO,wBAAwB,CAAC;AAAA,QACvC;AAAA,QAEA,aAAa,SAAU,GAAG;AACxB,YAAE,SAAS;AACX,eAAK,OAAO,wBAAwB,CAAC;AAAA,QACvC;AAAA,QAEA,YAAY,SAAU,GAAG;AACvB,YAAE,SAAS;AACX,eAAK,OAAO,uBAAuB,CAAC;AAAA,QACtC;AAAA;AAAA;AAAA,QAIA,QAAQ,WAAY;AAClB,gBAAM,OAAO,KAAK,QAAQ;AAC1B,eAAK,QAAQ,OAAO,KAAK,SAAS,GAAG,CAAC;AACtC,eAAK,OAAO,UAAU,YAAY,IAAI;AACtC,eAAK,OAAO,gBAAgB,EAAE,QAAQ,KAAK,QAAQ,QAAQ,KAAK,CAAC;AACjE,cAAI,CAAC,KAAK,QAAQ;AAAQ,iBAAK,OAAO,YAAY,KAAK,OAAO;AAC9D,cAAI;AAAM,iBAAK,kBAAkB;AACjC,eAAK,OAAO,QAAQ;AAAA,QACtB;AAAA;AAAA;AAAA,QAIA,UAAU,WAAY;AACpB,iBAAO,KAAK,QAAQ,QAAQ,KAAK,MAAM;AAAA,QACzC;AAAA;AAAA;AAAA,QAIA,cAAc,WAAY;AACxB,iBAAO,KAAK,QAAQ,SAAS;AAAA,QAC/B;AAAA;AAAA;AAAA,QAIA,aAAa,WAAY;AACvB,cAAI,KAAK,QAAQ,SAAS;AAAG;AAC7B,gBAAM,QAAQ,KAAK,SAAS;AAC5B,cAAI,gBAAgB,QAAQ;AAC5B,cAAI,UAAU,KAAK,KAAK,OAAO;AAAQ,4BAAgB,KAAK,aAAa;AACzE,gBAAM,WAAW,KAAK,QAAQ,aAAa;AAC3C,cAAI;AAAU,mBAAO,SAAS;AAAA,QAChC;AAAA;AAAA;AAAA,QAIA,SAAS,WAAY;AACnB,cAAI,KAAK,QAAQ,SAAS;AAAG;AAC7B,gBAAM,QAAQ,KAAK,SAAS;AAC5B,cAAI,YAAY,QAAQ;AACxB,cAAI,UAAU,KAAK,aAAa,KAAK,KAAK,OAAO;AAAQ,wBAAY;AACrE,gBAAM,OAAO,KAAK,QAAQ,SAAS;AACnC,cAAI;AAAM,mBAAO,KAAK;AAAA,QACxB;AAAA,QAEA,iBAAiB,SAAU,UAAU;AACnC,cAAI,CAAC,KAAK,OAAO,iBAAiB;AAAG;AACrC,qBAAW,YAAY,KAAK,YAAY;AACxC,cAAI,YAAY,CAAC,KAAK;AACpB,iBAAK,eAAe,KAAK,OAAO;AAAA,cAC9B;AAAA,cACA;AAAA,cACA,KAAK;AAAA,cACL,KAAK;AAAA,YACP;AAAA,QACJ;AAAA,QAEA,kBAAkB,WAAY;AAC5B,cAAI,CAAC,KAAK,OAAO,iBAAiB;AAAG;AACrC,gBAAM,WAAW,KAAK,YAAY;AAClC,cAAI;AAAU,iBAAK,gBAAgB,QAAQ;AAC3C,gBAAM,OAAO,KAAK,QAAQ;AAC1B,cAAI;AAAM,iBAAK,kBAAkB;AAAA,QACnC;AAAA,QAEA,mBAAmB,WAAY;AAC7B,cAAI,KAAK;AAAc,iBAAK,aAAa,OAAO;AAChD,eAAK,gBAAgB;AAAA,QACvB;AAAA;AAAA;AAAA,QAIA,OAAO,WAAY;AACjB,cAAI,CAAC,KAAK,OAAO;AAAY;AAC7B,eAAK,OAAO,WAAW,KAAK,SAAS,KAAK,SAAS,CAAC;AAAA,QACtD;AAAA;AAAA;AAAA,QAIA,UAAU,WAAY;AACpB,cAAI,CAAC,KAAK,OAAO;AAAkB;AACnC,gBAAM,QAAQ,KAAK,SAAS;AAC5B,cAAI,UAAU;AAAG,iBAAK,OAAO,iBAAiB,KAAK,OAAO;AAAA,mBACjD,UAAU,KAAK,aAAa;AAAG,iBAAK,OAAO,gBAAgB,KAAK,OAAO;AAAA,QAClF;AAAA,MACF,CAAC;AAED,QAAE,SAAS,aAAa;AAAA;AAAA;AAAA;AAAA,QAItB,mBAAmB,EAAE,SAAS;AAAA,MAChC,CAAC;AAED,QAAE,SAAS,eAAe,EAAE,OAAO,OAAO;AAAA,QACxC,SAAS;AAAA,UACP,SAAS;AAAA,UACT,WAAW;AAAA,UACX,WAAW;AAAA,QACb;AAAA,QAEA,YAAY,SAAU,MAAM,OAAO,SAAS,QAAQ,SAAS;AAC3D,eAAK,OAAO;AACZ,eAAK,QAAQ;AACb,eAAK,SAAS;AACd,eAAK,UAAU;AACf,YAAE,OAAO,UAAU,WAAW,KAAK,MAAM,KAAK,cAAc,GAAG,OAAO;AACtE,eAAK,WAAW,KAAK,QAAQ;AAC7B,eAAK,QAAQ,OAAO,KAAK,OAAO,MAAM,iBAAiB;AAAA,YACrD,WAAW,KAAK,QAAQ;AAAA,UAC1B,CAAC;AACD,eAAK,OAAO,UAAU,SAAS,IAAI;AACnC,eAAK,cAAc;AAAA,QACrB;AAAA,QAEA,eAAe,WAAY;AACzB,gBAAM,YAAY,KAAK,KAAK,uBAAuB,KAAK,KAAK,MAAM;AACnE,gBAAM,aAAa,KAAK,KAAK,uBAAuB,KAAK,MAAM,MAAM;AACrE,gBAAM,OAAO,EAAE,MAAM,KAAK,QAAQ,KAAK,QAAQ,QAAQ;AACvD,cAAI,UAAU,WAAW,UAAU,IAAI,KAAK,IAAI;AAAG,iBAAK,KAAK;AAAA;AACxD,iBAAK,KAAK;AAAA,QACjB;AAAA,QAEA,MAAM,WAAY;AAChB,eAAK,WAAW,KAAK,QAAQ;AAAA,QAC/B;AAAA,QAEA,MAAM,WAAY;AAChB,eAAK,WAAW,CAAC;AAAA,QACnB;AAAA,QAEA,cAAc,WAAY;AACxB,eAAK,UAAU,KAAK,cAAc,CAAC;AACnC,eAAK,cAAc;AAAA,QACrB;AAAA,QAEA,eAAe,WAAY;AACzB,gBAAM,YAAY,KAAK,OAAO,IAAI,uBAAuB,KAAK,KAAK,MAAM;AACzE,gBAAM,aAAa,KAAK,OAAO,IAAI,uBAAuB,KAAK,MAAM,MAAM;AAC3E,gBAAM,KAAK,UAAU,IAAI,WAAW,KAAK;AACzC,gBAAM,KAAK,UAAU,IAAI,WAAW,KAAK;AACzC,iBAAO,KAAK,OAAO,IAAI,uBAAuB,CAAC,GAAG,CAAC,CAAC;AAAA,QACtD;AAAA,QAEA,OAAO,SAAU,KAAK;AACpB,YAAE,OAAO,UAAU,MAAM,KAAK,MAAM,GAAG;AACvC,YAAE,SAAS,GAAG,KAAK,OAAO,wBAAwB,KAAK,aAAa,IAAI;AACxE,cAAI,GAAG,WAAW,KAAK,eAAe,IAAI;AAAA,QAC5C;AAAA,QAEA,UAAU,SAAU,KAAK;AACvB,iBAAO,KAAK,MAAM;AAClB,YAAE,SAAS,IAAI,KAAK,OAAO,wBAAwB,KAAK,aAAa,IAAI;AACzE,cAAI,IAAI,WAAW,KAAK,eAAe,IAAI;AAC3C,YAAE,OAAO,UAAU,SAAS,KAAK,MAAM,GAAG;AAAA,QAC5C;AAAA,QAEA,aAAa,SAAU,GAAG;AACxB,gBAAM,UAAU,EAAE,QAAQ,YAAY,KAAK,KAAK;AAChD,gBAAM,SAAS,KAAK,OAAO,IAAI,mBAAmB,OAAO;AACzD,cAAI;AAAA,YACF,eAAe;AAAA,YACf;AAAA,UACF;AACA,cAAI,KAAK,QAAQ,YAAY;AAAG;AAChC,YAAE,SAAS,gBAAgB,CAAC;AAC5B,eAAK,OAAO,wBAAwB,CAAC;AACrC,cAAI,EAAE;AAAY;AAClB,eAAK,QAAQ,OAAO,KAAK,MAAM,GAAG,GAAG,EAAE,MAAM;AAC7C,eAAK,OAAO,QAAQ;AACpB,gBAAM,OAAO,KAAK;AAClB,gBAAM,SAAS,KAAK,OAAO,gBAAgB,EAAE,QAAQ,KAAK,OAAO;AACjE,eAAK,OAAO,YAAY,MAAM;AAE9B,gBAAM,SAAS,OAAO,MAAM;AAC5B,iBAAO,YAAY,OAAO,KAAK;AAC/B,iBAAO,QAAQ;AACf,iBAAO,YAAY,OAAO,KAAK;AAC/B,iBAAO,UAAU;AACjB,iBAAO,iBAAiB;AACxB,iBAAO,WAAW,CAAC;AAGnB,YAAE,UAAU,YAAY;AACxB,iBAAO,SAAS,WAAW,QAAQ,EAAE,aAAa;AAClD,eAAK,OAAO;AAAA,QACd;AAAA,QAEA,QAAQ,WAAY;AAClB,eAAK,OAAO,UAAU,YAAY,IAAI;AAAA,QACxC;AAAA,QAEA,OAAO,WAAY;AACjB,iBAAO,KAAK,QAAQ,QAAQ,KAAK,MAAM,MAAM;AAAA,QAC/C;AAAA,MACF,CAAC;AAED,QAAE,SAAS,aAAa;AAAA;AAAA;AAAA;AAAA,QAItB,mBAAmB,EAAE,SAAS;AAAA,MAChC,CAAC;AAKD,QAAE,SAAS,aAAa,EAAE,QAAQ,OAAO;AAAA,QACvC,YAAY,SAAU,KAAK,SAAS,SAAS;AAC3C,YAAE,WAAW,MAAM,OAAO;AAC1B,eAAK,MAAM;AACX,eAAK,UAAU;AACf,eAAK,QAAQ,SAAS;AACtB,eAAK,YAAY,IAAI,EAAE,WAAW;AAClC,eAAK,QAAQ,KAAK,QAAQ,aAAa,IAAI;AAAA,QAC7C;AAAA;AAAA;AAAA,QAIA,UAAU,WAAY;AACpB,cAAI,KAAK,YAAY;AAAG,iBAAK,aAAa;AAAA;AACrC,iBAAK,QAAQ,KAAK,OAAO,KAAK,cAAc,IAAI;AACrD,eAAK,SAAS;AACd,eAAK,QAAQ,GAAG,KAAK,WAAW,GAAG,IAAI;AAAA,QACzC;AAAA;AAAA;AAAA,QAIA,aAAa,WAAY;AACvB,eAAK,QAAQ,IAAI,KAAK,WAAW,GAAG,IAAI;AACxC,cAAI,KAAK,QAAQ;AAAU,iBAAK,QAAQ,SAAS,QAAQ;AACzD,eAAK,UAAU,YAAY;AAC3B,eAAK,MAAM,UAAU,YAAY,KAAK,SAAS;AAC/C,eAAK,UAAU;AACf,cAAI,KAAK;AAAU,iBAAK,cAAc;AAAA,QACxC;AAAA;AAAA;AAAA,QAIA,SAAS,WAAY;AACnB,iBAAO,CAAC,CAAC,KAAK;AAAA,QAChB;AAAA,QAEA,OAAO,MAAM;AAAA,QAAC;AAAA,QAEd,cAAc,WAAY;AACxB,eAAK,MAAM,UAAU,SAAS,KAAK,SAAS;AAC5C,cAAI,KAAK,QAAQ;AAAU,iBAAK,QAAQ,SAAS,OAAO;AAAA,QAC1D;AAAA,QAEA,kBAAkB,WAAY;AAC5B,iBAAO,CAAC,KAAK,QAAQ,qBAAqB,CAAC,KAAK,MAAM,QAAQ;AAAA,QAChE;AAAA,QAEA,gBAAgB,SAAU,MAAM,GAAG;AACjC,cAAI,KAAK,CAAC;AACV,YAAE,QAAQ,KAAK;AACf,eAAK,QAAQ,KAAK,MAAM,CAAC;AACzB,eAAK,MAAM,eAAe,MAAM,CAAC;AAAA,QACnC;AAAA,QAEA,UAAU,WAAY;AAIpB,eAAK,eAAe,iBAAiB;AAAA,QACvC;AAAA,QAEA,WAAW,WAAY;AAIrB,eAAK,eAAe,kBAAkB;AAAA,QACxC;AAAA,QAEA,WAAW,WAAY;AAIrB,eAAK,eAAe,kBAAkB;AAAA,QACxC;AAAA,QAEA,UAAU,WAAY;AAIpB,eAAK,eAAe,iBAAiB;AAAA,QACvC;AAAA,QAEA,gBAAgB,WAAY;AAK1B,eAAK,eAAe,wBAAwB;AAAA,QAC9C;AAAA,QAEA,cAAc,WAAY;AAKxB,eAAK,eAAe,sBAAsB;AAAA,QAC5C;AAAA,QAEA,iBAAiB,WAAY;AAK3B,eAAK,eAAe,yBAAyB;AAAA,QAC/C;AAAA,QAEA,iBAAiB,SAAU,GAAG;AAK5B,eAAK,eAAe,2BAA2B,CAAC;AAChD,eAAK,SAAS;AAAA,QAChB;AAAA,QAEA,oBAAoB,SAAU,GAAG;AAK/B,eAAK,eAAe,8BAA8B,CAAC;AAAA,QACrD;AAAA,QAEA,kBAAkB,SAAU,GAAG;AAK7B,eAAK,eAAe,4BAA4B,CAAC;AAAA,QACnD;AAAA,QAEA,cAAc,WAAY;AACxB,cAAI,CAAC,KAAK;AAAU,iBAAK,WAAW,EAAE,SAAS;AAC/C,eAAK,MAAM,mBAAmB,IAAI;AAClC,eAAK,eAAe;AAAA,QACtB;AAAA,QAEA,eAAe,SAAU,GAAG;AAC1B,eAAK,gBAAgB,CAAC;AACtB,eAAK,WAAW;AAAA,QAClB;AAAA,QAEA,eAAe,WAAY;AAIzB,YAAE,UAAU,YAAY;AACxB,eAAK,gBAAgB;AACrB,eAAK,WAAW;AAAA,QAClB;AAAA,QAEA,YAAY,WAAY;AACtB,eAAK,WAAW;AAChB,eAAK,MAAM,qBAAqB,IAAI;AACpC,eAAK,aAAa;AAAA,QACpB;AAAA,QAEA,gBAAgB,SAAU,GAAG;AAC3B,cAAI,CAAC,KAAK,QAAQ;AAAG;AACrB,YAAE,SAAS,gBAAgB,CAAC;AAK5B,eAAK,eAAe,0BAA0B,CAAC;AAC/C,cAAI,EAAE;AAAY;AAClB,cAAI,CAAC,KAAK,YAAY;AAAG,iBAAK,QAAQ,CAAC;AACvC,eAAK,oBAAoB,CAAC;AAAA,QAC5B;AAAA,QAEA,aAAa,WAAY;AACvB,iBAAO,KAAK,IAAI,SAAS,KAAK,OAAO;AAAA,QACvC;AAAA,QAEA,SAAS,WAAY;AACnB,eAAK,MAAM,oBAAoB,KAAK,OAAO;AAC3C,eAAK,MAAM,UAAU,SAAS,KAAK,SAAS;AAAA,QAC9C;AAAA,QAEA,QAAQ,SAAU,GAAG;AAKnB,eAAK,eAAe,yBAAyB,CAAC;AAAA,QAChD;AAAA,QAEA,oBAAoB,SAAU,GAAG;AAC/B,eAAK,OAAO,CAAC;AAAA,QACf;AAAA,QAEA,YAAY,WAAY;AACtB,iBAAO;AAAA,YACL,WAAW,KAAK;AAAA,YAChB,MAAM,KAAK;AAAA,YACX,SAAS,KAAK;AAAA,YACd,QAAQ,KAAK;AAAA,UACf;AAAA,QACF;AAAA,QAEA,aAAa,SAAU,GAAG;AACxB,eAAK,UAAU;AAIf,eAAK,eAAe,sBAAsB,CAAC;AAAA,QAC7C;AAAA,QAEA,QAAQ,SAAU,GAAG;AACnB,eAAK,OAAO,CAAC;AAIb,eAAK,eAAe,iBAAiB,CAAC;AAAA,QACxC;AAAA,QAEA,WAAW,SAAU,GAAG;AAItB,eAAK,eAAe,oBAAoB,CAAC;AACzC,eAAK,SAAS;AAAA,QAChB;AAAA,MACF,CAAC;AAKD,QAAE,SAAS,eAAe,EAAE,SAAS,WAAW,OAAO;AAAA,QACrD,oBAAoB,SAAU,GAAG;AAC/B,YAAE,SAAS,WAAW,UAAU,mBAAmB,KAAK,MAAM,CAAC;AAC/D,cAAI,KAAK;AAAU,iBAAK,QAAQ,UAAU,EAAE,MAAM;AAAA,QACpD;AAAA,QAEA,qBAAqB,SAAU,GAAG;AAKhC,eAAK,eAAe,4BAA4B,CAAC;AACjD,eAAK,cAAc,CAAC;AAAA,QACtB;AAAA,QAEA,SAAS,SAAU,GAAG;AAGpB,cAAI;AAAG,iBAAK,QAAQ,UAAU,EAAE;AAChC,YAAE,SAAS,WAAW,UAAU,QAAQ,KAAK,MAAM,CAAC;AAAA,QACtD;AAAA,MACF,CAAC;AAKD,QAAE,SAAS,qBAAqB,EAAE,SAAS,WAAW,OAAO;AAAA,QAC3D,oBAAoB,SAAU,GAAG;AAC/B,YAAE,SAAS,WAAW,UAAU,mBAAmB,KAAK,MAAM,CAAC;AAC/D,cAAI,KAAK;AAAU,iBAAK,QAAQ,UAAU,EAAE,MAAM;AAAA,QACpD;AAAA,QAEA,qBAAqB,SAAU,GAAG;AAKhC,eAAK,eAAe,4BAA4B,CAAC;AACjD,eAAK,cAAc,CAAC;AAAA,QACtB;AAAA,QAEA,SAAS,SAAU,GAAG;AAGpB,cAAI;AAAG,iBAAK,QAAQ,UAAU,EAAE;AAChC,YAAE,SAAS,WAAW,UAAU,QAAQ,KAAK,MAAM,CAAC;AAAA,QACtD;AAAA,MACF,CAAC;AAKD,QAAE,SAAS,aAAa,EAAE,SAAS,WAAW,OAAO;AAAA,QACnD,QAAQ;AAAA,QACR,YAAY;AAAA,QAEZ,UAAU,WAAY;AACpB,YAAE,SAAS,WAAW,UAAU,SAAS,KAAK,IAAI;AAClD,cAAI,KAAK,SAAS;AAChB,iBAAK,kBAAkB;AACvB,iBAAK,IAAI,GAAG,WAAW,KAAK,WAAW,IAAI;AAAA,UAC7C;AACA,iBAAO;AAAA,QACT;AAAA,QAEA,aAAa,WAAY;AACvB,YAAE,SAAS,WAAW,UAAU,YAAY,KAAK,IAAI;AACrD,cAAI,KAAK,SAAS;AAChB,iBAAK,IAAI,IAAI,WAAW,KAAK,WAAW,IAAI;AAAA,UAC9C;AAAA,QACF;AAAA,QAEA,WAAW,WAAY;AACrB,eAAK,kBAAkB;AAAA,QACzB;AAAA,QAEA,mBAAmB,SAAU,SAAS;AACpC,cAAI,CAAC,KAAK,QAAQ;AAAG;AACrB,oBAAU,WAAW,KAAK,WAAW;AACrC,cAAI,OAAO,OAAO,GAAG;AACnB,iBAAK,iBAAiB,OAAO;AAAA,UAC/B,OAAO;AACL,uBAAW,UAAU,SAAS;AAC5B,mBAAK,kBAAkB,MAAM;AAAA,YAC/B;AAAA,UACF;AAAA,QACF;AAAA,QAEA,YAAY,WAAY;AACtB,iBAAO,KAAK,QAAQ,WAAW;AAAA,QACjC;AAAA;AAAA;AAAA,QAIA,OAAO,WAAY;AACjB,eAAK,UAAU,YAAY;AAC3B,eAAK,kBAAkB;AAAA,QACzB;AAAA,QAEA,iBAAiB,SAAU,QAAQ,SAAS;AAC1C,cAAI,OAAO,UAAU;AACnB,mBAAO,SAAS,QAAQ;AACxB,mBAAO,OAAO;AAAA,UAChB;AACA,iBAAO,IAAI,KAAK,MAAM,QAAQ,kBAAkB,QAAQ,SAAS,IAAI;AAAA,QACvE;AAAA,QAEA,aAAa,SAAU,QAAQ;AAK7B,eAAK,eAAe,uBAAuB;AAAA,YACzC,QAAQ,OAAO;AAAA,YACf;AAAA,UACF,CAAC;AAAA,QACH;AAAA,QAEA,kBAAkB,SAAU,SAAS;AACnC,gBAAM,SAAS,KAAK,IAAI,UAAU;AAClC,qBAAW,UAAU,SAAS;AAC5B,gBAAI,CAAC,OAAO,SAAS,MAAM;AAAG;AAC9B,iBAAK,gBAAgB,QAAQ,OAAO;AAAA,UACtC;AAAA,QACF;AAAA,QAEA,sBAAsB,SAAU,SAAS;AACvC,oBAAU,WAAW,KAAK,kBAAkB;AAC5C,qBAAW,UAAU,SAAS;AAC5B,mBAAO,SAAS,OAAO;AAAA,UACzB;AAAA,QACF;AAAA,QAEA,iBAAiB,SAAU,MAAM,OAAO,SAAS;AAC/C,iBAAO,IAAI,KAAK,MAAM,QAAQ,kBAAkB,MAAM,OAAO,SAAS,IAAI;AAAA,QAC5E;AAAA,QAEA,qBAAqB,SAAU,GAAG;AAChC,YAAE,SAAS,gBAAgB,CAAC;AAK5B,eAAK,eAAe,yBAAyB,CAAC;AAC9C,cAAI,EAAE;AAAY;AAClB,cAAI,KAAK,MAAM,QAAQ,KAAK,KAAK,MAAM,mBAAmB;AAAM;AAChE,gBAAM,QAAQ,EAAE,OAAO,SAAS;AAChC,cAAI;AACJ,cAAI,EAAE,cAAc,SAAS;AAC3B,iBAAK,wBAAwB,CAAC;AAAA,UAChC,WAAW,EAAE,cAAc,QAAQ;AACjC,iBAAK,uBAAuB,CAAC;AAAA,UAC/B,WAAW,EAAE,cAAc,UAAU;AACnC,iBAAK,yBAAyB,CAAC;AAAA,UACjC,WAAW,EAAE,cAAc,SAAS;AAClC,iBAAK,2BAA2B,CAAC;AAAA,UACnC,WACE,UAAU,EAAE,OAAO,aAAa,KAChC,KAAK,aAAa,EAAE,SAAS,SAC7B;AACA,gBAAI,SAAS,KAAK,aAAa;AAAG,uBAAS;AAAA,UAC7C,WACE,UAAU,KACV,KAAK,aAAa,EAAE,SAAS,YAC7B,KAAK,cAAc,UAAU,KAAK,YAClC;AACA,qBAAS;AAAA,UACX,WACE,UAAU,KACV,KAAK,aAAa,EAAE,SAAS,WAC7B,KAAK,cAAc,UAAU,KAAK,cAClC,KAAK,QACL;AACA,qBAAS;AAAA,UACX,OAAO;AACL,iBAAK,uBAAuB,CAAC;AAAA,UAC/B;AAKA,eAAK,eAAe,2BAA2B,CAAC;AAChD,cAAI;AAAQ,iBAAK,cAAc,CAAC;AAAA,QAClC;AAAA,QAEA,wBAAwB,SAAU,GAAG;AAKnC,eAAK,eAAe,4BAA4B,CAAC;AACjD,cAAI,EAAE;AAAY;AAClB,cAAI,CAAC,KAAK,mBAAmB,EAAE,MAAM;AAAG;AACxC,YAAE,OAAO,OAAO;AAAA,QAClB;AAAA,QAEA,oBAAoB,SAAU,QAAQ;AACpC,iBAAO,OAAO,QAAQ,SAAS,KAAK;AAAA,QACtC;AAAA,QAEA,iBAAiB,SAAU,GAAG;AAK5B,eAAK,eAAe,2BAA2B,CAAC;AAChD,eAAK,SAAS;AAAA,QAChB;AAAA,QAEA,yBAAyB,SAAU,GAAG;AAKpC,eAAK,eAAe,6BAA6B,CAAC;AAAA,QACpD;AAAA,QAEA,0BAA0B,SAAU,GAAG;AAKrC,eAAK,eAAe,8BAA8B,CAAC;AAAA,QACrD;AAAA,QAEA,4BAA4B,SAAU,GAAG;AAKvC,eAAK,eAAe,gCAAgC,CAAC;AAAA,QACvD;AAAA,QAEA,wBAAwB,SAAU,GAAG;AAKnC,eAAK,eAAe,4BAA4B,CAAC;AAAA,QACnD;AAAA,QAEA,2BAA2B,SAAU,GAAG;AAKtC,eAAK,eAAe,+BAA+B,CAAC;AAAA,QACtD;AAAA,QAEA,yBAAyB,SAAU,GAAG;AAKpC,eAAK,eAAe,6BAA6B,CAAC;AAAA,QACpD;AAAA,QAEA,yBAAyB,SAAU,GAAG;AAKpC,eAAK,eAAe,6BAA6B,CAAC;AAAA,QACpD;AAAA,QAEA,wBAAwB,SAAU,GAAG;AAKnC,eAAK,eAAe,4BAA4B,CAAC;AAAA,QACnD;AAAA,QAEA,yBAAyB,SAAU,GAAG;AAKpC,eAAK,eAAe,mCAAmC,CAAC;AAAA,QAC1D;AAAA,QAEA,oBAAoB,SAAU,GAAG;AAC/B,eAAK,OAAO,CAAC;AACb,cAAI,KAAK,QAAQ;AAAS,iBAAK,aAAa,CAAC;AAK7C,eAAK,eAAe,wBAAwB,CAAC;AAAA,QAC/C;AAAA,QAEA,yBAAyB,SAAU,GAAG;AAKpC,eAAK,eAAe,6BAA6B,CAAC;AAAA,QACpD;AAAA,QAEA,uBAAuB,SAAU,GAAG;AAKlC,eAAK,eAAe,2BAA2B,CAAC;AAChD,eAAK,SAAS;AAAA,QAChB;AAAA,QAEA,iBAAiB,SAAU,SAAS;AAClC,eAAK,gBAAgB,WAAW,KAAK,kBAAkB;AAAA,QACzD;AAAA,QAEA,cAAc,WAAY;AACxB,cAAI,CAAC,KAAK;AAAe,iBAAK,gBAAgB;AAC9C,YAAE,SAAS,WAAW,UAAU,aAAa,KAAK,IAAI;AAAA,QACxD;AAAA,QAEA,qBAAqB,WAAY;AAC/B,eAAK,aAAa;AAAA,QACpB;AAAA,QAEA,YAAY,WAAY;AACtB,eAAK,MAAM,uBAAuB;AAClC,eAAK,MAAM,wBAAwB;AACnC,cAAI,KAAK,iBAAiB,KAAK,cAAc,SAAS,KAAK;AACzD,iBAAK,YAAY,KAAK,aAAa;AACrC,YAAE,SAAS,WAAW,UAAU,WAAW,KAAK,IAAI;AACpD,iBAAO,KAAK;AAAA,QACd;AAAA,QAEA,WAAW,SAAU,QAAQ;AAC3B,cAAI,KAAK,aAAa,EAAE,SAAS;AAAS,iBAAK,cAAc,KAAK,MAAM;AAAA;AACnE,iBAAK,cAAc,QAAQ,MAAM;AACtC,eAAK,QAAQ,QAAQ,OAAO,MAAM;AAClC,gBAAM,SAAS,KAAK,gBAAgB,QAAQ,KAAK,aAAa;AAC9D,eAAK,YAAY,MAAM;AACvB,eAAK,QAAQ;AAAA,QACf;AAAA,QAEA,iBAAiB,SAAU,QAAQ;AACjC,eAAK,UAAU,MAAM;AACrB,eAAK,MAAM,uBAAuB;AAClC,eAAK,MAAM,uBAAuB,MAAM;AAAA,QAC1C;AAAA,QAEA,kBAAkB,SAAU,QAAQ;AAClC,eAAK,UAAU,MAAM;AACrB,eAAK,MAAM,wBAAwB,MAAM;AAAA,QAC3C;AAAA;AAAA;AAAA;AAAA,QAKA,MAAM,SAAU,QAAQ;AACtB,cAAI,CAAC;AACH,mBAAO,QAAQ;AAAA,cACb;AAAA,YACF;AACF,cAAI,KAAK,aAAa,EAAE,SAAS;AAAS,iBAAK,gBAAgB,MAAM;AAAA;AAChE,iBAAK,iBAAiB,MAAM;AAAA,QACnC;AAAA,QAEA,cAAc,SAAU,QAAQ;AAC9B,iBAAO,SAAS,OAAO;AACvB,eAAK,QAAQ;AAAA,QACf;AAAA;AAAA;AAAA,QAIA,KAAK,WAAY;AACf,cAAI,KAAK,cAAc,UAAU;AAAG;AACpC,cAAI;AACJ,cAAI,KAAK,aAAa,EAAE,SAAS,SAAS;AACxC,qBAAS,KAAK,cAAc,KAAK,cAAc,SAAS,CAAC;AAAA,UAC3D,OAAO;AACL,qBAAS,KAAK,cAAc,CAAC;AAAA,UAC/B;AACA,eAAK,aAAa,MAAM;AACxB,cAAI,KAAK,aAAa,EAAE,SAAS,SAAS;AACxC,iBAAK,MAAM;AAAA,cACT,KAAK,cAAc,KAAK,cAAc,SAAS,CAAC;AAAA,YAClD;AAAA,UACF,OAAO;AACL,iBAAK,MAAM,uBAAuB,KAAK,cAAc,CAAC,CAAC;AAAA,UACzD;AACA,iBAAO;AAAA,QACT;AAAA,QAEA,qBAAqB,SAAU,GAAG;AAChC,cAAI,EAAE,UAAU,EAAE,OAAO,WAAW;AAAM;AAC1C,cAAI,KAAK,aAAa,EAAE,SAAS;AAAS,iBAAK,gBAAgB,EAAE,MAAM;AAAA;AAClE,iBAAK,iBAAiB,EAAE,MAAM;AACnC,eAAK,eAAe,4BAA4B,CAAC;AAAA,QACnD;AAAA,QAEA,oBAAoB,SAAU,GAAG;AAC/B,YAAE,SAAS,WAAW,UAAU,mBAAmB,KAAK,MAAM,CAAC;AAC/D,cAAI,KAAK,UAAU;AACjB,iBAAK,MAAM,qBAAqB,EAAE,MAAM;AACxC,iBAAK,MAAM,sBAAsB,EAAE,MAAM;AAAA,UAC3C;AAAA,QACF;AAAA,QAEA,SAAS,WAAY;AACnB,eAAK,QAAQ,OAAO;AACpB,eAAK,UAAU;AAAA,QACjB;AAAA;AAAA;AAAA;AAAA;AAAA,QAMA,UAAU,SAAU,QAAQ;AAC1B,gBAAM,QAAQ,KAAK,iBAAiB;AACpC,cAAI,CAAC;AAAO;AACZ,eAAK,gBAAgB,MAAM,CAAC,KAAK,KAAK;AACtC,eAAK,oBAAoB;AAKzB,eAAK,eAAe,sBAAsB,EAAE,MAAa,CAAC;AAC1D,cAAI;AAAQ,iBAAK,gBAAgB,MAAM;AAAA,QACzC;AAAA,QAEA,aAAa,SAAU,OAAO,SAAS;AACrC,gBAAM,IAAI,EAAE,MAAa;AACzB,YAAE,SAAS,gBAAgB,CAAC;AAK5B,eAAK,eAAe,yBAAyB,CAAC;AAC9C,cAAI,EAAE;AAAY;AAClB,kBAAQ,KAAK,aAAa,OAAO,OAAO;AACxC,cAAI,KAAK;AAAe,iBAAK,cAAc;AAC3C,eAAK,QAAQ,WAAW,KAAK,WAAW,CAAC;AACzC,eAAK,QAAQ;AACb,eAAK,MAAM;AAKX,eAAK,eAAe,0BAA0B,EAAE,MAAa,CAAC;AAC9D,eAAK,SAAS;AACd,iBAAO;AAAA,QACT;AAAA,QAEA,cAAc,SAAU,OAAO,SAAS;AACtC,oBAAU,WAAW,KAAK,WAAW;AACrC,cAAI,CAAC,QAAQ;AAAQ;AACrB,gBAAM,gBAAgB,CAACC,UAASC,WAAU;AAExC,mBAAOD,SAAQ,OAAO,GAAG,OAAO,SAAS;AAAA,UAC3C;AACA,gBAAM,eAAe,CAACA,UAASC,WAAU;AAEvC,YAAAD,SAAQ,OAAOA,SAAQ,QAAQC,MAAK,GAAG,CAAC;AACxC,gBAAI,CAACD,SAAQ;AAAQ,mBAAK,aAAaA,QAAO;AAC9C,mBAAOC;AAAA,UACT;AACA,cAAI,YAAY;AAAO,mBAAO,cAAc,SAAS,KAAK;AAC1D,qBAAW,UAAU,SAAS;AAC5B,gBAAI,WAAW;AAAO,qBAAO,aAAa,SAAS,KAAK;AACxD,gBAAI,OAAO,QAAQ,KAAK,MAAM;AAAI,qBAAO,aAAa,QAAQ,KAAK;AAAA,UACrE;AAAA,QACF;AAAA;AAAA;AAAA;AAAA,QAKA,eAAe,SAAU,QAAQ;AAC/B,gBAAM,QAAQ,KAAK,QAAQ,QAAQ,MAAM;AACzC,cAAI;AAAO,mBAAO,KAAK,YAAY,KAAK;AAAA,QAC1C;AAAA;AAAA;AAAA,QAIA,aAAa,SAAU,OAAO;AAC5B,eAAK,YAAY,KAAK;AAAA,QACxB;AAAA;AAAA;AAAA,QAIA,cAAc,SAAU,OAAO;AAC7B,eAAK,YAAY,OAAO,CAAC;AAAA,QAC3B;AAAA;AAAA;AAAA,QAIA,aAAa,SAAU,OAAO,OAAO;AACnC,eAAK,YAAY;AACjB,kBAAQ,KAAK,YAAY,KAAK;AAC9B,cAAI,UAAU;AAAW,oBAAQ,KAAK,QAAQ,SAAS;AACvD,eAAK,QAAQ,SAAS,OAAO,OAAO,GAAG,KAAK;AAC5C,eAAK,QAAQ,OAAO;AACpB,cAAI,KAAK;AAAU,iBAAK,MAAM;AAAA,QAChC;AAAA,QAEA,cAAc,SAAU,GAAG;AACzB,eAAK,QAAQ,QAAQ,OAAO,EAAE,OAAO,MAAM;AAAA,QAC7C;AAAA,QAEA,aAAa,SAAU,GAAG;AACxB,eAAK,UAAU,YAAY;AAC3B,YAAE,SAAS,WAAW,UAAU,YAAY,KAAK,MAAM,CAAC;AAAA,QAC1D;AAAA,QAEA,WAAW,SAAU,GAAG;AACtB,eAAK,kBAAkB;AACvB,YAAE,SAAS,WAAW,UAAU,UAAU,KAAK,MAAM,CAAC;AAAA,QACxD;AAAA,MACF,CAAC;AAID,QAAE,SAAS,iBAAiB,EAAE,SAAS,WAAW,OAAO;AAAA,QACvD,sBAAsB,WAAY;AAChC,eAAK,WAAW,EAAE,SAAS;AAC3B,eAAK,aAAa;AAAA,QACpB;AAAA;AAAA;AAAA,QAIA,kBAAkB,SAAU,SAAS;AACnC,cAAI,KAAK,QAAQ;AAAG;AACpB,oBAAU,WAAW,KAAK,kBAAkB;AAC5C,eAAK,gBAAgB,OAAO;AAC5B,cAAI,QAAQ,SAAS,GAAG;AACtB,iBAAK,MAAM,wBAAwB;AACnC,iBAAK,MAAM,wBAAwB,QAAQ,CAAC,CAAC;AAAA,UAC/C;AACA,eAAK,qBAAqB;AAAA,QAC5B;AAAA;AAAA;AAAA,QAIA,iBAAiB,SAAU,SAAS;AAClC,cAAI,KAAK,QAAQ;AAAG;AACpB,oBAAU,WAAW,KAAK,kBAAkB;AAC5C,eAAK,gBAAgB,OAAO;AAC5B,cAAI,QAAQ,SAAS,GAAG;AACtB,iBAAK,MAAM,uBAAuB;AAClC,iBAAK,MAAM,uBAAuB,QAAQ,QAAQ,SAAS,CAAC,CAAC;AAAA,UAC/D;AACA,eAAK,oBAAoB;AAAA,QAC3B;AAAA,QAEA,mBAAmB,SAAU,SAAS;AACpC,oBAAU,WAAW,KAAK,QAAQ;AAClC,cAAI,CAAC,QAAQ,UAAU,QAAQ,CAAC,aAAa,EAAE;AAAQ,mBAAO;AAC9D,iBAAO,KAAK,kBAAkB,QAAQ,CAAC,CAAC;AAAA,QAC1C;AAAA,QAEA,aAAa,WAAY;AACvB,cAAI,KAAK,QAAQ,SAAS,UAAU,OAAO,KAAK,QAAQ,QAAQ,GAAG;AACjE,iBAAK,QAAQ,WAAW,CAAC,KAAK,QAAQ,QAAQ;AAAA,UAChD;AAAA,QACF;AAAA,QAEA,kBAAkB,WAAY;AAC5B,cAAI,KAAK,QAAQ,SAAS,QAAQ;AAChC,kBAAM,QAAQ,CAAC;AACf,iBAAK,YAAY,KAAK;AACtB,mBAAO;AAAA,UACT;AACA,iBAAO,KAAK,QAAQ;AAAA,QACtB;AAAA,QAEA,aAAa,SAAU,OAAO;AAC5B,cAAI,OAAO,KAAK;AAAG,mBAAO;AAC1B,cAAI,MAAM,CAAC;AAAG,mBAAO,KAAK,YAAY,MAAM,CAAC,CAAC;AAAA,QAChD;AAAA;AAAA;AAAA,QAIA,YAAY,SAAU,OAAO,OAAO;AAClC,cAAI,CAAC,SAAS,SAAS,MAAM,SAAS;AAAG;AACzC,eAAK,YAAY;AACjB,gBAAM,aAAa,KAAK,QAAQ,SAAS,QAAQ,KAAK;AACtD,cAAI,eAAe;AAAI;AACvB,gBAAM,QAAQ,MAAM,MAAM,GAAG,QAAQ,CAAC;AACtC,gBAAM,SAAS,MAAM,MAAM,KAAK;AAEhC,iBAAO,CAAC,IAAI,EAAE,OAAO,OAAO,CAAC,EAAE,KAAK,OAAO,CAAC,EAAE,KAAK,OAAO,CAAC,EAAE,GAAG;AAChE,eAAK,QAAQ,SAAS,OAAO,YAAY,GAAG,OAAO,MAAM;AACzD,eAAK,QAAQ;AACb,eAAK,MAAM;AACX,eAAK,SAAS;AAAA,QAChB;AAAA,MACF,CAAC;AAID,QAAE,SAAS,gBAAgB,EAAE,SAAS,WAAW,OAAO;AAAA,QACtD,QAAQ;AAAA,QACR,YAAY;AAAA,QAEZ,iBAAiB,SAAU,QAAQ;AACjC,YAAE,SAAS,WAAW,UAAU,gBAAgB,KAAK,MAAM,MAAM;AACjE,cAAI,CAAC,KAAK,MAAM,kBAAkB,SAAS;AACzC,iBAAK,MAAM,wBAAwB,MAAM;AAC3C,cAAI,KAAK,cAAc,WAAW;AAAG,iBAAK,MAAM,wBAAwB;AAAA,QAC1E;AAAA,QAEA,iBAAiB,SAAU,QAAQ;AACjC,eAAK,cAAc;AACnB,gBAAM,UAAU,KAAK,QAAQ,QAAQ,MAAM;AAC3C,cAAI,CAAC;AAAS;AACd,gBAAM,QAAQ,CAAC;AACf,kBAAQ,KAAK,KAAK;AAClB,iBAAO;AAAA,QACT;AAAA;AAAA;AAAA,QAIA,SAAS,SAAU,QAAQ;AACzB,gBAAM,QAAQ,KAAK,gBAAgB,MAAM;AACzC,cAAI,CAAC;AAAO;AACZ,eAAK,gBAAgB,KAAK;AAC1B,eAAK,oBAAoB;AACzB,cAAI;AAAQ,iBAAK,gBAAgB,MAAM;AAAA,QACzC;AAAA,QAEA,kBAAkB,WAAY;AAC5B,cAAI,KAAK,QAAQ,SAAS,UAAU,KAAK,QAAQ,SAAS,CAAC,EAAE,QAAQ;AACnE,kBAAM,QAAQ,CAAC;AACf,iBAAK,YAAY,KAAK;AACtB,mBAAO;AAAA,UACT;AACA,iBAAO,KAAK,QAAQ;AAAA,QACtB;AAAA,QAEA,aAAa,WAAY;AACvB,cAAI,KAAK,QAAQ,SAAS,UAAU,OAAO,KAAK,QAAQ,SAAS,CAAC,CAAC,GAAG;AACpE,iBAAK,QAAQ,WAAW,CAAC,KAAK,QAAQ,QAAQ;AAAA,UAChD;AAAA,QACF;AAAA,QAEA,eAAe,WAAY;AACzB,cAAI,CAAC,KAAK,QAAQ,SAAS,UAAU,OAAO,KAAK,QAAQ,QAAQ;AAC/D,iBAAK,QAAQ,WAAW,CAAC,KAAK,QAAQ,QAAQ;AAAA,QAClD;AAAA,QAEA,oBAAoB,SAAU,QAAQ;AACpC,gBAAM,SAAS,KAAK,QAAQ,YAAY,OAAO,OAAO;AACtD,gBAAM,MAAM,EAAE,KAAK,QAAQ,QAAQ,OAAO,OAAO;AACjD,cAAI,MAAM;AAAG,mBAAO;AACpB,iBAAO,EAAE,SAAS,WAAW,UAAU,mBAAmB,KAAK,MAAM,MAAM;AAAA,QAC7E;AAAA,QAEA,mBAAmB,WAAY;AAC7B,cAAI,CAAC,KAAK,QAAQ,SAAS;AAAQ,iBAAK,QAAQ,SAAS,KAAK,CAAC,CAAC;AAChE,iBAAO,KAAK,QAAQ,SAAS,CAAC;AAAA,QAChC;AAAA,QAEA,aAAa,CAAC,UAAU;AAItB,cAAI,OAAO,KAAK,MAAM,CAAC,MAAM,CAAC,KAAK,MAAM,CAAC,EAAE,WAAW;AAAI,mBAAO,CAAC,KAAK;AACxE,iBAAO;AAAA,QACT;AAAA,MACF,CAAC;AAID,QAAE,SAAS,kBAAkB,EAAE,SAAS,WAAW,OAAO;AAAA,QACxD,QAAQ;AAAA,QACR,YAAY;AAAA,QAEZ,SAAS;AAAA,UACP,mBAAmB;AAAA,QACrB;AAAA,QAEA,cAAc,SAAU,GAAG;AACzB,gBAAM,QAAQ,EAAE,OAAO,SAAS;AAChC,gBAAM,OAAO,EAAE,OAAO,QAAQ;AAC9B,gBAAM,WAAW,EAAE,OAAO,YAAY;AACtC,gBAAM,iBAAiB,QAAQ,KAAK;AACpC,gBAAM,WAAW,EAAE,OAAO,QAAQ,aAAa;AAC/C,gBAAM,SAAS,IAAI,EAAE,aAAa,EAAE,QAAQ,QAAQ;AAEpD,mBAAS,OAAO,OAAO,CAAC,EAAE,OAAO,KAAK,SAAS,GAAG,CAAC;AACnD,eAAK,OAAO,OAAO,CAAC,SAAS,KAAK,EAAE,OAAO,GAAG,CAAC;AAC/C,eAAK,aAAa,MAAM;AACxB,eAAK,qBAAqB;AAAA,QAC5B;AAAA,QAEA,oBAAoB,SAAU,GAAG;AAC/B,YAAE,SAAS,WAAW,UAAU,mBAAmB,KAAK,MAAM,CAAC;AAC/D,eAAK,QAAQ;AACb,gBAAM,UAAU,KAAK,kBAAkB;AAGvC,cAAI,QAAQ,WAAW;AAAG,oBAAQ,KAAK,EAAE,MAAM;AAC/C,gBAAM,SAAS,IAAI,EAAE,aAAa,EAAE,QAAQ,EAAE,MAAM;AACpD,eAAK,aAAa,MAAM;AACxB,eAAK,cAAc,MAAM;AACzB,eAAK,QAAQ;AACb,eAAK,MAAM;AAUX,YAAE,cAAc,aAAa;AAC7B,eAAK,IAAI,SAAS,WAAW,MAAM,EAAE,aAAa;AAIlD,kBAAQ,CAAC,EAAE,SAAS,SAAS,WAAW,QAAQ,EAAE,aAAa;AAAA,QACjE;AAAA,QAEA,kBAAkB,SAAU,GAAG;AAC7B,eAAK,cAAc,CAAC;AACpB,YAAE,cAAc,aAAa;AAC7B,YAAE,SAAS,WAAW,UAAU,iBAAiB,KAAK,MAAM,CAAC;AAAA,QAC/D;AAAA,QAEA,oBAAoB,SAAU,GAAG;AAC/B,YAAE,cAAc,aAAa;AAC7B,YAAE,SAAS,WAAW,UAAU,mBAAmB,KAAK,MAAM,CAAC;AAAA,QACjE;AAAA,QAEA,mBAAmB,SAAU,SAAS;AACpC,iBAAO,WAAW,KAAK,QAAQ,SAAS,CAAC;AAAA,QAC3C;AAAA,QAEA,cAAc,SAAU,QAAQ;AAC9B,eAAK,QAAQ,UAAU;AAAA,QACzB;AAAA,QAEA,eAAe,SAAU,QAAQ;AAC/B,gBAAM,UAAU,KAAK,kBAAkB;AACvC,gBAAM,aAAa,KAAK,QAAQ,iBAAiB,MAAM;AAEvD,mBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,oBAAQ,CAAC,EAAE,OAAO,WAAW,CAAC,CAAC;AAAA,UACjC;AAAA,QACF;AAAA,MACF,CAAC;AAID,QAAE,SAAS,eAAe,EAAE,SAAS,WAAW,OAAO;AAAA,QACrD,YAAY;AAAA,QAEZ,SAAS;AAAA,UACP,mBAAmB;AAAA,QACrB;AAAA,QAEA,YAAY,SAAU,KAAK,SAAS,SAAS;AAC3C,YAAE,SAAS,WAAW,UAAU,WAAW,KAAK,MAAM,KAAK,SAAS,OAAO;AAC3E,eAAK,gBAAgB,KAAK,oBAAoB;AAAA,QAChD;AAAA,QAEA,qBAAqB,WAAY;AAE/B,gBAAM,SACH,KAAK,QAAQ,WAAW,KAAK,QAAQ,YAAY,KAAK,IAAI,KAAK,KAAK,CAAC;AACxE,gBAAM,QAAQ,KAAK,IAAI,QAAQ,KAAK,QAAQ,OAAO;AACnD,iBAAO,KAAK,IAAI,UAAU,CAAC,MAAM,IAAI,OAAO,MAAM,IAAI,KAAK,CAAC;AAAA,QAC9D;AAAA,QAEA,oBAAoB,WAAY;AAC9B,eAAK,cAAc,OAAO,KAAK,oBAAoB,CAAC;AACpD,eAAK,cAAc,SAAS,OAAO;AAAA,QACrC;AAAA,QAEA,YAAY,WAAY;AACtB,iBAAO,CAAC,KAAK,QAAQ,SAAS,KAAK,aAAa;AAAA,QAClD;AAAA,QAEA,mBAAmB,WAAY;AAC7B,iBAAO,KAAK,WAAW;AAAA,QACzB;AAAA,QAEA,oBAAoB,SAAU,GAAG;AAC/B,cAAI,EAAE,OAAO,SAAS,MAAM;AAAG,iBAAK,OAAO,CAAC;AAAA;AACvC,iBAAK,mBAAmB,CAAC;AAC9B,YAAE,SAAS,WAAW,UAAU,mBAAmB,KAAK,MAAM,CAAC;AAAA,QACjE;AAAA,QAEA,QAAQ,SAAU,GAAG;AACnB,cAAI;AACJ,cAAI,KAAK,IAAI,QAAQ,KAAK;AACxB,qBAAS,KAAK,IAAI,QAAQ,IAAI,SAAS,KAAK,QAAQ,SAAS,EAAE,MAAM;AAAA,UACvE,OAAO;AACL,qBAAS,KAAK,QAAQ,QAAQ,WAAW,EAAE,MAAM;AAAA,UACnD;AACA,eAAK,QAAQ,UAAU,MAAM;AAAA,QAC/B;AAAA,QAEA,oBAAoB,SAAU,GAAG;AAC/B,YAAE,SAAS,WAAW,UAAU,mBAAmB,KAAK,MAAM,CAAC;AAC/D,eAAK,cAAc,OAAO,EAAE,MAAM;AAClC,eAAK,QAAQ,QAAQ,OAAO,EAAE,MAAM;AACpC,eAAK,QAAQ;AAEb,YAAE,cAAc,aAAa;AAC7B,eAAK,IAAI,SAAS,WAAW,MAAM,EAAE,aAAa;AAElD,eAAK,cAAc,SAAS,SAAS,WAAW,QAAQ,EAAE,aAAa;AAAA,QACzE;AAAA,QAEA,kBAAkB,SAAU,GAAG;AAC7B,eAAK,cAAc,CAAC;AACpB,YAAE,cAAc,aAAa;AAC7B,YAAE,SAAS,WAAW,UAAU,iBAAiB,KAAK,MAAM,CAAC;AAAA,QAC/D;AAAA,QAEA,oBAAoB,SAAU,GAAG;AAC/B,YAAE,cAAc,aAAa;AAC7B,YAAE,SAAS,WAAW,UAAU,mBAAmB,KAAK,MAAM,CAAC;AAAA,QACjE;AAAA,QAEA,QAAQ,SAAU,GAAG;AACnB,YAAE,SAAS,WAAW,UAAU,OAAO,KAAK,MAAM,CAAC;AACnD,eAAK,QAAQ,SAAS,aAAa,KAAK,aAAa;AAAA,QACvD;AAAA,MACF,CAAC;AAOD,YAAM,gBAAgB;AAAA,QACpB,cAAc,SAAU,KAAK;AAr1DjC;AAs1DM,gBAAM,OAAO,KAAK;AAClB,gBAAM,UAAQ,UAAK,QAAQ,gBAAb,mBAA0B,cAAa,IAAI;AACzD,cAAI,CAAC;AAAO,kBAAM,MAAM,qCAAqC;AAC7D,gBAAM,QAAQ,KAAK,QAAQ,eAAe,KAAK,eAAe,KAAK;AACnE,iBAAO,IAAI,MAAM,KAAK,MAAM,KAAK,QAAQ,WAAW;AAAA,QACtD;AAAA;AAAA;AAAA,QAIA,YAAY,SAAU,KAAK;AACzB,cAAI,CAAC,KAAK;AAAQ,iBAAK,aAAa,GAAG;AACvC,eAAK,OAAO,OAAO;AACnB,iBAAO,KAAK;AAAA,QACd;AAAA;AAAA;AAAA,QAIA,aAAa,WAAY;AAv2D7B;AAw2DM,kBAAO,UAAK,WAAL,mBAAa;AAAA,QACtB;AAAA;AAAA;AAAA,QAIA,aAAa,WAAY;AACvB,cAAI,KAAK,QAAQ;AACf,iBAAK,OAAO,QAAQ;AACpB,mBAAO,KAAK;AAAA,UACd;AAAA,QACF;AAAA;AAAA;AAAA,QAIA,YAAY,WAAY;AACtB,cAAI,KAAK,YAAY;AAAG,iBAAK,YAAY;AAAA;AACpC,iBAAK,WAAW;AAAA,QACvB;AAAA,QAEA,gBAAgB,WAAY;AAC1B,cAAI,KAAK;AAAQ,iBAAK,WAAW;AAAA,QACnC;AAAA,MACF;AAEA,YAAM,gBAAgB;AAAA,QACpB,gBAAgB,CAAC,UAAU;AAj4D/B;AAk4DM,mBAAO,oCAAO,YAAP,mBAAgB,wBAAuB,EAAE,SAAS;AAAA,QAC3D;AAAA,QAEA,SAAS,SAAU,QAAQ,SAAS;AAIlC,cAAI,QAAQ;AACZ,oBAAU,WAAW,KAAK;AAC1B,cAAI,CAAC,QAAQ;AAAQ,mBAAO;AAC5B,cAAI,OAAO,OAAO,KAAK,KAAK,YAAY,QAAQ,OAAO;AAAG,oBAAQ;AAAA,eAC7D;AACH,uBAAW,UAAU,SAAS;AAC5B,kBAAI,KAAK,YAAY,QAAQ,MAAM;AAAG,uBAAO;AAAA,YAC/C;AAAA,UACF;AACA,iBAAO;AAAA,QACT;AAAA,QAEA,aAAa,SAAU,GAAG,SAAS;AACjC,cAAI,CAAC;AAAS,mBAAO;AACrB,cAAI;AACJ,cAAI;AACJ,cAAI;AACJ,cAAI,OAAO,CAAC;AACZ,cAAI;AACJ,gBAAM,IAAI,KAAK,gBAAgB;AAC/B,eAAK,gBAAgB,SAAS,MAAM,KAAK,SAAS;AAClD,iBAAO,KAAK,CAAC;AACb,cAAI,KAAK,KAAK,mBAAmB,CAAC;AAElC,cAAI,CAAC,KAAK,UAAU,SAAS,CAAC,GAAG;AAC/B,mBAAO;AAAA,UACT;AACA,eAAK,IAAI,GAAG,MAAM,KAAK,QAAQ,IAAI,GAAG,IAAI,KAAK,IAAI,KAAK;AACtD,gBAAI,EAAE,SAAS,uBAAuB,GAAG,KAAK,CAAC,GAAG,KAAK,CAAC,CAAC,KAAK,GAAG;AAC/D,qBAAO;AAAA,YACT;AAAA,UACF;AACA,iBAAO;AAAA,QACT;AAAA,MACF;AAEA,YAAM,eAAe;AAAA,QACnB,gBAAgB,CAAC,UAAU;AA96D/B;AA+6DM,mBAAO,oCAAO,YAAP,mBAAgB,uBAAsB,EAAE,SAAS;AAAA,QAC1D;AAAA,QAEA,SAAS,SAAU,QAAQ,SAAS;AAKlC,cAAI,QAAQ;AACZ,oBAAU,WAAW,KAAK;AAC1B,cAAI,CAAC,QAAQ;AAAQ,mBAAO;AAC5B,cAAI,OAAO,OAAO,KAAK,KAAK,YAAY,QAAQ,OAAO;AAAG,oBAAQ;AAClE,cAAI,OAAO,QAAQ,CAAC,CAAC,KAAK,KAAK,YAAY,QAAQ,QAAQ,CAAC,CAAC,GAAG;AAC9D,oBAAQ;AAAA,UACV,OAAO;AACL,uBAAW,UAAU,SAAS;AAC5B,kBAAI,KAAK,YAAY,QAAQ,OAAO,CAAC,CAAC;AAAG,uBAAO;AAAA,YAClD;AAAA,UACF;AACA,iBAAO;AAAA,QACT;AAAA,QAEA,aAAa,CAAC,GAAG,YAAY;AAC3B,cAAI,SAAS;AACb,cAAI;AACJ,cAAI;AACJ,cAAI;AACJ,cAAI;AACJ,cAAI;AAEJ,eAAK,IAAI,GAAG,OAAO,QAAQ,QAAQ,IAAI,OAAO,GAAG,IAAI,MAAM,IAAI,KAAK;AAClE,iBAAK,QAAQ,CAAC;AACd,iBAAK,QAAQ,CAAC;AAEd,gBACE,GAAG,MAAM,EAAE,QAAQ,GAAG,MAAM,EAAE,OAC9B,EAAE,OAAQ,GAAG,MAAM,GAAG,QAAQ,EAAE,MAAM,GAAG,QAAS,GAAG,MAAM,GAAG,OAAO,GAAG,KACxE;AACA,uBAAS,CAAC;AAAA,YACZ;AAAA,UACF;AAEA,iBAAO;AAAA,QACT;AAAA,QAEA,aAAa,SAAU,OAAO,SAAS;AACrC,oBAAU,WAAW,KAAK;AAC1B,cAAI,CAAC;AAAS;AACd,cAAI,MAAM,EAAE,KAAK,QAAQ,SAAS,KAAK;AACvC,cAAI,QAAQ;AAAI,mBAAO;AACvB,qBAAW,UAAU,SAAS;AAC5B,kBAAM,EAAE,KAAK,QAAQ,QAAQ,KAAK;AAClC,gBAAI,QAAQ;AAAI,qBAAO;AAAA,UACzB;AAAA,QACF;AAAA,MACF;AAEA,YAAM,cAAc;AAAA,QAClB,gBAAgB,CAAC,UAAU;AAz+D/B;AA0+DM,mBAAO,oCAAO,YAAP,mBAAgB,sBAAqB,EAAE,SAAS;AAAA,QACzD;AAAA,MACF;AAEA,YAAM,oBAAoB;AAAA,QACxB,gBAAgB,CAAC,UAAU;AA/+D/B;AAg/DM,mBAAO,oCAAO,YAAP,mBAAgB,4BAA2B,EAAE,SAAS;AAAA,QAC/D;AAAA,MACF;AAEA,YAAM,iBAAiB;AAAA,QACrB,gBAAgB,CAAC,UAAU;AAr/D/B;AAs/DM,mBAAO,oCAAO,YAAP,mBAAgB,yBAAwB,EAAE,SAAS;AAAA,QAC5D;AAAA,MACF;AAEA,YAAM,cAAc;AAAA,QAClB,gBAAgB,CAAC,UAAU;AA3/D/B;AA4/DM,mBAAO,oCAAO,YAAP,mBAAgB,sBAAqB,EAAE,SAAS;AAAA,QACzD;AAAA,MACF;AAEA,YAAM,eAAe,WAAY;AAE/B,aAAK,GAAG,OAAO,KAAK,cAAc;AAAA,MACpC;AAEA,YAAM,SAAS,EAAE,SAAS,UAAU,EAAE,SAAS,SAAS,EAAE,SAAS;AAEnE,UAAI,EAAE,UAAU;AACd,UAAE,SAAS,QAAQ,aAAa;AAChC,UAAE,SAAS,QAAQ,aAAa;AAChC,UAAE,SAAS,YAAY,YAAY;AAAA,MACrC;AACA,UAAI,EAAE,SAAS;AACb,UAAE,QAAQ,QAAQ,aAAa;AAC/B,UAAE,QAAQ,QAAQ,YAAY;AAAA,MAChC;AACA,UAAI,EAAE,QAAQ;AACZ,UAAE,OAAO,QAAQ,aAAa;AAC9B,UAAE,OAAO,QAAQ,WAAW;AAC5B,UAAE,OAAO,YAAY,YAAY;AAAA,MACnC;AACA,UAAI,EAAE,cAAc;AAClB,UAAE,aAAa,QAAQ,aAAa;AACpC,UAAE,aAAa,QAAQ,iBAAiB;AACxC,UAAE,aAAa,YAAY,YAAY;AAAA,MACzC;AACA,UAAI,EAAE,WAAW;AACf,UAAE,UAAU,QAAQ,aAAa;AACjC,UAAE,UAAU,QAAQ,cAAc;AAAA,MACpC;AACA,UAAI,EAAE,QAAQ;AACZ,UAAE,OAAO,QAAQ,aAAa;AAC9B,UAAE,OAAO,QAAQ,WAAW;AAAA,MAC9B;AAEA,QAAE,OAAO,UAAU,SAAS,SAAU,QAAQ;AAC5C,iBAAS,EAAE,OAAO,MAAM;AACxB,aAAK,MAAM,OAAO;AAClB,aAAK,MAAM,OAAO;AAAA,MACpB;AAAA,IACF,GAAG,MAAM;AAAA;AAAA;", "names": ["window", "latlngs", "shape"]}