{"version": 3, "sources": ["../../moment/dist/moment.js"], "sourcesContent": ["//! moment.js\n//! version : 2.30.1\n//! authors : <PERSON>, <PERSON><PERSON><PERSON>, Moment.js contributors\n//! license : MIT\n//! momentjs.com\n\nvar hookCallback;\n\nfunction hooks() {\n    return hookCallback.apply(null, arguments);\n}\n\n// This is done to register the method called with moment()\n// without creating circular dependencies.\nfunction setHookCallback(callback) {\n    hookCallback = callback;\n}\n\nfunction isArray(input) {\n    return (\n        input instanceof Array ||\n        Object.prototype.toString.call(input) === '[object Array]'\n    );\n}\n\nfunction isObject(input) {\n    // IE8 will treat undefined and null as object if it wasn't for\n    // input != null\n    return (\n        input != null &&\n        Object.prototype.toString.call(input) === '[object Object]'\n    );\n}\n\nfunction hasOwnProp(a, b) {\n    return Object.prototype.hasOwnProperty.call(a, b);\n}\n\nfunction isObjectEmpty(obj) {\n    if (Object.getOwnPropertyNames) {\n        return Object.getOwnPropertyNames(obj).length === 0;\n    } else {\n        var k;\n        for (k in obj) {\n            if (hasOwnProp(obj, k)) {\n                return false;\n            }\n        }\n        return true;\n    }\n}\n\nfunction isUndefined(input) {\n    return input === void 0;\n}\n\nfunction isNumber(input) {\n    return (\n        typeof input === 'number' ||\n        Object.prototype.toString.call(input) === '[object Number]'\n    );\n}\n\nfunction isDate(input) {\n    return (\n        input instanceof Date ||\n        Object.prototype.toString.call(input) === '[object Date]'\n    );\n}\n\nfunction map(arr, fn) {\n    var res = [],\n        i,\n        arrLen = arr.length;\n    for (i = 0; i < arrLen; ++i) {\n        res.push(fn(arr[i], i));\n    }\n    return res;\n}\n\nfunction extend(a, b) {\n    for (var i in b) {\n        if (hasOwnProp(b, i)) {\n            a[i] = b[i];\n        }\n    }\n\n    if (hasOwnProp(b, 'toString')) {\n        a.toString = b.toString;\n    }\n\n    if (hasOwnProp(b, 'valueOf')) {\n        a.valueOf = b.valueOf;\n    }\n\n    return a;\n}\n\nfunction createUTC(input, format, locale, strict) {\n    return createLocalOrUTC(input, format, locale, strict, true).utc();\n}\n\nfunction defaultParsingFlags() {\n    // We need to deep clone this object.\n    return {\n        empty: false,\n        unusedTokens: [],\n        unusedInput: [],\n        overflow: -2,\n        charsLeftOver: 0,\n        nullInput: false,\n        invalidEra: null,\n        invalidMonth: null,\n        invalidFormat: false,\n        userInvalidated: false,\n        iso: false,\n        parsedDateParts: [],\n        era: null,\n        meridiem: null,\n        rfc2822: false,\n        weekdayMismatch: false,\n    };\n}\n\nfunction getParsingFlags(m) {\n    if (m._pf == null) {\n        m._pf = defaultParsingFlags();\n    }\n    return m._pf;\n}\n\nvar some;\nif (Array.prototype.some) {\n    some = Array.prototype.some;\n} else {\n    some = function (fun) {\n        var t = Object(this),\n            len = t.length >>> 0,\n            i;\n\n        for (i = 0; i < len; i++) {\n            if (i in t && fun.call(this, t[i], i, t)) {\n                return true;\n            }\n        }\n\n        return false;\n    };\n}\n\nfunction isValid(m) {\n    var flags = null,\n        parsedParts = false,\n        isNowValid = m._d && !isNaN(m._d.getTime());\n    if (isNowValid) {\n        flags = getParsingFlags(m);\n        parsedParts = some.call(flags.parsedDateParts, function (i) {\n            return i != null;\n        });\n        isNowValid =\n            flags.overflow < 0 &&\n            !flags.empty &&\n            !flags.invalidEra &&\n            !flags.invalidMonth &&\n            !flags.invalidWeekday &&\n            !flags.weekdayMismatch &&\n            !flags.nullInput &&\n            !flags.invalidFormat &&\n            !flags.userInvalidated &&\n            (!flags.meridiem || (flags.meridiem && parsedParts));\n        if (m._strict) {\n            isNowValid =\n                isNowValid &&\n                flags.charsLeftOver === 0 &&\n                flags.unusedTokens.length === 0 &&\n                flags.bigHour === undefined;\n        }\n    }\n    if (Object.isFrozen == null || !Object.isFrozen(m)) {\n        m._isValid = isNowValid;\n    } else {\n        return isNowValid;\n    }\n    return m._isValid;\n}\n\nfunction createInvalid(flags) {\n    var m = createUTC(NaN);\n    if (flags != null) {\n        extend(getParsingFlags(m), flags);\n    } else {\n        getParsingFlags(m).userInvalidated = true;\n    }\n\n    return m;\n}\n\n// Plugins that add properties should also add the key here (null value),\n// so we can properly clone ourselves.\nvar momentProperties = (hooks.momentProperties = []),\n    updateInProgress = false;\n\nfunction copyConfig(to, from) {\n    var i,\n        prop,\n        val,\n        momentPropertiesLen = momentProperties.length;\n\n    if (!isUndefined(from._isAMomentObject)) {\n        to._isAMomentObject = from._isAMomentObject;\n    }\n    if (!isUndefined(from._i)) {\n        to._i = from._i;\n    }\n    if (!isUndefined(from._f)) {\n        to._f = from._f;\n    }\n    if (!isUndefined(from._l)) {\n        to._l = from._l;\n    }\n    if (!isUndefined(from._strict)) {\n        to._strict = from._strict;\n    }\n    if (!isUndefined(from._tzm)) {\n        to._tzm = from._tzm;\n    }\n    if (!isUndefined(from._isUTC)) {\n        to._isUTC = from._isUTC;\n    }\n    if (!isUndefined(from._offset)) {\n        to._offset = from._offset;\n    }\n    if (!isUndefined(from._pf)) {\n        to._pf = getParsingFlags(from);\n    }\n    if (!isUndefined(from._locale)) {\n        to._locale = from._locale;\n    }\n\n    if (momentPropertiesLen > 0) {\n        for (i = 0; i < momentPropertiesLen; i++) {\n            prop = momentProperties[i];\n            val = from[prop];\n            if (!isUndefined(val)) {\n                to[prop] = val;\n            }\n        }\n    }\n\n    return to;\n}\n\n// Moment prototype object\nfunction Moment(config) {\n    copyConfig(this, config);\n    this._d = new Date(config._d != null ? config._d.getTime() : NaN);\n    if (!this.isValid()) {\n        this._d = new Date(NaN);\n    }\n    // Prevent infinite loop in case updateOffset creates new moment\n    // objects.\n    if (updateInProgress === false) {\n        updateInProgress = true;\n        hooks.updateOffset(this);\n        updateInProgress = false;\n    }\n}\n\nfunction isMoment(obj) {\n    return (\n        obj instanceof Moment || (obj != null && obj._isAMomentObject != null)\n    );\n}\n\nfunction warn(msg) {\n    if (\n        hooks.suppressDeprecationWarnings === false &&\n        typeof console !== 'undefined' &&\n        console.warn\n    ) {\n        console.warn('Deprecation warning: ' + msg);\n    }\n}\n\nfunction deprecate(msg, fn) {\n    var firstTime = true;\n\n    return extend(function () {\n        if (hooks.deprecationHandler != null) {\n            hooks.deprecationHandler(null, msg);\n        }\n        if (firstTime) {\n            var args = [],\n                arg,\n                i,\n                key,\n                argLen = arguments.length;\n            for (i = 0; i < argLen; i++) {\n                arg = '';\n                if (typeof arguments[i] === 'object') {\n                    arg += '\\n[' + i + '] ';\n                    for (key in arguments[0]) {\n                        if (hasOwnProp(arguments[0], key)) {\n                            arg += key + ': ' + arguments[0][key] + ', ';\n                        }\n                    }\n                    arg = arg.slice(0, -2); // Remove trailing comma and space\n                } else {\n                    arg = arguments[i];\n                }\n                args.push(arg);\n            }\n            warn(\n                msg +\n                    '\\nArguments: ' +\n                    Array.prototype.slice.call(args).join('') +\n                    '\\n' +\n                    new Error().stack\n            );\n            firstTime = false;\n        }\n        return fn.apply(this, arguments);\n    }, fn);\n}\n\nvar deprecations = {};\n\nfunction deprecateSimple(name, msg) {\n    if (hooks.deprecationHandler != null) {\n        hooks.deprecationHandler(name, msg);\n    }\n    if (!deprecations[name]) {\n        warn(msg);\n        deprecations[name] = true;\n    }\n}\n\nhooks.suppressDeprecationWarnings = false;\nhooks.deprecationHandler = null;\n\nfunction isFunction(input) {\n    return (\n        (typeof Function !== 'undefined' && input instanceof Function) ||\n        Object.prototype.toString.call(input) === '[object Function]'\n    );\n}\n\nfunction set(config) {\n    var prop, i;\n    for (i in config) {\n        if (hasOwnProp(config, i)) {\n            prop = config[i];\n            if (isFunction(prop)) {\n                this[i] = prop;\n            } else {\n                this['_' + i] = prop;\n            }\n        }\n    }\n    this._config = config;\n    // Lenient ordinal parsing accepts just a number in addition to\n    // number + (possibly) stuff coming from _dayOfMonthOrdinalParse.\n    // TODO: Remove \"ordinalParse\" fallback in next major release.\n    this._dayOfMonthOrdinalParseLenient = new RegExp(\n        (this._dayOfMonthOrdinalParse.source || this._ordinalParse.source) +\n            '|' +\n            /\\d{1,2}/.source\n    );\n}\n\nfunction mergeConfigs(parentConfig, childConfig) {\n    var res = extend({}, parentConfig),\n        prop;\n    for (prop in childConfig) {\n        if (hasOwnProp(childConfig, prop)) {\n            if (isObject(parentConfig[prop]) && isObject(childConfig[prop])) {\n                res[prop] = {};\n                extend(res[prop], parentConfig[prop]);\n                extend(res[prop], childConfig[prop]);\n            } else if (childConfig[prop] != null) {\n                res[prop] = childConfig[prop];\n            } else {\n                delete res[prop];\n            }\n        }\n    }\n    for (prop in parentConfig) {\n        if (\n            hasOwnProp(parentConfig, prop) &&\n            !hasOwnProp(childConfig, prop) &&\n            isObject(parentConfig[prop])\n        ) {\n            // make sure changes to properties don't modify parent config\n            res[prop] = extend({}, res[prop]);\n        }\n    }\n    return res;\n}\n\nfunction Locale(config) {\n    if (config != null) {\n        this.set(config);\n    }\n}\n\nvar keys;\n\nif (Object.keys) {\n    keys = Object.keys;\n} else {\n    keys = function (obj) {\n        var i,\n            res = [];\n        for (i in obj) {\n            if (hasOwnProp(obj, i)) {\n                res.push(i);\n            }\n        }\n        return res;\n    };\n}\n\nvar defaultCalendar = {\n    sameDay: '[Today at] LT',\n    nextDay: '[Tomorrow at] LT',\n    nextWeek: 'dddd [at] LT',\n    lastDay: '[Yesterday at] LT',\n    lastWeek: '[Last] dddd [at] LT',\n    sameElse: 'L',\n};\n\nfunction calendar(key, mom, now) {\n    var output = this._calendar[key] || this._calendar['sameElse'];\n    return isFunction(output) ? output.call(mom, now) : output;\n}\n\nfunction zeroFill(number, targetLength, forceSign) {\n    var absNumber = '' + Math.abs(number),\n        zerosToFill = targetLength - absNumber.length,\n        sign = number >= 0;\n    return (\n        (sign ? (forceSign ? '+' : '') : '-') +\n        Math.pow(10, Math.max(0, zerosToFill)).toString().substr(1) +\n        absNumber\n    );\n}\n\nvar formattingTokens =\n        /(\\[[^\\[]*\\])|(\\\\)?([Hh]mm(ss)?|Mo|MM?M?M?|Do|DDDo|DD?D?D?|ddd?d?|do?|w[o|w]?|W[o|W]?|Qo?|N{1,5}|YYYYYY|YYYYY|YYYY|YY|y{2,4}|yo?|gg(ggg?)?|GG(GGG?)?|e|E|a|A|hh?|HH?|kk?|mm?|ss?|S{1,9}|x|X|zz?|ZZ?|.)/g,\n    localFormattingTokens = /(\\[[^\\[]*\\])|(\\\\)?(LTS|LT|LL?L?L?|l{1,4})/g,\n    formatFunctions = {},\n    formatTokenFunctions = {};\n\n// token:    'M'\n// padded:   ['MM', 2]\n// ordinal:  'Mo'\n// callback: function () { this.month() + 1 }\nfunction addFormatToken(token, padded, ordinal, callback) {\n    var func = callback;\n    if (typeof callback === 'string') {\n        func = function () {\n            return this[callback]();\n        };\n    }\n    if (token) {\n        formatTokenFunctions[token] = func;\n    }\n    if (padded) {\n        formatTokenFunctions[padded[0]] = function () {\n            return zeroFill(func.apply(this, arguments), padded[1], padded[2]);\n        };\n    }\n    if (ordinal) {\n        formatTokenFunctions[ordinal] = function () {\n            return this.localeData().ordinal(\n                func.apply(this, arguments),\n                token\n            );\n        };\n    }\n}\n\nfunction removeFormattingTokens(input) {\n    if (input.match(/\\[[\\s\\S]/)) {\n        return input.replace(/^\\[|\\]$/g, '');\n    }\n    return input.replace(/\\\\/g, '');\n}\n\nfunction makeFormatFunction(format) {\n    var array = format.match(formattingTokens),\n        i,\n        length;\n\n    for (i = 0, length = array.length; i < length; i++) {\n        if (formatTokenFunctions[array[i]]) {\n            array[i] = formatTokenFunctions[array[i]];\n        } else {\n            array[i] = removeFormattingTokens(array[i]);\n        }\n    }\n\n    return function (mom) {\n        var output = '',\n            i;\n        for (i = 0; i < length; i++) {\n            output += isFunction(array[i])\n                ? array[i].call(mom, format)\n                : array[i];\n        }\n        return output;\n    };\n}\n\n// format date using native date object\nfunction formatMoment(m, format) {\n    if (!m.isValid()) {\n        return m.localeData().invalidDate();\n    }\n\n    format = expandFormat(format, m.localeData());\n    formatFunctions[format] =\n        formatFunctions[format] || makeFormatFunction(format);\n\n    return formatFunctions[format](m);\n}\n\nfunction expandFormat(format, locale) {\n    var i = 5;\n\n    function replaceLongDateFormatTokens(input) {\n        return locale.longDateFormat(input) || input;\n    }\n\n    localFormattingTokens.lastIndex = 0;\n    while (i >= 0 && localFormattingTokens.test(format)) {\n        format = format.replace(\n            localFormattingTokens,\n            replaceLongDateFormatTokens\n        );\n        localFormattingTokens.lastIndex = 0;\n        i -= 1;\n    }\n\n    return format;\n}\n\nvar defaultLongDateFormat = {\n    LTS: 'h:mm:ss A',\n    LT: 'h:mm A',\n    L: 'MM/DD/YYYY',\n    LL: 'MMMM D, YYYY',\n    LLL: 'MMMM D, YYYY h:mm A',\n    LLLL: 'dddd, MMMM D, YYYY h:mm A',\n};\n\nfunction longDateFormat(key) {\n    var format = this._longDateFormat[key],\n        formatUpper = this._longDateFormat[key.toUpperCase()];\n\n    if (format || !formatUpper) {\n        return format;\n    }\n\n    this._longDateFormat[key] = formatUpper\n        .match(formattingTokens)\n        .map(function (tok) {\n            if (\n                tok === 'MMMM' ||\n                tok === 'MM' ||\n                tok === 'DD' ||\n                tok === 'dddd'\n            ) {\n                return tok.slice(1);\n            }\n            return tok;\n        })\n        .join('');\n\n    return this._longDateFormat[key];\n}\n\nvar defaultInvalidDate = 'Invalid date';\n\nfunction invalidDate() {\n    return this._invalidDate;\n}\n\nvar defaultOrdinal = '%d',\n    defaultDayOfMonthOrdinalParse = /\\d{1,2}/;\n\nfunction ordinal(number) {\n    return this._ordinal.replace('%d', number);\n}\n\nvar defaultRelativeTime = {\n    future: 'in %s',\n    past: '%s ago',\n    s: 'a few seconds',\n    ss: '%d seconds',\n    m: 'a minute',\n    mm: '%d minutes',\n    h: 'an hour',\n    hh: '%d hours',\n    d: 'a day',\n    dd: '%d days',\n    w: 'a week',\n    ww: '%d weeks',\n    M: 'a month',\n    MM: '%d months',\n    y: 'a year',\n    yy: '%d years',\n};\n\nfunction relativeTime(number, withoutSuffix, string, isFuture) {\n    var output = this._relativeTime[string];\n    return isFunction(output)\n        ? output(number, withoutSuffix, string, isFuture)\n        : output.replace(/%d/i, number);\n}\n\nfunction pastFuture(diff, output) {\n    var format = this._relativeTime[diff > 0 ? 'future' : 'past'];\n    return isFunction(format) ? format(output) : format.replace(/%s/i, output);\n}\n\nvar aliases = {\n    D: 'date',\n    dates: 'date',\n    date: 'date',\n    d: 'day',\n    days: 'day',\n    day: 'day',\n    e: 'weekday',\n    weekdays: 'weekday',\n    weekday: 'weekday',\n    E: 'isoWeekday',\n    isoweekdays: 'isoWeekday',\n    isoweekday: 'isoWeekday',\n    DDD: 'dayOfYear',\n    dayofyears: 'dayOfYear',\n    dayofyear: 'dayOfYear',\n    h: 'hour',\n    hours: 'hour',\n    hour: 'hour',\n    ms: 'millisecond',\n    milliseconds: 'millisecond',\n    millisecond: 'millisecond',\n    m: 'minute',\n    minutes: 'minute',\n    minute: 'minute',\n    M: 'month',\n    months: 'month',\n    month: 'month',\n    Q: 'quarter',\n    quarters: 'quarter',\n    quarter: 'quarter',\n    s: 'second',\n    seconds: 'second',\n    second: 'second',\n    gg: 'weekYear',\n    weekyears: 'weekYear',\n    weekyear: 'weekYear',\n    GG: 'isoWeekYear',\n    isoweekyears: 'isoWeekYear',\n    isoweekyear: 'isoWeekYear',\n    w: 'week',\n    weeks: 'week',\n    week: 'week',\n    W: 'isoWeek',\n    isoweeks: 'isoWeek',\n    isoweek: 'isoWeek',\n    y: 'year',\n    years: 'year',\n    year: 'year',\n};\n\nfunction normalizeUnits(units) {\n    return typeof units === 'string'\n        ? aliases[units] || aliases[units.toLowerCase()]\n        : undefined;\n}\n\nfunction normalizeObjectUnits(inputObject) {\n    var normalizedInput = {},\n        normalizedProp,\n        prop;\n\n    for (prop in inputObject) {\n        if (hasOwnProp(inputObject, prop)) {\n            normalizedProp = normalizeUnits(prop);\n            if (normalizedProp) {\n                normalizedInput[normalizedProp] = inputObject[prop];\n            }\n        }\n    }\n\n    return normalizedInput;\n}\n\nvar priorities = {\n    date: 9,\n    day: 11,\n    weekday: 11,\n    isoWeekday: 11,\n    dayOfYear: 4,\n    hour: 13,\n    millisecond: 16,\n    minute: 14,\n    month: 8,\n    quarter: 7,\n    second: 15,\n    weekYear: 1,\n    isoWeekYear: 1,\n    week: 5,\n    isoWeek: 5,\n    year: 1,\n};\n\nfunction getPrioritizedUnits(unitsObj) {\n    var units = [],\n        u;\n    for (u in unitsObj) {\n        if (hasOwnProp(unitsObj, u)) {\n            units.push({ unit: u, priority: priorities[u] });\n        }\n    }\n    units.sort(function (a, b) {\n        return a.priority - b.priority;\n    });\n    return units;\n}\n\nvar match1 = /\\d/, //       0 - 9\n    match2 = /\\d\\d/, //      00 - 99\n    match3 = /\\d{3}/, //     000 - 999\n    match4 = /\\d{4}/, //    0000 - 9999\n    match6 = /[+-]?\\d{6}/, // -999999 - 999999\n    match1to2 = /\\d\\d?/, //       0 - 99\n    match3to4 = /\\d\\d\\d\\d?/, //     999 - 9999\n    match5to6 = /\\d\\d\\d\\d\\d\\d?/, //   99999 - 999999\n    match1to3 = /\\d{1,3}/, //       0 - 999\n    match1to4 = /\\d{1,4}/, //       0 - 9999\n    match1to6 = /[+-]?\\d{1,6}/, // -999999 - 999999\n    matchUnsigned = /\\d+/, //       0 - inf\n    matchSigned = /[+-]?\\d+/, //    -inf - inf\n    matchOffset = /Z|[+-]\\d\\d:?\\d\\d/gi, // +00:00 -00:00 +0000 -0000 or Z\n    matchShortOffset = /Z|[+-]\\d\\d(?::?\\d\\d)?/gi, // +00 -00 +00:00 -00:00 +0000 -0000 or Z\n    matchTimestamp = /[+-]?\\d+(\\.\\d{1,3})?/, // 123456789 123456789.123\n    // any word (or two) characters or numbers including two/three word month in arabic.\n    // includes scottish gaelic two word and hyphenated months\n    matchWord =\n        /[0-9]{0,256}['a-z\\u00A0-\\u05FF\\u0700-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFF07\\uFF10-\\uFFEF]{1,256}|[\\u0600-\\u06FF\\/]{1,256}(\\s*?[\\u0600-\\u06FF]{1,256}){1,2}/i,\n    match1to2NoLeadingZero = /^[1-9]\\d?/, //         1-99\n    match1to2HasZero = /^([1-9]\\d|\\d)/, //           0-99\n    regexes;\n\nregexes = {};\n\nfunction addRegexToken(token, regex, strictRegex) {\n    regexes[token] = isFunction(regex)\n        ? regex\n        : function (isStrict, localeData) {\n              return isStrict && strictRegex ? strictRegex : regex;\n          };\n}\n\nfunction getParseRegexForToken(token, config) {\n    if (!hasOwnProp(regexes, token)) {\n        return new RegExp(unescapeFormat(token));\n    }\n\n    return regexes[token](config._strict, config._locale);\n}\n\n// Code from http://stackoverflow.com/questions/3561493/is-there-a-regexp-escape-function-in-javascript\nfunction unescapeFormat(s) {\n    return regexEscape(\n        s\n            .replace('\\\\', '')\n            .replace(\n                /\\\\(\\[)|\\\\(\\])|\\[([^\\]\\[]*)\\]|\\\\(.)/g,\n                function (matched, p1, p2, p3, p4) {\n                    return p1 || p2 || p3 || p4;\n                }\n            )\n    );\n}\n\nfunction regexEscape(s) {\n    return s.replace(/[-\\/\\\\^$*+?.()|[\\]{}]/g, '\\\\$&');\n}\n\nfunction absFloor(number) {\n    if (number < 0) {\n        // -0 -> 0\n        return Math.ceil(number) || 0;\n    } else {\n        return Math.floor(number);\n    }\n}\n\nfunction toInt(argumentForCoercion) {\n    var coercedNumber = +argumentForCoercion,\n        value = 0;\n\n    if (coercedNumber !== 0 && isFinite(coercedNumber)) {\n        value = absFloor(coercedNumber);\n    }\n\n    return value;\n}\n\nvar tokens = {};\n\nfunction addParseToken(token, callback) {\n    var i,\n        func = callback,\n        tokenLen;\n    if (typeof token === 'string') {\n        token = [token];\n    }\n    if (isNumber(callback)) {\n        func = function (input, array) {\n            array[callback] = toInt(input);\n        };\n    }\n    tokenLen = token.length;\n    for (i = 0; i < tokenLen; i++) {\n        tokens[token[i]] = func;\n    }\n}\n\nfunction addWeekParseToken(token, callback) {\n    addParseToken(token, function (input, array, config, token) {\n        config._w = config._w || {};\n        callback(input, config._w, config, token);\n    });\n}\n\nfunction addTimeToArrayFromToken(token, input, config) {\n    if (input != null && hasOwnProp(tokens, token)) {\n        tokens[token](input, config._a, config, token);\n    }\n}\n\nfunction isLeapYear(year) {\n    return (year % 4 === 0 && year % 100 !== 0) || year % 400 === 0;\n}\n\nvar YEAR = 0,\n    MONTH = 1,\n    DATE = 2,\n    HOUR = 3,\n    MINUTE = 4,\n    SECOND = 5,\n    MILLISECOND = 6,\n    WEEK = 7,\n    WEEKDAY = 8;\n\n// FORMATTING\n\naddFormatToken('Y', 0, 0, function () {\n    var y = this.year();\n    return y <= 9999 ? zeroFill(y, 4) : '+' + y;\n});\n\naddFormatToken(0, ['YY', 2], 0, function () {\n    return this.year() % 100;\n});\n\naddFormatToken(0, ['YYYY', 4], 0, 'year');\naddFormatToken(0, ['YYYYY', 5], 0, 'year');\naddFormatToken(0, ['YYYYYY', 6, true], 0, 'year');\n\n// PARSING\n\naddRegexToken('Y', matchSigned);\naddRegexToken('YY', match1to2, match2);\naddRegexToken('YYYY', match1to4, match4);\naddRegexToken('YYYYY', match1to6, match6);\naddRegexToken('YYYYYY', match1to6, match6);\n\naddParseToken(['YYYYY', 'YYYYYY'], YEAR);\naddParseToken('YYYY', function (input, array) {\n    array[YEAR] =\n        input.length === 2 ? hooks.parseTwoDigitYear(input) : toInt(input);\n});\naddParseToken('YY', function (input, array) {\n    array[YEAR] = hooks.parseTwoDigitYear(input);\n});\naddParseToken('Y', function (input, array) {\n    array[YEAR] = parseInt(input, 10);\n});\n\n// HELPERS\n\nfunction daysInYear(year) {\n    return isLeapYear(year) ? 366 : 365;\n}\n\n// HOOKS\n\nhooks.parseTwoDigitYear = function (input) {\n    return toInt(input) + (toInt(input) > 68 ? 1900 : 2000);\n};\n\n// MOMENTS\n\nvar getSetYear = makeGetSet('FullYear', true);\n\nfunction getIsLeapYear() {\n    return isLeapYear(this.year());\n}\n\nfunction makeGetSet(unit, keepTime) {\n    return function (value) {\n        if (value != null) {\n            set$1(this, unit, value);\n            hooks.updateOffset(this, keepTime);\n            return this;\n        } else {\n            return get(this, unit);\n        }\n    };\n}\n\nfunction get(mom, unit) {\n    if (!mom.isValid()) {\n        return NaN;\n    }\n\n    var d = mom._d,\n        isUTC = mom._isUTC;\n\n    switch (unit) {\n        case 'Milliseconds':\n            return isUTC ? d.getUTCMilliseconds() : d.getMilliseconds();\n        case 'Seconds':\n            return isUTC ? d.getUTCSeconds() : d.getSeconds();\n        case 'Minutes':\n            return isUTC ? d.getUTCMinutes() : d.getMinutes();\n        case 'Hours':\n            return isUTC ? d.getUTCHours() : d.getHours();\n        case 'Date':\n            return isUTC ? d.getUTCDate() : d.getDate();\n        case 'Day':\n            return isUTC ? d.getUTCDay() : d.getDay();\n        case 'Month':\n            return isUTC ? d.getUTCMonth() : d.getMonth();\n        case 'FullYear':\n            return isUTC ? d.getUTCFullYear() : d.getFullYear();\n        default:\n            return NaN; // Just in case\n    }\n}\n\nfunction set$1(mom, unit, value) {\n    var d, isUTC, year, month, date;\n\n    if (!mom.isValid() || isNaN(value)) {\n        return;\n    }\n\n    d = mom._d;\n    isUTC = mom._isUTC;\n\n    switch (unit) {\n        case 'Milliseconds':\n            return void (isUTC\n                ? d.setUTCMilliseconds(value)\n                : d.setMilliseconds(value));\n        case 'Seconds':\n            return void (isUTC ? d.setUTCSeconds(value) : d.setSeconds(value));\n        case 'Minutes':\n            return void (isUTC ? d.setUTCMinutes(value) : d.setMinutes(value));\n        case 'Hours':\n            return void (isUTC ? d.setUTCHours(value) : d.setHours(value));\n        case 'Date':\n            return void (isUTC ? d.setUTCDate(value) : d.setDate(value));\n        // case 'Day': // Not real\n        //    return void (isUTC ? d.setUTCDay(value) : d.setDay(value));\n        // case 'Month': // Not used because we need to pass two variables\n        //     return void (isUTC ? d.setUTCMonth(value) : d.setMonth(value));\n        case 'FullYear':\n            break; // See below ...\n        default:\n            return; // Just in case\n    }\n\n    year = value;\n    month = mom.month();\n    date = mom.date();\n    date = date === 29 && month === 1 && !isLeapYear(year) ? 28 : date;\n    void (isUTC\n        ? d.setUTCFullYear(year, month, date)\n        : d.setFullYear(year, month, date));\n}\n\n// MOMENTS\n\nfunction stringGet(units) {\n    units = normalizeUnits(units);\n    if (isFunction(this[units])) {\n        return this[units]();\n    }\n    return this;\n}\n\nfunction stringSet(units, value) {\n    if (typeof units === 'object') {\n        units = normalizeObjectUnits(units);\n        var prioritized = getPrioritizedUnits(units),\n            i,\n            prioritizedLen = prioritized.length;\n        for (i = 0; i < prioritizedLen; i++) {\n            this[prioritized[i].unit](units[prioritized[i].unit]);\n        }\n    } else {\n        units = normalizeUnits(units);\n        if (isFunction(this[units])) {\n            return this[units](value);\n        }\n    }\n    return this;\n}\n\nfunction mod(n, x) {\n    return ((n % x) + x) % x;\n}\n\nvar indexOf;\n\nif (Array.prototype.indexOf) {\n    indexOf = Array.prototype.indexOf;\n} else {\n    indexOf = function (o) {\n        // I know\n        var i;\n        for (i = 0; i < this.length; ++i) {\n            if (this[i] === o) {\n                return i;\n            }\n        }\n        return -1;\n    };\n}\n\nfunction daysInMonth(year, month) {\n    if (isNaN(year) || isNaN(month)) {\n        return NaN;\n    }\n    var modMonth = mod(month, 12);\n    year += (month - modMonth) / 12;\n    return modMonth === 1\n        ? isLeapYear(year)\n            ? 29\n            : 28\n        : 31 - ((modMonth % 7) % 2);\n}\n\n// FORMATTING\n\naddFormatToken('M', ['MM', 2], 'Mo', function () {\n    return this.month() + 1;\n});\n\naddFormatToken('MMM', 0, 0, function (format) {\n    return this.localeData().monthsShort(this, format);\n});\n\naddFormatToken('MMMM', 0, 0, function (format) {\n    return this.localeData().months(this, format);\n});\n\n// PARSING\n\naddRegexToken('M', match1to2, match1to2NoLeadingZero);\naddRegexToken('MM', match1to2, match2);\naddRegexToken('MMM', function (isStrict, locale) {\n    return locale.monthsShortRegex(isStrict);\n});\naddRegexToken('MMMM', function (isStrict, locale) {\n    return locale.monthsRegex(isStrict);\n});\n\naddParseToken(['M', 'MM'], function (input, array) {\n    array[MONTH] = toInt(input) - 1;\n});\n\naddParseToken(['MMM', 'MMMM'], function (input, array, config, token) {\n    var month = config._locale.monthsParse(input, token, config._strict);\n    // if we didn't find a month name, mark the date as invalid.\n    if (month != null) {\n        array[MONTH] = month;\n    } else {\n        getParsingFlags(config).invalidMonth = input;\n    }\n});\n\n// LOCALES\n\nvar defaultLocaleMonths =\n        'January_February_March_April_May_June_July_August_September_October_November_December'.split(\n            '_'\n        ),\n    defaultLocaleMonthsShort =\n        'Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec'.split('_'),\n    MONTHS_IN_FORMAT = /D[oD]?(\\[[^\\[\\]]*\\]|\\s)+MMMM?/,\n    defaultMonthsShortRegex = matchWord,\n    defaultMonthsRegex = matchWord;\n\nfunction localeMonths(m, format) {\n    if (!m) {\n        return isArray(this._months)\n            ? this._months\n            : this._months['standalone'];\n    }\n    return isArray(this._months)\n        ? this._months[m.month()]\n        : this._months[\n              (this._months.isFormat || MONTHS_IN_FORMAT).test(format)\n                  ? 'format'\n                  : 'standalone'\n          ][m.month()];\n}\n\nfunction localeMonthsShort(m, format) {\n    if (!m) {\n        return isArray(this._monthsShort)\n            ? this._monthsShort\n            : this._monthsShort['standalone'];\n    }\n    return isArray(this._monthsShort)\n        ? this._monthsShort[m.month()]\n        : this._monthsShort[\n              MONTHS_IN_FORMAT.test(format) ? 'format' : 'standalone'\n          ][m.month()];\n}\n\nfunction handleStrictParse(monthName, format, strict) {\n    var i,\n        ii,\n        mom,\n        llc = monthName.toLocaleLowerCase();\n    if (!this._monthsParse) {\n        // this is not used\n        this._monthsParse = [];\n        this._longMonthsParse = [];\n        this._shortMonthsParse = [];\n        for (i = 0; i < 12; ++i) {\n            mom = createUTC([2000, i]);\n            this._shortMonthsParse[i] = this.monthsShort(\n                mom,\n                ''\n            ).toLocaleLowerCase();\n            this._longMonthsParse[i] = this.months(mom, '').toLocaleLowerCase();\n        }\n    }\n\n    if (strict) {\n        if (format === 'MMM') {\n            ii = indexOf.call(this._shortMonthsParse, llc);\n            return ii !== -1 ? ii : null;\n        } else {\n            ii = indexOf.call(this._longMonthsParse, llc);\n            return ii !== -1 ? ii : null;\n        }\n    } else {\n        if (format === 'MMM') {\n            ii = indexOf.call(this._shortMonthsParse, llc);\n            if (ii !== -1) {\n                return ii;\n            }\n            ii = indexOf.call(this._longMonthsParse, llc);\n            return ii !== -1 ? ii : null;\n        } else {\n            ii = indexOf.call(this._longMonthsParse, llc);\n            if (ii !== -1) {\n                return ii;\n            }\n            ii = indexOf.call(this._shortMonthsParse, llc);\n            return ii !== -1 ? ii : null;\n        }\n    }\n}\n\nfunction localeMonthsParse(monthName, format, strict) {\n    var i, mom, regex;\n\n    if (this._monthsParseExact) {\n        return handleStrictParse.call(this, monthName, format, strict);\n    }\n\n    if (!this._monthsParse) {\n        this._monthsParse = [];\n        this._longMonthsParse = [];\n        this._shortMonthsParse = [];\n    }\n\n    // TODO: add sorting\n    // Sorting makes sure if one month (or abbr) is a prefix of another\n    // see sorting in computeMonthsParse\n    for (i = 0; i < 12; i++) {\n        // make the regex if we don't have it already\n        mom = createUTC([2000, i]);\n        if (strict && !this._longMonthsParse[i]) {\n            this._longMonthsParse[i] = new RegExp(\n                '^' + this.months(mom, '').replace('.', '') + '$',\n                'i'\n            );\n            this._shortMonthsParse[i] = new RegExp(\n                '^' + this.monthsShort(mom, '').replace('.', '') + '$',\n                'i'\n            );\n        }\n        if (!strict && !this._monthsParse[i]) {\n            regex =\n                '^' + this.months(mom, '') + '|^' + this.monthsShort(mom, '');\n            this._monthsParse[i] = new RegExp(regex.replace('.', ''), 'i');\n        }\n        // test the regex\n        if (\n            strict &&\n            format === 'MMMM' &&\n            this._longMonthsParse[i].test(monthName)\n        ) {\n            return i;\n        } else if (\n            strict &&\n            format === 'MMM' &&\n            this._shortMonthsParse[i].test(monthName)\n        ) {\n            return i;\n        } else if (!strict && this._monthsParse[i].test(monthName)) {\n            return i;\n        }\n    }\n}\n\n// MOMENTS\n\nfunction setMonth(mom, value) {\n    if (!mom.isValid()) {\n        // No op\n        return mom;\n    }\n\n    if (typeof value === 'string') {\n        if (/^\\d+$/.test(value)) {\n            value = toInt(value);\n        } else {\n            value = mom.localeData().monthsParse(value);\n            // TODO: Another silent failure?\n            if (!isNumber(value)) {\n                return mom;\n            }\n        }\n    }\n\n    var month = value,\n        date = mom.date();\n\n    date = date < 29 ? date : Math.min(date, daysInMonth(mom.year(), month));\n    void (mom._isUTC\n        ? mom._d.setUTCMonth(month, date)\n        : mom._d.setMonth(month, date));\n    return mom;\n}\n\nfunction getSetMonth(value) {\n    if (value != null) {\n        setMonth(this, value);\n        hooks.updateOffset(this, true);\n        return this;\n    } else {\n        return get(this, 'Month');\n    }\n}\n\nfunction getDaysInMonth() {\n    return daysInMonth(this.year(), this.month());\n}\n\nfunction monthsShortRegex(isStrict) {\n    if (this._monthsParseExact) {\n        if (!hasOwnProp(this, '_monthsRegex')) {\n            computeMonthsParse.call(this);\n        }\n        if (isStrict) {\n            return this._monthsShortStrictRegex;\n        } else {\n            return this._monthsShortRegex;\n        }\n    } else {\n        if (!hasOwnProp(this, '_monthsShortRegex')) {\n            this._monthsShortRegex = defaultMonthsShortRegex;\n        }\n        return this._monthsShortStrictRegex && isStrict\n            ? this._monthsShortStrictRegex\n            : this._monthsShortRegex;\n    }\n}\n\nfunction monthsRegex(isStrict) {\n    if (this._monthsParseExact) {\n        if (!hasOwnProp(this, '_monthsRegex')) {\n            computeMonthsParse.call(this);\n        }\n        if (isStrict) {\n            return this._monthsStrictRegex;\n        } else {\n            return this._monthsRegex;\n        }\n    } else {\n        if (!hasOwnProp(this, '_monthsRegex')) {\n            this._monthsRegex = defaultMonthsRegex;\n        }\n        return this._monthsStrictRegex && isStrict\n            ? this._monthsStrictRegex\n            : this._monthsRegex;\n    }\n}\n\nfunction computeMonthsParse() {\n    function cmpLenRev(a, b) {\n        return b.length - a.length;\n    }\n\n    var shortPieces = [],\n        longPieces = [],\n        mixedPieces = [],\n        i,\n        mom,\n        shortP,\n        longP;\n    for (i = 0; i < 12; i++) {\n        // make the regex if we don't have it already\n        mom = createUTC([2000, i]);\n        shortP = regexEscape(this.monthsShort(mom, ''));\n        longP = regexEscape(this.months(mom, ''));\n        shortPieces.push(shortP);\n        longPieces.push(longP);\n        mixedPieces.push(longP);\n        mixedPieces.push(shortP);\n    }\n    // Sorting makes sure if one month (or abbr) is a prefix of another it\n    // will match the longer piece.\n    shortPieces.sort(cmpLenRev);\n    longPieces.sort(cmpLenRev);\n    mixedPieces.sort(cmpLenRev);\n\n    this._monthsRegex = new RegExp('^(' + mixedPieces.join('|') + ')', 'i');\n    this._monthsShortRegex = this._monthsRegex;\n    this._monthsStrictRegex = new RegExp(\n        '^(' + longPieces.join('|') + ')',\n        'i'\n    );\n    this._monthsShortStrictRegex = new RegExp(\n        '^(' + shortPieces.join('|') + ')',\n        'i'\n    );\n}\n\nfunction createDate(y, m, d, h, M, s, ms) {\n    // can't just apply() to create a date:\n    // https://stackoverflow.com/q/181348\n    var date;\n    // the date constructor remaps years 0-99 to 1900-1999\n    if (y < 100 && y >= 0) {\n        // preserve leap years using a full 400 year cycle, then reset\n        date = new Date(y + 400, m, d, h, M, s, ms);\n        if (isFinite(date.getFullYear())) {\n            date.setFullYear(y);\n        }\n    } else {\n        date = new Date(y, m, d, h, M, s, ms);\n    }\n\n    return date;\n}\n\nfunction createUTCDate(y) {\n    var date, args;\n    // the Date.UTC function remaps years 0-99 to 1900-1999\n    if (y < 100 && y >= 0) {\n        args = Array.prototype.slice.call(arguments);\n        // preserve leap years using a full 400 year cycle, then reset\n        args[0] = y + 400;\n        date = new Date(Date.UTC.apply(null, args));\n        if (isFinite(date.getUTCFullYear())) {\n            date.setUTCFullYear(y);\n        }\n    } else {\n        date = new Date(Date.UTC.apply(null, arguments));\n    }\n\n    return date;\n}\n\n// start-of-first-week - start-of-year\nfunction firstWeekOffset(year, dow, doy) {\n    var // first-week day -- which january is always in the first week (4 for iso, 1 for other)\n        fwd = 7 + dow - doy,\n        // first-week day local weekday -- which local weekday is fwd\n        fwdlw = (7 + createUTCDate(year, 0, fwd).getUTCDay() - dow) % 7;\n\n    return -fwdlw + fwd - 1;\n}\n\n// https://en.wikipedia.org/wiki/ISO_week_date#Calculating_a_date_given_the_year.2C_week_number_and_weekday\nfunction dayOfYearFromWeeks(year, week, weekday, dow, doy) {\n    var localWeekday = (7 + weekday - dow) % 7,\n        weekOffset = firstWeekOffset(year, dow, doy),\n        dayOfYear = 1 + 7 * (week - 1) + localWeekday + weekOffset,\n        resYear,\n        resDayOfYear;\n\n    if (dayOfYear <= 0) {\n        resYear = year - 1;\n        resDayOfYear = daysInYear(resYear) + dayOfYear;\n    } else if (dayOfYear > daysInYear(year)) {\n        resYear = year + 1;\n        resDayOfYear = dayOfYear - daysInYear(year);\n    } else {\n        resYear = year;\n        resDayOfYear = dayOfYear;\n    }\n\n    return {\n        year: resYear,\n        dayOfYear: resDayOfYear,\n    };\n}\n\nfunction weekOfYear(mom, dow, doy) {\n    var weekOffset = firstWeekOffset(mom.year(), dow, doy),\n        week = Math.floor((mom.dayOfYear() - weekOffset - 1) / 7) + 1,\n        resWeek,\n        resYear;\n\n    if (week < 1) {\n        resYear = mom.year() - 1;\n        resWeek = week + weeksInYear(resYear, dow, doy);\n    } else if (week > weeksInYear(mom.year(), dow, doy)) {\n        resWeek = week - weeksInYear(mom.year(), dow, doy);\n        resYear = mom.year() + 1;\n    } else {\n        resYear = mom.year();\n        resWeek = week;\n    }\n\n    return {\n        week: resWeek,\n        year: resYear,\n    };\n}\n\nfunction weeksInYear(year, dow, doy) {\n    var weekOffset = firstWeekOffset(year, dow, doy),\n        weekOffsetNext = firstWeekOffset(year + 1, dow, doy);\n    return (daysInYear(year) - weekOffset + weekOffsetNext) / 7;\n}\n\n// FORMATTING\n\naddFormatToken('w', ['ww', 2], 'wo', 'week');\naddFormatToken('W', ['WW', 2], 'Wo', 'isoWeek');\n\n// PARSING\n\naddRegexToken('w', match1to2, match1to2NoLeadingZero);\naddRegexToken('ww', match1to2, match2);\naddRegexToken('W', match1to2, match1to2NoLeadingZero);\naddRegexToken('WW', match1to2, match2);\n\naddWeekParseToken(\n    ['w', 'ww', 'W', 'WW'],\n    function (input, week, config, token) {\n        week[token.substr(0, 1)] = toInt(input);\n    }\n);\n\n// HELPERS\n\n// LOCALES\n\nfunction localeWeek(mom) {\n    return weekOfYear(mom, this._week.dow, this._week.doy).week;\n}\n\nvar defaultLocaleWeek = {\n    dow: 0, // Sunday is the first day of the week.\n    doy: 6, // The week that contains Jan 6th is the first week of the year.\n};\n\nfunction localeFirstDayOfWeek() {\n    return this._week.dow;\n}\n\nfunction localeFirstDayOfYear() {\n    return this._week.doy;\n}\n\n// MOMENTS\n\nfunction getSetWeek(input) {\n    var week = this.localeData().week(this);\n    return input == null ? week : this.add((input - week) * 7, 'd');\n}\n\nfunction getSetISOWeek(input) {\n    var week = weekOfYear(this, 1, 4).week;\n    return input == null ? week : this.add((input - week) * 7, 'd');\n}\n\n// FORMATTING\n\naddFormatToken('d', 0, 'do', 'day');\n\naddFormatToken('dd', 0, 0, function (format) {\n    return this.localeData().weekdaysMin(this, format);\n});\n\naddFormatToken('ddd', 0, 0, function (format) {\n    return this.localeData().weekdaysShort(this, format);\n});\n\naddFormatToken('dddd', 0, 0, function (format) {\n    return this.localeData().weekdays(this, format);\n});\n\naddFormatToken('e', 0, 0, 'weekday');\naddFormatToken('E', 0, 0, 'isoWeekday');\n\n// PARSING\n\naddRegexToken('d', match1to2);\naddRegexToken('e', match1to2);\naddRegexToken('E', match1to2);\naddRegexToken('dd', function (isStrict, locale) {\n    return locale.weekdaysMinRegex(isStrict);\n});\naddRegexToken('ddd', function (isStrict, locale) {\n    return locale.weekdaysShortRegex(isStrict);\n});\naddRegexToken('dddd', function (isStrict, locale) {\n    return locale.weekdaysRegex(isStrict);\n});\n\naddWeekParseToken(['dd', 'ddd', 'dddd'], function (input, week, config, token) {\n    var weekday = config._locale.weekdaysParse(input, token, config._strict);\n    // if we didn't get a weekday name, mark the date as invalid\n    if (weekday != null) {\n        week.d = weekday;\n    } else {\n        getParsingFlags(config).invalidWeekday = input;\n    }\n});\n\naddWeekParseToken(['d', 'e', 'E'], function (input, week, config, token) {\n    week[token] = toInt(input);\n});\n\n// HELPERS\n\nfunction parseWeekday(input, locale) {\n    if (typeof input !== 'string') {\n        return input;\n    }\n\n    if (!isNaN(input)) {\n        return parseInt(input, 10);\n    }\n\n    input = locale.weekdaysParse(input);\n    if (typeof input === 'number') {\n        return input;\n    }\n\n    return null;\n}\n\nfunction parseIsoWeekday(input, locale) {\n    if (typeof input === 'string') {\n        return locale.weekdaysParse(input) % 7 || 7;\n    }\n    return isNaN(input) ? null : input;\n}\n\n// LOCALES\nfunction shiftWeekdays(ws, n) {\n    return ws.slice(n, 7).concat(ws.slice(0, n));\n}\n\nvar defaultLocaleWeekdays =\n        'Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday'.split('_'),\n    defaultLocaleWeekdaysShort = 'Sun_Mon_Tue_Wed_Thu_Fri_Sat'.split('_'),\n    defaultLocaleWeekdaysMin = 'Su_Mo_Tu_We_Th_Fr_Sa'.split('_'),\n    defaultWeekdaysRegex = matchWord,\n    defaultWeekdaysShortRegex = matchWord,\n    defaultWeekdaysMinRegex = matchWord;\n\nfunction localeWeekdays(m, format) {\n    var weekdays = isArray(this._weekdays)\n        ? this._weekdays\n        : this._weekdays[\n              m && m !== true && this._weekdays.isFormat.test(format)\n                  ? 'format'\n                  : 'standalone'\n          ];\n    return m === true\n        ? shiftWeekdays(weekdays, this._week.dow)\n        : m\n          ? weekdays[m.day()]\n          : weekdays;\n}\n\nfunction localeWeekdaysShort(m) {\n    return m === true\n        ? shiftWeekdays(this._weekdaysShort, this._week.dow)\n        : m\n          ? this._weekdaysShort[m.day()]\n          : this._weekdaysShort;\n}\n\nfunction localeWeekdaysMin(m) {\n    return m === true\n        ? shiftWeekdays(this._weekdaysMin, this._week.dow)\n        : m\n          ? this._weekdaysMin[m.day()]\n          : this._weekdaysMin;\n}\n\nfunction handleStrictParse$1(weekdayName, format, strict) {\n    var i,\n        ii,\n        mom,\n        llc = weekdayName.toLocaleLowerCase();\n    if (!this._weekdaysParse) {\n        this._weekdaysParse = [];\n        this._shortWeekdaysParse = [];\n        this._minWeekdaysParse = [];\n\n        for (i = 0; i < 7; ++i) {\n            mom = createUTC([2000, 1]).day(i);\n            this._minWeekdaysParse[i] = this.weekdaysMin(\n                mom,\n                ''\n            ).toLocaleLowerCase();\n            this._shortWeekdaysParse[i] = this.weekdaysShort(\n                mom,\n                ''\n            ).toLocaleLowerCase();\n            this._weekdaysParse[i] = this.weekdays(mom, '').toLocaleLowerCase();\n        }\n    }\n\n    if (strict) {\n        if (format === 'dddd') {\n            ii = indexOf.call(this._weekdaysParse, llc);\n            return ii !== -1 ? ii : null;\n        } else if (format === 'ddd') {\n            ii = indexOf.call(this._shortWeekdaysParse, llc);\n            return ii !== -1 ? ii : null;\n        } else {\n            ii = indexOf.call(this._minWeekdaysParse, llc);\n            return ii !== -1 ? ii : null;\n        }\n    } else {\n        if (format === 'dddd') {\n            ii = indexOf.call(this._weekdaysParse, llc);\n            if (ii !== -1) {\n                return ii;\n            }\n            ii = indexOf.call(this._shortWeekdaysParse, llc);\n            if (ii !== -1) {\n                return ii;\n            }\n            ii = indexOf.call(this._minWeekdaysParse, llc);\n            return ii !== -1 ? ii : null;\n        } else if (format === 'ddd') {\n            ii = indexOf.call(this._shortWeekdaysParse, llc);\n            if (ii !== -1) {\n                return ii;\n            }\n            ii = indexOf.call(this._weekdaysParse, llc);\n            if (ii !== -1) {\n                return ii;\n            }\n            ii = indexOf.call(this._minWeekdaysParse, llc);\n            return ii !== -1 ? ii : null;\n        } else {\n            ii = indexOf.call(this._minWeekdaysParse, llc);\n            if (ii !== -1) {\n                return ii;\n            }\n            ii = indexOf.call(this._weekdaysParse, llc);\n            if (ii !== -1) {\n                return ii;\n            }\n            ii = indexOf.call(this._shortWeekdaysParse, llc);\n            return ii !== -1 ? ii : null;\n        }\n    }\n}\n\nfunction localeWeekdaysParse(weekdayName, format, strict) {\n    var i, mom, regex;\n\n    if (this._weekdaysParseExact) {\n        return handleStrictParse$1.call(this, weekdayName, format, strict);\n    }\n\n    if (!this._weekdaysParse) {\n        this._weekdaysParse = [];\n        this._minWeekdaysParse = [];\n        this._shortWeekdaysParse = [];\n        this._fullWeekdaysParse = [];\n    }\n\n    for (i = 0; i < 7; i++) {\n        // make the regex if we don't have it already\n\n        mom = createUTC([2000, 1]).day(i);\n        if (strict && !this._fullWeekdaysParse[i]) {\n            this._fullWeekdaysParse[i] = new RegExp(\n                '^' + this.weekdays(mom, '').replace('.', '\\\\.?') + '$',\n                'i'\n            );\n            this._shortWeekdaysParse[i] = new RegExp(\n                '^' + this.weekdaysShort(mom, '').replace('.', '\\\\.?') + '$',\n                'i'\n            );\n            this._minWeekdaysParse[i] = new RegExp(\n                '^' + this.weekdaysMin(mom, '').replace('.', '\\\\.?') + '$',\n                'i'\n            );\n        }\n        if (!this._weekdaysParse[i]) {\n            regex =\n                '^' +\n                this.weekdays(mom, '') +\n                '|^' +\n                this.weekdaysShort(mom, '') +\n                '|^' +\n                this.weekdaysMin(mom, '');\n            this._weekdaysParse[i] = new RegExp(regex.replace('.', ''), 'i');\n        }\n        // test the regex\n        if (\n            strict &&\n            format === 'dddd' &&\n            this._fullWeekdaysParse[i].test(weekdayName)\n        ) {\n            return i;\n        } else if (\n            strict &&\n            format === 'ddd' &&\n            this._shortWeekdaysParse[i].test(weekdayName)\n        ) {\n            return i;\n        } else if (\n            strict &&\n            format === 'dd' &&\n            this._minWeekdaysParse[i].test(weekdayName)\n        ) {\n            return i;\n        } else if (!strict && this._weekdaysParse[i].test(weekdayName)) {\n            return i;\n        }\n    }\n}\n\n// MOMENTS\n\nfunction getSetDayOfWeek(input) {\n    if (!this.isValid()) {\n        return input != null ? this : NaN;\n    }\n\n    var day = get(this, 'Day');\n    if (input != null) {\n        input = parseWeekday(input, this.localeData());\n        return this.add(input - day, 'd');\n    } else {\n        return day;\n    }\n}\n\nfunction getSetLocaleDayOfWeek(input) {\n    if (!this.isValid()) {\n        return input != null ? this : NaN;\n    }\n    var weekday = (this.day() + 7 - this.localeData()._week.dow) % 7;\n    return input == null ? weekday : this.add(input - weekday, 'd');\n}\n\nfunction getSetISODayOfWeek(input) {\n    if (!this.isValid()) {\n        return input != null ? this : NaN;\n    }\n\n    // behaves the same as moment#day except\n    // as a getter, returns 7 instead of 0 (1-7 range instead of 0-6)\n    // as a setter, sunday should belong to the previous week.\n\n    if (input != null) {\n        var weekday = parseIsoWeekday(input, this.localeData());\n        return this.day(this.day() % 7 ? weekday : weekday - 7);\n    } else {\n        return this.day() || 7;\n    }\n}\n\nfunction weekdaysRegex(isStrict) {\n    if (this._weekdaysParseExact) {\n        if (!hasOwnProp(this, '_weekdaysRegex')) {\n            computeWeekdaysParse.call(this);\n        }\n        if (isStrict) {\n            return this._weekdaysStrictRegex;\n        } else {\n            return this._weekdaysRegex;\n        }\n    } else {\n        if (!hasOwnProp(this, '_weekdaysRegex')) {\n            this._weekdaysRegex = defaultWeekdaysRegex;\n        }\n        return this._weekdaysStrictRegex && isStrict\n            ? this._weekdaysStrictRegex\n            : this._weekdaysRegex;\n    }\n}\n\nfunction weekdaysShortRegex(isStrict) {\n    if (this._weekdaysParseExact) {\n        if (!hasOwnProp(this, '_weekdaysRegex')) {\n            computeWeekdaysParse.call(this);\n        }\n        if (isStrict) {\n            return this._weekdaysShortStrictRegex;\n        } else {\n            return this._weekdaysShortRegex;\n        }\n    } else {\n        if (!hasOwnProp(this, '_weekdaysShortRegex')) {\n            this._weekdaysShortRegex = defaultWeekdaysShortRegex;\n        }\n        return this._weekdaysShortStrictRegex && isStrict\n            ? this._weekdaysShortStrictRegex\n            : this._weekdaysShortRegex;\n    }\n}\n\nfunction weekdaysMinRegex(isStrict) {\n    if (this._weekdaysParseExact) {\n        if (!hasOwnProp(this, '_weekdaysRegex')) {\n            computeWeekdaysParse.call(this);\n        }\n        if (isStrict) {\n            return this._weekdaysMinStrictRegex;\n        } else {\n            return this._weekdaysMinRegex;\n        }\n    } else {\n        if (!hasOwnProp(this, '_weekdaysMinRegex')) {\n            this._weekdaysMinRegex = defaultWeekdaysMinRegex;\n        }\n        return this._weekdaysMinStrictRegex && isStrict\n            ? this._weekdaysMinStrictRegex\n            : this._weekdaysMinRegex;\n    }\n}\n\nfunction computeWeekdaysParse() {\n    function cmpLenRev(a, b) {\n        return b.length - a.length;\n    }\n\n    var minPieces = [],\n        shortPieces = [],\n        longPieces = [],\n        mixedPieces = [],\n        i,\n        mom,\n        minp,\n        shortp,\n        longp;\n    for (i = 0; i < 7; i++) {\n        // make the regex if we don't have it already\n        mom = createUTC([2000, 1]).day(i);\n        minp = regexEscape(this.weekdaysMin(mom, ''));\n        shortp = regexEscape(this.weekdaysShort(mom, ''));\n        longp = regexEscape(this.weekdays(mom, ''));\n        minPieces.push(minp);\n        shortPieces.push(shortp);\n        longPieces.push(longp);\n        mixedPieces.push(minp);\n        mixedPieces.push(shortp);\n        mixedPieces.push(longp);\n    }\n    // Sorting makes sure if one weekday (or abbr) is a prefix of another it\n    // will match the longer piece.\n    minPieces.sort(cmpLenRev);\n    shortPieces.sort(cmpLenRev);\n    longPieces.sort(cmpLenRev);\n    mixedPieces.sort(cmpLenRev);\n\n    this._weekdaysRegex = new RegExp('^(' + mixedPieces.join('|') + ')', 'i');\n    this._weekdaysShortRegex = this._weekdaysRegex;\n    this._weekdaysMinRegex = this._weekdaysRegex;\n\n    this._weekdaysStrictRegex = new RegExp(\n        '^(' + longPieces.join('|') + ')',\n        'i'\n    );\n    this._weekdaysShortStrictRegex = new RegExp(\n        '^(' + shortPieces.join('|') + ')',\n        'i'\n    );\n    this._weekdaysMinStrictRegex = new RegExp(\n        '^(' + minPieces.join('|') + ')',\n        'i'\n    );\n}\n\n// FORMATTING\n\nfunction hFormat() {\n    return this.hours() % 12 || 12;\n}\n\nfunction kFormat() {\n    return this.hours() || 24;\n}\n\naddFormatToken('H', ['HH', 2], 0, 'hour');\naddFormatToken('h', ['hh', 2], 0, hFormat);\naddFormatToken('k', ['kk', 2], 0, kFormat);\n\naddFormatToken('hmm', 0, 0, function () {\n    return '' + hFormat.apply(this) + zeroFill(this.minutes(), 2);\n});\n\naddFormatToken('hmmss', 0, 0, function () {\n    return (\n        '' +\n        hFormat.apply(this) +\n        zeroFill(this.minutes(), 2) +\n        zeroFill(this.seconds(), 2)\n    );\n});\n\naddFormatToken('Hmm', 0, 0, function () {\n    return '' + this.hours() + zeroFill(this.minutes(), 2);\n});\n\naddFormatToken('Hmmss', 0, 0, function () {\n    return (\n        '' +\n        this.hours() +\n        zeroFill(this.minutes(), 2) +\n        zeroFill(this.seconds(), 2)\n    );\n});\n\nfunction meridiem(token, lowercase) {\n    addFormatToken(token, 0, 0, function () {\n        return this.localeData().meridiem(\n            this.hours(),\n            this.minutes(),\n            lowercase\n        );\n    });\n}\n\nmeridiem('a', true);\nmeridiem('A', false);\n\n// PARSING\n\nfunction matchMeridiem(isStrict, locale) {\n    return locale._meridiemParse;\n}\n\naddRegexToken('a', matchMeridiem);\naddRegexToken('A', matchMeridiem);\naddRegexToken('H', match1to2, match1to2HasZero);\naddRegexToken('h', match1to2, match1to2NoLeadingZero);\naddRegexToken('k', match1to2, match1to2NoLeadingZero);\naddRegexToken('HH', match1to2, match2);\naddRegexToken('hh', match1to2, match2);\naddRegexToken('kk', match1to2, match2);\n\naddRegexToken('hmm', match3to4);\naddRegexToken('hmmss', match5to6);\naddRegexToken('Hmm', match3to4);\naddRegexToken('Hmmss', match5to6);\n\naddParseToken(['H', 'HH'], HOUR);\naddParseToken(['k', 'kk'], function (input, array, config) {\n    var kInput = toInt(input);\n    array[HOUR] = kInput === 24 ? 0 : kInput;\n});\naddParseToken(['a', 'A'], function (input, array, config) {\n    config._isPm = config._locale.isPM(input);\n    config._meridiem = input;\n});\naddParseToken(['h', 'hh'], function (input, array, config) {\n    array[HOUR] = toInt(input);\n    getParsingFlags(config).bigHour = true;\n});\naddParseToken('hmm', function (input, array, config) {\n    var pos = input.length - 2;\n    array[HOUR] = toInt(input.substr(0, pos));\n    array[MINUTE] = toInt(input.substr(pos));\n    getParsingFlags(config).bigHour = true;\n});\naddParseToken('hmmss', function (input, array, config) {\n    var pos1 = input.length - 4,\n        pos2 = input.length - 2;\n    array[HOUR] = toInt(input.substr(0, pos1));\n    array[MINUTE] = toInt(input.substr(pos1, 2));\n    array[SECOND] = toInt(input.substr(pos2));\n    getParsingFlags(config).bigHour = true;\n});\naddParseToken('Hmm', function (input, array, config) {\n    var pos = input.length - 2;\n    array[HOUR] = toInt(input.substr(0, pos));\n    array[MINUTE] = toInt(input.substr(pos));\n});\naddParseToken('Hmmss', function (input, array, config) {\n    var pos1 = input.length - 4,\n        pos2 = input.length - 2;\n    array[HOUR] = toInt(input.substr(0, pos1));\n    array[MINUTE] = toInt(input.substr(pos1, 2));\n    array[SECOND] = toInt(input.substr(pos2));\n});\n\n// LOCALES\n\nfunction localeIsPM(input) {\n    // IE8 Quirks Mode & IE7 Standards Mode do not allow accessing strings like arrays\n    // Using charAt should be more compatible.\n    return (input + '').toLowerCase().charAt(0) === 'p';\n}\n\nvar defaultLocaleMeridiemParse = /[ap]\\.?m?\\.?/i,\n    // Setting the hour should keep the time, because the user explicitly\n    // specified which hour they want. So trying to maintain the same hour (in\n    // a new timezone) makes sense. Adding/subtracting hours does not follow\n    // this rule.\n    getSetHour = makeGetSet('Hours', true);\n\nfunction localeMeridiem(hours, minutes, isLower) {\n    if (hours > 11) {\n        return isLower ? 'pm' : 'PM';\n    } else {\n        return isLower ? 'am' : 'AM';\n    }\n}\n\nvar baseConfig = {\n    calendar: defaultCalendar,\n    longDateFormat: defaultLongDateFormat,\n    invalidDate: defaultInvalidDate,\n    ordinal: defaultOrdinal,\n    dayOfMonthOrdinalParse: defaultDayOfMonthOrdinalParse,\n    relativeTime: defaultRelativeTime,\n\n    months: defaultLocaleMonths,\n    monthsShort: defaultLocaleMonthsShort,\n\n    week: defaultLocaleWeek,\n\n    weekdays: defaultLocaleWeekdays,\n    weekdaysMin: defaultLocaleWeekdaysMin,\n    weekdaysShort: defaultLocaleWeekdaysShort,\n\n    meridiemParse: defaultLocaleMeridiemParse,\n};\n\n// internal storage for locale config files\nvar locales = {},\n    localeFamilies = {},\n    globalLocale;\n\nfunction commonPrefix(arr1, arr2) {\n    var i,\n        minl = Math.min(arr1.length, arr2.length);\n    for (i = 0; i < minl; i += 1) {\n        if (arr1[i] !== arr2[i]) {\n            return i;\n        }\n    }\n    return minl;\n}\n\nfunction normalizeLocale(key) {\n    return key ? key.toLowerCase().replace('_', '-') : key;\n}\n\n// pick the locale from the array\n// try ['en-au', 'en-gb'] as 'en-au', 'en-gb', 'en', as in move through the list trying each\n// substring from most specific to least, but move to the next array item if it's a more specific variant than the current root\nfunction chooseLocale(names) {\n    var i = 0,\n        j,\n        next,\n        locale,\n        split;\n\n    while (i < names.length) {\n        split = normalizeLocale(names[i]).split('-');\n        j = split.length;\n        next = normalizeLocale(names[i + 1]);\n        next = next ? next.split('-') : null;\n        while (j > 0) {\n            locale = loadLocale(split.slice(0, j).join('-'));\n            if (locale) {\n                return locale;\n            }\n            if (\n                next &&\n                next.length >= j &&\n                commonPrefix(split, next) >= j - 1\n            ) {\n                //the next array item is better than a shallower substring of this one\n                break;\n            }\n            j--;\n        }\n        i++;\n    }\n    return globalLocale;\n}\n\nfunction isLocaleNameSane(name) {\n    // Prevent names that look like filesystem paths, i.e contain '/' or '\\'\n    // Ensure name is available and function returns boolean\n    return !!(name && name.match('^[^/\\\\\\\\]*$'));\n}\n\nfunction loadLocale(name) {\n    var oldLocale = null,\n        aliasedRequire;\n    // TODO: Find a better way to register and load all the locales in Node\n    if (\n        locales[name] === undefined &&\n        typeof module !== 'undefined' &&\n        module &&\n        module.exports &&\n        isLocaleNameSane(name)\n    ) {\n        try {\n            oldLocale = globalLocale._abbr;\n            aliasedRequire = require;\n            aliasedRequire('./locale/' + name);\n            getSetGlobalLocale(oldLocale);\n        } catch (e) {\n            // mark as not found to avoid repeating expensive file require call causing high CPU\n            // when trying to find en-US, en_US, en-us for every format call\n            locales[name] = null; // null means not found\n        }\n    }\n    return locales[name];\n}\n\n// This function will load locale and then set the global locale.  If\n// no arguments are passed in, it will simply return the current global\n// locale key.\nfunction getSetGlobalLocale(key, values) {\n    var data;\n    if (key) {\n        if (isUndefined(values)) {\n            data = getLocale(key);\n        } else {\n            data = defineLocale(key, values);\n        }\n\n        if (data) {\n            // moment.duration._locale = moment._locale = data;\n            globalLocale = data;\n        } else {\n            if (typeof console !== 'undefined' && console.warn) {\n                //warn user if arguments are passed but the locale could not be set\n                console.warn(\n                    'Locale ' + key + ' not found. Did you forget to load it?'\n                );\n            }\n        }\n    }\n\n    return globalLocale._abbr;\n}\n\nfunction defineLocale(name, config) {\n    if (config !== null) {\n        var locale,\n            parentConfig = baseConfig;\n        config.abbr = name;\n        if (locales[name] != null) {\n            deprecateSimple(\n                'defineLocaleOverride',\n                'use moment.updateLocale(localeName, config) to change ' +\n                    'an existing locale. moment.defineLocale(localeName, ' +\n                    'config) should only be used for creating a new locale ' +\n                    'See http://momentjs.com/guides/#/warnings/define-locale/ for more info.'\n            );\n            parentConfig = locales[name]._config;\n        } else if (config.parentLocale != null) {\n            if (locales[config.parentLocale] != null) {\n                parentConfig = locales[config.parentLocale]._config;\n            } else {\n                locale = loadLocale(config.parentLocale);\n                if (locale != null) {\n                    parentConfig = locale._config;\n                } else {\n                    if (!localeFamilies[config.parentLocale]) {\n                        localeFamilies[config.parentLocale] = [];\n                    }\n                    localeFamilies[config.parentLocale].push({\n                        name: name,\n                        config: config,\n                    });\n                    return null;\n                }\n            }\n        }\n        locales[name] = new Locale(mergeConfigs(parentConfig, config));\n\n        if (localeFamilies[name]) {\n            localeFamilies[name].forEach(function (x) {\n                defineLocale(x.name, x.config);\n            });\n        }\n\n        // backwards compat for now: also set the locale\n        // make sure we set the locale AFTER all child locales have been\n        // created, so we won't end up with the child locale set.\n        getSetGlobalLocale(name);\n\n        return locales[name];\n    } else {\n        // useful for testing\n        delete locales[name];\n        return null;\n    }\n}\n\nfunction updateLocale(name, config) {\n    if (config != null) {\n        var locale,\n            tmpLocale,\n            parentConfig = baseConfig;\n\n        if (locales[name] != null && locales[name].parentLocale != null) {\n            // Update existing child locale in-place to avoid memory-leaks\n            locales[name].set(mergeConfigs(locales[name]._config, config));\n        } else {\n            // MERGE\n            tmpLocale = loadLocale(name);\n            if (tmpLocale != null) {\n                parentConfig = tmpLocale._config;\n            }\n            config = mergeConfigs(parentConfig, config);\n            if (tmpLocale == null) {\n                // updateLocale is called for creating a new locale\n                // Set abbr so it will have a name (getters return\n                // undefined otherwise).\n                config.abbr = name;\n            }\n            locale = new Locale(config);\n            locale.parentLocale = locales[name];\n            locales[name] = locale;\n        }\n\n        // backwards compat for now: also set the locale\n        getSetGlobalLocale(name);\n    } else {\n        // pass null for config to unupdate, useful for tests\n        if (locales[name] != null) {\n            if (locales[name].parentLocale != null) {\n                locales[name] = locales[name].parentLocale;\n                if (name === getSetGlobalLocale()) {\n                    getSetGlobalLocale(name);\n                }\n            } else if (locales[name] != null) {\n                delete locales[name];\n            }\n        }\n    }\n    return locales[name];\n}\n\n// returns locale data\nfunction getLocale(key) {\n    var locale;\n\n    if (key && key._locale && key._locale._abbr) {\n        key = key._locale._abbr;\n    }\n\n    if (!key) {\n        return globalLocale;\n    }\n\n    if (!isArray(key)) {\n        //short-circuit everything else\n        locale = loadLocale(key);\n        if (locale) {\n            return locale;\n        }\n        key = [key];\n    }\n\n    return chooseLocale(key);\n}\n\nfunction listLocales() {\n    return keys(locales);\n}\n\nfunction checkOverflow(m) {\n    var overflow,\n        a = m._a;\n\n    if (a && getParsingFlags(m).overflow === -2) {\n        overflow =\n            a[MONTH] < 0 || a[MONTH] > 11\n                ? MONTH\n                : a[DATE] < 1 || a[DATE] > daysInMonth(a[YEAR], a[MONTH])\n                  ? DATE\n                  : a[HOUR] < 0 ||\n                      a[HOUR] > 24 ||\n                      (a[HOUR] === 24 &&\n                          (a[MINUTE] !== 0 ||\n                              a[SECOND] !== 0 ||\n                              a[MILLISECOND] !== 0))\n                    ? HOUR\n                    : a[MINUTE] < 0 || a[MINUTE] > 59\n                      ? MINUTE\n                      : a[SECOND] < 0 || a[SECOND] > 59\n                        ? SECOND\n                        : a[MILLISECOND] < 0 || a[MILLISECOND] > 999\n                          ? MILLISECOND\n                          : -1;\n\n        if (\n            getParsingFlags(m)._overflowDayOfYear &&\n            (overflow < YEAR || overflow > DATE)\n        ) {\n            overflow = DATE;\n        }\n        if (getParsingFlags(m)._overflowWeeks && overflow === -1) {\n            overflow = WEEK;\n        }\n        if (getParsingFlags(m)._overflowWeekday && overflow === -1) {\n            overflow = WEEKDAY;\n        }\n\n        getParsingFlags(m).overflow = overflow;\n    }\n\n    return m;\n}\n\n// iso 8601 regex\n// 0000-00-00 0000-W00 or 0000-W00-0 + T + 00 or 00:00 or 00:00:00 or 00:00:00.000 + +00:00 or +0000 or +00)\nvar extendedIsoRegex =\n        /^\\s*((?:[+-]\\d{6}|\\d{4})-(?:\\d\\d-\\d\\d|W\\d\\d-\\d|W\\d\\d|\\d\\d\\d|\\d\\d))(?:(T| )(\\d\\d(?::\\d\\d(?::\\d\\d(?:[.,]\\d+)?)?)?)([+-]\\d\\d(?::?\\d\\d)?|\\s*Z)?)?$/,\n    basicIsoRegex =\n        /^\\s*((?:[+-]\\d{6}|\\d{4})(?:\\d\\d\\d\\d|W\\d\\d\\d|W\\d\\d|\\d\\d\\d|\\d\\d|))(?:(T| )(\\d\\d(?:\\d\\d(?:\\d\\d(?:[.,]\\d+)?)?)?)([+-]\\d\\d(?::?\\d\\d)?|\\s*Z)?)?$/,\n    tzRegex = /Z|[+-]\\d\\d(?::?\\d\\d)?/,\n    isoDates = [\n        ['YYYYYY-MM-DD', /[+-]\\d{6}-\\d\\d-\\d\\d/],\n        ['YYYY-MM-DD', /\\d{4}-\\d\\d-\\d\\d/],\n        ['GGGG-[W]WW-E', /\\d{4}-W\\d\\d-\\d/],\n        ['GGGG-[W]WW', /\\d{4}-W\\d\\d/, false],\n        ['YYYY-DDD', /\\d{4}-\\d{3}/],\n        ['YYYY-MM', /\\d{4}-\\d\\d/, false],\n        ['YYYYYYMMDD', /[+-]\\d{10}/],\n        ['YYYYMMDD', /\\d{8}/],\n        ['GGGG[W]WWE', /\\d{4}W\\d{3}/],\n        ['GGGG[W]WW', /\\d{4}W\\d{2}/, false],\n        ['YYYYDDD', /\\d{7}/],\n        ['YYYYMM', /\\d{6}/, false],\n        ['YYYY', /\\d{4}/, false],\n    ],\n    // iso time formats and regexes\n    isoTimes = [\n        ['HH:mm:ss.SSSS', /\\d\\d:\\d\\d:\\d\\d\\.\\d+/],\n        ['HH:mm:ss,SSSS', /\\d\\d:\\d\\d:\\d\\d,\\d+/],\n        ['HH:mm:ss', /\\d\\d:\\d\\d:\\d\\d/],\n        ['HH:mm', /\\d\\d:\\d\\d/],\n        ['HHmmss.SSSS', /\\d\\d\\d\\d\\d\\d\\.\\d+/],\n        ['HHmmss,SSSS', /\\d\\d\\d\\d\\d\\d,\\d+/],\n        ['HHmmss', /\\d\\d\\d\\d\\d\\d/],\n        ['HHmm', /\\d\\d\\d\\d/],\n        ['HH', /\\d\\d/],\n    ],\n    aspNetJsonRegex = /^\\/?Date\\((-?\\d+)/i,\n    // RFC 2822 regex: For details see https://tools.ietf.org/html/rfc2822#section-3.3\n    rfc2822 =\n        /^(?:(Mon|Tue|Wed|Thu|Fri|Sat|Sun),?\\s)?(\\d{1,2})\\s(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\\s(\\d{2,4})\\s(\\d\\d):(\\d\\d)(?::(\\d\\d))?\\s(?:(UT|GMT|[ECMP][SD]T)|([Zz])|([+-]\\d{4}))$/,\n    obsOffsets = {\n        UT: 0,\n        GMT: 0,\n        EDT: -4 * 60,\n        EST: -5 * 60,\n        CDT: -5 * 60,\n        CST: -6 * 60,\n        MDT: -6 * 60,\n        MST: -7 * 60,\n        PDT: -7 * 60,\n        PST: -8 * 60,\n    };\n\n// date from iso format\nfunction configFromISO(config) {\n    var i,\n        l,\n        string = config._i,\n        match = extendedIsoRegex.exec(string) || basicIsoRegex.exec(string),\n        allowTime,\n        dateFormat,\n        timeFormat,\n        tzFormat,\n        isoDatesLen = isoDates.length,\n        isoTimesLen = isoTimes.length;\n\n    if (match) {\n        getParsingFlags(config).iso = true;\n        for (i = 0, l = isoDatesLen; i < l; i++) {\n            if (isoDates[i][1].exec(match[1])) {\n                dateFormat = isoDates[i][0];\n                allowTime = isoDates[i][2] !== false;\n                break;\n            }\n        }\n        if (dateFormat == null) {\n            config._isValid = false;\n            return;\n        }\n        if (match[3]) {\n            for (i = 0, l = isoTimesLen; i < l; i++) {\n                if (isoTimes[i][1].exec(match[3])) {\n                    // match[2] should be 'T' or space\n                    timeFormat = (match[2] || ' ') + isoTimes[i][0];\n                    break;\n                }\n            }\n            if (timeFormat == null) {\n                config._isValid = false;\n                return;\n            }\n        }\n        if (!allowTime && timeFormat != null) {\n            config._isValid = false;\n            return;\n        }\n        if (match[4]) {\n            if (tzRegex.exec(match[4])) {\n                tzFormat = 'Z';\n            } else {\n                config._isValid = false;\n                return;\n            }\n        }\n        config._f = dateFormat + (timeFormat || '') + (tzFormat || '');\n        configFromStringAndFormat(config);\n    } else {\n        config._isValid = false;\n    }\n}\n\nfunction extractFromRFC2822Strings(\n    yearStr,\n    monthStr,\n    dayStr,\n    hourStr,\n    minuteStr,\n    secondStr\n) {\n    var result = [\n        untruncateYear(yearStr),\n        defaultLocaleMonthsShort.indexOf(monthStr),\n        parseInt(dayStr, 10),\n        parseInt(hourStr, 10),\n        parseInt(minuteStr, 10),\n    ];\n\n    if (secondStr) {\n        result.push(parseInt(secondStr, 10));\n    }\n\n    return result;\n}\n\nfunction untruncateYear(yearStr) {\n    var year = parseInt(yearStr, 10);\n    if (year <= 49) {\n        return 2000 + year;\n    } else if (year <= 999) {\n        return 1900 + year;\n    }\n    return year;\n}\n\nfunction preprocessRFC2822(s) {\n    // Remove comments and folding whitespace and replace multiple-spaces with a single space\n    return s\n        .replace(/\\([^()]*\\)|[\\n\\t]/g, ' ')\n        .replace(/(\\s\\s+)/g, ' ')\n        .replace(/^\\s\\s*/, '')\n        .replace(/\\s\\s*$/, '');\n}\n\nfunction checkWeekday(weekdayStr, parsedInput, config) {\n    if (weekdayStr) {\n        // TODO: Replace the vanilla JS Date object with an independent day-of-week check.\n        var weekdayProvided = defaultLocaleWeekdaysShort.indexOf(weekdayStr),\n            weekdayActual = new Date(\n                parsedInput[0],\n                parsedInput[1],\n                parsedInput[2]\n            ).getDay();\n        if (weekdayProvided !== weekdayActual) {\n            getParsingFlags(config).weekdayMismatch = true;\n            config._isValid = false;\n            return false;\n        }\n    }\n    return true;\n}\n\nfunction calculateOffset(obsOffset, militaryOffset, numOffset) {\n    if (obsOffset) {\n        return obsOffsets[obsOffset];\n    } else if (militaryOffset) {\n        // the only allowed military tz is Z\n        return 0;\n    } else {\n        var hm = parseInt(numOffset, 10),\n            m = hm % 100,\n            h = (hm - m) / 100;\n        return h * 60 + m;\n    }\n}\n\n// date and time from ref 2822 format\nfunction configFromRFC2822(config) {\n    var match = rfc2822.exec(preprocessRFC2822(config._i)),\n        parsedArray;\n    if (match) {\n        parsedArray = extractFromRFC2822Strings(\n            match[4],\n            match[3],\n            match[2],\n            match[5],\n            match[6],\n            match[7]\n        );\n        if (!checkWeekday(match[1], parsedArray, config)) {\n            return;\n        }\n\n        config._a = parsedArray;\n        config._tzm = calculateOffset(match[8], match[9], match[10]);\n\n        config._d = createUTCDate.apply(null, config._a);\n        config._d.setUTCMinutes(config._d.getUTCMinutes() - config._tzm);\n\n        getParsingFlags(config).rfc2822 = true;\n    } else {\n        config._isValid = false;\n    }\n}\n\n// date from 1) ASP.NET, 2) ISO, 3) RFC 2822 formats, or 4) optional fallback if parsing isn't strict\nfunction configFromString(config) {\n    var matched = aspNetJsonRegex.exec(config._i);\n    if (matched !== null) {\n        config._d = new Date(+matched[1]);\n        return;\n    }\n\n    configFromISO(config);\n    if (config._isValid === false) {\n        delete config._isValid;\n    } else {\n        return;\n    }\n\n    configFromRFC2822(config);\n    if (config._isValid === false) {\n        delete config._isValid;\n    } else {\n        return;\n    }\n\n    if (config._strict) {\n        config._isValid = false;\n    } else {\n        // Final attempt, use Input Fallback\n        hooks.createFromInputFallback(config);\n    }\n}\n\nhooks.createFromInputFallback = deprecate(\n    'value provided is not in a recognized RFC2822 or ISO format. moment construction falls back to js Date(), ' +\n        'which is not reliable across all browsers and versions. Non RFC2822/ISO date formats are ' +\n        'discouraged. Please refer to http://momentjs.com/guides/#/warnings/js-date/ for more info.',\n    function (config) {\n        config._d = new Date(config._i + (config._useUTC ? ' UTC' : ''));\n    }\n);\n\n// Pick the first defined of two or three arguments.\nfunction defaults(a, b, c) {\n    if (a != null) {\n        return a;\n    }\n    if (b != null) {\n        return b;\n    }\n    return c;\n}\n\nfunction currentDateArray(config) {\n    // hooks is actually the exported moment object\n    var nowValue = new Date(hooks.now());\n    if (config._useUTC) {\n        return [\n            nowValue.getUTCFullYear(),\n            nowValue.getUTCMonth(),\n            nowValue.getUTCDate(),\n        ];\n    }\n    return [nowValue.getFullYear(), nowValue.getMonth(), nowValue.getDate()];\n}\n\n// convert an array to a date.\n// the array should mirror the parameters below\n// note: all values past the year are optional and will default to the lowest possible value.\n// [year, month, day , hour, minute, second, millisecond]\nfunction configFromArray(config) {\n    var i,\n        date,\n        input = [],\n        currentDate,\n        expectedWeekday,\n        yearToUse;\n\n    if (config._d) {\n        return;\n    }\n\n    currentDate = currentDateArray(config);\n\n    //compute day of the year from weeks and weekdays\n    if (config._w && config._a[DATE] == null && config._a[MONTH] == null) {\n        dayOfYearFromWeekInfo(config);\n    }\n\n    //if the day of the year is set, figure out what it is\n    if (config._dayOfYear != null) {\n        yearToUse = defaults(config._a[YEAR], currentDate[YEAR]);\n\n        if (\n            config._dayOfYear > daysInYear(yearToUse) ||\n            config._dayOfYear === 0\n        ) {\n            getParsingFlags(config)._overflowDayOfYear = true;\n        }\n\n        date = createUTCDate(yearToUse, 0, config._dayOfYear);\n        config._a[MONTH] = date.getUTCMonth();\n        config._a[DATE] = date.getUTCDate();\n    }\n\n    // Default to current date.\n    // * if no year, month, day of month are given, default to today\n    // * if day of month is given, default month and year\n    // * if month is given, default only year\n    // * if year is given, don't default anything\n    for (i = 0; i < 3 && config._a[i] == null; ++i) {\n        config._a[i] = input[i] = currentDate[i];\n    }\n\n    // Zero out whatever was not defaulted, including time\n    for (; i < 7; i++) {\n        config._a[i] = input[i] =\n            config._a[i] == null ? (i === 2 ? 1 : 0) : config._a[i];\n    }\n\n    // Check for 24:00:00.000\n    if (\n        config._a[HOUR] === 24 &&\n        config._a[MINUTE] === 0 &&\n        config._a[SECOND] === 0 &&\n        config._a[MILLISECOND] === 0\n    ) {\n        config._nextDay = true;\n        config._a[HOUR] = 0;\n    }\n\n    config._d = (config._useUTC ? createUTCDate : createDate).apply(\n        null,\n        input\n    );\n    expectedWeekday = config._useUTC\n        ? config._d.getUTCDay()\n        : config._d.getDay();\n\n    // Apply timezone offset from input. The actual utcOffset can be changed\n    // with parseZone.\n    if (config._tzm != null) {\n        config._d.setUTCMinutes(config._d.getUTCMinutes() - config._tzm);\n    }\n\n    if (config._nextDay) {\n        config._a[HOUR] = 24;\n    }\n\n    // check for mismatching day of week\n    if (\n        config._w &&\n        typeof config._w.d !== 'undefined' &&\n        config._w.d !== expectedWeekday\n    ) {\n        getParsingFlags(config).weekdayMismatch = true;\n    }\n}\n\nfunction dayOfYearFromWeekInfo(config) {\n    var w, weekYear, week, weekday, dow, doy, temp, weekdayOverflow, curWeek;\n\n    w = config._w;\n    if (w.GG != null || w.W != null || w.E != null) {\n        dow = 1;\n        doy = 4;\n\n        // TODO: We need to take the current isoWeekYear, but that depends on\n        // how we interpret now (local, utc, fixed offset). So create\n        // a now version of current config (take local/utc/offset flags, and\n        // create now).\n        weekYear = defaults(\n            w.GG,\n            config._a[YEAR],\n            weekOfYear(createLocal(), 1, 4).year\n        );\n        week = defaults(w.W, 1);\n        weekday = defaults(w.E, 1);\n        if (weekday < 1 || weekday > 7) {\n            weekdayOverflow = true;\n        }\n    } else {\n        dow = config._locale._week.dow;\n        doy = config._locale._week.doy;\n\n        curWeek = weekOfYear(createLocal(), dow, doy);\n\n        weekYear = defaults(w.gg, config._a[YEAR], curWeek.year);\n\n        // Default to current week.\n        week = defaults(w.w, curWeek.week);\n\n        if (w.d != null) {\n            // weekday -- low day numbers are considered next week\n            weekday = w.d;\n            if (weekday < 0 || weekday > 6) {\n                weekdayOverflow = true;\n            }\n        } else if (w.e != null) {\n            // local weekday -- counting starts from beginning of week\n            weekday = w.e + dow;\n            if (w.e < 0 || w.e > 6) {\n                weekdayOverflow = true;\n            }\n        } else {\n            // default to beginning of week\n            weekday = dow;\n        }\n    }\n    if (week < 1 || week > weeksInYear(weekYear, dow, doy)) {\n        getParsingFlags(config)._overflowWeeks = true;\n    } else if (weekdayOverflow != null) {\n        getParsingFlags(config)._overflowWeekday = true;\n    } else {\n        temp = dayOfYearFromWeeks(weekYear, week, weekday, dow, doy);\n        config._a[YEAR] = temp.year;\n        config._dayOfYear = temp.dayOfYear;\n    }\n}\n\n// constant that refers to the ISO standard\nhooks.ISO_8601 = function () {};\n\n// constant that refers to the RFC 2822 form\nhooks.RFC_2822 = function () {};\n\n// date from string and format string\nfunction configFromStringAndFormat(config) {\n    // TODO: Move this to another part of the creation flow to prevent circular deps\n    if (config._f === hooks.ISO_8601) {\n        configFromISO(config);\n        return;\n    }\n    if (config._f === hooks.RFC_2822) {\n        configFromRFC2822(config);\n        return;\n    }\n    config._a = [];\n    getParsingFlags(config).empty = true;\n\n    // This array is used to make a Date, either with `new Date` or `Date.UTC`\n    var string = '' + config._i,\n        i,\n        parsedInput,\n        tokens,\n        token,\n        skipped,\n        stringLength = string.length,\n        totalParsedInputLength = 0,\n        era,\n        tokenLen;\n\n    tokens =\n        expandFormat(config._f, config._locale).match(formattingTokens) || [];\n    tokenLen = tokens.length;\n    for (i = 0; i < tokenLen; i++) {\n        token = tokens[i];\n        parsedInput = (string.match(getParseRegexForToken(token, config)) ||\n            [])[0];\n        if (parsedInput) {\n            skipped = string.substr(0, string.indexOf(parsedInput));\n            if (skipped.length > 0) {\n                getParsingFlags(config).unusedInput.push(skipped);\n            }\n            string = string.slice(\n                string.indexOf(parsedInput) + parsedInput.length\n            );\n            totalParsedInputLength += parsedInput.length;\n        }\n        // don't parse if it's not a known token\n        if (formatTokenFunctions[token]) {\n            if (parsedInput) {\n                getParsingFlags(config).empty = false;\n            } else {\n                getParsingFlags(config).unusedTokens.push(token);\n            }\n            addTimeToArrayFromToken(token, parsedInput, config);\n        } else if (config._strict && !parsedInput) {\n            getParsingFlags(config).unusedTokens.push(token);\n        }\n    }\n\n    // add remaining unparsed input length to the string\n    getParsingFlags(config).charsLeftOver =\n        stringLength - totalParsedInputLength;\n    if (string.length > 0) {\n        getParsingFlags(config).unusedInput.push(string);\n    }\n\n    // clear _12h flag if hour is <= 12\n    if (\n        config._a[HOUR] <= 12 &&\n        getParsingFlags(config).bigHour === true &&\n        config._a[HOUR] > 0\n    ) {\n        getParsingFlags(config).bigHour = undefined;\n    }\n\n    getParsingFlags(config).parsedDateParts = config._a.slice(0);\n    getParsingFlags(config).meridiem = config._meridiem;\n    // handle meridiem\n    config._a[HOUR] = meridiemFixWrap(\n        config._locale,\n        config._a[HOUR],\n        config._meridiem\n    );\n\n    // handle era\n    era = getParsingFlags(config).era;\n    if (era !== null) {\n        config._a[YEAR] = config._locale.erasConvertYear(era, config._a[YEAR]);\n    }\n\n    configFromArray(config);\n    checkOverflow(config);\n}\n\nfunction meridiemFixWrap(locale, hour, meridiem) {\n    var isPm;\n\n    if (meridiem == null) {\n        // nothing to do\n        return hour;\n    }\n    if (locale.meridiemHour != null) {\n        return locale.meridiemHour(hour, meridiem);\n    } else if (locale.isPM != null) {\n        // Fallback\n        isPm = locale.isPM(meridiem);\n        if (isPm && hour < 12) {\n            hour += 12;\n        }\n        if (!isPm && hour === 12) {\n            hour = 0;\n        }\n        return hour;\n    } else {\n        // this is not supposed to happen\n        return hour;\n    }\n}\n\n// date from string and array of format strings\nfunction configFromStringAndArray(config) {\n    var tempConfig,\n        bestMoment,\n        scoreToBeat,\n        i,\n        currentScore,\n        validFormatFound,\n        bestFormatIsValid = false,\n        configfLen = config._f.length;\n\n    if (configfLen === 0) {\n        getParsingFlags(config).invalidFormat = true;\n        config._d = new Date(NaN);\n        return;\n    }\n\n    for (i = 0; i < configfLen; i++) {\n        currentScore = 0;\n        validFormatFound = false;\n        tempConfig = copyConfig({}, config);\n        if (config._useUTC != null) {\n            tempConfig._useUTC = config._useUTC;\n        }\n        tempConfig._f = config._f[i];\n        configFromStringAndFormat(tempConfig);\n\n        if (isValid(tempConfig)) {\n            validFormatFound = true;\n        }\n\n        // if there is any input that was not parsed add a penalty for that format\n        currentScore += getParsingFlags(tempConfig).charsLeftOver;\n\n        //or tokens\n        currentScore += getParsingFlags(tempConfig).unusedTokens.length * 10;\n\n        getParsingFlags(tempConfig).score = currentScore;\n\n        if (!bestFormatIsValid) {\n            if (\n                scoreToBeat == null ||\n                currentScore < scoreToBeat ||\n                validFormatFound\n            ) {\n                scoreToBeat = currentScore;\n                bestMoment = tempConfig;\n                if (validFormatFound) {\n                    bestFormatIsValid = true;\n                }\n            }\n        } else {\n            if (currentScore < scoreToBeat) {\n                scoreToBeat = currentScore;\n                bestMoment = tempConfig;\n            }\n        }\n    }\n\n    extend(config, bestMoment || tempConfig);\n}\n\nfunction configFromObject(config) {\n    if (config._d) {\n        return;\n    }\n\n    var i = normalizeObjectUnits(config._i),\n        dayOrDate = i.day === undefined ? i.date : i.day;\n    config._a = map(\n        [i.year, i.month, dayOrDate, i.hour, i.minute, i.second, i.millisecond],\n        function (obj) {\n            return obj && parseInt(obj, 10);\n        }\n    );\n\n    configFromArray(config);\n}\n\nfunction createFromConfig(config) {\n    var res = new Moment(checkOverflow(prepareConfig(config)));\n    if (res._nextDay) {\n        // Adding is smart enough around DST\n        res.add(1, 'd');\n        res._nextDay = undefined;\n    }\n\n    return res;\n}\n\nfunction prepareConfig(config) {\n    var input = config._i,\n        format = config._f;\n\n    config._locale = config._locale || getLocale(config._l);\n\n    if (input === null || (format === undefined && input === '')) {\n        return createInvalid({ nullInput: true });\n    }\n\n    if (typeof input === 'string') {\n        config._i = input = config._locale.preparse(input);\n    }\n\n    if (isMoment(input)) {\n        return new Moment(checkOverflow(input));\n    } else if (isDate(input)) {\n        config._d = input;\n    } else if (isArray(format)) {\n        configFromStringAndArray(config);\n    } else if (format) {\n        configFromStringAndFormat(config);\n    } else {\n        configFromInput(config);\n    }\n\n    if (!isValid(config)) {\n        config._d = null;\n    }\n\n    return config;\n}\n\nfunction configFromInput(config) {\n    var input = config._i;\n    if (isUndefined(input)) {\n        config._d = new Date(hooks.now());\n    } else if (isDate(input)) {\n        config._d = new Date(input.valueOf());\n    } else if (typeof input === 'string') {\n        configFromString(config);\n    } else if (isArray(input)) {\n        config._a = map(input.slice(0), function (obj) {\n            return parseInt(obj, 10);\n        });\n        configFromArray(config);\n    } else if (isObject(input)) {\n        configFromObject(config);\n    } else if (isNumber(input)) {\n        // from milliseconds\n        config._d = new Date(input);\n    } else {\n        hooks.createFromInputFallback(config);\n    }\n}\n\nfunction createLocalOrUTC(input, format, locale, strict, isUTC) {\n    var c = {};\n\n    if (format === true || format === false) {\n        strict = format;\n        format = undefined;\n    }\n\n    if (locale === true || locale === false) {\n        strict = locale;\n        locale = undefined;\n    }\n\n    if (\n        (isObject(input) && isObjectEmpty(input)) ||\n        (isArray(input) && input.length === 0)\n    ) {\n        input = undefined;\n    }\n    // object construction must be done this way.\n    // https://github.com/moment/moment/issues/1423\n    c._isAMomentObject = true;\n    c._useUTC = c._isUTC = isUTC;\n    c._l = locale;\n    c._i = input;\n    c._f = format;\n    c._strict = strict;\n\n    return createFromConfig(c);\n}\n\nfunction createLocal(input, format, locale, strict) {\n    return createLocalOrUTC(input, format, locale, strict, false);\n}\n\nvar prototypeMin = deprecate(\n        'moment().min is deprecated, use moment.max instead. http://momentjs.com/guides/#/warnings/min-max/',\n        function () {\n            var other = createLocal.apply(null, arguments);\n            if (this.isValid() && other.isValid()) {\n                return other < this ? this : other;\n            } else {\n                return createInvalid();\n            }\n        }\n    ),\n    prototypeMax = deprecate(\n        'moment().max is deprecated, use moment.min instead. http://momentjs.com/guides/#/warnings/min-max/',\n        function () {\n            var other = createLocal.apply(null, arguments);\n            if (this.isValid() && other.isValid()) {\n                return other > this ? this : other;\n            } else {\n                return createInvalid();\n            }\n        }\n    );\n\n// Pick a moment m from moments so that m[fn](other) is true for all\n// other. This relies on the function fn to be transitive.\n//\n// moments should either be an array of moment objects or an array, whose\n// first element is an array of moment objects.\nfunction pickBy(fn, moments) {\n    var res, i;\n    if (moments.length === 1 && isArray(moments[0])) {\n        moments = moments[0];\n    }\n    if (!moments.length) {\n        return createLocal();\n    }\n    res = moments[0];\n    for (i = 1; i < moments.length; ++i) {\n        if (!moments[i].isValid() || moments[i][fn](res)) {\n            res = moments[i];\n        }\n    }\n    return res;\n}\n\n// TODO: Use [].sort instead?\nfunction min() {\n    var args = [].slice.call(arguments, 0);\n\n    return pickBy('isBefore', args);\n}\n\nfunction max() {\n    var args = [].slice.call(arguments, 0);\n\n    return pickBy('isAfter', args);\n}\n\nvar now = function () {\n    return Date.now ? Date.now() : +new Date();\n};\n\nvar ordering = [\n    'year',\n    'quarter',\n    'month',\n    'week',\n    'day',\n    'hour',\n    'minute',\n    'second',\n    'millisecond',\n];\n\nfunction isDurationValid(m) {\n    var key,\n        unitHasDecimal = false,\n        i,\n        orderLen = ordering.length;\n    for (key in m) {\n        if (\n            hasOwnProp(m, key) &&\n            !(\n                indexOf.call(ordering, key) !== -1 &&\n                (m[key] == null || !isNaN(m[key]))\n            )\n        ) {\n            return false;\n        }\n    }\n\n    for (i = 0; i < orderLen; ++i) {\n        if (m[ordering[i]]) {\n            if (unitHasDecimal) {\n                return false; // only allow non-integers for smallest unit\n            }\n            if (parseFloat(m[ordering[i]]) !== toInt(m[ordering[i]])) {\n                unitHasDecimal = true;\n            }\n        }\n    }\n\n    return true;\n}\n\nfunction isValid$1() {\n    return this._isValid;\n}\n\nfunction createInvalid$1() {\n    return createDuration(NaN);\n}\n\nfunction Duration(duration) {\n    var normalizedInput = normalizeObjectUnits(duration),\n        years = normalizedInput.year || 0,\n        quarters = normalizedInput.quarter || 0,\n        months = normalizedInput.month || 0,\n        weeks = normalizedInput.week || normalizedInput.isoWeek || 0,\n        days = normalizedInput.day || 0,\n        hours = normalizedInput.hour || 0,\n        minutes = normalizedInput.minute || 0,\n        seconds = normalizedInput.second || 0,\n        milliseconds = normalizedInput.millisecond || 0;\n\n    this._isValid = isDurationValid(normalizedInput);\n\n    // representation for dateAddRemove\n    this._milliseconds =\n        +milliseconds +\n        seconds * 1e3 + // 1000\n        minutes * 6e4 + // 1000 * 60\n        hours * 1000 * 60 * 60; //using 1000 * 60 * 60 instead of 36e5 to avoid floating point rounding errors https://github.com/moment/moment/issues/2978\n    // Because of dateAddRemove treats 24 hours as different from a\n    // day when working around DST, we need to store them separately\n    this._days = +days + weeks * 7;\n    // It is impossible to translate months into days without knowing\n    // which months you are are talking about, so we have to store\n    // it separately.\n    this._months = +months + quarters * 3 + years * 12;\n\n    this._data = {};\n\n    this._locale = getLocale();\n\n    this._bubble();\n}\n\nfunction isDuration(obj) {\n    return obj instanceof Duration;\n}\n\nfunction absRound(number) {\n    if (number < 0) {\n        return Math.round(-1 * number) * -1;\n    } else {\n        return Math.round(number);\n    }\n}\n\n// compare two arrays, return the number of differences\nfunction compareArrays(array1, array2, dontConvert) {\n    var len = Math.min(array1.length, array2.length),\n        lengthDiff = Math.abs(array1.length - array2.length),\n        diffs = 0,\n        i;\n    for (i = 0; i < len; i++) {\n        if (\n            (dontConvert && array1[i] !== array2[i]) ||\n            (!dontConvert && toInt(array1[i]) !== toInt(array2[i]))\n        ) {\n            diffs++;\n        }\n    }\n    return diffs + lengthDiff;\n}\n\n// FORMATTING\n\nfunction offset(token, separator) {\n    addFormatToken(token, 0, 0, function () {\n        var offset = this.utcOffset(),\n            sign = '+';\n        if (offset < 0) {\n            offset = -offset;\n            sign = '-';\n        }\n        return (\n            sign +\n            zeroFill(~~(offset / 60), 2) +\n            separator +\n            zeroFill(~~offset % 60, 2)\n        );\n    });\n}\n\noffset('Z', ':');\noffset('ZZ', '');\n\n// PARSING\n\naddRegexToken('Z', matchShortOffset);\naddRegexToken('ZZ', matchShortOffset);\naddParseToken(['Z', 'ZZ'], function (input, array, config) {\n    config._useUTC = true;\n    config._tzm = offsetFromString(matchShortOffset, input);\n});\n\n// HELPERS\n\n// timezone chunker\n// '+10:00' > ['10',  '00']\n// '-1530'  > ['-15', '30']\nvar chunkOffset = /([\\+\\-]|\\d\\d)/gi;\n\nfunction offsetFromString(matcher, string) {\n    var matches = (string || '').match(matcher),\n        chunk,\n        parts,\n        minutes;\n\n    if (matches === null) {\n        return null;\n    }\n\n    chunk = matches[matches.length - 1] || [];\n    parts = (chunk + '').match(chunkOffset) || ['-', 0, 0];\n    minutes = +(parts[1] * 60) + toInt(parts[2]);\n\n    return minutes === 0 ? 0 : parts[0] === '+' ? minutes : -minutes;\n}\n\n// Return a moment from input, that is local/utc/zone equivalent to model.\nfunction cloneWithOffset(input, model) {\n    var res, diff;\n    if (model._isUTC) {\n        res = model.clone();\n        diff =\n            (isMoment(input) || isDate(input)\n                ? input.valueOf()\n                : createLocal(input).valueOf()) - res.valueOf();\n        // Use low-level api, because this fn is low-level api.\n        res._d.setTime(res._d.valueOf() + diff);\n        hooks.updateOffset(res, false);\n        return res;\n    } else {\n        return createLocal(input).local();\n    }\n}\n\nfunction getDateOffset(m) {\n    // On Firefox.24 Date#getTimezoneOffset returns a floating point.\n    // https://github.com/moment/moment/pull/1871\n    return -Math.round(m._d.getTimezoneOffset());\n}\n\n// HOOKS\n\n// This function will be called whenever a moment is mutated.\n// It is intended to keep the offset in sync with the timezone.\nhooks.updateOffset = function () {};\n\n// MOMENTS\n\n// keepLocalTime = true means only change the timezone, without\n// affecting the local hour. So 5:31:26 +0300 --[utcOffset(2, true)]-->\n// 5:31:26 +0200 It is possible that 5:31:26 doesn't exist with offset\n// +0200, so we adjust the time as needed, to be valid.\n//\n// Keeping the time actually adds/subtracts (one hour)\n// from the actual represented time. That is why we call updateOffset\n// a second time. In case it wants us to change the offset again\n// _changeInProgress == true case, then we have to adjust, because\n// there is no such time in the given timezone.\nfunction getSetOffset(input, keepLocalTime, keepMinutes) {\n    var offset = this._offset || 0,\n        localAdjust;\n    if (!this.isValid()) {\n        return input != null ? this : NaN;\n    }\n    if (input != null) {\n        if (typeof input === 'string') {\n            input = offsetFromString(matchShortOffset, input);\n            if (input === null) {\n                return this;\n            }\n        } else if (Math.abs(input) < 16 && !keepMinutes) {\n            input = input * 60;\n        }\n        if (!this._isUTC && keepLocalTime) {\n            localAdjust = getDateOffset(this);\n        }\n        this._offset = input;\n        this._isUTC = true;\n        if (localAdjust != null) {\n            this.add(localAdjust, 'm');\n        }\n        if (offset !== input) {\n            if (!keepLocalTime || this._changeInProgress) {\n                addSubtract(\n                    this,\n                    createDuration(input - offset, 'm'),\n                    1,\n                    false\n                );\n            } else if (!this._changeInProgress) {\n                this._changeInProgress = true;\n                hooks.updateOffset(this, true);\n                this._changeInProgress = null;\n            }\n        }\n        return this;\n    } else {\n        return this._isUTC ? offset : getDateOffset(this);\n    }\n}\n\nfunction getSetZone(input, keepLocalTime) {\n    if (input != null) {\n        if (typeof input !== 'string') {\n            input = -input;\n        }\n\n        this.utcOffset(input, keepLocalTime);\n\n        return this;\n    } else {\n        return -this.utcOffset();\n    }\n}\n\nfunction setOffsetToUTC(keepLocalTime) {\n    return this.utcOffset(0, keepLocalTime);\n}\n\nfunction setOffsetToLocal(keepLocalTime) {\n    if (this._isUTC) {\n        this.utcOffset(0, keepLocalTime);\n        this._isUTC = false;\n\n        if (keepLocalTime) {\n            this.subtract(getDateOffset(this), 'm');\n        }\n    }\n    return this;\n}\n\nfunction setOffsetToParsedOffset() {\n    if (this._tzm != null) {\n        this.utcOffset(this._tzm, false, true);\n    } else if (typeof this._i === 'string') {\n        var tZone = offsetFromString(matchOffset, this._i);\n        if (tZone != null) {\n            this.utcOffset(tZone);\n        } else {\n            this.utcOffset(0, true);\n        }\n    }\n    return this;\n}\n\nfunction hasAlignedHourOffset(input) {\n    if (!this.isValid()) {\n        return false;\n    }\n    input = input ? createLocal(input).utcOffset() : 0;\n\n    return (this.utcOffset() - input) % 60 === 0;\n}\n\nfunction isDaylightSavingTime() {\n    return (\n        this.utcOffset() > this.clone().month(0).utcOffset() ||\n        this.utcOffset() > this.clone().month(5).utcOffset()\n    );\n}\n\nfunction isDaylightSavingTimeShifted() {\n    if (!isUndefined(this._isDSTShifted)) {\n        return this._isDSTShifted;\n    }\n\n    var c = {},\n        other;\n\n    copyConfig(c, this);\n    c = prepareConfig(c);\n\n    if (c._a) {\n        other = c._isUTC ? createUTC(c._a) : createLocal(c._a);\n        this._isDSTShifted =\n            this.isValid() && compareArrays(c._a, other.toArray()) > 0;\n    } else {\n        this._isDSTShifted = false;\n    }\n\n    return this._isDSTShifted;\n}\n\nfunction isLocal() {\n    return this.isValid() ? !this._isUTC : false;\n}\n\nfunction isUtcOffset() {\n    return this.isValid() ? this._isUTC : false;\n}\n\nfunction isUtc() {\n    return this.isValid() ? this._isUTC && this._offset === 0 : false;\n}\n\n// ASP.NET json date format regex\nvar aspNetRegex = /^(-|\\+)?(?:(\\d*)[. ])?(\\d+):(\\d+)(?::(\\d+)(\\.\\d*)?)?$/,\n    // from http://docs.closure-library.googlecode.com/git/closure_goog_date_date.js.source.html\n    // somewhat more in line with 4.4.3.2 2004 spec, but allows decimal anywhere\n    // and further modified to allow for strings containing both week and day\n    isoRegex =\n        /^(-|\\+)?P(?:([-+]?[0-9,.]*)Y)?(?:([-+]?[0-9,.]*)M)?(?:([-+]?[0-9,.]*)W)?(?:([-+]?[0-9,.]*)D)?(?:T(?:([-+]?[0-9,.]*)H)?(?:([-+]?[0-9,.]*)M)?(?:([-+]?[0-9,.]*)S)?)?$/;\n\nfunction createDuration(input, key) {\n    var duration = input,\n        // matching against regexp is expensive, do it on demand\n        match = null,\n        sign,\n        ret,\n        diffRes;\n\n    if (isDuration(input)) {\n        duration = {\n            ms: input._milliseconds,\n            d: input._days,\n            M: input._months,\n        };\n    } else if (isNumber(input) || !isNaN(+input)) {\n        duration = {};\n        if (key) {\n            duration[key] = +input;\n        } else {\n            duration.milliseconds = +input;\n        }\n    } else if ((match = aspNetRegex.exec(input))) {\n        sign = match[1] === '-' ? -1 : 1;\n        duration = {\n            y: 0,\n            d: toInt(match[DATE]) * sign,\n            h: toInt(match[HOUR]) * sign,\n            m: toInt(match[MINUTE]) * sign,\n            s: toInt(match[SECOND]) * sign,\n            ms: toInt(absRound(match[MILLISECOND] * 1000)) * sign, // the millisecond decimal point is included in the match\n        };\n    } else if ((match = isoRegex.exec(input))) {\n        sign = match[1] === '-' ? -1 : 1;\n        duration = {\n            y: parseIso(match[2], sign),\n            M: parseIso(match[3], sign),\n            w: parseIso(match[4], sign),\n            d: parseIso(match[5], sign),\n            h: parseIso(match[6], sign),\n            m: parseIso(match[7], sign),\n            s: parseIso(match[8], sign),\n        };\n    } else if (duration == null) {\n        // checks for null or undefined\n        duration = {};\n    } else if (\n        typeof duration === 'object' &&\n        ('from' in duration || 'to' in duration)\n    ) {\n        diffRes = momentsDifference(\n            createLocal(duration.from),\n            createLocal(duration.to)\n        );\n\n        duration = {};\n        duration.ms = diffRes.milliseconds;\n        duration.M = diffRes.months;\n    }\n\n    ret = new Duration(duration);\n\n    if (isDuration(input) && hasOwnProp(input, '_locale')) {\n        ret._locale = input._locale;\n    }\n\n    if (isDuration(input) && hasOwnProp(input, '_isValid')) {\n        ret._isValid = input._isValid;\n    }\n\n    return ret;\n}\n\ncreateDuration.fn = Duration.prototype;\ncreateDuration.invalid = createInvalid$1;\n\nfunction parseIso(inp, sign) {\n    // We'd normally use ~~inp for this, but unfortunately it also\n    // converts floats to ints.\n    // inp may be undefined, so careful calling replace on it.\n    var res = inp && parseFloat(inp.replace(',', '.'));\n    // apply sign while we're at it\n    return (isNaN(res) ? 0 : res) * sign;\n}\n\nfunction positiveMomentsDifference(base, other) {\n    var res = {};\n\n    res.months =\n        other.month() - base.month() + (other.year() - base.year()) * 12;\n    if (base.clone().add(res.months, 'M').isAfter(other)) {\n        --res.months;\n    }\n\n    res.milliseconds = +other - +base.clone().add(res.months, 'M');\n\n    return res;\n}\n\nfunction momentsDifference(base, other) {\n    var res;\n    if (!(base.isValid() && other.isValid())) {\n        return { milliseconds: 0, months: 0 };\n    }\n\n    other = cloneWithOffset(other, base);\n    if (base.isBefore(other)) {\n        res = positiveMomentsDifference(base, other);\n    } else {\n        res = positiveMomentsDifference(other, base);\n        res.milliseconds = -res.milliseconds;\n        res.months = -res.months;\n    }\n\n    return res;\n}\n\n// TODO: remove 'name' arg after deprecation is removed\nfunction createAdder(direction, name) {\n    return function (val, period) {\n        var dur, tmp;\n        //invert the arguments, but complain about it\n        if (period !== null && !isNaN(+period)) {\n            deprecateSimple(\n                name,\n                'moment().' +\n                    name +\n                    '(period, number) is deprecated. Please use moment().' +\n                    name +\n                    '(number, period). ' +\n                    'See http://momentjs.com/guides/#/warnings/add-inverted-param/ for more info.'\n            );\n            tmp = val;\n            val = period;\n            period = tmp;\n        }\n\n        dur = createDuration(val, period);\n        addSubtract(this, dur, direction);\n        return this;\n    };\n}\n\nfunction addSubtract(mom, duration, isAdding, updateOffset) {\n    var milliseconds = duration._milliseconds,\n        days = absRound(duration._days),\n        months = absRound(duration._months);\n\n    if (!mom.isValid()) {\n        // No op\n        return;\n    }\n\n    updateOffset = updateOffset == null ? true : updateOffset;\n\n    if (months) {\n        setMonth(mom, get(mom, 'Month') + months * isAdding);\n    }\n    if (days) {\n        set$1(mom, 'Date', get(mom, 'Date') + days * isAdding);\n    }\n    if (milliseconds) {\n        mom._d.setTime(mom._d.valueOf() + milliseconds * isAdding);\n    }\n    if (updateOffset) {\n        hooks.updateOffset(mom, days || months);\n    }\n}\n\nvar add = createAdder(1, 'add'),\n    subtract = createAdder(-1, 'subtract');\n\nfunction isString(input) {\n    return typeof input === 'string' || input instanceof String;\n}\n\n// type MomentInput = Moment | Date | string | number | (number | string)[] | MomentInputObject | void; // null | undefined\nfunction isMomentInput(input) {\n    return (\n        isMoment(input) ||\n        isDate(input) ||\n        isString(input) ||\n        isNumber(input) ||\n        isNumberOrStringArray(input) ||\n        isMomentInputObject(input) ||\n        input === null ||\n        input === undefined\n    );\n}\n\nfunction isMomentInputObject(input) {\n    var objectTest = isObject(input) && !isObjectEmpty(input),\n        propertyTest = false,\n        properties = [\n            'years',\n            'year',\n            'y',\n            'months',\n            'month',\n            'M',\n            'days',\n            'day',\n            'd',\n            'dates',\n            'date',\n            'D',\n            'hours',\n            'hour',\n            'h',\n            'minutes',\n            'minute',\n            'm',\n            'seconds',\n            'second',\n            's',\n            'milliseconds',\n            'millisecond',\n            'ms',\n        ],\n        i,\n        property,\n        propertyLen = properties.length;\n\n    for (i = 0; i < propertyLen; i += 1) {\n        property = properties[i];\n        propertyTest = propertyTest || hasOwnProp(input, property);\n    }\n\n    return objectTest && propertyTest;\n}\n\nfunction isNumberOrStringArray(input) {\n    var arrayTest = isArray(input),\n        dataTypeTest = false;\n    if (arrayTest) {\n        dataTypeTest =\n            input.filter(function (item) {\n                return !isNumber(item) && isString(input);\n            }).length === 0;\n    }\n    return arrayTest && dataTypeTest;\n}\n\nfunction isCalendarSpec(input) {\n    var objectTest = isObject(input) && !isObjectEmpty(input),\n        propertyTest = false,\n        properties = [\n            'sameDay',\n            'nextDay',\n            'lastDay',\n            'nextWeek',\n            'lastWeek',\n            'sameElse',\n        ],\n        i,\n        property;\n\n    for (i = 0; i < properties.length; i += 1) {\n        property = properties[i];\n        propertyTest = propertyTest || hasOwnProp(input, property);\n    }\n\n    return objectTest && propertyTest;\n}\n\nfunction getCalendarFormat(myMoment, now) {\n    var diff = myMoment.diff(now, 'days', true);\n    return diff < -6\n        ? 'sameElse'\n        : diff < -1\n          ? 'lastWeek'\n          : diff < 0\n            ? 'lastDay'\n            : diff < 1\n              ? 'sameDay'\n              : diff < 2\n                ? 'nextDay'\n                : diff < 7\n                  ? 'nextWeek'\n                  : 'sameElse';\n}\n\nfunction calendar$1(time, formats) {\n    // Support for single parameter, formats only overload to the calendar function\n    if (arguments.length === 1) {\n        if (!arguments[0]) {\n            time = undefined;\n            formats = undefined;\n        } else if (isMomentInput(arguments[0])) {\n            time = arguments[0];\n            formats = undefined;\n        } else if (isCalendarSpec(arguments[0])) {\n            formats = arguments[0];\n            time = undefined;\n        }\n    }\n    // We want to compare the start of today, vs this.\n    // Getting start-of-today depends on whether we're local/utc/offset or not.\n    var now = time || createLocal(),\n        sod = cloneWithOffset(now, this).startOf('day'),\n        format = hooks.calendarFormat(this, sod) || 'sameElse',\n        output =\n            formats &&\n            (isFunction(formats[format])\n                ? formats[format].call(this, now)\n                : formats[format]);\n\n    return this.format(\n        output || this.localeData().calendar(format, this, createLocal(now))\n    );\n}\n\nfunction clone() {\n    return new Moment(this);\n}\n\nfunction isAfter(input, units) {\n    var localInput = isMoment(input) ? input : createLocal(input);\n    if (!(this.isValid() && localInput.isValid())) {\n        return false;\n    }\n    units = normalizeUnits(units) || 'millisecond';\n    if (units === 'millisecond') {\n        return this.valueOf() > localInput.valueOf();\n    } else {\n        return localInput.valueOf() < this.clone().startOf(units).valueOf();\n    }\n}\n\nfunction isBefore(input, units) {\n    var localInput = isMoment(input) ? input : createLocal(input);\n    if (!(this.isValid() && localInput.isValid())) {\n        return false;\n    }\n    units = normalizeUnits(units) || 'millisecond';\n    if (units === 'millisecond') {\n        return this.valueOf() < localInput.valueOf();\n    } else {\n        return this.clone().endOf(units).valueOf() < localInput.valueOf();\n    }\n}\n\nfunction isBetween(from, to, units, inclusivity) {\n    var localFrom = isMoment(from) ? from : createLocal(from),\n        localTo = isMoment(to) ? to : createLocal(to);\n    if (!(this.isValid() && localFrom.isValid() && localTo.isValid())) {\n        return false;\n    }\n    inclusivity = inclusivity || '()';\n    return (\n        (inclusivity[0] === '('\n            ? this.isAfter(localFrom, units)\n            : !this.isBefore(localFrom, units)) &&\n        (inclusivity[1] === ')'\n            ? this.isBefore(localTo, units)\n            : !this.isAfter(localTo, units))\n    );\n}\n\nfunction isSame(input, units) {\n    var localInput = isMoment(input) ? input : createLocal(input),\n        inputMs;\n    if (!(this.isValid() && localInput.isValid())) {\n        return false;\n    }\n    units = normalizeUnits(units) || 'millisecond';\n    if (units === 'millisecond') {\n        return this.valueOf() === localInput.valueOf();\n    } else {\n        inputMs = localInput.valueOf();\n        return (\n            this.clone().startOf(units).valueOf() <= inputMs &&\n            inputMs <= this.clone().endOf(units).valueOf()\n        );\n    }\n}\n\nfunction isSameOrAfter(input, units) {\n    return this.isSame(input, units) || this.isAfter(input, units);\n}\n\nfunction isSameOrBefore(input, units) {\n    return this.isSame(input, units) || this.isBefore(input, units);\n}\n\nfunction diff(input, units, asFloat) {\n    var that, zoneDelta, output;\n\n    if (!this.isValid()) {\n        return NaN;\n    }\n\n    that = cloneWithOffset(input, this);\n\n    if (!that.isValid()) {\n        return NaN;\n    }\n\n    zoneDelta = (that.utcOffset() - this.utcOffset()) * 6e4;\n\n    units = normalizeUnits(units);\n\n    switch (units) {\n        case 'year':\n            output = monthDiff(this, that) / 12;\n            break;\n        case 'month':\n            output = monthDiff(this, that);\n            break;\n        case 'quarter':\n            output = monthDiff(this, that) / 3;\n            break;\n        case 'second':\n            output = (this - that) / 1e3;\n            break; // 1000\n        case 'minute':\n            output = (this - that) / 6e4;\n            break; // 1000 * 60\n        case 'hour':\n            output = (this - that) / 36e5;\n            break; // 1000 * 60 * 60\n        case 'day':\n            output = (this - that - zoneDelta) / 864e5;\n            break; // 1000 * 60 * 60 * 24, negate dst\n        case 'week':\n            output = (this - that - zoneDelta) / 6048e5;\n            break; // 1000 * 60 * 60 * 24 * 7, negate dst\n        default:\n            output = this - that;\n    }\n\n    return asFloat ? output : absFloor(output);\n}\n\nfunction monthDiff(a, b) {\n    if (a.date() < b.date()) {\n        // end-of-month calculations work correct when the start month has more\n        // days than the end month.\n        return -monthDiff(b, a);\n    }\n    // difference in months\n    var wholeMonthDiff = (b.year() - a.year()) * 12 + (b.month() - a.month()),\n        // b is in (anchor - 1 month, anchor + 1 month)\n        anchor = a.clone().add(wholeMonthDiff, 'months'),\n        anchor2,\n        adjust;\n\n    if (b - anchor < 0) {\n        anchor2 = a.clone().add(wholeMonthDiff - 1, 'months');\n        // linear across the month\n        adjust = (b - anchor) / (anchor - anchor2);\n    } else {\n        anchor2 = a.clone().add(wholeMonthDiff + 1, 'months');\n        // linear across the month\n        adjust = (b - anchor) / (anchor2 - anchor);\n    }\n\n    //check for negative zero, return zero if negative zero\n    return -(wholeMonthDiff + adjust) || 0;\n}\n\nhooks.defaultFormat = 'YYYY-MM-DDTHH:mm:ssZ';\nhooks.defaultFormatUtc = 'YYYY-MM-DDTHH:mm:ss[Z]';\n\nfunction toString() {\n    return this.clone().locale('en').format('ddd MMM DD YYYY HH:mm:ss [GMT]ZZ');\n}\n\nfunction toISOString(keepOffset) {\n    if (!this.isValid()) {\n        return null;\n    }\n    var utc = keepOffset !== true,\n        m = utc ? this.clone().utc() : this;\n    if (m.year() < 0 || m.year() > 9999) {\n        return formatMoment(\n            m,\n            utc\n                ? 'YYYYYY-MM-DD[T]HH:mm:ss.SSS[Z]'\n                : 'YYYYYY-MM-DD[T]HH:mm:ss.SSSZ'\n        );\n    }\n    if (isFunction(Date.prototype.toISOString)) {\n        // native implementation is ~50x faster, use it when we can\n        if (utc) {\n            return this.toDate().toISOString();\n        } else {\n            return new Date(this.valueOf() + this.utcOffset() * 60 * 1000)\n                .toISOString()\n                .replace('Z', formatMoment(m, 'Z'));\n        }\n    }\n    return formatMoment(\n        m,\n        utc ? 'YYYY-MM-DD[T]HH:mm:ss.SSS[Z]' : 'YYYY-MM-DD[T]HH:mm:ss.SSSZ'\n    );\n}\n\n/**\n * Return a human readable representation of a moment that can\n * also be evaluated to get a new moment which is the same\n *\n * @link https://nodejs.org/dist/latest/docs/api/util.html#util_custom_inspect_function_on_objects\n */\nfunction inspect() {\n    if (!this.isValid()) {\n        return 'moment.invalid(/* ' + this._i + ' */)';\n    }\n    var func = 'moment',\n        zone = '',\n        prefix,\n        year,\n        datetime,\n        suffix;\n    if (!this.isLocal()) {\n        func = this.utcOffset() === 0 ? 'moment.utc' : 'moment.parseZone';\n        zone = 'Z';\n    }\n    prefix = '[' + func + '(\"]';\n    year = 0 <= this.year() && this.year() <= 9999 ? 'YYYY' : 'YYYYYY';\n    datetime = '-MM-DD[T]HH:mm:ss.SSS';\n    suffix = zone + '[\")]';\n\n    return this.format(prefix + year + datetime + suffix);\n}\n\nfunction format(inputString) {\n    if (!inputString) {\n        inputString = this.isUtc()\n            ? hooks.defaultFormatUtc\n            : hooks.defaultFormat;\n    }\n    var output = formatMoment(this, inputString);\n    return this.localeData().postformat(output);\n}\n\nfunction from(time, withoutSuffix) {\n    if (\n        this.isValid() &&\n        ((isMoment(time) && time.isValid()) || createLocal(time).isValid())\n    ) {\n        return createDuration({ to: this, from: time })\n            .locale(this.locale())\n            .humanize(!withoutSuffix);\n    } else {\n        return this.localeData().invalidDate();\n    }\n}\n\nfunction fromNow(withoutSuffix) {\n    return this.from(createLocal(), withoutSuffix);\n}\n\nfunction to(time, withoutSuffix) {\n    if (\n        this.isValid() &&\n        ((isMoment(time) && time.isValid()) || createLocal(time).isValid())\n    ) {\n        return createDuration({ from: this, to: time })\n            .locale(this.locale())\n            .humanize(!withoutSuffix);\n    } else {\n        return this.localeData().invalidDate();\n    }\n}\n\nfunction toNow(withoutSuffix) {\n    return this.to(createLocal(), withoutSuffix);\n}\n\n// If passed a locale key, it will set the locale for this\n// instance.  Otherwise, it will return the locale configuration\n// variables for this instance.\nfunction locale(key) {\n    var newLocaleData;\n\n    if (key === undefined) {\n        return this._locale._abbr;\n    } else {\n        newLocaleData = getLocale(key);\n        if (newLocaleData != null) {\n            this._locale = newLocaleData;\n        }\n        return this;\n    }\n}\n\nvar lang = deprecate(\n    'moment().lang() is deprecated. Instead, use moment().localeData() to get the language configuration. Use moment().locale() to change languages.',\n    function (key) {\n        if (key === undefined) {\n            return this.localeData();\n        } else {\n            return this.locale(key);\n        }\n    }\n);\n\nfunction localeData() {\n    return this._locale;\n}\n\nvar MS_PER_SECOND = 1000,\n    MS_PER_MINUTE = 60 * MS_PER_SECOND,\n    MS_PER_HOUR = 60 * MS_PER_MINUTE,\n    MS_PER_400_YEARS = (365 * 400 + 97) * 24 * MS_PER_HOUR;\n\n// actual modulo - handles negative numbers (for dates before 1970):\nfunction mod$1(dividend, divisor) {\n    return ((dividend % divisor) + divisor) % divisor;\n}\n\nfunction localStartOfDate(y, m, d) {\n    // the date constructor remaps years 0-99 to 1900-1999\n    if (y < 100 && y >= 0) {\n        // preserve leap years using a full 400 year cycle, then reset\n        return new Date(y + 400, m, d) - MS_PER_400_YEARS;\n    } else {\n        return new Date(y, m, d).valueOf();\n    }\n}\n\nfunction utcStartOfDate(y, m, d) {\n    // Date.UTC remaps years 0-99 to 1900-1999\n    if (y < 100 && y >= 0) {\n        // preserve leap years using a full 400 year cycle, then reset\n        return Date.UTC(y + 400, m, d) - MS_PER_400_YEARS;\n    } else {\n        return Date.UTC(y, m, d);\n    }\n}\n\nfunction startOf(units) {\n    var time, startOfDate;\n    units = normalizeUnits(units);\n    if (units === undefined || units === 'millisecond' || !this.isValid()) {\n        return this;\n    }\n\n    startOfDate = this._isUTC ? utcStartOfDate : localStartOfDate;\n\n    switch (units) {\n        case 'year':\n            time = startOfDate(this.year(), 0, 1);\n            break;\n        case 'quarter':\n            time = startOfDate(\n                this.year(),\n                this.month() - (this.month() % 3),\n                1\n            );\n            break;\n        case 'month':\n            time = startOfDate(this.year(), this.month(), 1);\n            break;\n        case 'week':\n            time = startOfDate(\n                this.year(),\n                this.month(),\n                this.date() - this.weekday()\n            );\n            break;\n        case 'isoWeek':\n            time = startOfDate(\n                this.year(),\n                this.month(),\n                this.date() - (this.isoWeekday() - 1)\n            );\n            break;\n        case 'day':\n        case 'date':\n            time = startOfDate(this.year(), this.month(), this.date());\n            break;\n        case 'hour':\n            time = this._d.valueOf();\n            time -= mod$1(\n                time + (this._isUTC ? 0 : this.utcOffset() * MS_PER_MINUTE),\n                MS_PER_HOUR\n            );\n            break;\n        case 'minute':\n            time = this._d.valueOf();\n            time -= mod$1(time, MS_PER_MINUTE);\n            break;\n        case 'second':\n            time = this._d.valueOf();\n            time -= mod$1(time, MS_PER_SECOND);\n            break;\n    }\n\n    this._d.setTime(time);\n    hooks.updateOffset(this, true);\n    return this;\n}\n\nfunction endOf(units) {\n    var time, startOfDate;\n    units = normalizeUnits(units);\n    if (units === undefined || units === 'millisecond' || !this.isValid()) {\n        return this;\n    }\n\n    startOfDate = this._isUTC ? utcStartOfDate : localStartOfDate;\n\n    switch (units) {\n        case 'year':\n            time = startOfDate(this.year() + 1, 0, 1) - 1;\n            break;\n        case 'quarter':\n            time =\n                startOfDate(\n                    this.year(),\n                    this.month() - (this.month() % 3) + 3,\n                    1\n                ) - 1;\n            break;\n        case 'month':\n            time = startOfDate(this.year(), this.month() + 1, 1) - 1;\n            break;\n        case 'week':\n            time =\n                startOfDate(\n                    this.year(),\n                    this.month(),\n                    this.date() - this.weekday() + 7\n                ) - 1;\n            break;\n        case 'isoWeek':\n            time =\n                startOfDate(\n                    this.year(),\n                    this.month(),\n                    this.date() - (this.isoWeekday() - 1) + 7\n                ) - 1;\n            break;\n        case 'day':\n        case 'date':\n            time = startOfDate(this.year(), this.month(), this.date() + 1) - 1;\n            break;\n        case 'hour':\n            time = this._d.valueOf();\n            time +=\n                MS_PER_HOUR -\n                mod$1(\n                    time + (this._isUTC ? 0 : this.utcOffset() * MS_PER_MINUTE),\n                    MS_PER_HOUR\n                ) -\n                1;\n            break;\n        case 'minute':\n            time = this._d.valueOf();\n            time += MS_PER_MINUTE - mod$1(time, MS_PER_MINUTE) - 1;\n            break;\n        case 'second':\n            time = this._d.valueOf();\n            time += MS_PER_SECOND - mod$1(time, MS_PER_SECOND) - 1;\n            break;\n    }\n\n    this._d.setTime(time);\n    hooks.updateOffset(this, true);\n    return this;\n}\n\nfunction valueOf() {\n    return this._d.valueOf() - (this._offset || 0) * 60000;\n}\n\nfunction unix() {\n    return Math.floor(this.valueOf() / 1000);\n}\n\nfunction toDate() {\n    return new Date(this.valueOf());\n}\n\nfunction toArray() {\n    var m = this;\n    return [\n        m.year(),\n        m.month(),\n        m.date(),\n        m.hour(),\n        m.minute(),\n        m.second(),\n        m.millisecond(),\n    ];\n}\n\nfunction toObject() {\n    var m = this;\n    return {\n        years: m.year(),\n        months: m.month(),\n        date: m.date(),\n        hours: m.hours(),\n        minutes: m.minutes(),\n        seconds: m.seconds(),\n        milliseconds: m.milliseconds(),\n    };\n}\n\nfunction toJSON() {\n    // new Date(NaN).toJSON() === null\n    return this.isValid() ? this.toISOString() : null;\n}\n\nfunction isValid$2() {\n    return isValid(this);\n}\n\nfunction parsingFlags() {\n    return extend({}, getParsingFlags(this));\n}\n\nfunction invalidAt() {\n    return getParsingFlags(this).overflow;\n}\n\nfunction creationData() {\n    return {\n        input: this._i,\n        format: this._f,\n        locale: this._locale,\n        isUTC: this._isUTC,\n        strict: this._strict,\n    };\n}\n\naddFormatToken('N', 0, 0, 'eraAbbr');\naddFormatToken('NN', 0, 0, 'eraAbbr');\naddFormatToken('NNN', 0, 0, 'eraAbbr');\naddFormatToken('NNNN', 0, 0, 'eraName');\naddFormatToken('NNNNN', 0, 0, 'eraNarrow');\n\naddFormatToken('y', ['y', 1], 'yo', 'eraYear');\naddFormatToken('y', ['yy', 2], 0, 'eraYear');\naddFormatToken('y', ['yyy', 3], 0, 'eraYear');\naddFormatToken('y', ['yyyy', 4], 0, 'eraYear');\n\naddRegexToken('N', matchEraAbbr);\naddRegexToken('NN', matchEraAbbr);\naddRegexToken('NNN', matchEraAbbr);\naddRegexToken('NNNN', matchEraName);\naddRegexToken('NNNNN', matchEraNarrow);\n\naddParseToken(\n    ['N', 'NN', 'NNN', 'NNNN', 'NNNNN'],\n    function (input, array, config, token) {\n        var era = config._locale.erasParse(input, token, config._strict);\n        if (era) {\n            getParsingFlags(config).era = era;\n        } else {\n            getParsingFlags(config).invalidEra = input;\n        }\n    }\n);\n\naddRegexToken('y', matchUnsigned);\naddRegexToken('yy', matchUnsigned);\naddRegexToken('yyy', matchUnsigned);\naddRegexToken('yyyy', matchUnsigned);\naddRegexToken('yo', matchEraYearOrdinal);\n\naddParseToken(['y', 'yy', 'yyy', 'yyyy'], YEAR);\naddParseToken(['yo'], function (input, array, config, token) {\n    var match;\n    if (config._locale._eraYearOrdinalRegex) {\n        match = input.match(config._locale._eraYearOrdinalRegex);\n    }\n\n    if (config._locale.eraYearOrdinalParse) {\n        array[YEAR] = config._locale.eraYearOrdinalParse(input, match);\n    } else {\n        array[YEAR] = parseInt(input, 10);\n    }\n});\n\nfunction localeEras(m, format) {\n    var i,\n        l,\n        date,\n        eras = this._eras || getLocale('en')._eras;\n    for (i = 0, l = eras.length; i < l; ++i) {\n        switch (typeof eras[i].since) {\n            case 'string':\n                // truncate time\n                date = hooks(eras[i].since).startOf('day');\n                eras[i].since = date.valueOf();\n                break;\n        }\n\n        switch (typeof eras[i].until) {\n            case 'undefined':\n                eras[i].until = +Infinity;\n                break;\n            case 'string':\n                // truncate time\n                date = hooks(eras[i].until).startOf('day').valueOf();\n                eras[i].until = date.valueOf();\n                break;\n        }\n    }\n    return eras;\n}\n\nfunction localeErasParse(eraName, format, strict) {\n    var i,\n        l,\n        eras = this.eras(),\n        name,\n        abbr,\n        narrow;\n    eraName = eraName.toUpperCase();\n\n    for (i = 0, l = eras.length; i < l; ++i) {\n        name = eras[i].name.toUpperCase();\n        abbr = eras[i].abbr.toUpperCase();\n        narrow = eras[i].narrow.toUpperCase();\n\n        if (strict) {\n            switch (format) {\n                case 'N':\n                case 'NN':\n                case 'NNN':\n                    if (abbr === eraName) {\n                        return eras[i];\n                    }\n                    break;\n\n                case 'NNNN':\n                    if (name === eraName) {\n                        return eras[i];\n                    }\n                    break;\n\n                case 'NNNNN':\n                    if (narrow === eraName) {\n                        return eras[i];\n                    }\n                    break;\n            }\n        } else if ([name, abbr, narrow].indexOf(eraName) >= 0) {\n            return eras[i];\n        }\n    }\n}\n\nfunction localeErasConvertYear(era, year) {\n    var dir = era.since <= era.until ? +1 : -1;\n    if (year === undefined) {\n        return hooks(era.since).year();\n    } else {\n        return hooks(era.since).year() + (year - era.offset) * dir;\n    }\n}\n\nfunction getEraName() {\n    var i,\n        l,\n        val,\n        eras = this.localeData().eras();\n    for (i = 0, l = eras.length; i < l; ++i) {\n        // truncate time\n        val = this.clone().startOf('day').valueOf();\n\n        if (eras[i].since <= val && val <= eras[i].until) {\n            return eras[i].name;\n        }\n        if (eras[i].until <= val && val <= eras[i].since) {\n            return eras[i].name;\n        }\n    }\n\n    return '';\n}\n\nfunction getEraNarrow() {\n    var i,\n        l,\n        val,\n        eras = this.localeData().eras();\n    for (i = 0, l = eras.length; i < l; ++i) {\n        // truncate time\n        val = this.clone().startOf('day').valueOf();\n\n        if (eras[i].since <= val && val <= eras[i].until) {\n            return eras[i].narrow;\n        }\n        if (eras[i].until <= val && val <= eras[i].since) {\n            return eras[i].narrow;\n        }\n    }\n\n    return '';\n}\n\nfunction getEraAbbr() {\n    var i,\n        l,\n        val,\n        eras = this.localeData().eras();\n    for (i = 0, l = eras.length; i < l; ++i) {\n        // truncate time\n        val = this.clone().startOf('day').valueOf();\n\n        if (eras[i].since <= val && val <= eras[i].until) {\n            return eras[i].abbr;\n        }\n        if (eras[i].until <= val && val <= eras[i].since) {\n            return eras[i].abbr;\n        }\n    }\n\n    return '';\n}\n\nfunction getEraYear() {\n    var i,\n        l,\n        dir,\n        val,\n        eras = this.localeData().eras();\n    for (i = 0, l = eras.length; i < l; ++i) {\n        dir = eras[i].since <= eras[i].until ? +1 : -1;\n\n        // truncate time\n        val = this.clone().startOf('day').valueOf();\n\n        if (\n            (eras[i].since <= val && val <= eras[i].until) ||\n            (eras[i].until <= val && val <= eras[i].since)\n        ) {\n            return (\n                (this.year() - hooks(eras[i].since).year()) * dir +\n                eras[i].offset\n            );\n        }\n    }\n\n    return this.year();\n}\n\nfunction erasNameRegex(isStrict) {\n    if (!hasOwnProp(this, '_erasNameRegex')) {\n        computeErasParse.call(this);\n    }\n    return isStrict ? this._erasNameRegex : this._erasRegex;\n}\n\nfunction erasAbbrRegex(isStrict) {\n    if (!hasOwnProp(this, '_erasAbbrRegex')) {\n        computeErasParse.call(this);\n    }\n    return isStrict ? this._erasAbbrRegex : this._erasRegex;\n}\n\nfunction erasNarrowRegex(isStrict) {\n    if (!hasOwnProp(this, '_erasNarrowRegex')) {\n        computeErasParse.call(this);\n    }\n    return isStrict ? this._erasNarrowRegex : this._erasRegex;\n}\n\nfunction matchEraAbbr(isStrict, locale) {\n    return locale.erasAbbrRegex(isStrict);\n}\n\nfunction matchEraName(isStrict, locale) {\n    return locale.erasNameRegex(isStrict);\n}\n\nfunction matchEraNarrow(isStrict, locale) {\n    return locale.erasNarrowRegex(isStrict);\n}\n\nfunction matchEraYearOrdinal(isStrict, locale) {\n    return locale._eraYearOrdinalRegex || matchUnsigned;\n}\n\nfunction computeErasParse() {\n    var abbrPieces = [],\n        namePieces = [],\n        narrowPieces = [],\n        mixedPieces = [],\n        i,\n        l,\n        erasName,\n        erasAbbr,\n        erasNarrow,\n        eras = this.eras();\n\n    for (i = 0, l = eras.length; i < l; ++i) {\n        erasName = regexEscape(eras[i].name);\n        erasAbbr = regexEscape(eras[i].abbr);\n        erasNarrow = regexEscape(eras[i].narrow);\n\n        namePieces.push(erasName);\n        abbrPieces.push(erasAbbr);\n        narrowPieces.push(erasNarrow);\n        mixedPieces.push(erasName);\n        mixedPieces.push(erasAbbr);\n        mixedPieces.push(erasNarrow);\n    }\n\n    this._erasRegex = new RegExp('^(' + mixedPieces.join('|') + ')', 'i');\n    this._erasNameRegex = new RegExp('^(' + namePieces.join('|') + ')', 'i');\n    this._erasAbbrRegex = new RegExp('^(' + abbrPieces.join('|') + ')', 'i');\n    this._erasNarrowRegex = new RegExp(\n        '^(' + narrowPieces.join('|') + ')',\n        'i'\n    );\n}\n\n// FORMATTING\n\naddFormatToken(0, ['gg', 2], 0, function () {\n    return this.weekYear() % 100;\n});\n\naddFormatToken(0, ['GG', 2], 0, function () {\n    return this.isoWeekYear() % 100;\n});\n\nfunction addWeekYearFormatToken(token, getter) {\n    addFormatToken(0, [token, token.length], 0, getter);\n}\n\naddWeekYearFormatToken('gggg', 'weekYear');\naddWeekYearFormatToken('ggggg', 'weekYear');\naddWeekYearFormatToken('GGGG', 'isoWeekYear');\naddWeekYearFormatToken('GGGGG', 'isoWeekYear');\n\n// ALIASES\n\n// PARSING\n\naddRegexToken('G', matchSigned);\naddRegexToken('g', matchSigned);\naddRegexToken('GG', match1to2, match2);\naddRegexToken('gg', match1to2, match2);\naddRegexToken('GGGG', match1to4, match4);\naddRegexToken('gggg', match1to4, match4);\naddRegexToken('GGGGG', match1to6, match6);\naddRegexToken('ggggg', match1to6, match6);\n\naddWeekParseToken(\n    ['gggg', 'ggggg', 'GGGG', 'GGGGG'],\n    function (input, week, config, token) {\n        week[token.substr(0, 2)] = toInt(input);\n    }\n);\n\naddWeekParseToken(['gg', 'GG'], function (input, week, config, token) {\n    week[token] = hooks.parseTwoDigitYear(input);\n});\n\n// MOMENTS\n\nfunction getSetWeekYear(input) {\n    return getSetWeekYearHelper.call(\n        this,\n        input,\n        this.week(),\n        this.weekday() + this.localeData()._week.dow,\n        this.localeData()._week.dow,\n        this.localeData()._week.doy\n    );\n}\n\nfunction getSetISOWeekYear(input) {\n    return getSetWeekYearHelper.call(\n        this,\n        input,\n        this.isoWeek(),\n        this.isoWeekday(),\n        1,\n        4\n    );\n}\n\nfunction getISOWeeksInYear() {\n    return weeksInYear(this.year(), 1, 4);\n}\n\nfunction getISOWeeksInISOWeekYear() {\n    return weeksInYear(this.isoWeekYear(), 1, 4);\n}\n\nfunction getWeeksInYear() {\n    var weekInfo = this.localeData()._week;\n    return weeksInYear(this.year(), weekInfo.dow, weekInfo.doy);\n}\n\nfunction getWeeksInWeekYear() {\n    var weekInfo = this.localeData()._week;\n    return weeksInYear(this.weekYear(), weekInfo.dow, weekInfo.doy);\n}\n\nfunction getSetWeekYearHelper(input, week, weekday, dow, doy) {\n    var weeksTarget;\n    if (input == null) {\n        return weekOfYear(this, dow, doy).year;\n    } else {\n        weeksTarget = weeksInYear(input, dow, doy);\n        if (week > weeksTarget) {\n            week = weeksTarget;\n        }\n        return setWeekAll.call(this, input, week, weekday, dow, doy);\n    }\n}\n\nfunction setWeekAll(weekYear, week, weekday, dow, doy) {\n    var dayOfYearData = dayOfYearFromWeeks(weekYear, week, weekday, dow, doy),\n        date = createUTCDate(dayOfYearData.year, 0, dayOfYearData.dayOfYear);\n\n    this.year(date.getUTCFullYear());\n    this.month(date.getUTCMonth());\n    this.date(date.getUTCDate());\n    return this;\n}\n\n// FORMATTING\n\naddFormatToken('Q', 0, 'Qo', 'quarter');\n\n// PARSING\n\naddRegexToken('Q', match1);\naddParseToken('Q', function (input, array) {\n    array[MONTH] = (toInt(input) - 1) * 3;\n});\n\n// MOMENTS\n\nfunction getSetQuarter(input) {\n    return input == null\n        ? Math.ceil((this.month() + 1) / 3)\n        : this.month((input - 1) * 3 + (this.month() % 3));\n}\n\n// FORMATTING\n\naddFormatToken('D', ['DD', 2], 'Do', 'date');\n\n// PARSING\n\naddRegexToken('D', match1to2, match1to2NoLeadingZero);\naddRegexToken('DD', match1to2, match2);\naddRegexToken('Do', function (isStrict, locale) {\n    // TODO: Remove \"ordinalParse\" fallback in next major release.\n    return isStrict\n        ? locale._dayOfMonthOrdinalParse || locale._ordinalParse\n        : locale._dayOfMonthOrdinalParseLenient;\n});\n\naddParseToken(['D', 'DD'], DATE);\naddParseToken('Do', function (input, array) {\n    array[DATE] = toInt(input.match(match1to2)[0]);\n});\n\n// MOMENTS\n\nvar getSetDayOfMonth = makeGetSet('Date', true);\n\n// FORMATTING\n\naddFormatToken('DDD', ['DDDD', 3], 'DDDo', 'dayOfYear');\n\n// PARSING\n\naddRegexToken('DDD', match1to3);\naddRegexToken('DDDD', match3);\naddParseToken(['DDD', 'DDDD'], function (input, array, config) {\n    config._dayOfYear = toInt(input);\n});\n\n// HELPERS\n\n// MOMENTS\n\nfunction getSetDayOfYear(input) {\n    var dayOfYear =\n        Math.round(\n            (this.clone().startOf('day') - this.clone().startOf('year')) / 864e5\n        ) + 1;\n    return input == null ? dayOfYear : this.add(input - dayOfYear, 'd');\n}\n\n// FORMATTING\n\naddFormatToken('m', ['mm', 2], 0, 'minute');\n\n// PARSING\n\naddRegexToken('m', match1to2, match1to2HasZero);\naddRegexToken('mm', match1to2, match2);\naddParseToken(['m', 'mm'], MINUTE);\n\n// MOMENTS\n\nvar getSetMinute = makeGetSet('Minutes', false);\n\n// FORMATTING\n\naddFormatToken('s', ['ss', 2], 0, 'second');\n\n// PARSING\n\naddRegexToken('s', match1to2, match1to2HasZero);\naddRegexToken('ss', match1to2, match2);\naddParseToken(['s', 'ss'], SECOND);\n\n// MOMENTS\n\nvar getSetSecond = makeGetSet('Seconds', false);\n\n// FORMATTING\n\naddFormatToken('S', 0, 0, function () {\n    return ~~(this.millisecond() / 100);\n});\n\naddFormatToken(0, ['SS', 2], 0, function () {\n    return ~~(this.millisecond() / 10);\n});\n\naddFormatToken(0, ['SSS', 3], 0, 'millisecond');\naddFormatToken(0, ['SSSS', 4], 0, function () {\n    return this.millisecond() * 10;\n});\naddFormatToken(0, ['SSSSS', 5], 0, function () {\n    return this.millisecond() * 100;\n});\naddFormatToken(0, ['SSSSSS', 6], 0, function () {\n    return this.millisecond() * 1000;\n});\naddFormatToken(0, ['SSSSSSS', 7], 0, function () {\n    return this.millisecond() * 10000;\n});\naddFormatToken(0, ['SSSSSSSS', 8], 0, function () {\n    return this.millisecond() * 100000;\n});\naddFormatToken(0, ['SSSSSSSSS', 9], 0, function () {\n    return this.millisecond() * 1000000;\n});\n\n// PARSING\n\naddRegexToken('S', match1to3, match1);\naddRegexToken('SS', match1to3, match2);\naddRegexToken('SSS', match1to3, match3);\n\nvar token, getSetMillisecond;\nfor (token = 'SSSS'; token.length <= 9; token += 'S') {\n    addRegexToken(token, matchUnsigned);\n}\n\nfunction parseMs(input, array) {\n    array[MILLISECOND] = toInt(('0.' + input) * 1000);\n}\n\nfor (token = 'S'; token.length <= 9; token += 'S') {\n    addParseToken(token, parseMs);\n}\n\ngetSetMillisecond = makeGetSet('Milliseconds', false);\n\n// FORMATTING\n\naddFormatToken('z', 0, 0, 'zoneAbbr');\naddFormatToken('zz', 0, 0, 'zoneName');\n\n// MOMENTS\n\nfunction getZoneAbbr() {\n    return this._isUTC ? 'UTC' : '';\n}\n\nfunction getZoneName() {\n    return this._isUTC ? 'Coordinated Universal Time' : '';\n}\n\nvar proto = Moment.prototype;\n\nproto.add = add;\nproto.calendar = calendar$1;\nproto.clone = clone;\nproto.diff = diff;\nproto.endOf = endOf;\nproto.format = format;\nproto.from = from;\nproto.fromNow = fromNow;\nproto.to = to;\nproto.toNow = toNow;\nproto.get = stringGet;\nproto.invalidAt = invalidAt;\nproto.isAfter = isAfter;\nproto.isBefore = isBefore;\nproto.isBetween = isBetween;\nproto.isSame = isSame;\nproto.isSameOrAfter = isSameOrAfter;\nproto.isSameOrBefore = isSameOrBefore;\nproto.isValid = isValid$2;\nproto.lang = lang;\nproto.locale = locale;\nproto.localeData = localeData;\nproto.max = prototypeMax;\nproto.min = prototypeMin;\nproto.parsingFlags = parsingFlags;\nproto.set = stringSet;\nproto.startOf = startOf;\nproto.subtract = subtract;\nproto.toArray = toArray;\nproto.toObject = toObject;\nproto.toDate = toDate;\nproto.toISOString = toISOString;\nproto.inspect = inspect;\nif (typeof Symbol !== 'undefined' && Symbol.for != null) {\n    proto[Symbol.for('nodejs.util.inspect.custom')] = function () {\n        return 'Moment<' + this.format() + '>';\n    };\n}\nproto.toJSON = toJSON;\nproto.toString = toString;\nproto.unix = unix;\nproto.valueOf = valueOf;\nproto.creationData = creationData;\nproto.eraName = getEraName;\nproto.eraNarrow = getEraNarrow;\nproto.eraAbbr = getEraAbbr;\nproto.eraYear = getEraYear;\nproto.year = getSetYear;\nproto.isLeapYear = getIsLeapYear;\nproto.weekYear = getSetWeekYear;\nproto.isoWeekYear = getSetISOWeekYear;\nproto.quarter = proto.quarters = getSetQuarter;\nproto.month = getSetMonth;\nproto.daysInMonth = getDaysInMonth;\nproto.week = proto.weeks = getSetWeek;\nproto.isoWeek = proto.isoWeeks = getSetISOWeek;\nproto.weeksInYear = getWeeksInYear;\nproto.weeksInWeekYear = getWeeksInWeekYear;\nproto.isoWeeksInYear = getISOWeeksInYear;\nproto.isoWeeksInISOWeekYear = getISOWeeksInISOWeekYear;\nproto.date = getSetDayOfMonth;\nproto.day = proto.days = getSetDayOfWeek;\nproto.weekday = getSetLocaleDayOfWeek;\nproto.isoWeekday = getSetISODayOfWeek;\nproto.dayOfYear = getSetDayOfYear;\nproto.hour = proto.hours = getSetHour;\nproto.minute = proto.minutes = getSetMinute;\nproto.second = proto.seconds = getSetSecond;\nproto.millisecond = proto.milliseconds = getSetMillisecond;\nproto.utcOffset = getSetOffset;\nproto.utc = setOffsetToUTC;\nproto.local = setOffsetToLocal;\nproto.parseZone = setOffsetToParsedOffset;\nproto.hasAlignedHourOffset = hasAlignedHourOffset;\nproto.isDST = isDaylightSavingTime;\nproto.isLocal = isLocal;\nproto.isUtcOffset = isUtcOffset;\nproto.isUtc = isUtc;\nproto.isUTC = isUtc;\nproto.zoneAbbr = getZoneAbbr;\nproto.zoneName = getZoneName;\nproto.dates = deprecate(\n    'dates accessor is deprecated. Use date instead.',\n    getSetDayOfMonth\n);\nproto.months = deprecate(\n    'months accessor is deprecated. Use month instead',\n    getSetMonth\n);\nproto.years = deprecate(\n    'years accessor is deprecated. Use year instead',\n    getSetYear\n);\nproto.zone = deprecate(\n    'moment().zone is deprecated, use moment().utcOffset instead. http://momentjs.com/guides/#/warnings/zone/',\n    getSetZone\n);\nproto.isDSTShifted = deprecate(\n    'isDSTShifted is deprecated. See http://momentjs.com/guides/#/warnings/dst-shifted/ for more information',\n    isDaylightSavingTimeShifted\n);\n\nfunction createUnix(input) {\n    return createLocal(input * 1000);\n}\n\nfunction createInZone() {\n    return createLocal.apply(null, arguments).parseZone();\n}\n\nfunction preParsePostFormat(string) {\n    return string;\n}\n\nvar proto$1 = Locale.prototype;\n\nproto$1.calendar = calendar;\nproto$1.longDateFormat = longDateFormat;\nproto$1.invalidDate = invalidDate;\nproto$1.ordinal = ordinal;\nproto$1.preparse = preParsePostFormat;\nproto$1.postformat = preParsePostFormat;\nproto$1.relativeTime = relativeTime;\nproto$1.pastFuture = pastFuture;\nproto$1.set = set;\nproto$1.eras = localeEras;\nproto$1.erasParse = localeErasParse;\nproto$1.erasConvertYear = localeErasConvertYear;\nproto$1.erasAbbrRegex = erasAbbrRegex;\nproto$1.erasNameRegex = erasNameRegex;\nproto$1.erasNarrowRegex = erasNarrowRegex;\n\nproto$1.months = localeMonths;\nproto$1.monthsShort = localeMonthsShort;\nproto$1.monthsParse = localeMonthsParse;\nproto$1.monthsRegex = monthsRegex;\nproto$1.monthsShortRegex = monthsShortRegex;\nproto$1.week = localeWeek;\nproto$1.firstDayOfYear = localeFirstDayOfYear;\nproto$1.firstDayOfWeek = localeFirstDayOfWeek;\n\nproto$1.weekdays = localeWeekdays;\nproto$1.weekdaysMin = localeWeekdaysMin;\nproto$1.weekdaysShort = localeWeekdaysShort;\nproto$1.weekdaysParse = localeWeekdaysParse;\n\nproto$1.weekdaysRegex = weekdaysRegex;\nproto$1.weekdaysShortRegex = weekdaysShortRegex;\nproto$1.weekdaysMinRegex = weekdaysMinRegex;\n\nproto$1.isPM = localeIsPM;\nproto$1.meridiem = localeMeridiem;\n\nfunction get$1(format, index, field, setter) {\n    var locale = getLocale(),\n        utc = createUTC().set(setter, index);\n    return locale[field](utc, format);\n}\n\nfunction listMonthsImpl(format, index, field) {\n    if (isNumber(format)) {\n        index = format;\n        format = undefined;\n    }\n\n    format = format || '';\n\n    if (index != null) {\n        return get$1(format, index, field, 'month');\n    }\n\n    var i,\n        out = [];\n    for (i = 0; i < 12; i++) {\n        out[i] = get$1(format, i, field, 'month');\n    }\n    return out;\n}\n\n// ()\n// (5)\n// (fmt, 5)\n// (fmt)\n// (true)\n// (true, 5)\n// (true, fmt, 5)\n// (true, fmt)\nfunction listWeekdaysImpl(localeSorted, format, index, field) {\n    if (typeof localeSorted === 'boolean') {\n        if (isNumber(format)) {\n            index = format;\n            format = undefined;\n        }\n\n        format = format || '';\n    } else {\n        format = localeSorted;\n        index = format;\n        localeSorted = false;\n\n        if (isNumber(format)) {\n            index = format;\n            format = undefined;\n        }\n\n        format = format || '';\n    }\n\n    var locale = getLocale(),\n        shift = localeSorted ? locale._week.dow : 0,\n        i,\n        out = [];\n\n    if (index != null) {\n        return get$1(format, (index + shift) % 7, field, 'day');\n    }\n\n    for (i = 0; i < 7; i++) {\n        out[i] = get$1(format, (i + shift) % 7, field, 'day');\n    }\n    return out;\n}\n\nfunction listMonths(format, index) {\n    return listMonthsImpl(format, index, 'months');\n}\n\nfunction listMonthsShort(format, index) {\n    return listMonthsImpl(format, index, 'monthsShort');\n}\n\nfunction listWeekdays(localeSorted, format, index) {\n    return listWeekdaysImpl(localeSorted, format, index, 'weekdays');\n}\n\nfunction listWeekdaysShort(localeSorted, format, index) {\n    return listWeekdaysImpl(localeSorted, format, index, 'weekdaysShort');\n}\n\nfunction listWeekdaysMin(localeSorted, format, index) {\n    return listWeekdaysImpl(localeSorted, format, index, 'weekdaysMin');\n}\n\ngetSetGlobalLocale('en', {\n    eras: [\n        {\n            since: '0001-01-01',\n            until: +Infinity,\n            offset: 1,\n            name: 'Anno Domini',\n            narrow: 'AD',\n            abbr: 'AD',\n        },\n        {\n            since: '0000-12-31',\n            until: -Infinity,\n            offset: 1,\n            name: 'Before Christ',\n            narrow: 'BC',\n            abbr: 'BC',\n        },\n    ],\n    dayOfMonthOrdinalParse: /\\d{1,2}(th|st|nd|rd)/,\n    ordinal: function (number) {\n        var b = number % 10,\n            output =\n                toInt((number % 100) / 10) === 1\n                    ? 'th'\n                    : b === 1\n                      ? 'st'\n                      : b === 2\n                        ? 'nd'\n                        : b === 3\n                          ? 'rd'\n                          : 'th';\n        return number + output;\n    },\n});\n\n// Side effect imports\n\nhooks.lang = deprecate(\n    'moment.lang is deprecated. Use moment.locale instead.',\n    getSetGlobalLocale\n);\nhooks.langData = deprecate(\n    'moment.langData is deprecated. Use moment.localeData instead.',\n    getLocale\n);\n\nvar mathAbs = Math.abs;\n\nfunction abs() {\n    var data = this._data;\n\n    this._milliseconds = mathAbs(this._milliseconds);\n    this._days = mathAbs(this._days);\n    this._months = mathAbs(this._months);\n\n    data.milliseconds = mathAbs(data.milliseconds);\n    data.seconds = mathAbs(data.seconds);\n    data.minutes = mathAbs(data.minutes);\n    data.hours = mathAbs(data.hours);\n    data.months = mathAbs(data.months);\n    data.years = mathAbs(data.years);\n\n    return this;\n}\n\nfunction addSubtract$1(duration, input, value, direction) {\n    var other = createDuration(input, value);\n\n    duration._milliseconds += direction * other._milliseconds;\n    duration._days += direction * other._days;\n    duration._months += direction * other._months;\n\n    return duration._bubble();\n}\n\n// supports only 2.0-style add(1, 's') or add(duration)\nfunction add$1(input, value) {\n    return addSubtract$1(this, input, value, 1);\n}\n\n// supports only 2.0-style subtract(1, 's') or subtract(duration)\nfunction subtract$1(input, value) {\n    return addSubtract$1(this, input, value, -1);\n}\n\nfunction absCeil(number) {\n    if (number < 0) {\n        return Math.floor(number);\n    } else {\n        return Math.ceil(number);\n    }\n}\n\nfunction bubble() {\n    var milliseconds = this._milliseconds,\n        days = this._days,\n        months = this._months,\n        data = this._data,\n        seconds,\n        minutes,\n        hours,\n        years,\n        monthsFromDays;\n\n    // if we have a mix of positive and negative values, bubble down first\n    // check: https://github.com/moment/moment/issues/2166\n    if (\n        !(\n            (milliseconds >= 0 && days >= 0 && months >= 0) ||\n            (milliseconds <= 0 && days <= 0 && months <= 0)\n        )\n    ) {\n        milliseconds += absCeil(monthsToDays(months) + days) * 864e5;\n        days = 0;\n        months = 0;\n    }\n\n    // The following code bubbles up values, see the tests for\n    // examples of what that means.\n    data.milliseconds = milliseconds % 1000;\n\n    seconds = absFloor(milliseconds / 1000);\n    data.seconds = seconds % 60;\n\n    minutes = absFloor(seconds / 60);\n    data.minutes = minutes % 60;\n\n    hours = absFloor(minutes / 60);\n    data.hours = hours % 24;\n\n    days += absFloor(hours / 24);\n\n    // convert days to months\n    monthsFromDays = absFloor(daysToMonths(days));\n    months += monthsFromDays;\n    days -= absCeil(monthsToDays(monthsFromDays));\n\n    // 12 months -> 1 year\n    years = absFloor(months / 12);\n    months %= 12;\n\n    data.days = days;\n    data.months = months;\n    data.years = years;\n\n    return this;\n}\n\nfunction daysToMonths(days) {\n    // 400 years have 146097 days (taking into account leap year rules)\n    // 400 years have 12 months === 4800\n    return (days * 4800) / 146097;\n}\n\nfunction monthsToDays(months) {\n    // the reverse of daysToMonths\n    return (months * 146097) / 4800;\n}\n\nfunction as(units) {\n    if (!this.isValid()) {\n        return NaN;\n    }\n    var days,\n        months,\n        milliseconds = this._milliseconds;\n\n    units = normalizeUnits(units);\n\n    if (units === 'month' || units === 'quarter' || units === 'year') {\n        days = this._days + milliseconds / 864e5;\n        months = this._months + daysToMonths(days);\n        switch (units) {\n            case 'month':\n                return months;\n            case 'quarter':\n                return months / 3;\n            case 'year':\n                return months / 12;\n        }\n    } else {\n        // handle milliseconds separately because of floating point math errors (issue #1867)\n        days = this._days + Math.round(monthsToDays(this._months));\n        switch (units) {\n            case 'week':\n                return days / 7 + milliseconds / 6048e5;\n            case 'day':\n                return days + milliseconds / 864e5;\n            case 'hour':\n                return days * 24 + milliseconds / 36e5;\n            case 'minute':\n                return days * 1440 + milliseconds / 6e4;\n            case 'second':\n                return days * 86400 + milliseconds / 1000;\n            // Math.floor prevents floating point math errors here\n            case 'millisecond':\n                return Math.floor(days * 864e5) + milliseconds;\n            default:\n                throw new Error('Unknown unit ' + units);\n        }\n    }\n}\n\nfunction makeAs(alias) {\n    return function () {\n        return this.as(alias);\n    };\n}\n\nvar asMilliseconds = makeAs('ms'),\n    asSeconds = makeAs('s'),\n    asMinutes = makeAs('m'),\n    asHours = makeAs('h'),\n    asDays = makeAs('d'),\n    asWeeks = makeAs('w'),\n    asMonths = makeAs('M'),\n    asQuarters = makeAs('Q'),\n    asYears = makeAs('y'),\n    valueOf$1 = asMilliseconds;\n\nfunction clone$1() {\n    return createDuration(this);\n}\n\nfunction get$2(units) {\n    units = normalizeUnits(units);\n    return this.isValid() ? this[units + 's']() : NaN;\n}\n\nfunction makeGetter(name) {\n    return function () {\n        return this.isValid() ? this._data[name] : NaN;\n    };\n}\n\nvar milliseconds = makeGetter('milliseconds'),\n    seconds = makeGetter('seconds'),\n    minutes = makeGetter('minutes'),\n    hours = makeGetter('hours'),\n    days = makeGetter('days'),\n    months = makeGetter('months'),\n    years = makeGetter('years');\n\nfunction weeks() {\n    return absFloor(this.days() / 7);\n}\n\nvar round = Math.round,\n    thresholds = {\n        ss: 44, // a few seconds to seconds\n        s: 45, // seconds to minute\n        m: 45, // minutes to hour\n        h: 22, // hours to day\n        d: 26, // days to month/week\n        w: null, // weeks to month\n        M: 11, // months to year\n    };\n\n// helper function for moment.fn.from, moment.fn.fromNow, and moment.duration.fn.humanize\nfunction substituteTimeAgo(string, number, withoutSuffix, isFuture, locale) {\n    return locale.relativeTime(number || 1, !!withoutSuffix, string, isFuture);\n}\n\nfunction relativeTime$1(posNegDuration, withoutSuffix, thresholds, locale) {\n    var duration = createDuration(posNegDuration).abs(),\n        seconds = round(duration.as('s')),\n        minutes = round(duration.as('m')),\n        hours = round(duration.as('h')),\n        days = round(duration.as('d')),\n        months = round(duration.as('M')),\n        weeks = round(duration.as('w')),\n        years = round(duration.as('y')),\n        a =\n            (seconds <= thresholds.ss && ['s', seconds]) ||\n            (seconds < thresholds.s && ['ss', seconds]) ||\n            (minutes <= 1 && ['m']) ||\n            (minutes < thresholds.m && ['mm', minutes]) ||\n            (hours <= 1 && ['h']) ||\n            (hours < thresholds.h && ['hh', hours]) ||\n            (days <= 1 && ['d']) ||\n            (days < thresholds.d && ['dd', days]);\n\n    if (thresholds.w != null) {\n        a =\n            a ||\n            (weeks <= 1 && ['w']) ||\n            (weeks < thresholds.w && ['ww', weeks]);\n    }\n    a = a ||\n        (months <= 1 && ['M']) ||\n        (months < thresholds.M && ['MM', months]) ||\n        (years <= 1 && ['y']) || ['yy', years];\n\n    a[2] = withoutSuffix;\n    a[3] = +posNegDuration > 0;\n    a[4] = locale;\n    return substituteTimeAgo.apply(null, a);\n}\n\n// This function allows you to set the rounding function for relative time strings\nfunction getSetRelativeTimeRounding(roundingFunction) {\n    if (roundingFunction === undefined) {\n        return round;\n    }\n    if (typeof roundingFunction === 'function') {\n        round = roundingFunction;\n        return true;\n    }\n    return false;\n}\n\n// This function allows you to set a threshold for relative time strings\nfunction getSetRelativeTimeThreshold(threshold, limit) {\n    if (thresholds[threshold] === undefined) {\n        return false;\n    }\n    if (limit === undefined) {\n        return thresholds[threshold];\n    }\n    thresholds[threshold] = limit;\n    if (threshold === 's') {\n        thresholds.ss = limit - 1;\n    }\n    return true;\n}\n\nfunction humanize(argWithSuffix, argThresholds) {\n    if (!this.isValid()) {\n        return this.localeData().invalidDate();\n    }\n\n    var withSuffix = false,\n        th = thresholds,\n        locale,\n        output;\n\n    if (typeof argWithSuffix === 'object') {\n        argThresholds = argWithSuffix;\n        argWithSuffix = false;\n    }\n    if (typeof argWithSuffix === 'boolean') {\n        withSuffix = argWithSuffix;\n    }\n    if (typeof argThresholds === 'object') {\n        th = Object.assign({}, thresholds, argThresholds);\n        if (argThresholds.s != null && argThresholds.ss == null) {\n            th.ss = argThresholds.s - 1;\n        }\n    }\n\n    locale = this.localeData();\n    output = relativeTime$1(this, !withSuffix, th, locale);\n\n    if (withSuffix) {\n        output = locale.pastFuture(+this, output);\n    }\n\n    return locale.postformat(output);\n}\n\nvar abs$1 = Math.abs;\n\nfunction sign(x) {\n    return (x > 0) - (x < 0) || +x;\n}\n\nfunction toISOString$1() {\n    // for ISO strings we do not use the normal bubbling rules:\n    //  * milliseconds bubble up until they become hours\n    //  * days do not bubble at all\n    //  * months bubble up until they become years\n    // This is because there is no context-free conversion between hours and days\n    // (think of clock changes)\n    // and also not between days and months (28-31 days per month)\n    if (!this.isValid()) {\n        return this.localeData().invalidDate();\n    }\n\n    var seconds = abs$1(this._milliseconds) / 1000,\n        days = abs$1(this._days),\n        months = abs$1(this._months),\n        minutes,\n        hours,\n        years,\n        s,\n        total = this.asSeconds(),\n        totalSign,\n        ymSign,\n        daysSign,\n        hmsSign;\n\n    if (!total) {\n        // this is the same as C#'s (Noda) and python (isodate)...\n        // but not other JS (goog.date)\n        return 'P0D';\n    }\n\n    // 3600 seconds -> 60 minutes -> 1 hour\n    minutes = absFloor(seconds / 60);\n    hours = absFloor(minutes / 60);\n    seconds %= 60;\n    minutes %= 60;\n\n    // 12 months -> 1 year\n    years = absFloor(months / 12);\n    months %= 12;\n\n    // inspired by https://github.com/dordille/moment-isoduration/blob/master/moment.isoduration.js\n    s = seconds ? seconds.toFixed(3).replace(/\\.?0+$/, '') : '';\n\n    totalSign = total < 0 ? '-' : '';\n    ymSign = sign(this._months) !== sign(total) ? '-' : '';\n    daysSign = sign(this._days) !== sign(total) ? '-' : '';\n    hmsSign = sign(this._milliseconds) !== sign(total) ? '-' : '';\n\n    return (\n        totalSign +\n        'P' +\n        (years ? ymSign + years + 'Y' : '') +\n        (months ? ymSign + months + 'M' : '') +\n        (days ? daysSign + days + 'D' : '') +\n        (hours || minutes || seconds ? 'T' : '') +\n        (hours ? hmsSign + hours + 'H' : '') +\n        (minutes ? hmsSign + minutes + 'M' : '') +\n        (seconds ? hmsSign + s + 'S' : '')\n    );\n}\n\nvar proto$2 = Duration.prototype;\n\nproto$2.isValid = isValid$1;\nproto$2.abs = abs;\nproto$2.add = add$1;\nproto$2.subtract = subtract$1;\nproto$2.as = as;\nproto$2.asMilliseconds = asMilliseconds;\nproto$2.asSeconds = asSeconds;\nproto$2.asMinutes = asMinutes;\nproto$2.asHours = asHours;\nproto$2.asDays = asDays;\nproto$2.asWeeks = asWeeks;\nproto$2.asMonths = asMonths;\nproto$2.asQuarters = asQuarters;\nproto$2.asYears = asYears;\nproto$2.valueOf = valueOf$1;\nproto$2._bubble = bubble;\nproto$2.clone = clone$1;\nproto$2.get = get$2;\nproto$2.milliseconds = milliseconds;\nproto$2.seconds = seconds;\nproto$2.minutes = minutes;\nproto$2.hours = hours;\nproto$2.days = days;\nproto$2.weeks = weeks;\nproto$2.months = months;\nproto$2.years = years;\nproto$2.humanize = humanize;\nproto$2.toISOString = toISOString$1;\nproto$2.toString = toISOString$1;\nproto$2.toJSON = toISOString$1;\nproto$2.locale = locale;\nproto$2.localeData = localeData;\n\nproto$2.toIsoString = deprecate(\n    'toIsoString() is deprecated. Please use toISOString() instead (notice the capitals)',\n    toISOString$1\n);\nproto$2.lang = lang;\n\n// FORMATTING\n\naddFormatToken('X', 0, 0, 'unix');\naddFormatToken('x', 0, 0, 'valueOf');\n\n// PARSING\n\naddRegexToken('x', matchSigned);\naddRegexToken('X', matchTimestamp);\naddParseToken('X', function (input, array, config) {\n    config._d = new Date(parseFloat(input) * 1000);\n});\naddParseToken('x', function (input, array, config) {\n    config._d = new Date(toInt(input));\n});\n\n//! moment.js\n\nhooks.version = '2.30.1';\n\nsetHookCallback(createLocal);\n\nhooks.fn = proto;\nhooks.min = min;\nhooks.max = max;\nhooks.now = now;\nhooks.utc = createUTC;\nhooks.unix = createUnix;\nhooks.months = listMonths;\nhooks.isDate = isDate;\nhooks.locale = getSetGlobalLocale;\nhooks.invalid = createInvalid;\nhooks.duration = createDuration;\nhooks.isMoment = isMoment;\nhooks.weekdays = listWeekdays;\nhooks.parseZone = createInZone;\nhooks.localeData = getLocale;\nhooks.isDuration = isDuration;\nhooks.monthsShort = listMonthsShort;\nhooks.weekdaysMin = listWeekdaysMin;\nhooks.defineLocale = defineLocale;\nhooks.updateLocale = updateLocale;\nhooks.locales = listLocales;\nhooks.weekdaysShort = listWeekdaysShort;\nhooks.normalizeUnits = normalizeUnits;\nhooks.relativeTimeRounding = getSetRelativeTimeRounding;\nhooks.relativeTimeThreshold = getSetRelativeTimeThreshold;\nhooks.calendarFormat = getCalendarFormat;\nhooks.prototype = proto;\n\n// currently HTML5 input type only supports 24-hour formats\nhooks.HTML5_FMT = {\n    DATETIME_LOCAL: 'YYYY-MM-DDTHH:mm', // <input type=\"datetime-local\" />\n    DATETIME_LOCAL_SECONDS: 'YYYY-MM-DDTHH:mm:ss', // <input type=\"datetime-local\" step=\"1\" />\n    DATETIME_LOCAL_MS: 'YYYY-MM-DDTHH:mm:ss.SSS', // <input type=\"datetime-local\" step=\"0.001\" />\n    DATE: 'YYYY-MM-DD', // <input type=\"date\" />\n    TIME: 'HH:mm', // <input type=\"time\" />\n    TIME_SECONDS: 'HH:mm:ss', // <input type=\"time\" step=\"1\" />\n    TIME_MS: 'HH:mm:ss.SSS', // <input type=\"time\" step=\"0.001\" />\n    WEEK: 'GGGG-[W]WW', // <input type=\"week\" />\n    MONTH: 'YYYY-MM', // <input type=\"month\" />\n};\n\nexport default hooks;\n"], "mappings": ";;;;;AAMA,IAAI;AAEJ,SAAS,QAAQ;AACb,SAAO,aAAa,MAAM,MAAM,SAAS;AAC7C;AAIA,SAAS,gBAAgB,UAAU;AAC/B,iBAAe;AACnB;AAEA,SAAS,QAAQ,OAAO;AACpB,SACI,iBAAiB,SACjB,OAAO,UAAU,SAAS,KAAK,KAAK,MAAM;AAElD;AAEA,SAAS,SAAS,OAAO;AAGrB,SACI,SAAS,QACT,OAAO,UAAU,SAAS,KAAK,KAAK,MAAM;AAElD;AAEA,SAAS,WAAW,GAAG,GAAG;AACtB,SAAO,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC;AACpD;AAEA,SAAS,cAAc,KAAK;AACxB,MAAI,OAAO,qBAAqB;AAC5B,WAAO,OAAO,oBAAoB,GAAG,EAAE,WAAW;AAAA,EACtD,OAAO;AACH,QAAI;AACJ,SAAK,KAAK,KAAK;AACX,UAAI,WAAW,KAAK,CAAC,GAAG;AACpB,eAAO;AAAA,MACX;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AACJ;AAEA,SAAS,YAAY,OAAO;AACxB,SAAO,UAAU;AACrB;AAEA,SAAS,SAAS,OAAO;AACrB,SACI,OAAO,UAAU,YACjB,OAAO,UAAU,SAAS,KAAK,KAAK,MAAM;AAElD;AAEA,SAAS,OAAO,OAAO;AACnB,SACI,iBAAiB,QACjB,OAAO,UAAU,SAAS,KAAK,KAAK,MAAM;AAElD;AAEA,SAAS,IAAI,KAAK,IAAI;AAClB,MAAI,MAAM,CAAC,GACP,GACA,SAAS,IAAI;AACjB,OAAK,IAAI,GAAG,IAAI,QAAQ,EAAE,GAAG;AACzB,QAAI,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;AAAA,EAC1B;AACA,SAAO;AACX;AAEA,SAAS,OAAO,GAAG,GAAG;AAClB,WAAS,KAAK,GAAG;AACb,QAAI,WAAW,GAAG,CAAC,GAAG;AAClB,QAAE,CAAC,IAAI,EAAE,CAAC;AAAA,IACd;AAAA,EACJ;AAEA,MAAI,WAAW,GAAG,UAAU,GAAG;AAC3B,MAAE,WAAW,EAAE;AAAA,EACnB;AAEA,MAAI,WAAW,GAAG,SAAS,GAAG;AAC1B,MAAE,UAAU,EAAE;AAAA,EAClB;AAEA,SAAO;AACX;AAEA,SAAS,UAAU,OAAOA,SAAQC,SAAQ,QAAQ;AAC9C,SAAO,iBAAiB,OAAOD,SAAQC,SAAQ,QAAQ,IAAI,EAAE,IAAI;AACrE;AAEA,SAAS,sBAAsB;AAE3B,SAAO;AAAA,IACH,OAAO;AAAA,IACP,cAAc,CAAC;AAAA,IACf,aAAa,CAAC;AAAA,IACd,UAAU;AAAA,IACV,eAAe;AAAA,IACf,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,eAAe;AAAA,IACf,iBAAiB;AAAA,IACjB,KAAK;AAAA,IACL,iBAAiB,CAAC;AAAA,IAClB,KAAK;AAAA,IACL,UAAU;AAAA,IACV,SAAS;AAAA,IACT,iBAAiB;AAAA,EACrB;AACJ;AAEA,SAAS,gBAAgB,GAAG;AACxB,MAAI,EAAE,OAAO,MAAM;AACf,MAAE,MAAM,oBAAoB;AAAA,EAChC;AACA,SAAO,EAAE;AACb;AAEA,IAAI;AACJ,IAAI,MAAM,UAAU,MAAM;AACtB,SAAO,MAAM,UAAU;AAC3B,OAAO;AACH,SAAO,SAAU,KAAK;AAClB,QAAI,IAAI,OAAO,IAAI,GACf,MAAM,EAAE,WAAW,GACnB;AAEJ,SAAK,IAAI,GAAG,IAAI,KAAK,KAAK;AACtB,UAAI,KAAK,KAAK,IAAI,KAAK,MAAM,EAAE,CAAC,GAAG,GAAG,CAAC,GAAG;AACtC,eAAO;AAAA,MACX;AAAA,IACJ;AAEA,WAAO;AAAA,EACX;AACJ;AAEA,SAAS,QAAQ,GAAG;AAChB,MAAI,QAAQ,MACR,cAAc,OACd,aAAa,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,QAAQ,CAAC;AAC9C,MAAI,YAAY;AACZ,YAAQ,gBAAgB,CAAC;AACzB,kBAAc,KAAK,KAAK,MAAM,iBAAiB,SAAU,GAAG;AACxD,aAAO,KAAK;AAAA,IAChB,CAAC;AACD,iBACI,MAAM,WAAW,KACjB,CAAC,MAAM,SACP,CAAC,MAAM,cACP,CAAC,MAAM,gBACP,CAAC,MAAM,kBACP,CAAC,MAAM,mBACP,CAAC,MAAM,aACP,CAAC,MAAM,iBACP,CAAC,MAAM,oBACN,CAAC,MAAM,YAAa,MAAM,YAAY;AAC3C,QAAI,EAAE,SAAS;AACX,mBACI,cACA,MAAM,kBAAkB,KACxB,MAAM,aAAa,WAAW,KAC9B,MAAM,YAAY;AAAA,IAC1B;AAAA,EACJ;AACA,MAAI,OAAO,YAAY,QAAQ,CAAC,OAAO,SAAS,CAAC,GAAG;AAChD,MAAE,WAAW;AAAA,EACjB,OAAO;AACH,WAAO;AAAA,EACX;AACA,SAAO,EAAE;AACb;AAEA,SAAS,cAAc,OAAO;AAC1B,MAAI,IAAI,UAAU,GAAG;AACrB,MAAI,SAAS,MAAM;AACf,WAAO,gBAAgB,CAAC,GAAG,KAAK;AAAA,EACpC,OAAO;AACH,oBAAgB,CAAC,EAAE,kBAAkB;AAAA,EACzC;AAEA,SAAO;AACX;AAIA,IAAI,mBAAoB,MAAM,mBAAmB,CAAC;AAAlD,IACI,mBAAmB;AAEvB,SAAS,WAAWC,KAAIC,OAAM;AAC1B,MAAI,GACA,MACA,KACA,sBAAsB,iBAAiB;AAE3C,MAAI,CAAC,YAAYA,MAAK,gBAAgB,GAAG;AACrC,IAAAD,IAAG,mBAAmBC,MAAK;AAAA,EAC/B;AACA,MAAI,CAAC,YAAYA,MAAK,EAAE,GAAG;AACvB,IAAAD,IAAG,KAAKC,MAAK;AAAA,EACjB;AACA,MAAI,CAAC,YAAYA,MAAK,EAAE,GAAG;AACvB,IAAAD,IAAG,KAAKC,MAAK;AAAA,EACjB;AACA,MAAI,CAAC,YAAYA,MAAK,EAAE,GAAG;AACvB,IAAAD,IAAG,KAAKC,MAAK;AAAA,EACjB;AACA,MAAI,CAAC,YAAYA,MAAK,OAAO,GAAG;AAC5B,IAAAD,IAAG,UAAUC,MAAK;AAAA,EACtB;AACA,MAAI,CAAC,YAAYA,MAAK,IAAI,GAAG;AACzB,IAAAD,IAAG,OAAOC,MAAK;AAAA,EACnB;AACA,MAAI,CAAC,YAAYA,MAAK,MAAM,GAAG;AAC3B,IAAAD,IAAG,SAASC,MAAK;AAAA,EACrB;AACA,MAAI,CAAC,YAAYA,MAAK,OAAO,GAAG;AAC5B,IAAAD,IAAG,UAAUC,MAAK;AAAA,EACtB;AACA,MAAI,CAAC,YAAYA,MAAK,GAAG,GAAG;AACxB,IAAAD,IAAG,MAAM,gBAAgBC,KAAI;AAAA,EACjC;AACA,MAAI,CAAC,YAAYA,MAAK,OAAO,GAAG;AAC5B,IAAAD,IAAG,UAAUC,MAAK;AAAA,EACtB;AAEA,MAAI,sBAAsB,GAAG;AACzB,SAAK,IAAI,GAAG,IAAI,qBAAqB,KAAK;AACtC,aAAO,iBAAiB,CAAC;AACzB,YAAMA,MAAK,IAAI;AACf,UAAI,CAAC,YAAY,GAAG,GAAG;AACnB,QAAAD,IAAG,IAAI,IAAI;AAAA,MACf;AAAA,IACJ;AAAA,EACJ;AAEA,SAAOA;AACX;AAGA,SAAS,OAAO,QAAQ;AACpB,aAAW,MAAM,MAAM;AACvB,OAAK,KAAK,IAAI,KAAK,OAAO,MAAM,OAAO,OAAO,GAAG,QAAQ,IAAI,GAAG;AAChE,MAAI,CAAC,KAAK,QAAQ,GAAG;AACjB,SAAK,KAAK,oBAAI,KAAK,GAAG;AAAA,EAC1B;AAGA,MAAI,qBAAqB,OAAO;AAC5B,uBAAmB;AACnB,UAAM,aAAa,IAAI;AACvB,uBAAmB;AAAA,EACvB;AACJ;AAEA,SAAS,SAAS,KAAK;AACnB,SACI,eAAe,UAAW,OAAO,QAAQ,IAAI,oBAAoB;AAEzE;AAEA,SAAS,KAAK,KAAK;AACf,MACI,MAAM,gCAAgC,SACtC,OAAO,YAAY,eACnB,QAAQ,MACV;AACE,YAAQ,KAAK,0BAA0B,GAAG;AAAA,EAC9C;AACJ;AAEA,SAAS,UAAU,KAAK,IAAI;AACxB,MAAI,YAAY;AAEhB,SAAO,OAAO,WAAY;AACtB,QAAI,MAAM,sBAAsB,MAAM;AAClC,YAAM,mBAAmB,MAAM,GAAG;AAAA,IACtC;AACA,QAAI,WAAW;AACX,UAAI,OAAO,CAAC,GACR,KACA,GACA,KACA,SAAS,UAAU;AACvB,WAAK,IAAI,GAAG,IAAI,QAAQ,KAAK;AACzB,cAAM;AACN,YAAI,OAAO,UAAU,CAAC,MAAM,UAAU;AAClC,iBAAO,QAAQ,IAAI;AACnB,eAAK,OAAO,UAAU,CAAC,GAAG;AACtB,gBAAI,WAAW,UAAU,CAAC,GAAG,GAAG,GAAG;AAC/B,qBAAO,MAAM,OAAO,UAAU,CAAC,EAAE,GAAG,IAAI;AAAA,YAC5C;AAAA,UACJ;AACA,gBAAM,IAAI,MAAM,GAAG,EAAE;AAAA,QACzB,OAAO;AACH,gBAAM,UAAU,CAAC;AAAA,QACrB;AACA,aAAK,KAAK,GAAG;AAAA,MACjB;AACA;AAAA,QACI,MACI,kBACA,MAAM,UAAU,MAAM,KAAK,IAAI,EAAE,KAAK,EAAE,IACxC,OACA,IAAI,MAAM,EAAE;AAAA,MACpB;AACA,kBAAY;AAAA,IAChB;AACA,WAAO,GAAG,MAAM,MAAM,SAAS;AAAA,EACnC,GAAG,EAAE;AACT;AAEA,IAAI,eAAe,CAAC;AAEpB,SAAS,gBAAgB,MAAM,KAAK;AAChC,MAAI,MAAM,sBAAsB,MAAM;AAClC,UAAM,mBAAmB,MAAM,GAAG;AAAA,EACtC;AACA,MAAI,CAAC,aAAa,IAAI,GAAG;AACrB,SAAK,GAAG;AACR,iBAAa,IAAI,IAAI;AAAA,EACzB;AACJ;AAEA,MAAM,8BAA8B;AACpC,MAAM,qBAAqB;AAE3B,SAAS,WAAW,OAAO;AACvB,SACK,OAAO,aAAa,eAAe,iBAAiB,YACrD,OAAO,UAAU,SAAS,KAAK,KAAK,MAAM;AAElD;AAEA,SAAS,IAAI,QAAQ;AACjB,MAAI,MAAM;AACV,OAAK,KAAK,QAAQ;AACd,QAAI,WAAW,QAAQ,CAAC,GAAG;AACvB,aAAO,OAAO,CAAC;AACf,UAAI,WAAW,IAAI,GAAG;AAClB,aAAK,CAAC,IAAI;AAAA,MACd,OAAO;AACH,aAAK,MAAM,CAAC,IAAI;AAAA,MACpB;AAAA,IACJ;AAAA,EACJ;AACA,OAAK,UAAU;AAIf,OAAK,iCAAiC,IAAI;AAAA,KACrC,KAAK,wBAAwB,UAAU,KAAK,cAAc,UACvD,MACA,UAAU;AAAA,EAClB;AACJ;AAEA,SAAS,aAAa,cAAc,aAAa;AAC7C,MAAI,MAAM,OAAO,CAAC,GAAG,YAAY,GAC7B;AACJ,OAAK,QAAQ,aAAa;AACtB,QAAI,WAAW,aAAa,IAAI,GAAG;AAC/B,UAAI,SAAS,aAAa,IAAI,CAAC,KAAK,SAAS,YAAY,IAAI,CAAC,GAAG;AAC7D,YAAI,IAAI,IAAI,CAAC;AACb,eAAO,IAAI,IAAI,GAAG,aAAa,IAAI,CAAC;AACpC,eAAO,IAAI,IAAI,GAAG,YAAY,IAAI,CAAC;AAAA,MACvC,WAAW,YAAY,IAAI,KAAK,MAAM;AAClC,YAAI,IAAI,IAAI,YAAY,IAAI;AAAA,MAChC,OAAO;AACH,eAAO,IAAI,IAAI;AAAA,MACnB;AAAA,IACJ;AAAA,EACJ;AACA,OAAK,QAAQ,cAAc;AACvB,QACI,WAAW,cAAc,IAAI,KAC7B,CAAC,WAAW,aAAa,IAAI,KAC7B,SAAS,aAAa,IAAI,CAAC,GAC7B;AAEE,UAAI,IAAI,IAAI,OAAO,CAAC,GAAG,IAAI,IAAI,CAAC;AAAA,IACpC;AAAA,EACJ;AACA,SAAO;AACX;AAEA,SAAS,OAAO,QAAQ;AACpB,MAAI,UAAU,MAAM;AAChB,SAAK,IAAI,MAAM;AAAA,EACnB;AACJ;AAEA,IAAI;AAEJ,IAAI,OAAO,MAAM;AACb,SAAO,OAAO;AAClB,OAAO;AACH,SAAO,SAAU,KAAK;AAClB,QAAI,GACA,MAAM,CAAC;AACX,SAAK,KAAK,KAAK;AACX,UAAI,WAAW,KAAK,CAAC,GAAG;AACpB,YAAI,KAAK,CAAC;AAAA,MACd;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AACJ;AAEA,IAAI,kBAAkB;AAAA,EAClB,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU;AACd;AAEA,SAAS,SAAS,KAAK,KAAKE,MAAK;AAC7B,MAAI,SAAS,KAAK,UAAU,GAAG,KAAK,KAAK,UAAU,UAAU;AAC7D,SAAO,WAAW,MAAM,IAAI,OAAO,KAAK,KAAKA,IAAG,IAAI;AACxD;AAEA,SAAS,SAAS,QAAQ,cAAc,WAAW;AAC/C,MAAI,YAAY,KAAK,KAAK,IAAI,MAAM,GAChC,cAAc,eAAe,UAAU,QACvCC,QAAO,UAAU;AACrB,UACKA,QAAQ,YAAY,MAAM,KAAM,OACjC,KAAK,IAAI,IAAI,KAAK,IAAI,GAAG,WAAW,CAAC,EAAE,SAAS,EAAE,OAAO,CAAC,IAC1D;AAER;AAEA,IAAI,mBACI;AADR,IAEI,wBAAwB;AAF5B,IAGI,kBAAkB,CAAC;AAHvB,IAII,uBAAuB,CAAC;AAM5B,SAAS,eAAeC,QAAO,QAAQC,UAAS,UAAU;AACtD,MAAI,OAAO;AACX,MAAI,OAAO,aAAa,UAAU;AAC9B,WAAO,WAAY;AACf,aAAO,KAAK,QAAQ,EAAE;AAAA,IAC1B;AAAA,EACJ;AACA,MAAID,QAAO;AACP,yBAAqBA,MAAK,IAAI;AAAA,EAClC;AACA,MAAI,QAAQ;AACR,yBAAqB,OAAO,CAAC,CAAC,IAAI,WAAY;AAC1C,aAAO,SAAS,KAAK,MAAM,MAAM,SAAS,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,CAAC;AAAA,IACrE;AAAA,EACJ;AACA,MAAIC,UAAS;AACT,yBAAqBA,QAAO,IAAI,WAAY;AACxC,aAAO,KAAK,WAAW,EAAE;AAAA,QACrB,KAAK,MAAM,MAAM,SAAS;AAAA,QAC1BD;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AACJ;AAEA,SAAS,uBAAuB,OAAO;AACnC,MAAI,MAAM,MAAM,UAAU,GAAG;AACzB,WAAO,MAAM,QAAQ,YAAY,EAAE;AAAA,EACvC;AACA,SAAO,MAAM,QAAQ,OAAO,EAAE;AAClC;AAEA,SAAS,mBAAmBN,SAAQ;AAChC,MAAI,QAAQA,QAAO,MAAM,gBAAgB,GACrC,GACA;AAEJ,OAAK,IAAI,GAAG,SAAS,MAAM,QAAQ,IAAI,QAAQ,KAAK;AAChD,QAAI,qBAAqB,MAAM,CAAC,CAAC,GAAG;AAChC,YAAM,CAAC,IAAI,qBAAqB,MAAM,CAAC,CAAC;AAAA,IAC5C,OAAO;AACH,YAAM,CAAC,IAAI,uBAAuB,MAAM,CAAC,CAAC;AAAA,IAC9C;AAAA,EACJ;AAEA,SAAO,SAAU,KAAK;AAClB,QAAI,SAAS,IACTQ;AACJ,SAAKA,KAAI,GAAGA,KAAI,QAAQA,MAAK;AACzB,gBAAU,WAAW,MAAMA,EAAC,CAAC,IACvB,MAAMA,EAAC,EAAE,KAAK,KAAKR,OAAM,IACzB,MAAMQ,EAAC;AAAA,IACjB;AACA,WAAO;AAAA,EACX;AACJ;AAGA,SAAS,aAAa,GAAGR,SAAQ;AAC7B,MAAI,CAAC,EAAE,QAAQ,GAAG;AACd,WAAO,EAAE,WAAW,EAAE,YAAY;AAAA,EACtC;AAEA,EAAAA,UAAS,aAAaA,SAAQ,EAAE,WAAW,CAAC;AAC5C,kBAAgBA,OAAM,IAClB,gBAAgBA,OAAM,KAAK,mBAAmBA,OAAM;AAExD,SAAO,gBAAgBA,OAAM,EAAE,CAAC;AACpC;AAEA,SAAS,aAAaA,SAAQC,SAAQ;AAClC,MAAI,IAAI;AAER,WAAS,4BAA4B,OAAO;AACxC,WAAOA,QAAO,eAAe,KAAK,KAAK;AAAA,EAC3C;AAEA,wBAAsB,YAAY;AAClC,SAAO,KAAK,KAAK,sBAAsB,KAAKD,OAAM,GAAG;AACjD,IAAAA,UAASA,QAAO;AAAA,MACZ;AAAA,MACA;AAAA,IACJ;AACA,0BAAsB,YAAY;AAClC,SAAK;AAAA,EACT;AAEA,SAAOA;AACX;AAEA,IAAI,wBAAwB;AAAA,EACxB,KAAK;AAAA,EACL,IAAI;AAAA,EACJ,GAAG;AAAA,EACH,IAAI;AAAA,EACJ,KAAK;AAAA,EACL,MAAM;AACV;AAEA,SAAS,eAAe,KAAK;AACzB,MAAIA,UAAS,KAAK,gBAAgB,GAAG,GACjC,cAAc,KAAK,gBAAgB,IAAI,YAAY,CAAC;AAExD,MAAIA,WAAU,CAAC,aAAa;AACxB,WAAOA;AAAA,EACX;AAEA,OAAK,gBAAgB,GAAG,IAAI,YACvB,MAAM,gBAAgB,EACtB,IAAI,SAAU,KAAK;AAChB,QACI,QAAQ,UACR,QAAQ,QACR,QAAQ,QACR,QAAQ,QACV;AACE,aAAO,IAAI,MAAM,CAAC;AAAA,IACtB;AACA,WAAO;AAAA,EACX,CAAC,EACA,KAAK,EAAE;AAEZ,SAAO,KAAK,gBAAgB,GAAG;AACnC;AAEA,IAAI,qBAAqB;AAEzB,SAAS,cAAc;AACnB,SAAO,KAAK;AAChB;AAEA,IAAI,iBAAiB;AAArB,IACI,gCAAgC;AAEpC,SAAS,QAAQ,QAAQ;AACrB,SAAO,KAAK,SAAS,QAAQ,MAAM,MAAM;AAC7C;AAEA,IAAI,sBAAsB;AAAA,EACtB,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,GAAG;AAAA,EACH,IAAI;AAAA,EACJ,GAAG;AAAA,EACH,IAAI;AAAA,EACJ,GAAG;AAAA,EACH,IAAI;AAAA,EACJ,GAAG;AAAA,EACH,IAAI;AAAA,EACJ,GAAG;AAAA,EACH,IAAI;AAAA,EACJ,GAAG;AAAA,EACH,IAAI;AAAA,EACJ,GAAG;AAAA,EACH,IAAI;AACR;AAEA,SAAS,aAAa,QAAQ,eAAe,QAAQ,UAAU;AAC3D,MAAI,SAAS,KAAK,cAAc,MAAM;AACtC,SAAO,WAAW,MAAM,IAClB,OAAO,QAAQ,eAAe,QAAQ,QAAQ,IAC9C,OAAO,QAAQ,OAAO,MAAM;AACtC;AAEA,SAAS,WAAWS,OAAM,QAAQ;AAC9B,MAAIT,UAAS,KAAK,cAAcS,QAAO,IAAI,WAAW,MAAM;AAC5D,SAAO,WAAWT,OAAM,IAAIA,QAAO,MAAM,IAAIA,QAAO,QAAQ,OAAO,MAAM;AAC7E;AAEA,IAAI,UAAU;AAAA,EACV,GAAG;AAAA,EACH,OAAO;AAAA,EACP,MAAM;AAAA,EACN,GAAG;AAAA,EACH,MAAM;AAAA,EACN,KAAK;AAAA,EACL,GAAG;AAAA,EACH,UAAU;AAAA,EACV,SAAS;AAAA,EACT,GAAG;AAAA,EACH,aAAa;AAAA,EACb,YAAY;AAAA,EACZ,KAAK;AAAA,EACL,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,GAAG;AAAA,EACH,OAAO;AAAA,EACP,MAAM;AAAA,EACN,IAAI;AAAA,EACJ,cAAc;AAAA,EACd,aAAa;AAAA,EACb,GAAG;AAAA,EACH,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,GAAG;AAAA,EACH,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,GAAG;AAAA,EACH,UAAU;AAAA,EACV,SAAS;AAAA,EACT,GAAG;AAAA,EACH,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,IAAI;AAAA,EACJ,WAAW;AAAA,EACX,UAAU;AAAA,EACV,IAAI;AAAA,EACJ,cAAc;AAAA,EACd,aAAa;AAAA,EACb,GAAG;AAAA,EACH,OAAO;AAAA,EACP,MAAM;AAAA,EACN,GAAG;AAAA,EACH,UAAU;AAAA,EACV,SAAS;AAAA,EACT,GAAG;AAAA,EACH,OAAO;AAAA,EACP,MAAM;AACV;AAEA,SAAS,eAAe,OAAO;AAC3B,SAAO,OAAO,UAAU,WAClB,QAAQ,KAAK,KAAK,QAAQ,MAAM,YAAY,CAAC,IAC7C;AACV;AAEA,SAAS,qBAAqB,aAAa;AACvC,MAAI,kBAAkB,CAAC,GACnB,gBACA;AAEJ,OAAK,QAAQ,aAAa;AACtB,QAAI,WAAW,aAAa,IAAI,GAAG;AAC/B,uBAAiB,eAAe,IAAI;AACpC,UAAI,gBAAgB;AAChB,wBAAgB,cAAc,IAAI,YAAY,IAAI;AAAA,MACtD;AAAA,IACJ;AAAA,EACJ;AAEA,SAAO;AACX;AAEA,IAAI,aAAa;AAAA,EACb,MAAM;AAAA,EACN,KAAK;AAAA,EACL,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,MAAM;AAAA,EACN,aAAa;AAAA,EACb,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,aAAa;AAAA,EACb,MAAM;AAAA,EACN,SAAS;AAAA,EACT,MAAM;AACV;AAEA,SAAS,oBAAoB,UAAU;AACnC,MAAI,QAAQ,CAAC,GACT;AACJ,OAAK,KAAK,UAAU;AAChB,QAAI,WAAW,UAAU,CAAC,GAAG;AACzB,YAAM,KAAK,EAAE,MAAM,GAAG,UAAU,WAAW,CAAC,EAAE,CAAC;AAAA,IACnD;AAAA,EACJ;AACA,QAAM,KAAK,SAAU,GAAG,GAAG;AACvB,WAAO,EAAE,WAAW,EAAE;AAAA,EAC1B,CAAC;AACD,SAAO;AACX;AAEA,IAAI,SAAS;AAAb,IACI,SAAS;AADb,IAEI,SAAS;AAFb,IAGI,SAAS;AAHb,IAII,SAAS;AAJb,IAKI,YAAY;AALhB,IAMI,YAAY;AANhB,IAOI,YAAY;AAPhB,IAQI,YAAY;AARhB,IASI,YAAY;AAThB,IAUI,YAAY;AAVhB,IAWI,gBAAgB;AAXpB,IAYI,cAAc;AAZlB,IAaI,cAAc;AAblB,IAcI,mBAAmB;AAdvB,IAeI,iBAAiB;AAfrB,IAkBI,YACI;AAnBR,IAoBI,yBAAyB;AApB7B,IAqBI,mBAAmB;AArBvB,IAsBI;AAEJ,UAAU,CAAC;AAEX,SAAS,cAAcM,QAAO,OAAO,aAAa;AAC9C,UAAQA,MAAK,IAAI,WAAW,KAAK,IAC3B,QACA,SAAU,UAAUI,aAAY;AAC5B,WAAO,YAAY,cAAc,cAAc;AAAA,EACnD;AACV;AAEA,SAAS,sBAAsBJ,QAAO,QAAQ;AAC1C,MAAI,CAAC,WAAW,SAASA,MAAK,GAAG;AAC7B,WAAO,IAAI,OAAO,eAAeA,MAAK,CAAC;AAAA,EAC3C;AAEA,SAAO,QAAQA,MAAK,EAAE,OAAO,SAAS,OAAO,OAAO;AACxD;AAGA,SAAS,eAAe,GAAG;AACvB,SAAO;AAAA,IACH,EACK,QAAQ,MAAM,EAAE,EAChB;AAAA,MACG;AAAA,MACA,SAAU,SAAS,IAAI,IAAI,IAAI,IAAI;AAC/B,eAAO,MAAM,MAAM,MAAM;AAAA,MAC7B;AAAA,IACJ;AAAA,EACR;AACJ;AAEA,SAAS,YAAY,GAAG;AACpB,SAAO,EAAE,QAAQ,0BAA0B,MAAM;AACrD;AAEA,SAAS,SAAS,QAAQ;AACtB,MAAI,SAAS,GAAG;AAEZ,WAAO,KAAK,KAAK,MAAM,KAAK;AAAA,EAChC,OAAO;AACH,WAAO,KAAK,MAAM,MAAM;AAAA,EAC5B;AACJ;AAEA,SAAS,MAAM,qBAAqB;AAChC,MAAI,gBAAgB,CAAC,qBACjB,QAAQ;AAEZ,MAAI,kBAAkB,KAAK,SAAS,aAAa,GAAG;AAChD,YAAQ,SAAS,aAAa;AAAA,EAClC;AAEA,SAAO;AACX;AAEA,IAAI,SAAS,CAAC;AAEd,SAAS,cAAcA,QAAO,UAAU;AACpC,MAAI,GACA,OAAO,UACP;AACJ,MAAI,OAAOA,WAAU,UAAU;AAC3B,IAAAA,SAAQ,CAACA,MAAK;AAAA,EAClB;AACA,MAAI,SAAS,QAAQ,GAAG;AACpB,WAAO,SAAU,OAAO,OAAO;AAC3B,YAAM,QAAQ,IAAI,MAAM,KAAK;AAAA,IACjC;AAAA,EACJ;AACA,aAAWA,OAAM;AACjB,OAAK,IAAI,GAAG,IAAI,UAAU,KAAK;AAC3B,WAAOA,OAAM,CAAC,CAAC,IAAI;AAAA,EACvB;AACJ;AAEA,SAAS,kBAAkBA,QAAO,UAAU;AACxC,gBAAcA,QAAO,SAAU,OAAO,OAAO,QAAQA,QAAO;AACxD,WAAO,KAAK,OAAO,MAAM,CAAC;AAC1B,aAAS,OAAO,OAAO,IAAI,QAAQA,MAAK;AAAA,EAC5C,CAAC;AACL;AAEA,SAAS,wBAAwBA,QAAO,OAAO,QAAQ;AACnD,MAAI,SAAS,QAAQ,WAAW,QAAQA,MAAK,GAAG;AAC5C,WAAOA,MAAK,EAAE,OAAO,OAAO,IAAI,QAAQA,MAAK;AAAA,EACjD;AACJ;AAEA,SAAS,WAAW,MAAM;AACtB,SAAQ,OAAO,MAAM,KAAK,OAAO,QAAQ,KAAM,OAAO,QAAQ;AAClE;AAEA,IAAI,OAAO;AAAX,IACI,QAAQ;AADZ,IAEI,OAAO;AAFX,IAGI,OAAO;AAHX,IAII,SAAS;AAJb,IAKI,SAAS;AALb,IAMI,cAAc;AANlB,IAOI,OAAO;AAPX,IAQI,UAAU;AAId,eAAe,KAAK,GAAG,GAAG,WAAY;AAClC,MAAI,IAAI,KAAK,KAAK;AAClB,SAAO,KAAK,OAAO,SAAS,GAAG,CAAC,IAAI,MAAM;AAC9C,CAAC;AAED,eAAe,GAAG,CAAC,MAAM,CAAC,GAAG,GAAG,WAAY;AACxC,SAAO,KAAK,KAAK,IAAI;AACzB,CAAC;AAED,eAAe,GAAG,CAAC,QAAQ,CAAC,GAAG,GAAG,MAAM;AACxC,eAAe,GAAG,CAAC,SAAS,CAAC,GAAG,GAAG,MAAM;AACzC,eAAe,GAAG,CAAC,UAAU,GAAG,IAAI,GAAG,GAAG,MAAM;AAIhD,cAAc,KAAK,WAAW;AAC9B,cAAc,MAAM,WAAW,MAAM;AACrC,cAAc,QAAQ,WAAW,MAAM;AACvC,cAAc,SAAS,WAAW,MAAM;AACxC,cAAc,UAAU,WAAW,MAAM;AAEzC,cAAc,CAAC,SAAS,QAAQ,GAAG,IAAI;AACvC,cAAc,QAAQ,SAAU,OAAO,OAAO;AAC1C,QAAM,IAAI,IACN,MAAM,WAAW,IAAI,MAAM,kBAAkB,KAAK,IAAI,MAAM,KAAK;AACzE,CAAC;AACD,cAAc,MAAM,SAAU,OAAO,OAAO;AACxC,QAAM,IAAI,IAAI,MAAM,kBAAkB,KAAK;AAC/C,CAAC;AACD,cAAc,KAAK,SAAU,OAAO,OAAO;AACvC,QAAM,IAAI,IAAI,SAAS,OAAO,EAAE;AACpC,CAAC;AAID,SAAS,WAAW,MAAM;AACtB,SAAO,WAAW,IAAI,IAAI,MAAM;AACpC;AAIA,MAAM,oBAAoB,SAAU,OAAO;AACvC,SAAO,MAAM,KAAK,KAAK,MAAM,KAAK,IAAI,KAAK,OAAO;AACtD;AAIA,IAAI,aAAa,WAAW,YAAY,IAAI;AAE5C,SAAS,gBAAgB;AACrB,SAAO,WAAW,KAAK,KAAK,CAAC;AACjC;AAEA,SAAS,WAAW,MAAM,UAAU;AAChC,SAAO,SAAU,OAAO;AACpB,QAAI,SAAS,MAAM;AACf,YAAM,MAAM,MAAM,KAAK;AACvB,YAAM,aAAa,MAAM,QAAQ;AACjC,aAAO;AAAA,IACX,OAAO;AACH,aAAO,IAAI,MAAM,IAAI;AAAA,IACzB;AAAA,EACJ;AACJ;AAEA,SAAS,IAAI,KAAK,MAAM;AACpB,MAAI,CAAC,IAAI,QAAQ,GAAG;AAChB,WAAO;AAAA,EACX;AAEA,MAAI,IAAI,IAAI,IACR,QAAQ,IAAI;AAEhB,UAAQ,MAAM;AAAA,IACV,KAAK;AACD,aAAO,QAAQ,EAAE,mBAAmB,IAAI,EAAE,gBAAgB;AAAA,IAC9D,KAAK;AACD,aAAO,QAAQ,EAAE,cAAc,IAAI,EAAE,WAAW;AAAA,IACpD,KAAK;AACD,aAAO,QAAQ,EAAE,cAAc,IAAI,EAAE,WAAW;AAAA,IACpD,KAAK;AACD,aAAO,QAAQ,EAAE,YAAY,IAAI,EAAE,SAAS;AAAA,IAChD,KAAK;AACD,aAAO,QAAQ,EAAE,WAAW,IAAI,EAAE,QAAQ;AAAA,IAC9C,KAAK;AACD,aAAO,QAAQ,EAAE,UAAU,IAAI,EAAE,OAAO;AAAA,IAC5C,KAAK;AACD,aAAO,QAAQ,EAAE,YAAY,IAAI,EAAE,SAAS;AAAA,IAChD,KAAK;AACD,aAAO,QAAQ,EAAE,eAAe,IAAI,EAAE,YAAY;AAAA,IACtD;AACI,aAAO;AAAA,EACf;AACJ;AAEA,SAAS,MAAM,KAAK,MAAM,OAAO;AAC7B,MAAI,GAAG,OAAO,MAAM,OAAO;AAE3B,MAAI,CAAC,IAAI,QAAQ,KAAK,MAAM,KAAK,GAAG;AAChC;AAAA,EACJ;AAEA,MAAI,IAAI;AACR,UAAQ,IAAI;AAEZ,UAAQ,MAAM;AAAA,IACV,KAAK;AACD,aAAO,MAAM,QACP,EAAE,mBAAmB,KAAK,IAC1B,EAAE,gBAAgB,KAAK;AAAA,IACjC,KAAK;AACD,aAAO,MAAM,QAAQ,EAAE,cAAc,KAAK,IAAI,EAAE,WAAW,KAAK;AAAA,IACpE,KAAK;AACD,aAAO,MAAM,QAAQ,EAAE,cAAc,KAAK,IAAI,EAAE,WAAW,KAAK;AAAA,IACpE,KAAK;AACD,aAAO,MAAM,QAAQ,EAAE,YAAY,KAAK,IAAI,EAAE,SAAS,KAAK;AAAA,IAChE,KAAK;AACD,aAAO,MAAM,QAAQ,EAAE,WAAW,KAAK,IAAI,EAAE,QAAQ,KAAK;AAAA,IAK9D,KAAK;AACD;AAAA,IACJ;AACI;AAAA,EACR;AAEA,SAAO;AACP,UAAQ,IAAI,MAAM;AAClB,SAAO,IAAI,KAAK;AAChB,SAAO,SAAS,MAAM,UAAU,KAAK,CAAC,WAAW,IAAI,IAAI,KAAK;AAC9D,QAAM,QACA,EAAE,eAAe,MAAM,OAAO,IAAI,IAClC,EAAE,YAAY,MAAM,OAAO,IAAI;AACzC;AAIA,SAAS,UAAU,OAAO;AACtB,UAAQ,eAAe,KAAK;AAC5B,MAAI,WAAW,KAAK,KAAK,CAAC,GAAG;AACzB,WAAO,KAAK,KAAK,EAAE;AAAA,EACvB;AACA,SAAO;AACX;AAEA,SAAS,UAAU,OAAO,OAAO;AAC7B,MAAI,OAAO,UAAU,UAAU;AAC3B,YAAQ,qBAAqB,KAAK;AAClC,QAAI,cAAc,oBAAoB,KAAK,GACvC,GACA,iBAAiB,YAAY;AACjC,SAAK,IAAI,GAAG,IAAI,gBAAgB,KAAK;AACjC,WAAK,YAAY,CAAC,EAAE,IAAI,EAAE,MAAM,YAAY,CAAC,EAAE,IAAI,CAAC;AAAA,IACxD;AAAA,EACJ,OAAO;AACH,YAAQ,eAAe,KAAK;AAC5B,QAAI,WAAW,KAAK,KAAK,CAAC,GAAG;AACzB,aAAO,KAAK,KAAK,EAAE,KAAK;AAAA,IAC5B;AAAA,EACJ;AACA,SAAO;AACX;AAEA,SAAS,IAAI,GAAG,GAAG;AACf,UAAS,IAAI,IAAK,KAAK;AAC3B;AAEA,IAAI;AAEJ,IAAI,MAAM,UAAU,SAAS;AACzB,YAAU,MAAM,UAAU;AAC9B,OAAO;AACH,YAAU,SAAU,GAAG;AAEnB,QAAI;AACJ,SAAK,IAAI,GAAG,IAAI,KAAK,QAAQ,EAAE,GAAG;AAC9B,UAAI,KAAK,CAAC,MAAM,GAAG;AACf,eAAO;AAAA,MACX;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AACJ;AAEA,SAAS,YAAY,MAAM,OAAO;AAC9B,MAAI,MAAM,IAAI,KAAK,MAAM,KAAK,GAAG;AAC7B,WAAO;AAAA,EACX;AACA,MAAI,WAAW,IAAI,OAAO,EAAE;AAC5B,WAAS,QAAQ,YAAY;AAC7B,SAAO,aAAa,IACd,WAAW,IAAI,IACX,KACA,KACJ,KAAO,WAAW,IAAK;AACjC;AAIA,eAAe,KAAK,CAAC,MAAM,CAAC,GAAG,MAAM,WAAY;AAC7C,SAAO,KAAK,MAAM,IAAI;AAC1B,CAAC;AAED,eAAe,OAAO,GAAG,GAAG,SAAUN,SAAQ;AAC1C,SAAO,KAAK,WAAW,EAAE,YAAY,MAAMA,OAAM;AACrD,CAAC;AAED,eAAe,QAAQ,GAAG,GAAG,SAAUA,SAAQ;AAC3C,SAAO,KAAK,WAAW,EAAE,OAAO,MAAMA,OAAM;AAChD,CAAC;AAID,cAAc,KAAK,WAAW,sBAAsB;AACpD,cAAc,MAAM,WAAW,MAAM;AACrC,cAAc,OAAO,SAAU,UAAUC,SAAQ;AAC7C,SAAOA,QAAO,iBAAiB,QAAQ;AAC3C,CAAC;AACD,cAAc,QAAQ,SAAU,UAAUA,SAAQ;AAC9C,SAAOA,QAAO,YAAY,QAAQ;AACtC,CAAC;AAED,cAAc,CAAC,KAAK,IAAI,GAAG,SAAU,OAAO,OAAO;AAC/C,QAAM,KAAK,IAAI,MAAM,KAAK,IAAI;AAClC,CAAC;AAED,cAAc,CAAC,OAAO,MAAM,GAAG,SAAU,OAAO,OAAO,QAAQK,QAAO;AAClE,MAAI,QAAQ,OAAO,QAAQ,YAAY,OAAOA,QAAO,OAAO,OAAO;AAEnE,MAAI,SAAS,MAAM;AACf,UAAM,KAAK,IAAI;AAAA,EACnB,OAAO;AACH,oBAAgB,MAAM,EAAE,eAAe;AAAA,EAC3C;AACJ,CAAC;AAID,IAAI,sBACI,wFAAwF;AAAA,EACpF;AACJ;AAHR,IAII,2BACI,kDAAkD,MAAM,GAAG;AALnE,IAMI,mBAAmB;AANvB,IAOI,0BAA0B;AAP9B,IAQI,qBAAqB;AAEzB,SAAS,aAAa,GAAGN,SAAQ;AAC7B,MAAI,CAAC,GAAG;AACJ,WAAO,QAAQ,KAAK,OAAO,IACrB,KAAK,UACL,KAAK,QAAQ,YAAY;AAAA,EACnC;AACA,SAAO,QAAQ,KAAK,OAAO,IACrB,KAAK,QAAQ,EAAE,MAAM,CAAC,IACtB,KAAK,SACA,KAAK,QAAQ,YAAY,kBAAkB,KAAKA,OAAM,IACjD,WACA,YACV,EAAE,EAAE,MAAM,CAAC;AACrB;AAEA,SAAS,kBAAkB,GAAGA,SAAQ;AAClC,MAAI,CAAC,GAAG;AACJ,WAAO,QAAQ,KAAK,YAAY,IAC1B,KAAK,eACL,KAAK,aAAa,YAAY;AAAA,EACxC;AACA,SAAO,QAAQ,KAAK,YAAY,IAC1B,KAAK,aAAa,EAAE,MAAM,CAAC,IAC3B,KAAK,aACD,iBAAiB,KAAKA,OAAM,IAAI,WAAW,YAC/C,EAAE,EAAE,MAAM,CAAC;AACrB;AAEA,SAAS,kBAAkB,WAAWA,SAAQ,QAAQ;AAClD,MAAI,GACA,IACA,KACA,MAAM,UAAU,kBAAkB;AACtC,MAAI,CAAC,KAAK,cAAc;AAEpB,SAAK,eAAe,CAAC;AACrB,SAAK,mBAAmB,CAAC;AACzB,SAAK,oBAAoB,CAAC;AAC1B,SAAK,IAAI,GAAG,IAAI,IAAI,EAAE,GAAG;AACrB,YAAM,UAAU,CAAC,KAAM,CAAC,CAAC;AACzB,WAAK,kBAAkB,CAAC,IAAI,KAAK;AAAA,QAC7B;AAAA,QACA;AAAA,MACJ,EAAE,kBAAkB;AACpB,WAAK,iBAAiB,CAAC,IAAI,KAAK,OAAO,KAAK,EAAE,EAAE,kBAAkB;AAAA,IACtE;AAAA,EACJ;AAEA,MAAI,QAAQ;AACR,QAAIA,YAAW,OAAO;AAClB,WAAK,QAAQ,KAAK,KAAK,mBAAmB,GAAG;AAC7C,aAAO,OAAO,KAAK,KAAK;AAAA,IAC5B,OAAO;AACH,WAAK,QAAQ,KAAK,KAAK,kBAAkB,GAAG;AAC5C,aAAO,OAAO,KAAK,KAAK;AAAA,IAC5B;AAAA,EACJ,OAAO;AACH,QAAIA,YAAW,OAAO;AAClB,WAAK,QAAQ,KAAK,KAAK,mBAAmB,GAAG;AAC7C,UAAI,OAAO,IAAI;AACX,eAAO;AAAA,MACX;AACA,WAAK,QAAQ,KAAK,KAAK,kBAAkB,GAAG;AAC5C,aAAO,OAAO,KAAK,KAAK;AAAA,IAC5B,OAAO;AACH,WAAK,QAAQ,KAAK,KAAK,kBAAkB,GAAG;AAC5C,UAAI,OAAO,IAAI;AACX,eAAO;AAAA,MACX;AACA,WAAK,QAAQ,KAAK,KAAK,mBAAmB,GAAG;AAC7C,aAAO,OAAO,KAAK,KAAK;AAAA,IAC5B;AAAA,EACJ;AACJ;AAEA,SAAS,kBAAkB,WAAWA,SAAQ,QAAQ;AAClD,MAAI,GAAG,KAAK;AAEZ,MAAI,KAAK,mBAAmB;AACxB,WAAO,kBAAkB,KAAK,MAAM,WAAWA,SAAQ,MAAM;AAAA,EACjE;AAEA,MAAI,CAAC,KAAK,cAAc;AACpB,SAAK,eAAe,CAAC;AACrB,SAAK,mBAAmB,CAAC;AACzB,SAAK,oBAAoB,CAAC;AAAA,EAC9B;AAKA,OAAK,IAAI,GAAG,IAAI,IAAI,KAAK;AAErB,UAAM,UAAU,CAAC,KAAM,CAAC,CAAC;AACzB,QAAI,UAAU,CAAC,KAAK,iBAAiB,CAAC,GAAG;AACrC,WAAK,iBAAiB,CAAC,IAAI,IAAI;AAAA,QAC3B,MAAM,KAAK,OAAO,KAAK,EAAE,EAAE,QAAQ,KAAK,EAAE,IAAI;AAAA,QAC9C;AAAA,MACJ;AACA,WAAK,kBAAkB,CAAC,IAAI,IAAI;AAAA,QAC5B,MAAM,KAAK,YAAY,KAAK,EAAE,EAAE,QAAQ,KAAK,EAAE,IAAI;AAAA,QACnD;AAAA,MACJ;AAAA,IACJ;AACA,QAAI,CAAC,UAAU,CAAC,KAAK,aAAa,CAAC,GAAG;AAClC,cACI,MAAM,KAAK,OAAO,KAAK,EAAE,IAAI,OAAO,KAAK,YAAY,KAAK,EAAE;AAChE,WAAK,aAAa,CAAC,IAAI,IAAI,OAAO,MAAM,QAAQ,KAAK,EAAE,GAAG,GAAG;AAAA,IACjE;AAEA,QACI,UACAA,YAAW,UACX,KAAK,iBAAiB,CAAC,EAAE,KAAK,SAAS,GACzC;AACE,aAAO;AAAA,IACX,WACI,UACAA,YAAW,SACX,KAAK,kBAAkB,CAAC,EAAE,KAAK,SAAS,GAC1C;AACE,aAAO;AAAA,IACX,WAAW,CAAC,UAAU,KAAK,aAAa,CAAC,EAAE,KAAK,SAAS,GAAG;AACxD,aAAO;AAAA,IACX;AAAA,EACJ;AACJ;AAIA,SAAS,SAAS,KAAK,OAAO;AAC1B,MAAI,CAAC,IAAI,QAAQ,GAAG;AAEhB,WAAO;AAAA,EACX;AAEA,MAAI,OAAO,UAAU,UAAU;AAC3B,QAAI,QAAQ,KAAK,KAAK,GAAG;AACrB,cAAQ,MAAM,KAAK;AAAA,IACvB,OAAO;AACH,cAAQ,IAAI,WAAW,EAAE,YAAY,KAAK;AAE1C,UAAI,CAAC,SAAS,KAAK,GAAG;AAClB,eAAO;AAAA,MACX;AAAA,IACJ;AAAA,EACJ;AAEA,MAAI,QAAQ,OACR,OAAO,IAAI,KAAK;AAEpB,SAAO,OAAO,KAAK,OAAO,KAAK,IAAI,MAAM,YAAY,IAAI,KAAK,GAAG,KAAK,CAAC;AACvE,QAAM,IAAI,SACJ,IAAI,GAAG,YAAY,OAAO,IAAI,IAC9B,IAAI,GAAG,SAAS,OAAO,IAAI;AACjC,SAAO;AACX;AAEA,SAAS,YAAY,OAAO;AACxB,MAAI,SAAS,MAAM;AACf,aAAS,MAAM,KAAK;AACpB,UAAM,aAAa,MAAM,IAAI;AAC7B,WAAO;AAAA,EACX,OAAO;AACH,WAAO,IAAI,MAAM,OAAO;AAAA,EAC5B;AACJ;AAEA,SAAS,iBAAiB;AACtB,SAAO,YAAY,KAAK,KAAK,GAAG,KAAK,MAAM,CAAC;AAChD;AAEA,SAAS,iBAAiB,UAAU;AAChC,MAAI,KAAK,mBAAmB;AACxB,QAAI,CAAC,WAAW,MAAM,cAAc,GAAG;AACnC,yBAAmB,KAAK,IAAI;AAAA,IAChC;AACA,QAAI,UAAU;AACV,aAAO,KAAK;AAAA,IAChB,OAAO;AACH,aAAO,KAAK;AAAA,IAChB;AAAA,EACJ,OAAO;AACH,QAAI,CAAC,WAAW,MAAM,mBAAmB,GAAG;AACxC,WAAK,oBAAoB;AAAA,IAC7B;AACA,WAAO,KAAK,2BAA2B,WACjC,KAAK,0BACL,KAAK;AAAA,EACf;AACJ;AAEA,SAAS,YAAY,UAAU;AAC3B,MAAI,KAAK,mBAAmB;AACxB,QAAI,CAAC,WAAW,MAAM,cAAc,GAAG;AACnC,yBAAmB,KAAK,IAAI;AAAA,IAChC;AACA,QAAI,UAAU;AACV,aAAO,KAAK;AAAA,IAChB,OAAO;AACH,aAAO,KAAK;AAAA,IAChB;AAAA,EACJ,OAAO;AACH,QAAI,CAAC,WAAW,MAAM,cAAc,GAAG;AACnC,WAAK,eAAe;AAAA,IACxB;AACA,WAAO,KAAK,sBAAsB,WAC5B,KAAK,qBACL,KAAK;AAAA,EACf;AACJ;AAEA,SAAS,qBAAqB;AAC1B,WAAS,UAAU,GAAG,GAAG;AACrB,WAAO,EAAE,SAAS,EAAE;AAAA,EACxB;AAEA,MAAI,cAAc,CAAC,GACf,aAAa,CAAC,GACd,cAAc,CAAC,GACf,GACA,KACA,QACA;AACJ,OAAK,IAAI,GAAG,IAAI,IAAI,KAAK;AAErB,UAAM,UAAU,CAAC,KAAM,CAAC,CAAC;AACzB,aAAS,YAAY,KAAK,YAAY,KAAK,EAAE,CAAC;AAC9C,YAAQ,YAAY,KAAK,OAAO,KAAK,EAAE,CAAC;AACxC,gBAAY,KAAK,MAAM;AACvB,eAAW,KAAK,KAAK;AACrB,gBAAY,KAAK,KAAK;AACtB,gBAAY,KAAK,MAAM;AAAA,EAC3B;AAGA,cAAY,KAAK,SAAS;AAC1B,aAAW,KAAK,SAAS;AACzB,cAAY,KAAK,SAAS;AAE1B,OAAK,eAAe,IAAI,OAAO,OAAO,YAAY,KAAK,GAAG,IAAI,KAAK,GAAG;AACtE,OAAK,oBAAoB,KAAK;AAC9B,OAAK,qBAAqB,IAAI;AAAA,IAC1B,OAAO,WAAW,KAAK,GAAG,IAAI;AAAA,IAC9B;AAAA,EACJ;AACA,OAAK,0BAA0B,IAAI;AAAA,IAC/B,OAAO,YAAY,KAAK,GAAG,IAAI;AAAA,IAC/B;AAAA,EACJ;AACJ;AAEA,SAAS,WAAW,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI;AAGtC,MAAI;AAEJ,MAAI,IAAI,OAAO,KAAK,GAAG;AAEnB,WAAO,IAAI,KAAK,IAAI,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE;AAC1C,QAAI,SAAS,KAAK,YAAY,CAAC,GAAG;AAC9B,WAAK,YAAY,CAAC;AAAA,IACtB;AAAA,EACJ,OAAO;AACH,WAAO,IAAI,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE;AAAA,EACxC;AAEA,SAAO;AACX;AAEA,SAAS,cAAc,GAAG;AACtB,MAAI,MAAM;AAEV,MAAI,IAAI,OAAO,KAAK,GAAG;AACnB,WAAO,MAAM,UAAU,MAAM,KAAK,SAAS;AAE3C,SAAK,CAAC,IAAI,IAAI;AACd,WAAO,IAAI,KAAK,KAAK,IAAI,MAAM,MAAM,IAAI,CAAC;AAC1C,QAAI,SAAS,KAAK,eAAe,CAAC,GAAG;AACjC,WAAK,eAAe,CAAC;AAAA,IACzB;AAAA,EACJ,OAAO;AACH,WAAO,IAAI,KAAK,KAAK,IAAI,MAAM,MAAM,SAAS,CAAC;AAAA,EACnD;AAEA,SAAO;AACX;AAGA,SAAS,gBAAgB,MAAM,KAAK,KAAK;AACrC,MACI,MAAM,IAAI,MAAM,KAEhB,SAAS,IAAI,cAAc,MAAM,GAAG,GAAG,EAAE,UAAU,IAAI,OAAO;AAElE,SAAO,CAAC,QAAQ,MAAM;AAC1B;AAGA,SAAS,mBAAmB,MAAM,MAAM,SAAS,KAAK,KAAK;AACvD,MAAI,gBAAgB,IAAI,UAAU,OAAO,GACrC,aAAa,gBAAgB,MAAM,KAAK,GAAG,GAC3C,YAAY,IAAI,KAAK,OAAO,KAAK,eAAe,YAChD,SACA;AAEJ,MAAI,aAAa,GAAG;AAChB,cAAU,OAAO;AACjB,mBAAe,WAAW,OAAO,IAAI;AAAA,EACzC,WAAW,YAAY,WAAW,IAAI,GAAG;AACrC,cAAU,OAAO;AACjB,mBAAe,YAAY,WAAW,IAAI;AAAA,EAC9C,OAAO;AACH,cAAU;AACV,mBAAe;AAAA,EACnB;AAEA,SAAO;AAAA,IACH,MAAM;AAAA,IACN,WAAW;AAAA,EACf;AACJ;AAEA,SAAS,WAAW,KAAK,KAAK,KAAK;AAC/B,MAAI,aAAa,gBAAgB,IAAI,KAAK,GAAG,KAAK,GAAG,GACjD,OAAO,KAAK,OAAO,IAAI,UAAU,IAAI,aAAa,KAAK,CAAC,IAAI,GAC5D,SACA;AAEJ,MAAI,OAAO,GAAG;AACV,cAAU,IAAI,KAAK,IAAI;AACvB,cAAU,OAAO,YAAY,SAAS,KAAK,GAAG;AAAA,EAClD,WAAW,OAAO,YAAY,IAAI,KAAK,GAAG,KAAK,GAAG,GAAG;AACjD,cAAU,OAAO,YAAY,IAAI,KAAK,GAAG,KAAK,GAAG;AACjD,cAAU,IAAI,KAAK,IAAI;AAAA,EAC3B,OAAO;AACH,cAAU,IAAI,KAAK;AACnB,cAAU;AAAA,EACd;AAEA,SAAO;AAAA,IACH,MAAM;AAAA,IACN,MAAM;AAAA,EACV;AACJ;AAEA,SAAS,YAAY,MAAM,KAAK,KAAK;AACjC,MAAI,aAAa,gBAAgB,MAAM,KAAK,GAAG,GAC3C,iBAAiB,gBAAgB,OAAO,GAAG,KAAK,GAAG;AACvD,UAAQ,WAAW,IAAI,IAAI,aAAa,kBAAkB;AAC9D;AAIA,eAAe,KAAK,CAAC,MAAM,CAAC,GAAG,MAAM,MAAM;AAC3C,eAAe,KAAK,CAAC,MAAM,CAAC,GAAG,MAAM,SAAS;AAI9C,cAAc,KAAK,WAAW,sBAAsB;AACpD,cAAc,MAAM,WAAW,MAAM;AACrC,cAAc,KAAK,WAAW,sBAAsB;AACpD,cAAc,MAAM,WAAW,MAAM;AAErC;AAAA,EACI,CAAC,KAAK,MAAM,KAAK,IAAI;AAAA,EACrB,SAAU,OAAO,MAAM,QAAQM,QAAO;AAClC,SAAKA,OAAM,OAAO,GAAG,CAAC,CAAC,IAAI,MAAM,KAAK;AAAA,EAC1C;AACJ;AAMA,SAAS,WAAW,KAAK;AACrB,SAAO,WAAW,KAAK,KAAK,MAAM,KAAK,KAAK,MAAM,GAAG,EAAE;AAC3D;AAEA,IAAI,oBAAoB;AAAA,EACpB,KAAK;AAAA;AAAA,EACL,KAAK;AAAA;AACT;AAEA,SAAS,uBAAuB;AAC5B,SAAO,KAAK,MAAM;AACtB;AAEA,SAAS,uBAAuB;AAC5B,SAAO,KAAK,MAAM;AACtB;AAIA,SAAS,WAAW,OAAO;AACvB,MAAI,OAAO,KAAK,WAAW,EAAE,KAAK,IAAI;AACtC,SAAO,SAAS,OAAO,OAAO,KAAK,KAAK,QAAQ,QAAQ,GAAG,GAAG;AAClE;AAEA,SAAS,cAAc,OAAO;AAC1B,MAAI,OAAO,WAAW,MAAM,GAAG,CAAC,EAAE;AAClC,SAAO,SAAS,OAAO,OAAO,KAAK,KAAK,QAAQ,QAAQ,GAAG,GAAG;AAClE;AAIA,eAAe,KAAK,GAAG,MAAM,KAAK;AAElC,eAAe,MAAM,GAAG,GAAG,SAAUN,SAAQ;AACzC,SAAO,KAAK,WAAW,EAAE,YAAY,MAAMA,OAAM;AACrD,CAAC;AAED,eAAe,OAAO,GAAG,GAAG,SAAUA,SAAQ;AAC1C,SAAO,KAAK,WAAW,EAAE,cAAc,MAAMA,OAAM;AACvD,CAAC;AAED,eAAe,QAAQ,GAAG,GAAG,SAAUA,SAAQ;AAC3C,SAAO,KAAK,WAAW,EAAE,SAAS,MAAMA,OAAM;AAClD,CAAC;AAED,eAAe,KAAK,GAAG,GAAG,SAAS;AACnC,eAAe,KAAK,GAAG,GAAG,YAAY;AAItC,cAAc,KAAK,SAAS;AAC5B,cAAc,KAAK,SAAS;AAC5B,cAAc,KAAK,SAAS;AAC5B,cAAc,MAAM,SAAU,UAAUC,SAAQ;AAC5C,SAAOA,QAAO,iBAAiB,QAAQ;AAC3C,CAAC;AACD,cAAc,OAAO,SAAU,UAAUA,SAAQ;AAC7C,SAAOA,QAAO,mBAAmB,QAAQ;AAC7C,CAAC;AACD,cAAc,QAAQ,SAAU,UAAUA,SAAQ;AAC9C,SAAOA,QAAO,cAAc,QAAQ;AACxC,CAAC;AAED,kBAAkB,CAAC,MAAM,OAAO,MAAM,GAAG,SAAU,OAAO,MAAM,QAAQK,QAAO;AAC3E,MAAI,UAAU,OAAO,QAAQ,cAAc,OAAOA,QAAO,OAAO,OAAO;AAEvE,MAAI,WAAW,MAAM;AACjB,SAAK,IAAI;AAAA,EACb,OAAO;AACH,oBAAgB,MAAM,EAAE,iBAAiB;AAAA,EAC7C;AACJ,CAAC;AAED,kBAAkB,CAAC,KAAK,KAAK,GAAG,GAAG,SAAU,OAAO,MAAM,QAAQA,QAAO;AACrE,OAAKA,MAAK,IAAI,MAAM,KAAK;AAC7B,CAAC;AAID,SAAS,aAAa,OAAOL,SAAQ;AACjC,MAAI,OAAO,UAAU,UAAU;AAC3B,WAAO;AAAA,EACX;AAEA,MAAI,CAAC,MAAM,KAAK,GAAG;AACf,WAAO,SAAS,OAAO,EAAE;AAAA,EAC7B;AAEA,UAAQA,QAAO,cAAc,KAAK;AAClC,MAAI,OAAO,UAAU,UAAU;AAC3B,WAAO;AAAA,EACX;AAEA,SAAO;AACX;AAEA,SAAS,gBAAgB,OAAOA,SAAQ;AACpC,MAAI,OAAO,UAAU,UAAU;AAC3B,WAAOA,QAAO,cAAc,KAAK,IAAI,KAAK;AAAA,EAC9C;AACA,SAAO,MAAM,KAAK,IAAI,OAAO;AACjC;AAGA,SAAS,cAAc,IAAI,GAAG;AAC1B,SAAO,GAAG,MAAM,GAAG,CAAC,EAAE,OAAO,GAAG,MAAM,GAAG,CAAC,CAAC;AAC/C;AAEA,IAAI,wBACI,2DAA2D,MAAM,GAAG;AAD5E,IAEI,6BAA6B,8BAA8B,MAAM,GAAG;AAFxE,IAGI,2BAA2B,uBAAuB,MAAM,GAAG;AAH/D,IAII,uBAAuB;AAJ3B,IAKI,4BAA4B;AALhC,IAMI,0BAA0B;AAE9B,SAAS,eAAe,GAAGD,SAAQ;AAC/B,MAAI,WAAW,QAAQ,KAAK,SAAS,IAC/B,KAAK,YACL,KAAK,UACD,KAAK,MAAM,QAAQ,KAAK,UAAU,SAAS,KAAKA,OAAM,IAChD,WACA,YACV;AACN,SAAO,MAAM,OACP,cAAc,UAAU,KAAK,MAAM,GAAG,IACtC,IACE,SAAS,EAAE,IAAI,CAAC,IAChB;AACZ;AAEA,SAAS,oBAAoB,GAAG;AAC5B,SAAO,MAAM,OACP,cAAc,KAAK,gBAAgB,KAAK,MAAM,GAAG,IACjD,IACE,KAAK,eAAe,EAAE,IAAI,CAAC,IAC3B,KAAK;AACjB;AAEA,SAAS,kBAAkB,GAAG;AAC1B,SAAO,MAAM,OACP,cAAc,KAAK,cAAc,KAAK,MAAM,GAAG,IAC/C,IACE,KAAK,aAAa,EAAE,IAAI,CAAC,IACzB,KAAK;AACjB;AAEA,SAAS,oBAAoB,aAAaA,SAAQ,QAAQ;AACtD,MAAI,GACA,IACA,KACA,MAAM,YAAY,kBAAkB;AACxC,MAAI,CAAC,KAAK,gBAAgB;AACtB,SAAK,iBAAiB,CAAC;AACvB,SAAK,sBAAsB,CAAC;AAC5B,SAAK,oBAAoB,CAAC;AAE1B,SAAK,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACpB,YAAM,UAAU,CAAC,KAAM,CAAC,CAAC,EAAE,IAAI,CAAC;AAChC,WAAK,kBAAkB,CAAC,IAAI,KAAK;AAAA,QAC7B;AAAA,QACA;AAAA,MACJ,EAAE,kBAAkB;AACpB,WAAK,oBAAoB,CAAC,IAAI,KAAK;AAAA,QAC/B;AAAA,QACA;AAAA,MACJ,EAAE,kBAAkB;AACpB,WAAK,eAAe,CAAC,IAAI,KAAK,SAAS,KAAK,EAAE,EAAE,kBAAkB;AAAA,IACtE;AAAA,EACJ;AAEA,MAAI,QAAQ;AACR,QAAIA,YAAW,QAAQ;AACnB,WAAK,QAAQ,KAAK,KAAK,gBAAgB,GAAG;AAC1C,aAAO,OAAO,KAAK,KAAK;AAAA,IAC5B,WAAWA,YAAW,OAAO;AACzB,WAAK,QAAQ,KAAK,KAAK,qBAAqB,GAAG;AAC/C,aAAO,OAAO,KAAK,KAAK;AAAA,IAC5B,OAAO;AACH,WAAK,QAAQ,KAAK,KAAK,mBAAmB,GAAG;AAC7C,aAAO,OAAO,KAAK,KAAK;AAAA,IAC5B;AAAA,EACJ,OAAO;AACH,QAAIA,YAAW,QAAQ;AACnB,WAAK,QAAQ,KAAK,KAAK,gBAAgB,GAAG;AAC1C,UAAI,OAAO,IAAI;AACX,eAAO;AAAA,MACX;AACA,WAAK,QAAQ,KAAK,KAAK,qBAAqB,GAAG;AAC/C,UAAI,OAAO,IAAI;AACX,eAAO;AAAA,MACX;AACA,WAAK,QAAQ,KAAK,KAAK,mBAAmB,GAAG;AAC7C,aAAO,OAAO,KAAK,KAAK;AAAA,IAC5B,WAAWA,YAAW,OAAO;AACzB,WAAK,QAAQ,KAAK,KAAK,qBAAqB,GAAG;AAC/C,UAAI,OAAO,IAAI;AACX,eAAO;AAAA,MACX;AACA,WAAK,QAAQ,KAAK,KAAK,gBAAgB,GAAG;AAC1C,UAAI,OAAO,IAAI;AACX,eAAO;AAAA,MACX;AACA,WAAK,QAAQ,KAAK,KAAK,mBAAmB,GAAG;AAC7C,aAAO,OAAO,KAAK,KAAK;AAAA,IAC5B,OAAO;AACH,WAAK,QAAQ,KAAK,KAAK,mBAAmB,GAAG;AAC7C,UAAI,OAAO,IAAI;AACX,eAAO;AAAA,MACX;AACA,WAAK,QAAQ,KAAK,KAAK,gBAAgB,GAAG;AAC1C,UAAI,OAAO,IAAI;AACX,eAAO;AAAA,MACX;AACA,WAAK,QAAQ,KAAK,KAAK,qBAAqB,GAAG;AAC/C,aAAO,OAAO,KAAK,KAAK;AAAA,IAC5B;AAAA,EACJ;AACJ;AAEA,SAAS,oBAAoB,aAAaA,SAAQ,QAAQ;AACtD,MAAI,GAAG,KAAK;AAEZ,MAAI,KAAK,qBAAqB;AAC1B,WAAO,oBAAoB,KAAK,MAAM,aAAaA,SAAQ,MAAM;AAAA,EACrE;AAEA,MAAI,CAAC,KAAK,gBAAgB;AACtB,SAAK,iBAAiB,CAAC;AACvB,SAAK,oBAAoB,CAAC;AAC1B,SAAK,sBAAsB,CAAC;AAC5B,SAAK,qBAAqB,CAAC;AAAA,EAC/B;AAEA,OAAK,IAAI,GAAG,IAAI,GAAG,KAAK;AAGpB,UAAM,UAAU,CAAC,KAAM,CAAC,CAAC,EAAE,IAAI,CAAC;AAChC,QAAI,UAAU,CAAC,KAAK,mBAAmB,CAAC,GAAG;AACvC,WAAK,mBAAmB,CAAC,IAAI,IAAI;AAAA,QAC7B,MAAM,KAAK,SAAS,KAAK,EAAE,EAAE,QAAQ,KAAK,MAAM,IAAI;AAAA,QACpD;AAAA,MACJ;AACA,WAAK,oBAAoB,CAAC,IAAI,IAAI;AAAA,QAC9B,MAAM,KAAK,cAAc,KAAK,EAAE,EAAE,QAAQ,KAAK,MAAM,IAAI;AAAA,QACzD;AAAA,MACJ;AACA,WAAK,kBAAkB,CAAC,IAAI,IAAI;AAAA,QAC5B,MAAM,KAAK,YAAY,KAAK,EAAE,EAAE,QAAQ,KAAK,MAAM,IAAI;AAAA,QACvD;AAAA,MACJ;AAAA,IACJ;AACA,QAAI,CAAC,KAAK,eAAe,CAAC,GAAG;AACzB,cACI,MACA,KAAK,SAAS,KAAK,EAAE,IACrB,OACA,KAAK,cAAc,KAAK,EAAE,IAC1B,OACA,KAAK,YAAY,KAAK,EAAE;AAC5B,WAAK,eAAe,CAAC,IAAI,IAAI,OAAO,MAAM,QAAQ,KAAK,EAAE,GAAG,GAAG;AAAA,IACnE;AAEA,QACI,UACAA,YAAW,UACX,KAAK,mBAAmB,CAAC,EAAE,KAAK,WAAW,GAC7C;AACE,aAAO;AAAA,IACX,WACI,UACAA,YAAW,SACX,KAAK,oBAAoB,CAAC,EAAE,KAAK,WAAW,GAC9C;AACE,aAAO;AAAA,IACX,WACI,UACAA,YAAW,QACX,KAAK,kBAAkB,CAAC,EAAE,KAAK,WAAW,GAC5C;AACE,aAAO;AAAA,IACX,WAAW,CAAC,UAAU,KAAK,eAAe,CAAC,EAAE,KAAK,WAAW,GAAG;AAC5D,aAAO;AAAA,IACX;AAAA,EACJ;AACJ;AAIA,SAAS,gBAAgB,OAAO;AAC5B,MAAI,CAAC,KAAK,QAAQ,GAAG;AACjB,WAAO,SAAS,OAAO,OAAO;AAAA,EAClC;AAEA,MAAI,MAAM,IAAI,MAAM,KAAK;AACzB,MAAI,SAAS,MAAM;AACf,YAAQ,aAAa,OAAO,KAAK,WAAW,CAAC;AAC7C,WAAO,KAAK,IAAI,QAAQ,KAAK,GAAG;AAAA,EACpC,OAAO;AACH,WAAO;AAAA,EACX;AACJ;AAEA,SAAS,sBAAsB,OAAO;AAClC,MAAI,CAAC,KAAK,QAAQ,GAAG;AACjB,WAAO,SAAS,OAAO,OAAO;AAAA,EAClC;AACA,MAAI,WAAW,KAAK,IAAI,IAAI,IAAI,KAAK,WAAW,EAAE,MAAM,OAAO;AAC/D,SAAO,SAAS,OAAO,UAAU,KAAK,IAAI,QAAQ,SAAS,GAAG;AAClE;AAEA,SAAS,mBAAmB,OAAO;AAC/B,MAAI,CAAC,KAAK,QAAQ,GAAG;AACjB,WAAO,SAAS,OAAO,OAAO;AAAA,EAClC;AAMA,MAAI,SAAS,MAAM;AACf,QAAI,UAAU,gBAAgB,OAAO,KAAK,WAAW,CAAC;AACtD,WAAO,KAAK,IAAI,KAAK,IAAI,IAAI,IAAI,UAAU,UAAU,CAAC;AAAA,EAC1D,OAAO;AACH,WAAO,KAAK,IAAI,KAAK;AAAA,EACzB;AACJ;AAEA,SAAS,cAAc,UAAU;AAC7B,MAAI,KAAK,qBAAqB;AAC1B,QAAI,CAAC,WAAW,MAAM,gBAAgB,GAAG;AACrC,2BAAqB,KAAK,IAAI;AAAA,IAClC;AACA,QAAI,UAAU;AACV,aAAO,KAAK;AAAA,IAChB,OAAO;AACH,aAAO,KAAK;AAAA,IAChB;AAAA,EACJ,OAAO;AACH,QAAI,CAAC,WAAW,MAAM,gBAAgB,GAAG;AACrC,WAAK,iBAAiB;AAAA,IAC1B;AACA,WAAO,KAAK,wBAAwB,WAC9B,KAAK,uBACL,KAAK;AAAA,EACf;AACJ;AAEA,SAAS,mBAAmB,UAAU;AAClC,MAAI,KAAK,qBAAqB;AAC1B,QAAI,CAAC,WAAW,MAAM,gBAAgB,GAAG;AACrC,2BAAqB,KAAK,IAAI;AAAA,IAClC;AACA,QAAI,UAAU;AACV,aAAO,KAAK;AAAA,IAChB,OAAO;AACH,aAAO,KAAK;AAAA,IAChB;AAAA,EACJ,OAAO;AACH,QAAI,CAAC,WAAW,MAAM,qBAAqB,GAAG;AAC1C,WAAK,sBAAsB;AAAA,IAC/B;AACA,WAAO,KAAK,6BAA6B,WACnC,KAAK,4BACL,KAAK;AAAA,EACf;AACJ;AAEA,SAAS,iBAAiB,UAAU;AAChC,MAAI,KAAK,qBAAqB;AAC1B,QAAI,CAAC,WAAW,MAAM,gBAAgB,GAAG;AACrC,2BAAqB,KAAK,IAAI;AAAA,IAClC;AACA,QAAI,UAAU;AACV,aAAO,KAAK;AAAA,IAChB,OAAO;AACH,aAAO,KAAK;AAAA,IAChB;AAAA,EACJ,OAAO;AACH,QAAI,CAAC,WAAW,MAAM,mBAAmB,GAAG;AACxC,WAAK,oBAAoB;AAAA,IAC7B;AACA,WAAO,KAAK,2BAA2B,WACjC,KAAK,0BACL,KAAK;AAAA,EACf;AACJ;AAEA,SAAS,uBAAuB;AAC5B,WAAS,UAAU,GAAG,GAAG;AACrB,WAAO,EAAE,SAAS,EAAE;AAAA,EACxB;AAEA,MAAI,YAAY,CAAC,GACb,cAAc,CAAC,GACf,aAAa,CAAC,GACd,cAAc,CAAC,GACf,GACA,KACA,MACA,QACA;AACJ,OAAK,IAAI,GAAG,IAAI,GAAG,KAAK;AAEpB,UAAM,UAAU,CAAC,KAAM,CAAC,CAAC,EAAE,IAAI,CAAC;AAChC,WAAO,YAAY,KAAK,YAAY,KAAK,EAAE,CAAC;AAC5C,aAAS,YAAY,KAAK,cAAc,KAAK,EAAE,CAAC;AAChD,YAAQ,YAAY,KAAK,SAAS,KAAK,EAAE,CAAC;AAC1C,cAAU,KAAK,IAAI;AACnB,gBAAY,KAAK,MAAM;AACvB,eAAW,KAAK,KAAK;AACrB,gBAAY,KAAK,IAAI;AACrB,gBAAY,KAAK,MAAM;AACvB,gBAAY,KAAK,KAAK;AAAA,EAC1B;AAGA,YAAU,KAAK,SAAS;AACxB,cAAY,KAAK,SAAS;AAC1B,aAAW,KAAK,SAAS;AACzB,cAAY,KAAK,SAAS;AAE1B,OAAK,iBAAiB,IAAI,OAAO,OAAO,YAAY,KAAK,GAAG,IAAI,KAAK,GAAG;AACxE,OAAK,sBAAsB,KAAK;AAChC,OAAK,oBAAoB,KAAK;AAE9B,OAAK,uBAAuB,IAAI;AAAA,IAC5B,OAAO,WAAW,KAAK,GAAG,IAAI;AAAA,IAC9B;AAAA,EACJ;AACA,OAAK,4BAA4B,IAAI;AAAA,IACjC,OAAO,YAAY,KAAK,GAAG,IAAI;AAAA,IAC/B;AAAA,EACJ;AACA,OAAK,0BAA0B,IAAI;AAAA,IAC/B,OAAO,UAAU,KAAK,GAAG,IAAI;AAAA,IAC7B;AAAA,EACJ;AACJ;AAIA,SAAS,UAAU;AACf,SAAO,KAAK,MAAM,IAAI,MAAM;AAChC;AAEA,SAAS,UAAU;AACf,SAAO,KAAK,MAAM,KAAK;AAC3B;AAEA,eAAe,KAAK,CAAC,MAAM,CAAC,GAAG,GAAG,MAAM;AACxC,eAAe,KAAK,CAAC,MAAM,CAAC,GAAG,GAAG,OAAO;AACzC,eAAe,KAAK,CAAC,MAAM,CAAC,GAAG,GAAG,OAAO;AAEzC,eAAe,OAAO,GAAG,GAAG,WAAY;AACpC,SAAO,KAAK,QAAQ,MAAM,IAAI,IAAI,SAAS,KAAK,QAAQ,GAAG,CAAC;AAChE,CAAC;AAED,eAAe,SAAS,GAAG,GAAG,WAAY;AACtC,SACI,KACA,QAAQ,MAAM,IAAI,IAClB,SAAS,KAAK,QAAQ,GAAG,CAAC,IAC1B,SAAS,KAAK,QAAQ,GAAG,CAAC;AAElC,CAAC;AAED,eAAe,OAAO,GAAG,GAAG,WAAY;AACpC,SAAO,KAAK,KAAK,MAAM,IAAI,SAAS,KAAK,QAAQ,GAAG,CAAC;AACzD,CAAC;AAED,eAAe,SAAS,GAAG,GAAG,WAAY;AACtC,SACI,KACA,KAAK,MAAM,IACX,SAAS,KAAK,QAAQ,GAAG,CAAC,IAC1B,SAAS,KAAK,QAAQ,GAAG,CAAC;AAElC,CAAC;AAED,SAAS,SAASM,QAAO,WAAW;AAChC,iBAAeA,QAAO,GAAG,GAAG,WAAY;AACpC,WAAO,KAAK,WAAW,EAAE;AAAA,MACrB,KAAK,MAAM;AAAA,MACX,KAAK,QAAQ;AAAA,MACb;AAAA,IACJ;AAAA,EACJ,CAAC;AACL;AAEA,SAAS,KAAK,IAAI;AAClB,SAAS,KAAK,KAAK;AAInB,SAAS,cAAc,UAAUL,SAAQ;AACrC,SAAOA,QAAO;AAClB;AAEA,cAAc,KAAK,aAAa;AAChC,cAAc,KAAK,aAAa;AAChC,cAAc,KAAK,WAAW,gBAAgB;AAC9C,cAAc,KAAK,WAAW,sBAAsB;AACpD,cAAc,KAAK,WAAW,sBAAsB;AACpD,cAAc,MAAM,WAAW,MAAM;AACrC,cAAc,MAAM,WAAW,MAAM;AACrC,cAAc,MAAM,WAAW,MAAM;AAErC,cAAc,OAAO,SAAS;AAC9B,cAAc,SAAS,SAAS;AAChC,cAAc,OAAO,SAAS;AAC9B,cAAc,SAAS,SAAS;AAEhC,cAAc,CAAC,KAAK,IAAI,GAAG,IAAI;AAC/B,cAAc,CAAC,KAAK,IAAI,GAAG,SAAU,OAAO,OAAO,QAAQ;AACvD,MAAI,SAAS,MAAM,KAAK;AACxB,QAAM,IAAI,IAAI,WAAW,KAAK,IAAI;AACtC,CAAC;AACD,cAAc,CAAC,KAAK,GAAG,GAAG,SAAU,OAAO,OAAO,QAAQ;AACtD,SAAO,QAAQ,OAAO,QAAQ,KAAK,KAAK;AACxC,SAAO,YAAY;AACvB,CAAC;AACD,cAAc,CAAC,KAAK,IAAI,GAAG,SAAU,OAAO,OAAO,QAAQ;AACvD,QAAM,IAAI,IAAI,MAAM,KAAK;AACzB,kBAAgB,MAAM,EAAE,UAAU;AACtC,CAAC;AACD,cAAc,OAAO,SAAU,OAAO,OAAO,QAAQ;AACjD,MAAI,MAAM,MAAM,SAAS;AACzB,QAAM,IAAI,IAAI,MAAM,MAAM,OAAO,GAAG,GAAG,CAAC;AACxC,QAAM,MAAM,IAAI,MAAM,MAAM,OAAO,GAAG,CAAC;AACvC,kBAAgB,MAAM,EAAE,UAAU;AACtC,CAAC;AACD,cAAc,SAAS,SAAU,OAAO,OAAO,QAAQ;AACnD,MAAI,OAAO,MAAM,SAAS,GACtB,OAAO,MAAM,SAAS;AAC1B,QAAM,IAAI,IAAI,MAAM,MAAM,OAAO,GAAG,IAAI,CAAC;AACzC,QAAM,MAAM,IAAI,MAAM,MAAM,OAAO,MAAM,CAAC,CAAC;AAC3C,QAAM,MAAM,IAAI,MAAM,MAAM,OAAO,IAAI,CAAC;AACxC,kBAAgB,MAAM,EAAE,UAAU;AACtC,CAAC;AACD,cAAc,OAAO,SAAU,OAAO,OAAO,QAAQ;AACjD,MAAI,MAAM,MAAM,SAAS;AACzB,QAAM,IAAI,IAAI,MAAM,MAAM,OAAO,GAAG,GAAG,CAAC;AACxC,QAAM,MAAM,IAAI,MAAM,MAAM,OAAO,GAAG,CAAC;AAC3C,CAAC;AACD,cAAc,SAAS,SAAU,OAAO,OAAO,QAAQ;AACnD,MAAI,OAAO,MAAM,SAAS,GACtB,OAAO,MAAM,SAAS;AAC1B,QAAM,IAAI,IAAI,MAAM,MAAM,OAAO,GAAG,IAAI,CAAC;AACzC,QAAM,MAAM,IAAI,MAAM,MAAM,OAAO,MAAM,CAAC,CAAC;AAC3C,QAAM,MAAM,IAAI,MAAM,MAAM,OAAO,IAAI,CAAC;AAC5C,CAAC;AAID,SAAS,WAAW,OAAO;AAGvB,UAAQ,QAAQ,IAAI,YAAY,EAAE,OAAO,CAAC,MAAM;AACpD;AAEA,IAAI,6BAA6B;AAAjC,IAKI,aAAa,WAAW,SAAS,IAAI;AAEzC,SAAS,eAAeU,QAAOC,UAAS,SAAS;AAC7C,MAAID,SAAQ,IAAI;AACZ,WAAO,UAAU,OAAO;AAAA,EAC5B,OAAO;AACH,WAAO,UAAU,OAAO;AAAA,EAC5B;AACJ;AAEA,IAAI,aAAa;AAAA,EACb,UAAU;AAAA,EACV,gBAAgB;AAAA,EAChB,aAAa;AAAA,EACb,SAAS;AAAA,EACT,wBAAwB;AAAA,EACxB,cAAc;AAAA,EAEd,QAAQ;AAAA,EACR,aAAa;AAAA,EAEb,MAAM;AAAA,EAEN,UAAU;AAAA,EACV,aAAa;AAAA,EACb,eAAe;AAAA,EAEf,eAAe;AACnB;AAGA,IAAI,UAAU,CAAC;AAAf,IACI,iBAAiB,CAAC;AADtB,IAEI;AAEJ,SAAS,aAAa,MAAM,MAAM;AAC9B,MAAI,GACA,OAAO,KAAK,IAAI,KAAK,QAAQ,KAAK,MAAM;AAC5C,OAAK,IAAI,GAAG,IAAI,MAAM,KAAK,GAAG;AAC1B,QAAI,KAAK,CAAC,MAAM,KAAK,CAAC,GAAG;AACrB,aAAO;AAAA,IACX;AAAA,EACJ;AACA,SAAO;AACX;AAEA,SAAS,gBAAgB,KAAK;AAC1B,SAAO,MAAM,IAAI,YAAY,EAAE,QAAQ,KAAK,GAAG,IAAI;AACvD;AAKA,SAAS,aAAa,OAAO;AACzB,MAAI,IAAI,GACJ,GACA,MACAV,SACA;AAEJ,SAAO,IAAI,MAAM,QAAQ;AACrB,YAAQ,gBAAgB,MAAM,CAAC,CAAC,EAAE,MAAM,GAAG;AAC3C,QAAI,MAAM;AACV,WAAO,gBAAgB,MAAM,IAAI,CAAC,CAAC;AACnC,WAAO,OAAO,KAAK,MAAM,GAAG,IAAI;AAChC,WAAO,IAAI,GAAG;AACV,MAAAA,UAAS,WAAW,MAAM,MAAM,GAAG,CAAC,EAAE,KAAK,GAAG,CAAC;AAC/C,UAAIA,SAAQ;AACR,eAAOA;AAAA,MACX;AACA,UACI,QACA,KAAK,UAAU,KACf,aAAa,OAAO,IAAI,KAAK,IAAI,GACnC;AAEE;AAAA,MACJ;AACA;AAAA,IACJ;AACA;AAAA,EACJ;AACA,SAAO;AACX;AAEA,SAAS,iBAAiB,MAAM;AAG5B,SAAO,CAAC,EAAE,QAAQ,KAAK,MAAM,aAAa;AAC9C;AAEA,SAAS,WAAW,MAAM;AACtB,MAAI,YAAY,MACZ;AAEJ,MACI,QAAQ,IAAI,MAAM,UAClB,OAAO,WAAW,eAClB,UACA,OAAO,WACP,iBAAiB,IAAI,GACvB;AACE,QAAI;AACA,kBAAY,aAAa;AACzB,uBAAiB;AACjB,qBAAe,cAAc,IAAI;AACjC,yBAAmB,SAAS;AAAA,IAChC,SAAS,GAAG;AAGR,cAAQ,IAAI,IAAI;AAAA,IACpB;AAAA,EACJ;AACA,SAAO,QAAQ,IAAI;AACvB;AAKA,SAAS,mBAAmB,KAAK,QAAQ;AACrC,MAAI;AACJ,MAAI,KAAK;AACL,QAAI,YAAY,MAAM,GAAG;AACrB,aAAO,UAAU,GAAG;AAAA,IACxB,OAAO;AACH,aAAO,aAAa,KAAK,MAAM;AAAA,IACnC;AAEA,QAAI,MAAM;AAEN,qBAAe;AAAA,IACnB,OAAO;AACH,UAAI,OAAO,YAAY,eAAe,QAAQ,MAAM;AAEhD,gBAAQ;AAAA,UACJ,YAAY,MAAM;AAAA,QACtB;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AAEA,SAAO,aAAa;AACxB;AAEA,SAAS,aAAa,MAAM,QAAQ;AAChC,MAAI,WAAW,MAAM;AACjB,QAAIA,SACA,eAAe;AACnB,WAAO,OAAO;AACd,QAAI,QAAQ,IAAI,KAAK,MAAM;AACvB;AAAA,QACI;AAAA,QACA;AAAA,MAIJ;AACA,qBAAe,QAAQ,IAAI,EAAE;AAAA,IACjC,WAAW,OAAO,gBAAgB,MAAM;AACpC,UAAI,QAAQ,OAAO,YAAY,KAAK,MAAM;AACtC,uBAAe,QAAQ,OAAO,YAAY,EAAE;AAAA,MAChD,OAAO;AACH,QAAAA,UAAS,WAAW,OAAO,YAAY;AACvC,YAAIA,WAAU,MAAM;AAChB,yBAAeA,QAAO;AAAA,QAC1B,OAAO;AACH,cAAI,CAAC,eAAe,OAAO,YAAY,GAAG;AACtC,2BAAe,OAAO,YAAY,IAAI,CAAC;AAAA,UAC3C;AACA,yBAAe,OAAO,YAAY,EAAE,KAAK;AAAA,YACrC;AAAA,YACA;AAAA,UACJ,CAAC;AACD,iBAAO;AAAA,QACX;AAAA,MACJ;AAAA,IACJ;AACA,YAAQ,IAAI,IAAI,IAAI,OAAO,aAAa,cAAc,MAAM,CAAC;AAE7D,QAAI,eAAe,IAAI,GAAG;AACtB,qBAAe,IAAI,EAAE,QAAQ,SAAU,GAAG;AACtC,qBAAa,EAAE,MAAM,EAAE,MAAM;AAAA,MACjC,CAAC;AAAA,IACL;AAKA,uBAAmB,IAAI;AAEvB,WAAO,QAAQ,IAAI;AAAA,EACvB,OAAO;AAEH,WAAO,QAAQ,IAAI;AACnB,WAAO;AAAA,EACX;AACJ;AAEA,SAAS,aAAa,MAAM,QAAQ;AAChC,MAAI,UAAU,MAAM;AAChB,QAAIA,SACA,WACA,eAAe;AAEnB,QAAI,QAAQ,IAAI,KAAK,QAAQ,QAAQ,IAAI,EAAE,gBAAgB,MAAM;AAE7D,cAAQ,IAAI,EAAE,IAAI,aAAa,QAAQ,IAAI,EAAE,SAAS,MAAM,CAAC;AAAA,IACjE,OAAO;AAEH,kBAAY,WAAW,IAAI;AAC3B,UAAI,aAAa,MAAM;AACnB,uBAAe,UAAU;AAAA,MAC7B;AACA,eAAS,aAAa,cAAc,MAAM;AAC1C,UAAI,aAAa,MAAM;AAInB,eAAO,OAAO;AAAA,MAClB;AACA,MAAAA,UAAS,IAAI,OAAO,MAAM;AAC1B,MAAAA,QAAO,eAAe,QAAQ,IAAI;AAClC,cAAQ,IAAI,IAAIA;AAAA,IACpB;AAGA,uBAAmB,IAAI;AAAA,EAC3B,OAAO;AAEH,QAAI,QAAQ,IAAI,KAAK,MAAM;AACvB,UAAI,QAAQ,IAAI,EAAE,gBAAgB,MAAM;AACpC,gBAAQ,IAAI,IAAI,QAAQ,IAAI,EAAE;AAC9B,YAAI,SAAS,mBAAmB,GAAG;AAC/B,6BAAmB,IAAI;AAAA,QAC3B;AAAA,MACJ,WAAW,QAAQ,IAAI,KAAK,MAAM;AAC9B,eAAO,QAAQ,IAAI;AAAA,MACvB;AAAA,IACJ;AAAA,EACJ;AACA,SAAO,QAAQ,IAAI;AACvB;AAGA,SAAS,UAAU,KAAK;AACpB,MAAIA;AAEJ,MAAI,OAAO,IAAI,WAAW,IAAI,QAAQ,OAAO;AACzC,UAAM,IAAI,QAAQ;AAAA,EACtB;AAEA,MAAI,CAAC,KAAK;AACN,WAAO;AAAA,EACX;AAEA,MAAI,CAAC,QAAQ,GAAG,GAAG;AAEf,IAAAA,UAAS,WAAW,GAAG;AACvB,QAAIA,SAAQ;AACR,aAAOA;AAAA,IACX;AACA,UAAM,CAAC,GAAG;AAAA,EACd;AAEA,SAAO,aAAa,GAAG;AAC3B;AAEA,SAAS,cAAc;AACnB,SAAO,KAAK,OAAO;AACvB;AAEA,SAAS,cAAc,GAAG;AACtB,MAAI,UACA,IAAI,EAAE;AAEV,MAAI,KAAK,gBAAgB,CAAC,EAAE,aAAa,IAAI;AACzC,eACI,EAAE,KAAK,IAAI,KAAK,EAAE,KAAK,IAAI,KACrB,QACA,EAAE,IAAI,IAAI,KAAK,EAAE,IAAI,IAAI,YAAY,EAAE,IAAI,GAAG,EAAE,KAAK,CAAC,IACpD,OACA,EAAE,IAAI,IAAI,KACR,EAAE,IAAI,IAAI,MACT,EAAE,IAAI,MAAM,OACR,EAAE,MAAM,MAAM,KACX,EAAE,MAAM,MAAM,KACd,EAAE,WAAW,MAAM,KAC3B,OACA,EAAE,MAAM,IAAI,KAAK,EAAE,MAAM,IAAI,KAC3B,SACA,EAAE,MAAM,IAAI,KAAK,EAAE,MAAM,IAAI,KAC3B,SACA,EAAE,WAAW,IAAI,KAAK,EAAE,WAAW,IAAI,MACrC,cACA;AAEpB,QACI,gBAAgB,CAAC,EAAE,uBAClB,WAAW,QAAQ,WAAW,OACjC;AACE,iBAAW;AAAA,IACf;AACA,QAAI,gBAAgB,CAAC,EAAE,kBAAkB,aAAa,IAAI;AACtD,iBAAW;AAAA,IACf;AACA,QAAI,gBAAgB,CAAC,EAAE,oBAAoB,aAAa,IAAI;AACxD,iBAAW;AAAA,IACf;AAEA,oBAAgB,CAAC,EAAE,WAAW;AAAA,EAClC;AAEA,SAAO;AACX;AAIA,IAAI,mBACI;AADR,IAEI,gBACI;AAHR,IAII,UAAU;AAJd,IAKI,WAAW;AAAA,EACP,CAAC,gBAAgB,qBAAqB;AAAA,EACtC,CAAC,cAAc,iBAAiB;AAAA,EAChC,CAAC,gBAAgB,gBAAgB;AAAA,EACjC,CAAC,cAAc,eAAe,KAAK;AAAA,EACnC,CAAC,YAAY,aAAa;AAAA,EAC1B,CAAC,WAAW,cAAc,KAAK;AAAA,EAC/B,CAAC,cAAc,YAAY;AAAA,EAC3B,CAAC,YAAY,OAAO;AAAA,EACpB,CAAC,cAAc,aAAa;AAAA,EAC5B,CAAC,aAAa,eAAe,KAAK;AAAA,EAClC,CAAC,WAAW,OAAO;AAAA,EACnB,CAAC,UAAU,SAAS,KAAK;AAAA,EACzB,CAAC,QAAQ,SAAS,KAAK;AAC3B;AAnBJ,IAqBI,WAAW;AAAA,EACP,CAAC,iBAAiB,qBAAqB;AAAA,EACvC,CAAC,iBAAiB,oBAAoB;AAAA,EACtC,CAAC,YAAY,gBAAgB;AAAA,EAC7B,CAAC,SAAS,WAAW;AAAA,EACrB,CAAC,eAAe,mBAAmB;AAAA,EACnC,CAAC,eAAe,kBAAkB;AAAA,EAClC,CAAC,UAAU,cAAc;AAAA,EACzB,CAAC,QAAQ,UAAU;AAAA,EACnB,CAAC,MAAM,MAAM;AACjB;AA/BJ,IAgCI,kBAAkB;AAhCtB,IAkCI,UACI;AAnCR,IAoCI,aAAa;AAAA,EACT,IAAI;AAAA,EACJ,KAAK;AAAA,EACL,KAAK,KAAK;AAAA,EACV,KAAK,KAAK;AAAA,EACV,KAAK,KAAK;AAAA,EACV,KAAK,KAAK;AAAA,EACV,KAAK,KAAK;AAAA,EACV,KAAK,KAAK;AAAA,EACV,KAAK,KAAK;AAAA,EACV,KAAK,KAAK;AACd;AAGJ,SAAS,cAAc,QAAQ;AAC3B,MAAI,GACA,GACA,SAAS,OAAO,IAChB,QAAQ,iBAAiB,KAAK,MAAM,KAAK,cAAc,KAAK,MAAM,GAClE,WACA,YACA,YACA,UACA,cAAc,SAAS,QACvB,cAAc,SAAS;AAE3B,MAAI,OAAO;AACP,oBAAgB,MAAM,EAAE,MAAM;AAC9B,SAAK,IAAI,GAAG,IAAI,aAAa,IAAI,GAAG,KAAK;AACrC,UAAI,SAAS,CAAC,EAAE,CAAC,EAAE,KAAK,MAAM,CAAC,CAAC,GAAG;AAC/B,qBAAa,SAAS,CAAC,EAAE,CAAC;AAC1B,oBAAY,SAAS,CAAC,EAAE,CAAC,MAAM;AAC/B;AAAA,MACJ;AAAA,IACJ;AACA,QAAI,cAAc,MAAM;AACpB,aAAO,WAAW;AAClB;AAAA,IACJ;AACA,QAAI,MAAM,CAAC,GAAG;AACV,WAAK,IAAI,GAAG,IAAI,aAAa,IAAI,GAAG,KAAK;AACrC,YAAI,SAAS,CAAC,EAAE,CAAC,EAAE,KAAK,MAAM,CAAC,CAAC,GAAG;AAE/B,wBAAc,MAAM,CAAC,KAAK,OAAO,SAAS,CAAC,EAAE,CAAC;AAC9C;AAAA,QACJ;AAAA,MACJ;AACA,UAAI,cAAc,MAAM;AACpB,eAAO,WAAW;AAClB;AAAA,MACJ;AAAA,IACJ;AACA,QAAI,CAAC,aAAa,cAAc,MAAM;AAClC,aAAO,WAAW;AAClB;AAAA,IACJ;AACA,QAAI,MAAM,CAAC,GAAG;AACV,UAAI,QAAQ,KAAK,MAAM,CAAC,CAAC,GAAG;AACxB,mBAAW;AAAA,MACf,OAAO;AACH,eAAO,WAAW;AAClB;AAAA,MACJ;AAAA,IACJ;AACA,WAAO,KAAK,cAAc,cAAc,OAAO,YAAY;AAC3D,8BAA0B,MAAM;AAAA,EACpC,OAAO;AACH,WAAO,WAAW;AAAA,EACtB;AACJ;AAEA,SAAS,0BACL,SACA,UACA,QACA,SACA,WACA,WACF;AACE,MAAI,SAAS;AAAA,IACT,eAAe,OAAO;AAAA,IACtB,yBAAyB,QAAQ,QAAQ;AAAA,IACzC,SAAS,QAAQ,EAAE;AAAA,IACnB,SAAS,SAAS,EAAE;AAAA,IACpB,SAAS,WAAW,EAAE;AAAA,EAC1B;AAEA,MAAI,WAAW;AACX,WAAO,KAAK,SAAS,WAAW,EAAE,CAAC;AAAA,EACvC;AAEA,SAAO;AACX;AAEA,SAAS,eAAe,SAAS;AAC7B,MAAI,OAAO,SAAS,SAAS,EAAE;AAC/B,MAAI,QAAQ,IAAI;AACZ,WAAO,MAAO;AAAA,EAClB,WAAW,QAAQ,KAAK;AACpB,WAAO,OAAO;AAAA,EAClB;AACA,SAAO;AACX;AAEA,SAAS,kBAAkB,GAAG;AAE1B,SAAO,EACF,QAAQ,sBAAsB,GAAG,EACjC,QAAQ,YAAY,GAAG,EACvB,QAAQ,UAAU,EAAE,EACpB,QAAQ,UAAU,EAAE;AAC7B;AAEA,SAAS,aAAa,YAAY,aAAa,QAAQ;AACnD,MAAI,YAAY;AAEZ,QAAI,kBAAkB,2BAA2B,QAAQ,UAAU,GAC/D,gBAAgB,IAAI;AAAA,MAChB,YAAY,CAAC;AAAA,MACb,YAAY,CAAC;AAAA,MACb,YAAY,CAAC;AAAA,IACjB,EAAE,OAAO;AACb,QAAI,oBAAoB,eAAe;AACnC,sBAAgB,MAAM,EAAE,kBAAkB;AAC1C,aAAO,WAAW;AAClB,aAAO;AAAA,IACX;AAAA,EACJ;AACA,SAAO;AACX;AAEA,SAAS,gBAAgB,WAAW,gBAAgB,WAAW;AAC3D,MAAI,WAAW;AACX,WAAO,WAAW,SAAS;AAAA,EAC/B,WAAW,gBAAgB;AAEvB,WAAO;AAAA,EACX,OAAO;AACH,QAAI,KAAK,SAAS,WAAW,EAAE,GAC3B,IAAI,KAAK,KACT,KAAK,KAAK,KAAK;AACnB,WAAO,IAAI,KAAK;AAAA,EACpB;AACJ;AAGA,SAAS,kBAAkB,QAAQ;AAC/B,MAAI,QAAQ,QAAQ,KAAK,kBAAkB,OAAO,EAAE,CAAC,GACjD;AACJ,MAAI,OAAO;AACP,kBAAc;AAAA,MACV,MAAM,CAAC;AAAA,MACP,MAAM,CAAC;AAAA,MACP,MAAM,CAAC;AAAA,MACP,MAAM,CAAC;AAAA,MACP,MAAM,CAAC;AAAA,MACP,MAAM,CAAC;AAAA,IACX;AACA,QAAI,CAAC,aAAa,MAAM,CAAC,GAAG,aAAa,MAAM,GAAG;AAC9C;AAAA,IACJ;AAEA,WAAO,KAAK;AACZ,WAAO,OAAO,gBAAgB,MAAM,CAAC,GAAG,MAAM,CAAC,GAAG,MAAM,EAAE,CAAC;AAE3D,WAAO,KAAK,cAAc,MAAM,MAAM,OAAO,EAAE;AAC/C,WAAO,GAAG,cAAc,OAAO,GAAG,cAAc,IAAI,OAAO,IAAI;AAE/D,oBAAgB,MAAM,EAAE,UAAU;AAAA,EACtC,OAAO;AACH,WAAO,WAAW;AAAA,EACtB;AACJ;AAGA,SAAS,iBAAiB,QAAQ;AAC9B,MAAI,UAAU,gBAAgB,KAAK,OAAO,EAAE;AAC5C,MAAI,YAAY,MAAM;AAClB,WAAO,KAAK,oBAAI,KAAK,CAAC,QAAQ,CAAC,CAAC;AAChC;AAAA,EACJ;AAEA,gBAAc,MAAM;AACpB,MAAI,OAAO,aAAa,OAAO;AAC3B,WAAO,OAAO;AAAA,EAClB,OAAO;AACH;AAAA,EACJ;AAEA,oBAAkB,MAAM;AACxB,MAAI,OAAO,aAAa,OAAO;AAC3B,WAAO,OAAO;AAAA,EAClB,OAAO;AACH;AAAA,EACJ;AAEA,MAAI,OAAO,SAAS;AAChB,WAAO,WAAW;AAAA,EACtB,OAAO;AAEH,UAAM,wBAAwB,MAAM;AAAA,EACxC;AACJ;AAEA,MAAM,0BAA0B;AAAA,EAC5B;AAAA,EAGA,SAAU,QAAQ;AACd,WAAO,KAAK,oBAAI,KAAK,OAAO,MAAM,OAAO,UAAU,SAAS,GAAG;AAAA,EACnE;AACJ;AAGA,SAAS,SAAS,GAAG,GAAG,GAAG;AACvB,MAAI,KAAK,MAAM;AACX,WAAO;AAAA,EACX;AACA,MAAI,KAAK,MAAM;AACX,WAAO;AAAA,EACX;AACA,SAAO;AACX;AAEA,SAAS,iBAAiB,QAAQ;AAE9B,MAAI,WAAW,IAAI,KAAK,MAAM,IAAI,CAAC;AACnC,MAAI,OAAO,SAAS;AAChB,WAAO;AAAA,MACH,SAAS,eAAe;AAAA,MACxB,SAAS,YAAY;AAAA,MACrB,SAAS,WAAW;AAAA,IACxB;AAAA,EACJ;AACA,SAAO,CAAC,SAAS,YAAY,GAAG,SAAS,SAAS,GAAG,SAAS,QAAQ,CAAC;AAC3E;AAMA,SAAS,gBAAgB,QAAQ;AAC7B,MAAI,GACA,MACA,QAAQ,CAAC,GACT,aACA,iBACA;AAEJ,MAAI,OAAO,IAAI;AACX;AAAA,EACJ;AAEA,gBAAc,iBAAiB,MAAM;AAGrC,MAAI,OAAO,MAAM,OAAO,GAAG,IAAI,KAAK,QAAQ,OAAO,GAAG,KAAK,KAAK,MAAM;AAClE,0BAAsB,MAAM;AAAA,EAChC;AAGA,MAAI,OAAO,cAAc,MAAM;AAC3B,gBAAY,SAAS,OAAO,GAAG,IAAI,GAAG,YAAY,IAAI,CAAC;AAEvD,QACI,OAAO,aAAa,WAAW,SAAS,KACxC,OAAO,eAAe,GACxB;AACE,sBAAgB,MAAM,EAAE,qBAAqB;AAAA,IACjD;AAEA,WAAO,cAAc,WAAW,GAAG,OAAO,UAAU;AACpD,WAAO,GAAG,KAAK,IAAI,KAAK,YAAY;AACpC,WAAO,GAAG,IAAI,IAAI,KAAK,WAAW;AAAA,EACtC;AAOA,OAAK,IAAI,GAAG,IAAI,KAAK,OAAO,GAAG,CAAC,KAAK,MAAM,EAAE,GAAG;AAC5C,WAAO,GAAG,CAAC,IAAI,MAAM,CAAC,IAAI,YAAY,CAAC;AAAA,EAC3C;AAGA,SAAO,IAAI,GAAG,KAAK;AACf,WAAO,GAAG,CAAC,IAAI,MAAM,CAAC,IAClB,OAAO,GAAG,CAAC,KAAK,OAAQ,MAAM,IAAI,IAAI,IAAK,OAAO,GAAG,CAAC;AAAA,EAC9D;AAGA,MACI,OAAO,GAAG,IAAI,MAAM,MACpB,OAAO,GAAG,MAAM,MAAM,KACtB,OAAO,GAAG,MAAM,MAAM,KACtB,OAAO,GAAG,WAAW,MAAM,GAC7B;AACE,WAAO,WAAW;AAClB,WAAO,GAAG,IAAI,IAAI;AAAA,EACtB;AAEA,SAAO,MAAM,OAAO,UAAU,gBAAgB,YAAY;AAAA,IACtD;AAAA,IACA;AAAA,EACJ;AACA,oBAAkB,OAAO,UACnB,OAAO,GAAG,UAAU,IACpB,OAAO,GAAG,OAAO;AAIvB,MAAI,OAAO,QAAQ,MAAM;AACrB,WAAO,GAAG,cAAc,OAAO,GAAG,cAAc,IAAI,OAAO,IAAI;AAAA,EACnE;AAEA,MAAI,OAAO,UAAU;AACjB,WAAO,GAAG,IAAI,IAAI;AAAA,EACtB;AAGA,MACI,OAAO,MACP,OAAO,OAAO,GAAG,MAAM,eACvB,OAAO,GAAG,MAAM,iBAClB;AACE,oBAAgB,MAAM,EAAE,kBAAkB;AAAA,EAC9C;AACJ;AAEA,SAAS,sBAAsB,QAAQ;AACnC,MAAI,GAAG,UAAU,MAAM,SAAS,KAAK,KAAK,MAAM,iBAAiB;AAEjE,MAAI,OAAO;AACX,MAAI,EAAE,MAAM,QAAQ,EAAE,KAAK,QAAQ,EAAE,KAAK,MAAM;AAC5C,UAAM;AACN,UAAM;AAMN,eAAW;AAAA,MACP,EAAE;AAAA,MACF,OAAO,GAAG,IAAI;AAAA,MACd,WAAW,YAAY,GAAG,GAAG,CAAC,EAAE;AAAA,IACpC;AACA,WAAO,SAAS,EAAE,GAAG,CAAC;AACtB,cAAU,SAAS,EAAE,GAAG,CAAC;AACzB,QAAI,UAAU,KAAK,UAAU,GAAG;AAC5B,wBAAkB;AAAA,IACtB;AAAA,EACJ,OAAO;AACH,UAAM,OAAO,QAAQ,MAAM;AAC3B,UAAM,OAAO,QAAQ,MAAM;AAE3B,cAAU,WAAW,YAAY,GAAG,KAAK,GAAG;AAE5C,eAAW,SAAS,EAAE,IAAI,OAAO,GAAG,IAAI,GAAG,QAAQ,IAAI;AAGvD,WAAO,SAAS,EAAE,GAAG,QAAQ,IAAI;AAEjC,QAAI,EAAE,KAAK,MAAM;AAEb,gBAAU,EAAE;AACZ,UAAI,UAAU,KAAK,UAAU,GAAG;AAC5B,0BAAkB;AAAA,MACtB;AAAA,IACJ,WAAW,EAAE,KAAK,MAAM;AAEpB,gBAAU,EAAE,IAAI;AAChB,UAAI,EAAE,IAAI,KAAK,EAAE,IAAI,GAAG;AACpB,0BAAkB;AAAA,MACtB;AAAA,IACJ,OAAO;AAEH,gBAAU;AAAA,IACd;AAAA,EACJ;AACA,MAAI,OAAO,KAAK,OAAO,YAAY,UAAU,KAAK,GAAG,GAAG;AACpD,oBAAgB,MAAM,EAAE,iBAAiB;AAAA,EAC7C,WAAW,mBAAmB,MAAM;AAChC,oBAAgB,MAAM,EAAE,mBAAmB;AAAA,EAC/C,OAAO;AACH,WAAO,mBAAmB,UAAU,MAAM,SAAS,KAAK,GAAG;AAC3D,WAAO,GAAG,IAAI,IAAI,KAAK;AACvB,WAAO,aAAa,KAAK;AAAA,EAC7B;AACJ;AAGA,MAAM,WAAW,WAAY;AAAC;AAG9B,MAAM,WAAW,WAAY;AAAC;AAG9B,SAAS,0BAA0B,QAAQ;AAEvC,MAAI,OAAO,OAAO,MAAM,UAAU;AAC9B,kBAAc,MAAM;AACpB;AAAA,EACJ;AACA,MAAI,OAAO,OAAO,MAAM,UAAU;AAC9B,sBAAkB,MAAM;AACxB;AAAA,EACJ;AACA,SAAO,KAAK,CAAC;AACb,kBAAgB,MAAM,EAAE,QAAQ;AAGhC,MAAI,SAAS,KAAK,OAAO,IACrB,GACA,aACAY,SACAP,QACA,SACA,eAAe,OAAO,QACtB,yBAAyB,GACzB,KACA;AAEJ,EAAAO,UACI,aAAa,OAAO,IAAI,OAAO,OAAO,EAAE,MAAM,gBAAgB,KAAK,CAAC;AACxE,aAAWA,QAAO;AAClB,OAAK,IAAI,GAAG,IAAI,UAAU,KAAK;AAC3B,IAAAP,SAAQO,QAAO,CAAC;AAChB,mBAAe,OAAO,MAAM,sBAAsBP,QAAO,MAAM,CAAC,KAC5D,CAAC,GAAG,CAAC;AACT,QAAI,aAAa;AACb,gBAAU,OAAO,OAAO,GAAG,OAAO,QAAQ,WAAW,CAAC;AACtD,UAAI,QAAQ,SAAS,GAAG;AACpB,wBAAgB,MAAM,EAAE,YAAY,KAAK,OAAO;AAAA,MACpD;AACA,eAAS,OAAO;AAAA,QACZ,OAAO,QAAQ,WAAW,IAAI,YAAY;AAAA,MAC9C;AACA,gCAA0B,YAAY;AAAA,IAC1C;AAEA,QAAI,qBAAqBA,MAAK,GAAG;AAC7B,UAAI,aAAa;AACb,wBAAgB,MAAM,EAAE,QAAQ;AAAA,MACpC,OAAO;AACH,wBAAgB,MAAM,EAAE,aAAa,KAAKA,MAAK;AAAA,MACnD;AACA,8BAAwBA,QAAO,aAAa,MAAM;AAAA,IACtD,WAAW,OAAO,WAAW,CAAC,aAAa;AACvC,sBAAgB,MAAM,EAAE,aAAa,KAAKA,MAAK;AAAA,IACnD;AAAA,EACJ;AAGA,kBAAgB,MAAM,EAAE,gBACpB,eAAe;AACnB,MAAI,OAAO,SAAS,GAAG;AACnB,oBAAgB,MAAM,EAAE,YAAY,KAAK,MAAM;AAAA,EACnD;AAGA,MACI,OAAO,GAAG,IAAI,KAAK,MACnB,gBAAgB,MAAM,EAAE,YAAY,QACpC,OAAO,GAAG,IAAI,IAAI,GACpB;AACE,oBAAgB,MAAM,EAAE,UAAU;AAAA,EACtC;AAEA,kBAAgB,MAAM,EAAE,kBAAkB,OAAO,GAAG,MAAM,CAAC;AAC3D,kBAAgB,MAAM,EAAE,WAAW,OAAO;AAE1C,SAAO,GAAG,IAAI,IAAI;AAAA,IACd,OAAO;AAAA,IACP,OAAO,GAAG,IAAI;AAAA,IACd,OAAO;AAAA,EACX;AAGA,QAAM,gBAAgB,MAAM,EAAE;AAC9B,MAAI,QAAQ,MAAM;AACd,WAAO,GAAG,IAAI,IAAI,OAAO,QAAQ,gBAAgB,KAAK,OAAO,GAAG,IAAI,CAAC;AAAA,EACzE;AAEA,kBAAgB,MAAM;AACtB,gBAAc,MAAM;AACxB;AAEA,SAAS,gBAAgBL,SAAQ,MAAMa,WAAU;AAC7C,MAAI;AAEJ,MAAIA,aAAY,MAAM;AAElB,WAAO;AAAA,EACX;AACA,MAAIb,QAAO,gBAAgB,MAAM;AAC7B,WAAOA,QAAO,aAAa,MAAMa,SAAQ;AAAA,EAC7C,WAAWb,QAAO,QAAQ,MAAM;AAE5B,WAAOA,QAAO,KAAKa,SAAQ;AAC3B,QAAI,QAAQ,OAAO,IAAI;AACnB,cAAQ;AAAA,IACZ;AACA,QAAI,CAAC,QAAQ,SAAS,IAAI;AACtB,aAAO;AAAA,IACX;AACA,WAAO;AAAA,EACX,OAAO;AAEH,WAAO;AAAA,EACX;AACJ;AAGA,SAAS,yBAAyB,QAAQ;AACtC,MAAI,YACA,YACA,aACA,GACA,cACA,kBACA,oBAAoB,OACpB,aAAa,OAAO,GAAG;AAE3B,MAAI,eAAe,GAAG;AAClB,oBAAgB,MAAM,EAAE,gBAAgB;AACxC,WAAO,KAAK,oBAAI,KAAK,GAAG;AACxB;AAAA,EACJ;AAEA,OAAK,IAAI,GAAG,IAAI,YAAY,KAAK;AAC7B,mBAAe;AACf,uBAAmB;AACnB,iBAAa,WAAW,CAAC,GAAG,MAAM;AAClC,QAAI,OAAO,WAAW,MAAM;AACxB,iBAAW,UAAU,OAAO;AAAA,IAChC;AACA,eAAW,KAAK,OAAO,GAAG,CAAC;AAC3B,8BAA0B,UAAU;AAEpC,QAAI,QAAQ,UAAU,GAAG;AACrB,yBAAmB;AAAA,IACvB;AAGA,oBAAgB,gBAAgB,UAAU,EAAE;AAG5C,oBAAgB,gBAAgB,UAAU,EAAE,aAAa,SAAS;AAElE,oBAAgB,UAAU,EAAE,QAAQ;AAEpC,QAAI,CAAC,mBAAmB;AACpB,UACI,eAAe,QACf,eAAe,eACf,kBACF;AACE,sBAAc;AACd,qBAAa;AACb,YAAI,kBAAkB;AAClB,8BAAoB;AAAA,QACxB;AAAA,MACJ;AAAA,IACJ,OAAO;AACH,UAAI,eAAe,aAAa;AAC5B,sBAAc;AACd,qBAAa;AAAA,MACjB;AAAA,IACJ;AAAA,EACJ;AAEA,SAAO,QAAQ,cAAc,UAAU;AAC3C;AAEA,SAAS,iBAAiB,QAAQ;AAC9B,MAAI,OAAO,IAAI;AACX;AAAA,EACJ;AAEA,MAAI,IAAI,qBAAqB,OAAO,EAAE,GAClC,YAAY,EAAE,QAAQ,SAAY,EAAE,OAAO,EAAE;AACjD,SAAO,KAAK;AAAA,IACR,CAAC,EAAE,MAAM,EAAE,OAAO,WAAW,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,WAAW;AAAA,IACtE,SAAU,KAAK;AACX,aAAO,OAAO,SAAS,KAAK,EAAE;AAAA,IAClC;AAAA,EACJ;AAEA,kBAAgB,MAAM;AAC1B;AAEA,SAAS,iBAAiB,QAAQ;AAC9B,MAAI,MAAM,IAAI,OAAO,cAAc,cAAc,MAAM,CAAC,CAAC;AACzD,MAAI,IAAI,UAAU;AAEd,QAAI,IAAI,GAAG,GAAG;AACd,QAAI,WAAW;AAAA,EACnB;AAEA,SAAO;AACX;AAEA,SAAS,cAAc,QAAQ;AAC3B,MAAI,QAAQ,OAAO,IACfd,UAAS,OAAO;AAEpB,SAAO,UAAU,OAAO,WAAW,UAAU,OAAO,EAAE;AAEtD,MAAI,UAAU,QAASA,YAAW,UAAa,UAAU,IAAK;AAC1D,WAAO,cAAc,EAAE,WAAW,KAAK,CAAC;AAAA,EAC5C;AAEA,MAAI,OAAO,UAAU,UAAU;AAC3B,WAAO,KAAK,QAAQ,OAAO,QAAQ,SAAS,KAAK;AAAA,EACrD;AAEA,MAAI,SAAS,KAAK,GAAG;AACjB,WAAO,IAAI,OAAO,cAAc,KAAK,CAAC;AAAA,EAC1C,WAAW,OAAO,KAAK,GAAG;AACtB,WAAO,KAAK;AAAA,EAChB,WAAW,QAAQA,OAAM,GAAG;AACxB,6BAAyB,MAAM;AAAA,EACnC,WAAWA,SAAQ;AACf,8BAA0B,MAAM;AAAA,EACpC,OAAO;AACH,oBAAgB,MAAM;AAAA,EAC1B;AAEA,MAAI,CAAC,QAAQ,MAAM,GAAG;AAClB,WAAO,KAAK;AAAA,EAChB;AAEA,SAAO;AACX;AAEA,SAAS,gBAAgB,QAAQ;AAC7B,MAAI,QAAQ,OAAO;AACnB,MAAI,YAAY,KAAK,GAAG;AACpB,WAAO,KAAK,IAAI,KAAK,MAAM,IAAI,CAAC;AAAA,EACpC,WAAW,OAAO,KAAK,GAAG;AACtB,WAAO,KAAK,IAAI,KAAK,MAAM,QAAQ,CAAC;AAAA,EACxC,WAAW,OAAO,UAAU,UAAU;AAClC,qBAAiB,MAAM;AAAA,EAC3B,WAAW,QAAQ,KAAK,GAAG;AACvB,WAAO,KAAK,IAAI,MAAM,MAAM,CAAC,GAAG,SAAU,KAAK;AAC3C,aAAO,SAAS,KAAK,EAAE;AAAA,IAC3B,CAAC;AACD,oBAAgB,MAAM;AAAA,EAC1B,WAAW,SAAS,KAAK,GAAG;AACxB,qBAAiB,MAAM;AAAA,EAC3B,WAAW,SAAS,KAAK,GAAG;AAExB,WAAO,KAAK,IAAI,KAAK,KAAK;AAAA,EAC9B,OAAO;AACH,UAAM,wBAAwB,MAAM;AAAA,EACxC;AACJ;AAEA,SAAS,iBAAiB,OAAOA,SAAQC,SAAQ,QAAQ,OAAO;AAC5D,MAAI,IAAI,CAAC;AAET,MAAID,YAAW,QAAQA,YAAW,OAAO;AACrC,aAASA;AACT,IAAAA,UAAS;AAAA,EACb;AAEA,MAAIC,YAAW,QAAQA,YAAW,OAAO;AACrC,aAASA;AACT,IAAAA,UAAS;AAAA,EACb;AAEA,MACK,SAAS,KAAK,KAAK,cAAc,KAAK,KACtC,QAAQ,KAAK,KAAK,MAAM,WAAW,GACtC;AACE,YAAQ;AAAA,EACZ;AAGA,IAAE,mBAAmB;AACrB,IAAE,UAAU,EAAE,SAAS;AACvB,IAAE,KAAKA;AACP,IAAE,KAAK;AACP,IAAE,KAAKD;AACP,IAAE,UAAU;AAEZ,SAAO,iBAAiB,CAAC;AAC7B;AAEA,SAAS,YAAY,OAAOA,SAAQC,SAAQ,QAAQ;AAChD,SAAO,iBAAiB,OAAOD,SAAQC,SAAQ,QAAQ,KAAK;AAChE;AAEA,IAAI,eAAe;AAAA,EACX;AAAA,EACA,WAAY;AACR,QAAI,QAAQ,YAAY,MAAM,MAAM,SAAS;AAC7C,QAAI,KAAK,QAAQ,KAAK,MAAM,QAAQ,GAAG;AACnC,aAAO,QAAQ,OAAO,OAAO;AAAA,IACjC,OAAO;AACH,aAAO,cAAc;AAAA,IACzB;AAAA,EACJ;AACJ;AAVJ,IAWI,eAAe;AAAA,EACX;AAAA,EACA,WAAY;AACR,QAAI,QAAQ,YAAY,MAAM,MAAM,SAAS;AAC7C,QAAI,KAAK,QAAQ,KAAK,MAAM,QAAQ,GAAG;AACnC,aAAO,QAAQ,OAAO,OAAO;AAAA,IACjC,OAAO;AACH,aAAO,cAAc;AAAA,IACzB;AAAA,EACJ;AACJ;AAOJ,SAAS,OAAO,IAAI,SAAS;AACzB,MAAI,KAAK;AACT,MAAI,QAAQ,WAAW,KAAK,QAAQ,QAAQ,CAAC,CAAC,GAAG;AAC7C,cAAU,QAAQ,CAAC;AAAA,EACvB;AACA,MAAI,CAAC,QAAQ,QAAQ;AACjB,WAAO,YAAY;AAAA,EACvB;AACA,QAAM,QAAQ,CAAC;AACf,OAAK,IAAI,GAAG,IAAI,QAAQ,QAAQ,EAAE,GAAG;AACjC,QAAI,CAAC,QAAQ,CAAC,EAAE,QAAQ,KAAK,QAAQ,CAAC,EAAE,EAAE,EAAE,GAAG,GAAG;AAC9C,YAAM,QAAQ,CAAC;AAAA,IACnB;AAAA,EACJ;AACA,SAAO;AACX;AAGA,SAAS,MAAM;AACX,MAAI,OAAO,CAAC,EAAE,MAAM,KAAK,WAAW,CAAC;AAErC,SAAO,OAAO,YAAY,IAAI;AAClC;AAEA,SAAS,MAAM;AACX,MAAI,OAAO,CAAC,EAAE,MAAM,KAAK,WAAW,CAAC;AAErC,SAAO,OAAO,WAAW,IAAI;AACjC;AAEA,IAAI,MAAM,WAAY;AAClB,SAAO,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,oBAAI,KAAK;AAC7C;AAEA,IAAI,WAAW;AAAA,EACX;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ;AAEA,SAAS,gBAAgB,GAAG;AACxB,MAAI,KACA,iBAAiB,OACjB,GACA,WAAW,SAAS;AACxB,OAAK,OAAO,GAAG;AACX,QACI,WAAW,GAAG,GAAG,KACjB,EACI,QAAQ,KAAK,UAAU,GAAG,MAAM,OAC/B,EAAE,GAAG,KAAK,QAAQ,CAAC,MAAM,EAAE,GAAG,CAAC,KAEtC;AACE,aAAO;AAAA,IACX;AAAA,EACJ;AAEA,OAAK,IAAI,GAAG,IAAI,UAAU,EAAE,GAAG;AAC3B,QAAI,EAAE,SAAS,CAAC,CAAC,GAAG;AAChB,UAAI,gBAAgB;AAChB,eAAO;AAAA,MACX;AACA,UAAI,WAAW,EAAE,SAAS,CAAC,CAAC,CAAC,MAAM,MAAM,EAAE,SAAS,CAAC,CAAC,CAAC,GAAG;AACtD,yBAAiB;AAAA,MACrB;AAAA,IACJ;AAAA,EACJ;AAEA,SAAO;AACX;AAEA,SAAS,YAAY;AACjB,SAAO,KAAK;AAChB;AAEA,SAAS,kBAAkB;AACvB,SAAO,eAAe,GAAG;AAC7B;AAEA,SAAS,SAAS,UAAU;AACxB,MAAI,kBAAkB,qBAAqB,QAAQ,GAC/Cc,SAAQ,gBAAgB,QAAQ,GAChC,WAAW,gBAAgB,WAAW,GACtCC,UAAS,gBAAgB,SAAS,GAClCC,SAAQ,gBAAgB,QAAQ,gBAAgB,WAAW,GAC3DC,QAAO,gBAAgB,OAAO,GAC9BP,SAAQ,gBAAgB,QAAQ,GAChCC,WAAU,gBAAgB,UAAU,GACpCO,WAAU,gBAAgB,UAAU,GACpCC,gBAAe,gBAAgB,eAAe;AAElD,OAAK,WAAW,gBAAgB,eAAe;AAG/C,OAAK,gBACD,CAACA,gBACDD,WAAU;AAAA,EACVP,WAAU;AAAA,EACVD,SAAQ,MAAO,KAAK;AAGxB,OAAK,QAAQ,CAACO,QAAOD,SAAQ;AAI7B,OAAK,UAAU,CAACD,UAAS,WAAW,IAAID,SAAQ;AAEhD,OAAK,QAAQ,CAAC;AAEd,OAAK,UAAU,UAAU;AAEzB,OAAK,QAAQ;AACjB;AAEA,SAAS,WAAW,KAAK;AACrB,SAAO,eAAe;AAC1B;AAEA,SAAS,SAAS,QAAQ;AACtB,MAAI,SAAS,GAAG;AACZ,WAAO,KAAK,MAAM,KAAK,MAAM,IAAI;AAAA,EACrC,OAAO;AACH,WAAO,KAAK,MAAM,MAAM;AAAA,EAC5B;AACJ;AAGA,SAAS,cAAc,QAAQ,QAAQ,aAAa;AAChD,MAAI,MAAM,KAAK,IAAI,OAAO,QAAQ,OAAO,MAAM,GAC3C,aAAa,KAAK,IAAI,OAAO,SAAS,OAAO,MAAM,GACnD,QAAQ,GACR;AACJ,OAAK,IAAI,GAAG,IAAI,KAAK,KAAK;AACtB,QACK,eAAe,OAAO,CAAC,MAAM,OAAO,CAAC,KACrC,CAAC,eAAe,MAAM,OAAO,CAAC,CAAC,MAAM,MAAM,OAAO,CAAC,CAAC,GACvD;AACE;AAAA,IACJ;AAAA,EACJ;AACA,SAAO,QAAQ;AACnB;AAIA,SAAS,OAAOT,QAAO,WAAW;AAC9B,iBAAeA,QAAO,GAAG,GAAG,WAAY;AACpC,QAAIe,UAAS,KAAK,UAAU,GACxBhB,QAAO;AACX,QAAIgB,UAAS,GAAG;AACZ,MAAAA,UAAS,CAACA;AACV,MAAAhB,QAAO;AAAA,IACX;AACA,WACIA,QACA,SAAS,CAAC,EAAEgB,UAAS,KAAK,CAAC,IAC3B,YACA,SAAS,CAAC,CAACA,UAAS,IAAI,CAAC;AAAA,EAEjC,CAAC;AACL;AAEA,OAAO,KAAK,GAAG;AACf,OAAO,MAAM,EAAE;AAIf,cAAc,KAAK,gBAAgB;AACnC,cAAc,MAAM,gBAAgB;AACpC,cAAc,CAAC,KAAK,IAAI,GAAG,SAAU,OAAO,OAAO,QAAQ;AACvD,SAAO,UAAU;AACjB,SAAO,OAAO,iBAAiB,kBAAkB,KAAK;AAC1D,CAAC;AAOD,IAAI,cAAc;AAElB,SAAS,iBAAiB,SAAS,QAAQ;AACvC,MAAI,WAAW,UAAU,IAAI,MAAM,OAAO,GACtC,OACA,OACAT;AAEJ,MAAI,YAAY,MAAM;AAClB,WAAO;AAAA,EACX;AAEA,UAAQ,QAAQ,QAAQ,SAAS,CAAC,KAAK,CAAC;AACxC,WAAS,QAAQ,IAAI,MAAM,WAAW,KAAK,CAAC,KAAK,GAAG,CAAC;AACrD,EAAAA,WAAU,EAAE,MAAM,CAAC,IAAI,MAAM,MAAM,MAAM,CAAC,CAAC;AAE3C,SAAOA,aAAY,IAAI,IAAI,MAAM,CAAC,MAAM,MAAMA,WAAU,CAACA;AAC7D;AAGA,SAAS,gBAAgB,OAAO,OAAO;AACnC,MAAI,KAAKH;AACT,MAAI,MAAM,QAAQ;AACd,UAAM,MAAM,MAAM;AAClB,IAAAA,SACK,SAAS,KAAK,KAAK,OAAO,KAAK,IAC1B,MAAM,QAAQ,IACd,YAAY,KAAK,EAAE,QAAQ,KAAK,IAAI,QAAQ;AAEtD,QAAI,GAAG,QAAQ,IAAI,GAAG,QAAQ,IAAIA,KAAI;AACtC,UAAM,aAAa,KAAK,KAAK;AAC7B,WAAO;AAAA,EACX,OAAO;AACH,WAAO,YAAY,KAAK,EAAE,MAAM;AAAA,EACpC;AACJ;AAEA,SAAS,cAAc,GAAG;AAGtB,SAAO,CAAC,KAAK,MAAM,EAAE,GAAG,kBAAkB,CAAC;AAC/C;AAMA,MAAM,eAAe,WAAY;AAAC;AAclC,SAAS,aAAa,OAAO,eAAe,aAAa;AACrD,MAAIY,UAAS,KAAK,WAAW,GACzB;AACJ,MAAI,CAAC,KAAK,QAAQ,GAAG;AACjB,WAAO,SAAS,OAAO,OAAO;AAAA,EAClC;AACA,MAAI,SAAS,MAAM;AACf,QAAI,OAAO,UAAU,UAAU;AAC3B,cAAQ,iBAAiB,kBAAkB,KAAK;AAChD,UAAI,UAAU,MAAM;AAChB,eAAO;AAAA,MACX;AAAA,IACJ,WAAW,KAAK,IAAI,KAAK,IAAI,MAAM,CAAC,aAAa;AAC7C,cAAQ,QAAQ;AAAA,IACpB;AACA,QAAI,CAAC,KAAK,UAAU,eAAe;AAC/B,oBAAc,cAAc,IAAI;AAAA,IACpC;AACA,SAAK,UAAU;AACf,SAAK,SAAS;AACd,QAAI,eAAe,MAAM;AACrB,WAAK,IAAI,aAAa,GAAG;AAAA,IAC7B;AACA,QAAIA,YAAW,OAAO;AAClB,UAAI,CAAC,iBAAiB,KAAK,mBAAmB;AAC1C;AAAA,UACI;AAAA,UACA,eAAe,QAAQA,SAAQ,GAAG;AAAA,UAClC;AAAA,UACA;AAAA,QACJ;AAAA,MACJ,WAAW,CAAC,KAAK,mBAAmB;AAChC,aAAK,oBAAoB;AACzB,cAAM,aAAa,MAAM,IAAI;AAC7B,aAAK,oBAAoB;AAAA,MAC7B;AAAA,IACJ;AACA,WAAO;AAAA,EACX,OAAO;AACH,WAAO,KAAK,SAASA,UAAS,cAAc,IAAI;AAAA,EACpD;AACJ;AAEA,SAAS,WAAW,OAAO,eAAe;AACtC,MAAI,SAAS,MAAM;AACf,QAAI,OAAO,UAAU,UAAU;AAC3B,cAAQ,CAAC;AAAA,IACb;AAEA,SAAK,UAAU,OAAO,aAAa;AAEnC,WAAO;AAAA,EACX,OAAO;AACH,WAAO,CAAC,KAAK,UAAU;AAAA,EAC3B;AACJ;AAEA,SAAS,eAAe,eAAe;AACnC,SAAO,KAAK,UAAU,GAAG,aAAa;AAC1C;AAEA,SAAS,iBAAiB,eAAe;AACrC,MAAI,KAAK,QAAQ;AACb,SAAK,UAAU,GAAG,aAAa;AAC/B,SAAK,SAAS;AAEd,QAAI,eAAe;AACf,WAAK,SAAS,cAAc,IAAI,GAAG,GAAG;AAAA,IAC1C;AAAA,EACJ;AACA,SAAO;AACX;AAEA,SAAS,0BAA0B;AAC/B,MAAI,KAAK,QAAQ,MAAM;AACnB,SAAK,UAAU,KAAK,MAAM,OAAO,IAAI;AAAA,EACzC,WAAW,OAAO,KAAK,OAAO,UAAU;AACpC,QAAI,QAAQ,iBAAiB,aAAa,KAAK,EAAE;AACjD,QAAI,SAAS,MAAM;AACf,WAAK,UAAU,KAAK;AAAA,IACxB,OAAO;AACH,WAAK,UAAU,GAAG,IAAI;AAAA,IAC1B;AAAA,EACJ;AACA,SAAO;AACX;AAEA,SAAS,qBAAqB,OAAO;AACjC,MAAI,CAAC,KAAK,QAAQ,GAAG;AACjB,WAAO;AAAA,EACX;AACA,UAAQ,QAAQ,YAAY,KAAK,EAAE,UAAU,IAAI;AAEjD,UAAQ,KAAK,UAAU,IAAI,SAAS,OAAO;AAC/C;AAEA,SAAS,uBAAuB;AAC5B,SACI,KAAK,UAAU,IAAI,KAAK,MAAM,EAAE,MAAM,CAAC,EAAE,UAAU,KACnD,KAAK,UAAU,IAAI,KAAK,MAAM,EAAE,MAAM,CAAC,EAAE,UAAU;AAE3D;AAEA,SAAS,8BAA8B;AACnC,MAAI,CAAC,YAAY,KAAK,aAAa,GAAG;AAClC,WAAO,KAAK;AAAA,EAChB;AAEA,MAAI,IAAI,CAAC,GACL;AAEJ,aAAW,GAAG,IAAI;AAClB,MAAI,cAAc,CAAC;AAEnB,MAAI,EAAE,IAAI;AACN,YAAQ,EAAE,SAAS,UAAU,EAAE,EAAE,IAAI,YAAY,EAAE,EAAE;AACrD,SAAK,gBACD,KAAK,QAAQ,KAAK,cAAc,EAAE,IAAI,MAAM,QAAQ,CAAC,IAAI;AAAA,EACjE,OAAO;AACH,SAAK,gBAAgB;AAAA,EACzB;AAEA,SAAO,KAAK;AAChB;AAEA,SAAS,UAAU;AACf,SAAO,KAAK,QAAQ,IAAI,CAAC,KAAK,SAAS;AAC3C;AAEA,SAAS,cAAc;AACnB,SAAO,KAAK,QAAQ,IAAI,KAAK,SAAS;AAC1C;AAEA,SAAS,QAAQ;AACb,SAAO,KAAK,QAAQ,IAAI,KAAK,UAAU,KAAK,YAAY,IAAI;AAChE;AAGA,IAAI,cAAc;AAAlB,IAII,WACI;AAER,SAAS,eAAe,OAAO,KAAK;AAChC,MAAI,WAAW,OAEX,QAAQ,MACRhB,OACA,KACA;AAEJ,MAAI,WAAW,KAAK,GAAG;AACnB,eAAW;AAAA,MACP,IAAI,MAAM;AAAA,MACV,GAAG,MAAM;AAAA,MACT,GAAG,MAAM;AAAA,IACb;AAAA,EACJ,WAAW,SAAS,KAAK,KAAK,CAAC,MAAM,CAAC,KAAK,GAAG;AAC1C,eAAW,CAAC;AACZ,QAAI,KAAK;AACL,eAAS,GAAG,IAAI,CAAC;AAAA,IACrB,OAAO;AACH,eAAS,eAAe,CAAC;AAAA,IAC7B;AAAA,EACJ,WAAY,QAAQ,YAAY,KAAK,KAAK,GAAI;AAC1C,IAAAA,QAAO,MAAM,CAAC,MAAM,MAAM,KAAK;AAC/B,eAAW;AAAA,MACP,GAAG;AAAA,MACH,GAAG,MAAM,MAAM,IAAI,CAAC,IAAIA;AAAA,MACxB,GAAG,MAAM,MAAM,IAAI,CAAC,IAAIA;AAAA,MACxB,GAAG,MAAM,MAAM,MAAM,CAAC,IAAIA;AAAA,MAC1B,GAAG,MAAM,MAAM,MAAM,CAAC,IAAIA;AAAA,MAC1B,IAAI,MAAM,SAAS,MAAM,WAAW,IAAI,GAAI,CAAC,IAAIA;AAAA;AAAA,IACrD;AAAA,EACJ,WAAY,QAAQ,SAAS,KAAK,KAAK,GAAI;AACvC,IAAAA,QAAO,MAAM,CAAC,MAAM,MAAM,KAAK;AAC/B,eAAW;AAAA,MACP,GAAG,SAAS,MAAM,CAAC,GAAGA,KAAI;AAAA,MAC1B,GAAG,SAAS,MAAM,CAAC,GAAGA,KAAI;AAAA,MAC1B,GAAG,SAAS,MAAM,CAAC,GAAGA,KAAI;AAAA,MAC1B,GAAG,SAAS,MAAM,CAAC,GAAGA,KAAI;AAAA,MAC1B,GAAG,SAAS,MAAM,CAAC,GAAGA,KAAI;AAAA,MAC1B,GAAG,SAAS,MAAM,CAAC,GAAGA,KAAI;AAAA,MAC1B,GAAG,SAAS,MAAM,CAAC,GAAGA,KAAI;AAAA,IAC9B;AAAA,EACJ,WAAW,YAAY,MAAM;AAEzB,eAAW,CAAC;AAAA,EAChB,WACI,OAAO,aAAa,aACnB,UAAU,YAAY,QAAQ,WACjC;AACE,cAAU;AAAA,MACN,YAAY,SAAS,IAAI;AAAA,MACzB,YAAY,SAAS,EAAE;AAAA,IAC3B;AAEA,eAAW,CAAC;AACZ,aAAS,KAAK,QAAQ;AACtB,aAAS,IAAI,QAAQ;AAAA,EACzB;AAEA,QAAM,IAAI,SAAS,QAAQ;AAE3B,MAAI,WAAW,KAAK,KAAK,WAAW,OAAO,SAAS,GAAG;AACnD,QAAI,UAAU,MAAM;AAAA,EACxB;AAEA,MAAI,WAAW,KAAK,KAAK,WAAW,OAAO,UAAU,GAAG;AACpD,QAAI,WAAW,MAAM;AAAA,EACzB;AAEA,SAAO;AACX;AAEA,eAAe,KAAK,SAAS;AAC7B,eAAe,UAAU;AAEzB,SAAS,SAAS,KAAKA,OAAM;AAIzB,MAAI,MAAM,OAAO,WAAW,IAAI,QAAQ,KAAK,GAAG,CAAC;AAEjD,UAAQ,MAAM,GAAG,IAAI,IAAI,OAAOA;AACpC;AAEA,SAAS,0BAA0B,MAAM,OAAO;AAC5C,MAAI,MAAM,CAAC;AAEX,MAAI,SACA,MAAM,MAAM,IAAI,KAAK,MAAM,KAAK,MAAM,KAAK,IAAI,KAAK,KAAK,KAAK;AAClE,MAAI,KAAK,MAAM,EAAE,IAAI,IAAI,QAAQ,GAAG,EAAE,QAAQ,KAAK,GAAG;AAClD,MAAE,IAAI;AAAA,EACV;AAEA,MAAI,eAAe,CAAC,QAAQ,CAAC,KAAK,MAAM,EAAE,IAAI,IAAI,QAAQ,GAAG;AAE7D,SAAO;AACX;AAEA,SAAS,kBAAkB,MAAM,OAAO;AACpC,MAAI;AACJ,MAAI,EAAE,KAAK,QAAQ,KAAK,MAAM,QAAQ,IAAI;AACtC,WAAO,EAAE,cAAc,GAAG,QAAQ,EAAE;AAAA,EACxC;AAEA,UAAQ,gBAAgB,OAAO,IAAI;AACnC,MAAI,KAAK,SAAS,KAAK,GAAG;AACtB,UAAM,0BAA0B,MAAM,KAAK;AAAA,EAC/C,OAAO;AACH,UAAM,0BAA0B,OAAO,IAAI;AAC3C,QAAI,eAAe,CAAC,IAAI;AACxB,QAAI,SAAS,CAAC,IAAI;AAAA,EACtB;AAEA,SAAO;AACX;AAGA,SAAS,YAAY,WAAW,MAAM;AAClC,SAAO,SAAU,KAAK,QAAQ;AAC1B,QAAI,KAAK;AAET,QAAI,WAAW,QAAQ,CAAC,MAAM,CAAC,MAAM,GAAG;AACpC;AAAA,QACI;AAAA,QACA,cACI,OACA,yDACA,OACA;AAAA,MAER;AACA,YAAM;AACN,YAAM;AACN,eAAS;AAAA,IACb;AAEA,UAAM,eAAe,KAAK,MAAM;AAChC,gBAAY,MAAM,KAAK,SAAS;AAChC,WAAO;AAAA,EACX;AACJ;AAEA,SAAS,YAAY,KAAK,UAAU,UAAU,cAAc;AACxD,MAAIe,gBAAe,SAAS,eACxBF,QAAO,SAAS,SAAS,KAAK,GAC9BF,UAAS,SAAS,SAAS,OAAO;AAEtC,MAAI,CAAC,IAAI,QAAQ,GAAG;AAEhB;AAAA,EACJ;AAEA,iBAAe,gBAAgB,OAAO,OAAO;AAE7C,MAAIA,SAAQ;AACR,aAAS,KAAK,IAAI,KAAK,OAAO,IAAIA,UAAS,QAAQ;AAAA,EACvD;AACA,MAAIE,OAAM;AACN,UAAM,KAAK,QAAQ,IAAI,KAAK,MAAM,IAAIA,QAAO,QAAQ;AAAA,EACzD;AACA,MAAIE,eAAc;AACd,QAAI,GAAG,QAAQ,IAAI,GAAG,QAAQ,IAAIA,gBAAe,QAAQ;AAAA,EAC7D;AACA,MAAI,cAAc;AACd,UAAM,aAAa,KAAKF,SAAQF,OAAM;AAAA,EAC1C;AACJ;AAEA,IAAI,MAAM,YAAY,GAAG,KAAK;AAA9B,IACI,WAAW,YAAY,IAAI,UAAU;AAEzC,SAAS,SAAS,OAAO;AACrB,SAAO,OAAO,UAAU,YAAY,iBAAiB;AACzD;AAGA,SAAS,cAAc,OAAO;AAC1B,SACI,SAAS,KAAK,KACd,OAAO,KAAK,KACZ,SAAS,KAAK,KACd,SAAS,KAAK,KACd,sBAAsB,KAAK,KAC3B,oBAAoB,KAAK,KACzB,UAAU,QACV,UAAU;AAElB;AAEA,SAAS,oBAAoB,OAAO;AAChC,MAAI,aAAa,SAAS,KAAK,KAAK,CAAC,cAAc,KAAK,GACpD,eAAe,OACf,aAAa;AAAA,IACT;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ,GACA,GACA,UACA,cAAc,WAAW;AAE7B,OAAK,IAAI,GAAG,IAAI,aAAa,KAAK,GAAG;AACjC,eAAW,WAAW,CAAC;AACvB,mBAAe,gBAAgB,WAAW,OAAO,QAAQ;AAAA,EAC7D;AAEA,SAAO,cAAc;AACzB;AAEA,SAAS,sBAAsB,OAAO;AAClC,MAAI,YAAY,QAAQ,KAAK,GACzB,eAAe;AACnB,MAAI,WAAW;AACX,mBACI,MAAM,OAAO,SAAU,MAAM;AACzB,aAAO,CAAC,SAAS,IAAI,KAAK,SAAS,KAAK;AAAA,IAC5C,CAAC,EAAE,WAAW;AAAA,EACtB;AACA,SAAO,aAAa;AACxB;AAEA,SAAS,eAAe,OAAO;AAC3B,MAAI,aAAa,SAAS,KAAK,KAAK,CAAC,cAAc,KAAK,GACpD,eAAe,OACf,aAAa;AAAA,IACT;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ,GACA,GACA;AAEJ,OAAK,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK,GAAG;AACvC,eAAW,WAAW,CAAC;AACvB,mBAAe,gBAAgB,WAAW,OAAO,QAAQ;AAAA,EAC7D;AAEA,SAAO,cAAc;AACzB;AAEA,SAAS,kBAAkB,UAAUZ,MAAK;AACtC,MAAIK,QAAO,SAAS,KAAKL,MAAK,QAAQ,IAAI;AAC1C,SAAOK,QAAO,KACR,aACAA,QAAO,KACL,aACAA,QAAO,IACL,YACAA,QAAO,IACL,YACAA,QAAO,IACL,YACAA,QAAO,IACL,aACA;AACpB;AAEA,SAAS,WAAW,MAAM,SAAS;AAE/B,MAAI,UAAU,WAAW,GAAG;AACxB,QAAI,CAAC,UAAU,CAAC,GAAG;AACf,aAAO;AACP,gBAAU;AAAA,IACd,WAAW,cAAc,UAAU,CAAC,CAAC,GAAG;AACpC,aAAO,UAAU,CAAC;AAClB,gBAAU;AAAA,IACd,WAAW,eAAe,UAAU,CAAC,CAAC,GAAG;AACrC,gBAAU,UAAU,CAAC;AACrB,aAAO;AAAA,IACX;AAAA,EACJ;AAGA,MAAIL,OAAM,QAAQ,YAAY,GAC1B,MAAM,gBAAgBA,MAAK,IAAI,EAAE,QAAQ,KAAK,GAC9CJ,UAAS,MAAM,eAAe,MAAM,GAAG,KAAK,YAC5C,SACI,YACC,WAAW,QAAQA,OAAM,CAAC,IACrB,QAAQA,OAAM,EAAE,KAAK,MAAMI,IAAG,IAC9B,QAAQJ,OAAM;AAE5B,SAAO,KAAK;AAAA,IACR,UAAU,KAAK,WAAW,EAAE,SAASA,SAAQ,MAAM,YAAYI,IAAG,CAAC;AAAA,EACvE;AACJ;AAEA,SAAS,QAAQ;AACb,SAAO,IAAI,OAAO,IAAI;AAC1B;AAEA,SAAS,QAAQ,OAAO,OAAO;AAC3B,MAAI,aAAa,SAAS,KAAK,IAAI,QAAQ,YAAY,KAAK;AAC5D,MAAI,EAAE,KAAK,QAAQ,KAAK,WAAW,QAAQ,IAAI;AAC3C,WAAO;AAAA,EACX;AACA,UAAQ,eAAe,KAAK,KAAK;AACjC,MAAI,UAAU,eAAe;AACzB,WAAO,KAAK,QAAQ,IAAI,WAAW,QAAQ;AAAA,EAC/C,OAAO;AACH,WAAO,WAAW,QAAQ,IAAI,KAAK,MAAM,EAAE,QAAQ,KAAK,EAAE,QAAQ;AAAA,EACtE;AACJ;AAEA,SAAS,SAAS,OAAO,OAAO;AAC5B,MAAI,aAAa,SAAS,KAAK,IAAI,QAAQ,YAAY,KAAK;AAC5D,MAAI,EAAE,KAAK,QAAQ,KAAK,WAAW,QAAQ,IAAI;AAC3C,WAAO;AAAA,EACX;AACA,UAAQ,eAAe,KAAK,KAAK;AACjC,MAAI,UAAU,eAAe;AACzB,WAAO,KAAK,QAAQ,IAAI,WAAW,QAAQ;AAAA,EAC/C,OAAO;AACH,WAAO,KAAK,MAAM,EAAE,MAAM,KAAK,EAAE,QAAQ,IAAI,WAAW,QAAQ;AAAA,EACpE;AACJ;AAEA,SAAS,UAAUD,OAAMD,KAAI,OAAO,aAAa;AAC7C,MAAI,YAAY,SAASC,KAAI,IAAIA,QAAO,YAAYA,KAAI,GACpD,UAAU,SAASD,GAAE,IAAIA,MAAK,YAAYA,GAAE;AAChD,MAAI,EAAE,KAAK,QAAQ,KAAK,UAAU,QAAQ,KAAK,QAAQ,QAAQ,IAAI;AAC/D,WAAO;AAAA,EACX;AACA,gBAAc,eAAe;AAC7B,UACK,YAAY,CAAC,MAAM,MACd,KAAK,QAAQ,WAAW,KAAK,IAC7B,CAAC,KAAK,SAAS,WAAW,KAAK,OACpC,YAAY,CAAC,MAAM,MACd,KAAK,SAAS,SAAS,KAAK,IAC5B,CAAC,KAAK,QAAQ,SAAS,KAAK;AAE1C;AAEA,SAAS,OAAO,OAAO,OAAO;AAC1B,MAAI,aAAa,SAAS,KAAK,IAAI,QAAQ,YAAY,KAAK,GACxD;AACJ,MAAI,EAAE,KAAK,QAAQ,KAAK,WAAW,QAAQ,IAAI;AAC3C,WAAO;AAAA,EACX;AACA,UAAQ,eAAe,KAAK,KAAK;AACjC,MAAI,UAAU,eAAe;AACzB,WAAO,KAAK,QAAQ,MAAM,WAAW,QAAQ;AAAA,EACjD,OAAO;AACH,cAAU,WAAW,QAAQ;AAC7B,WACI,KAAK,MAAM,EAAE,QAAQ,KAAK,EAAE,QAAQ,KAAK,WACzC,WAAW,KAAK,MAAM,EAAE,MAAM,KAAK,EAAE,QAAQ;AAAA,EAErD;AACJ;AAEA,SAAS,cAAc,OAAO,OAAO;AACjC,SAAO,KAAK,OAAO,OAAO,KAAK,KAAK,KAAK,QAAQ,OAAO,KAAK;AACjE;AAEA,SAAS,eAAe,OAAO,OAAO;AAClC,SAAO,KAAK,OAAO,OAAO,KAAK,KAAK,KAAK,SAAS,OAAO,KAAK;AAClE;AAEA,SAAS,KAAK,OAAO,OAAO,SAAS;AACjC,MAAI,MAAM,WAAW;AAErB,MAAI,CAAC,KAAK,QAAQ,GAAG;AACjB,WAAO;AAAA,EACX;AAEA,SAAO,gBAAgB,OAAO,IAAI;AAElC,MAAI,CAAC,KAAK,QAAQ,GAAG;AACjB,WAAO;AAAA,EACX;AAEA,eAAa,KAAK,UAAU,IAAI,KAAK,UAAU,KAAK;AAEpD,UAAQ,eAAe,KAAK;AAE5B,UAAQ,OAAO;AAAA,IACX,KAAK;AACD,eAAS,UAAU,MAAM,IAAI,IAAI;AACjC;AAAA,IACJ,KAAK;AACD,eAAS,UAAU,MAAM,IAAI;AAC7B;AAAA,IACJ,KAAK;AACD,eAAS,UAAU,MAAM,IAAI,IAAI;AACjC;AAAA,IACJ,KAAK;AACD,gBAAU,OAAO,QAAQ;AACzB;AAAA,IACJ,KAAK;AACD,gBAAU,OAAO,QAAQ;AACzB;AAAA,IACJ,KAAK;AACD,gBAAU,OAAO,QAAQ;AACzB;AAAA,IACJ,KAAK;AACD,gBAAU,OAAO,OAAO,aAAa;AACrC;AAAA,IACJ,KAAK;AACD,gBAAU,OAAO,OAAO,aAAa;AACrC;AAAA,IACJ;AACI,eAAS,OAAO;AAAA,EACxB;AAEA,SAAO,UAAU,SAAS,SAAS,MAAM;AAC7C;AAEA,SAAS,UAAU,GAAG,GAAG;AACrB,MAAI,EAAE,KAAK,IAAI,EAAE,KAAK,GAAG;AAGrB,WAAO,CAAC,UAAU,GAAG,CAAC;AAAA,EAC1B;AAEA,MAAI,kBAAkB,EAAE,KAAK,IAAI,EAAE,KAAK,KAAK,MAAM,EAAE,MAAM,IAAI,EAAE,MAAM,IAEnE,SAAS,EAAE,MAAM,EAAE,IAAI,gBAAgB,QAAQ,GAC/C,SACA;AAEJ,MAAI,IAAI,SAAS,GAAG;AAChB,cAAU,EAAE,MAAM,EAAE,IAAI,iBAAiB,GAAG,QAAQ;AAEpD,cAAU,IAAI,WAAW,SAAS;AAAA,EACtC,OAAO;AACH,cAAU,EAAE,MAAM,EAAE,IAAI,iBAAiB,GAAG,QAAQ;AAEpD,cAAU,IAAI,WAAW,UAAU;AAAA,EACvC;AAGA,SAAO,EAAE,iBAAiB,WAAW;AACzC;AAEA,MAAM,gBAAgB;AACtB,MAAM,mBAAmB;AAEzB,SAAS,WAAW;AAChB,SAAO,KAAK,MAAM,EAAE,OAAO,IAAI,EAAE,OAAO,kCAAkC;AAC9E;AAEA,SAAS,YAAY,YAAY;AAC7B,MAAI,CAAC,KAAK,QAAQ,GAAG;AACjB,WAAO;AAAA,EACX;AACA,MAAI,MAAM,eAAe,MACrB,IAAI,MAAM,KAAK,MAAM,EAAE,IAAI,IAAI;AACnC,MAAI,EAAE,KAAK,IAAI,KAAK,EAAE,KAAK,IAAI,MAAM;AACjC,WAAO;AAAA,MACH;AAAA,MACA,MACM,mCACA;AAAA,IACV;AAAA,EACJ;AACA,MAAI,WAAW,KAAK,UAAU,WAAW,GAAG;AAExC,QAAI,KAAK;AACL,aAAO,KAAK,OAAO,EAAE,YAAY;AAAA,IACrC,OAAO;AACH,aAAO,IAAI,KAAK,KAAK,QAAQ,IAAI,KAAK,UAAU,IAAI,KAAK,GAAI,EACxD,YAAY,EACZ,QAAQ,KAAK,aAAa,GAAG,GAAG,CAAC;AAAA,IAC1C;AAAA,EACJ;AACA,SAAO;AAAA,IACH;AAAA,IACA,MAAM,iCAAiC;AAAA,EAC3C;AACJ;AAQA,SAAS,UAAU;AACf,MAAI,CAAC,KAAK,QAAQ,GAAG;AACjB,WAAO,uBAAuB,KAAK,KAAK;AAAA,EAC5C;AACA,MAAI,OAAO,UACP,OAAO,IACP,QACA,MACA,UACA;AACJ,MAAI,CAAC,KAAK,QAAQ,GAAG;AACjB,WAAO,KAAK,UAAU,MAAM,IAAI,eAAe;AAC/C,WAAO;AAAA,EACX;AACA,WAAS,MAAM,OAAO;AACtB,SAAO,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,OAAO,SAAS;AAC1D,aAAW;AACX,WAAS,OAAO;AAEhB,SAAO,KAAK,OAAO,SAAS,OAAO,WAAW,MAAM;AACxD;AAEA,SAAS,OAAO,aAAa;AACzB,MAAI,CAAC,aAAa;AACd,kBAAc,KAAK,MAAM,IACnB,MAAM,mBACN,MAAM;AAAA,EAChB;AACA,MAAI,SAAS,aAAa,MAAM,WAAW;AAC3C,SAAO,KAAK,WAAW,EAAE,WAAW,MAAM;AAC9C;AAEA,SAAS,KAAK,MAAM,eAAe;AAC/B,MACI,KAAK,QAAQ,MACX,SAAS,IAAI,KAAK,KAAK,QAAQ,KAAM,YAAY,IAAI,EAAE,QAAQ,IACnE;AACE,WAAO,eAAe,EAAE,IAAI,MAAM,MAAM,KAAK,CAAC,EACzC,OAAO,KAAK,OAAO,CAAC,EACpB,SAAS,CAAC,aAAa;AAAA,EAChC,OAAO;AACH,WAAO,KAAK,WAAW,EAAE,YAAY;AAAA,EACzC;AACJ;AAEA,SAAS,QAAQ,eAAe;AAC5B,SAAO,KAAK,KAAK,YAAY,GAAG,aAAa;AACjD;AAEA,SAAS,GAAG,MAAM,eAAe;AAC7B,MACI,KAAK,QAAQ,MACX,SAAS,IAAI,KAAK,KAAK,QAAQ,KAAM,YAAY,IAAI,EAAE,QAAQ,IACnE;AACE,WAAO,eAAe,EAAE,MAAM,MAAM,IAAI,KAAK,CAAC,EACzC,OAAO,KAAK,OAAO,CAAC,EACpB,SAAS,CAAC,aAAa;AAAA,EAChC,OAAO;AACH,WAAO,KAAK,WAAW,EAAE,YAAY;AAAA,EACzC;AACJ;AAEA,SAAS,MAAM,eAAe;AAC1B,SAAO,KAAK,GAAG,YAAY,GAAG,aAAa;AAC/C;AAKA,SAAS,OAAO,KAAK;AACjB,MAAI;AAEJ,MAAI,QAAQ,QAAW;AACnB,WAAO,KAAK,QAAQ;AAAA,EACxB,OAAO;AACH,oBAAgB,UAAU,GAAG;AAC7B,QAAI,iBAAiB,MAAM;AACvB,WAAK,UAAU;AAAA,IACnB;AACA,WAAO;AAAA,EACX;AACJ;AAEA,IAAI,OAAO;AAAA,EACP;AAAA,EACA,SAAU,KAAK;AACX,QAAI,QAAQ,QAAW;AACnB,aAAO,KAAK,WAAW;AAAA,IAC3B,OAAO;AACH,aAAO,KAAK,OAAO,GAAG;AAAA,IAC1B;AAAA,EACJ;AACJ;AAEA,SAAS,aAAa;AAClB,SAAO,KAAK;AAChB;AAEA,IAAI,gBAAgB;AAApB,IACI,gBAAgB,KAAK;AADzB,IAEI,cAAc,KAAK;AAFvB,IAGI,oBAAoB,MAAM,MAAM,MAAM,KAAK;AAG/C,SAAS,MAAM,UAAU,SAAS;AAC9B,UAAS,WAAW,UAAW,WAAW;AAC9C;AAEA,SAAS,iBAAiB,GAAG,GAAG,GAAG;AAE/B,MAAI,IAAI,OAAO,KAAK,GAAG;AAEnB,WAAO,IAAI,KAAK,IAAI,KAAK,GAAG,CAAC,IAAI;AAAA,EACrC,OAAO;AACH,WAAO,IAAI,KAAK,GAAG,GAAG,CAAC,EAAE,QAAQ;AAAA,EACrC;AACJ;AAEA,SAAS,eAAe,GAAG,GAAG,GAAG;AAE7B,MAAI,IAAI,OAAO,KAAK,GAAG;AAEnB,WAAO,KAAK,IAAI,IAAI,KAAK,GAAG,CAAC,IAAI;AAAA,EACrC,OAAO;AACH,WAAO,KAAK,IAAI,GAAG,GAAG,CAAC;AAAA,EAC3B;AACJ;AAEA,SAAS,QAAQ,OAAO;AACpB,MAAI,MAAM;AACV,UAAQ,eAAe,KAAK;AAC5B,MAAI,UAAU,UAAa,UAAU,iBAAiB,CAAC,KAAK,QAAQ,GAAG;AACnE,WAAO;AAAA,EACX;AAEA,gBAAc,KAAK,SAAS,iBAAiB;AAE7C,UAAQ,OAAO;AAAA,IACX,KAAK;AACD,aAAO,YAAY,KAAK,KAAK,GAAG,GAAG,CAAC;AACpC;AAAA,IACJ,KAAK;AACD,aAAO;AAAA,QACH,KAAK,KAAK;AAAA,QACV,KAAK,MAAM,IAAK,KAAK,MAAM,IAAI;AAAA,QAC/B;AAAA,MACJ;AACA;AAAA,IACJ,KAAK;AACD,aAAO,YAAY,KAAK,KAAK,GAAG,KAAK,MAAM,GAAG,CAAC;AAC/C;AAAA,IACJ,KAAK;AACD,aAAO;AAAA,QACH,KAAK,KAAK;AAAA,QACV,KAAK,MAAM;AAAA,QACX,KAAK,KAAK,IAAI,KAAK,QAAQ;AAAA,MAC/B;AACA;AAAA,IACJ,KAAK;AACD,aAAO;AAAA,QACH,KAAK,KAAK;AAAA,QACV,KAAK,MAAM;AAAA,QACX,KAAK,KAAK,KAAK,KAAK,WAAW,IAAI;AAAA,MACvC;AACA;AAAA,IACJ,KAAK;AAAA,IACL,KAAK;AACD,aAAO,YAAY,KAAK,KAAK,GAAG,KAAK,MAAM,GAAG,KAAK,KAAK,CAAC;AACzD;AAAA,IACJ,KAAK;AACD,aAAO,KAAK,GAAG,QAAQ;AACvB,cAAQ;AAAA,QACJ,QAAQ,KAAK,SAAS,IAAI,KAAK,UAAU,IAAI;AAAA,QAC7C;AAAA,MACJ;AACA;AAAA,IACJ,KAAK;AACD,aAAO,KAAK,GAAG,QAAQ;AACvB,cAAQ,MAAM,MAAM,aAAa;AACjC;AAAA,IACJ,KAAK;AACD,aAAO,KAAK,GAAG,QAAQ;AACvB,cAAQ,MAAM,MAAM,aAAa;AACjC;AAAA,EACR;AAEA,OAAK,GAAG,QAAQ,IAAI;AACpB,QAAM,aAAa,MAAM,IAAI;AAC7B,SAAO;AACX;AAEA,SAAS,MAAM,OAAO;AAClB,MAAI,MAAM;AACV,UAAQ,eAAe,KAAK;AAC5B,MAAI,UAAU,UAAa,UAAU,iBAAiB,CAAC,KAAK,QAAQ,GAAG;AACnE,WAAO;AAAA,EACX;AAEA,gBAAc,KAAK,SAAS,iBAAiB;AAE7C,UAAQ,OAAO;AAAA,IACX,KAAK;AACD,aAAO,YAAY,KAAK,KAAK,IAAI,GAAG,GAAG,CAAC,IAAI;AAC5C;AAAA,IACJ,KAAK;AACD,aACI;AAAA,QACI,KAAK,KAAK;AAAA,QACV,KAAK,MAAM,IAAK,KAAK,MAAM,IAAI,IAAK;AAAA,QACpC;AAAA,MACJ,IAAI;AACR;AAAA,IACJ,KAAK;AACD,aAAO,YAAY,KAAK,KAAK,GAAG,KAAK,MAAM,IAAI,GAAG,CAAC,IAAI;AACvD;AAAA,IACJ,KAAK;AACD,aACI;AAAA,QACI,KAAK,KAAK;AAAA,QACV,KAAK,MAAM;AAAA,QACX,KAAK,KAAK,IAAI,KAAK,QAAQ,IAAI;AAAA,MACnC,IAAI;AACR;AAAA,IACJ,KAAK;AACD,aACI;AAAA,QACI,KAAK,KAAK;AAAA,QACV,KAAK,MAAM;AAAA,QACX,KAAK,KAAK,KAAK,KAAK,WAAW,IAAI,KAAK;AAAA,MAC5C,IAAI;AACR;AAAA,IACJ,KAAK;AAAA,IACL,KAAK;AACD,aAAO,YAAY,KAAK,KAAK,GAAG,KAAK,MAAM,GAAG,KAAK,KAAK,IAAI,CAAC,IAAI;AACjE;AAAA,IACJ,KAAK;AACD,aAAO,KAAK,GAAG,QAAQ;AACvB,cACI,cACA;AAAA,QACI,QAAQ,KAAK,SAAS,IAAI,KAAK,UAAU,IAAI;AAAA,QAC7C;AAAA,MACJ,IACA;AACJ;AAAA,IACJ,KAAK;AACD,aAAO,KAAK,GAAG,QAAQ;AACvB,cAAQ,gBAAgB,MAAM,MAAM,aAAa,IAAI;AACrD;AAAA,IACJ,KAAK;AACD,aAAO,KAAK,GAAG,QAAQ;AACvB,cAAQ,gBAAgB,MAAM,MAAM,aAAa,IAAI;AACrD;AAAA,EACR;AAEA,OAAK,GAAG,QAAQ,IAAI;AACpB,QAAM,aAAa,MAAM,IAAI;AAC7B,SAAO;AACX;AAEA,SAAS,UAAU;AACf,SAAO,KAAK,GAAG,QAAQ,KAAK,KAAK,WAAW,KAAK;AACrD;AAEA,SAAS,OAAO;AACZ,SAAO,KAAK,MAAM,KAAK,QAAQ,IAAI,GAAI;AAC3C;AAEA,SAAS,SAAS;AACd,SAAO,IAAI,KAAK,KAAK,QAAQ,CAAC;AAClC;AAEA,SAAS,UAAU;AACf,MAAI,IAAI;AACR,SAAO;AAAA,IACH,EAAE,KAAK;AAAA,IACP,EAAE,MAAM;AAAA,IACR,EAAE,KAAK;AAAA,IACP,EAAE,KAAK;AAAA,IACP,EAAE,OAAO;AAAA,IACT,EAAE,OAAO;AAAA,IACT,EAAE,YAAY;AAAA,EAClB;AACJ;AAEA,SAAS,WAAW;AAChB,MAAI,IAAI;AACR,SAAO;AAAA,IACH,OAAO,EAAE,KAAK;AAAA,IACd,QAAQ,EAAE,MAAM;AAAA,IAChB,MAAM,EAAE,KAAK;AAAA,IACb,OAAO,EAAE,MAAM;AAAA,IACf,SAAS,EAAE,QAAQ;AAAA,IACnB,SAAS,EAAE,QAAQ;AAAA,IACnB,cAAc,EAAE,aAAa;AAAA,EACjC;AACJ;AAEA,SAAS,SAAS;AAEd,SAAO,KAAK,QAAQ,IAAI,KAAK,YAAY,IAAI;AACjD;AAEA,SAAS,YAAY;AACjB,SAAO,QAAQ,IAAI;AACvB;AAEA,SAAS,eAAe;AACpB,SAAO,OAAO,CAAC,GAAG,gBAAgB,IAAI,CAAC;AAC3C;AAEA,SAAS,YAAY;AACjB,SAAO,gBAAgB,IAAI,EAAE;AACjC;AAEA,SAAS,eAAe;AACpB,SAAO;AAAA,IACH,OAAO,KAAK;AAAA,IACZ,QAAQ,KAAK;AAAA,IACb,QAAQ,KAAK;AAAA,IACb,OAAO,KAAK;AAAA,IACZ,QAAQ,KAAK;AAAA,EACjB;AACJ;AAEA,eAAe,KAAK,GAAG,GAAG,SAAS;AACnC,eAAe,MAAM,GAAG,GAAG,SAAS;AACpC,eAAe,OAAO,GAAG,GAAG,SAAS;AACrC,eAAe,QAAQ,GAAG,GAAG,SAAS;AACtC,eAAe,SAAS,GAAG,GAAG,WAAW;AAEzC,eAAe,KAAK,CAAC,KAAK,CAAC,GAAG,MAAM,SAAS;AAC7C,eAAe,KAAK,CAAC,MAAM,CAAC,GAAG,GAAG,SAAS;AAC3C,eAAe,KAAK,CAAC,OAAO,CAAC,GAAG,GAAG,SAAS;AAC5C,eAAe,KAAK,CAAC,QAAQ,CAAC,GAAG,GAAG,SAAS;AAE7C,cAAc,KAAK,YAAY;AAC/B,cAAc,MAAM,YAAY;AAChC,cAAc,OAAO,YAAY;AACjC,cAAc,QAAQ,YAAY;AAClC,cAAc,SAAS,cAAc;AAErC;AAAA,EACI,CAAC,KAAK,MAAM,OAAO,QAAQ,OAAO;AAAA,EAClC,SAAU,OAAO,OAAO,QAAQI,QAAO;AACnC,QAAI,MAAM,OAAO,QAAQ,UAAU,OAAOA,QAAO,OAAO,OAAO;AAC/D,QAAI,KAAK;AACL,sBAAgB,MAAM,EAAE,MAAM;AAAA,IAClC,OAAO;AACH,sBAAgB,MAAM,EAAE,aAAa;AAAA,IACzC;AAAA,EACJ;AACJ;AAEA,cAAc,KAAK,aAAa;AAChC,cAAc,MAAM,aAAa;AACjC,cAAc,OAAO,aAAa;AAClC,cAAc,QAAQ,aAAa;AACnC,cAAc,MAAM,mBAAmB;AAEvC,cAAc,CAAC,KAAK,MAAM,OAAO,MAAM,GAAG,IAAI;AAC9C,cAAc,CAAC,IAAI,GAAG,SAAU,OAAO,OAAO,QAAQA,QAAO;AACzD,MAAI;AACJ,MAAI,OAAO,QAAQ,sBAAsB;AACrC,YAAQ,MAAM,MAAM,OAAO,QAAQ,oBAAoB;AAAA,EAC3D;AAEA,MAAI,OAAO,QAAQ,qBAAqB;AACpC,UAAM,IAAI,IAAI,OAAO,QAAQ,oBAAoB,OAAO,KAAK;AAAA,EACjE,OAAO;AACH,UAAM,IAAI,IAAI,SAAS,OAAO,EAAE;AAAA,EACpC;AACJ,CAAC;AAED,SAAS,WAAW,GAAGN,SAAQ;AAC3B,MAAI,GACA,GACA,MACA,OAAO,KAAK,SAAS,UAAU,IAAI,EAAE;AACzC,OAAK,IAAI,GAAG,IAAI,KAAK,QAAQ,IAAI,GAAG,EAAE,GAAG;AACrC,YAAQ,OAAO,KAAK,CAAC,EAAE,OAAO;AAAA,MAC1B,KAAK;AAED,eAAO,MAAM,KAAK,CAAC,EAAE,KAAK,EAAE,QAAQ,KAAK;AACzC,aAAK,CAAC,EAAE,QAAQ,KAAK,QAAQ;AAC7B;AAAA,IACR;AAEA,YAAQ,OAAO,KAAK,CAAC,EAAE,OAAO;AAAA,MAC1B,KAAK;AACD,aAAK,CAAC,EAAE,QAAQ;AAChB;AAAA,MACJ,KAAK;AAED,eAAO,MAAM,KAAK,CAAC,EAAE,KAAK,EAAE,QAAQ,KAAK,EAAE,QAAQ;AACnD,aAAK,CAAC,EAAE,QAAQ,KAAK,QAAQ;AAC7B;AAAA,IACR;AAAA,EACJ;AACA,SAAO;AACX;AAEA,SAAS,gBAAgB,SAASA,SAAQ,QAAQ;AAC9C,MAAI,GACA,GACA,OAAO,KAAK,KAAK,GACjB,MACA,MACA;AACJ,YAAU,QAAQ,YAAY;AAE9B,OAAK,IAAI,GAAG,IAAI,KAAK,QAAQ,IAAI,GAAG,EAAE,GAAG;AACrC,WAAO,KAAK,CAAC,EAAE,KAAK,YAAY;AAChC,WAAO,KAAK,CAAC,EAAE,KAAK,YAAY;AAChC,aAAS,KAAK,CAAC,EAAE,OAAO,YAAY;AAEpC,QAAI,QAAQ;AACR,cAAQA,SAAQ;AAAA,QACZ,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACD,cAAI,SAAS,SAAS;AAClB,mBAAO,KAAK,CAAC;AAAA,UACjB;AACA;AAAA,QAEJ,KAAK;AACD,cAAI,SAAS,SAAS;AAClB,mBAAO,KAAK,CAAC;AAAA,UACjB;AACA;AAAA,QAEJ,KAAK;AACD,cAAI,WAAW,SAAS;AACpB,mBAAO,KAAK,CAAC;AAAA,UACjB;AACA;AAAA,MACR;AAAA,IACJ,WAAW,CAAC,MAAM,MAAM,MAAM,EAAE,QAAQ,OAAO,KAAK,GAAG;AACnD,aAAO,KAAK,CAAC;AAAA,IACjB;AAAA,EACJ;AACJ;AAEA,SAAS,sBAAsB,KAAK,MAAM;AACtC,MAAI,MAAM,IAAI,SAAS,IAAI,QAAQ,IAAK;AACxC,MAAI,SAAS,QAAW;AACpB,WAAO,MAAM,IAAI,KAAK,EAAE,KAAK;AAAA,EACjC,OAAO;AACH,WAAO,MAAM,IAAI,KAAK,EAAE,KAAK,KAAK,OAAO,IAAI,UAAU;AAAA,EAC3D;AACJ;AAEA,SAAS,aAAa;AAClB,MAAI,GACA,GACA,KACA,OAAO,KAAK,WAAW,EAAE,KAAK;AAClC,OAAK,IAAI,GAAG,IAAI,KAAK,QAAQ,IAAI,GAAG,EAAE,GAAG;AAErC,UAAM,KAAK,MAAM,EAAE,QAAQ,KAAK,EAAE,QAAQ;AAE1C,QAAI,KAAK,CAAC,EAAE,SAAS,OAAO,OAAO,KAAK,CAAC,EAAE,OAAO;AAC9C,aAAO,KAAK,CAAC,EAAE;AAAA,IACnB;AACA,QAAI,KAAK,CAAC,EAAE,SAAS,OAAO,OAAO,KAAK,CAAC,EAAE,OAAO;AAC9C,aAAO,KAAK,CAAC,EAAE;AAAA,IACnB;AAAA,EACJ;AAEA,SAAO;AACX;AAEA,SAAS,eAAe;AACpB,MAAI,GACA,GACA,KACA,OAAO,KAAK,WAAW,EAAE,KAAK;AAClC,OAAK,IAAI,GAAG,IAAI,KAAK,QAAQ,IAAI,GAAG,EAAE,GAAG;AAErC,UAAM,KAAK,MAAM,EAAE,QAAQ,KAAK,EAAE,QAAQ;AAE1C,QAAI,KAAK,CAAC,EAAE,SAAS,OAAO,OAAO,KAAK,CAAC,EAAE,OAAO;AAC9C,aAAO,KAAK,CAAC,EAAE;AAAA,IACnB;AACA,QAAI,KAAK,CAAC,EAAE,SAAS,OAAO,OAAO,KAAK,CAAC,EAAE,OAAO;AAC9C,aAAO,KAAK,CAAC,EAAE;AAAA,IACnB;AAAA,EACJ;AAEA,SAAO;AACX;AAEA,SAAS,aAAa;AAClB,MAAI,GACA,GACA,KACA,OAAO,KAAK,WAAW,EAAE,KAAK;AAClC,OAAK,IAAI,GAAG,IAAI,KAAK,QAAQ,IAAI,GAAG,EAAE,GAAG;AAErC,UAAM,KAAK,MAAM,EAAE,QAAQ,KAAK,EAAE,QAAQ;AAE1C,QAAI,KAAK,CAAC,EAAE,SAAS,OAAO,OAAO,KAAK,CAAC,EAAE,OAAO;AAC9C,aAAO,KAAK,CAAC,EAAE;AAAA,IACnB;AACA,QAAI,KAAK,CAAC,EAAE,SAAS,OAAO,OAAO,KAAK,CAAC,EAAE,OAAO;AAC9C,aAAO,KAAK,CAAC,EAAE;AAAA,IACnB;AAAA,EACJ;AAEA,SAAO;AACX;AAEA,SAAS,aAAa;AAClB,MAAI,GACA,GACA,KACA,KACA,OAAO,KAAK,WAAW,EAAE,KAAK;AAClC,OAAK,IAAI,GAAG,IAAI,KAAK,QAAQ,IAAI,GAAG,EAAE,GAAG;AACrC,UAAM,KAAK,CAAC,EAAE,SAAS,KAAK,CAAC,EAAE,QAAQ,IAAK;AAG5C,UAAM,KAAK,MAAM,EAAE,QAAQ,KAAK,EAAE,QAAQ;AAE1C,QACK,KAAK,CAAC,EAAE,SAAS,OAAO,OAAO,KAAK,CAAC,EAAE,SACvC,KAAK,CAAC,EAAE,SAAS,OAAO,OAAO,KAAK,CAAC,EAAE,OAC1C;AACE,cACK,KAAK,KAAK,IAAI,MAAM,KAAK,CAAC,EAAE,KAAK,EAAE,KAAK,KAAK,MAC9C,KAAK,CAAC,EAAE;AAAA,IAEhB;AAAA,EACJ;AAEA,SAAO,KAAK,KAAK;AACrB;AAEA,SAAS,cAAc,UAAU;AAC7B,MAAI,CAAC,WAAW,MAAM,gBAAgB,GAAG;AACrC,qBAAiB,KAAK,IAAI;AAAA,EAC9B;AACA,SAAO,WAAW,KAAK,iBAAiB,KAAK;AACjD;AAEA,SAAS,cAAc,UAAU;AAC7B,MAAI,CAAC,WAAW,MAAM,gBAAgB,GAAG;AACrC,qBAAiB,KAAK,IAAI;AAAA,EAC9B;AACA,SAAO,WAAW,KAAK,iBAAiB,KAAK;AACjD;AAEA,SAAS,gBAAgB,UAAU;AAC/B,MAAI,CAAC,WAAW,MAAM,kBAAkB,GAAG;AACvC,qBAAiB,KAAK,IAAI;AAAA,EAC9B;AACA,SAAO,WAAW,KAAK,mBAAmB,KAAK;AACnD;AAEA,SAAS,aAAa,UAAUC,SAAQ;AACpC,SAAOA,QAAO,cAAc,QAAQ;AACxC;AAEA,SAAS,aAAa,UAAUA,SAAQ;AACpC,SAAOA,QAAO,cAAc,QAAQ;AACxC;AAEA,SAAS,eAAe,UAAUA,SAAQ;AACtC,SAAOA,QAAO,gBAAgB,QAAQ;AAC1C;AAEA,SAAS,oBAAoB,UAAUA,SAAQ;AAC3C,SAAOA,QAAO,wBAAwB;AAC1C;AAEA,SAAS,mBAAmB;AACxB,MAAI,aAAa,CAAC,GACd,aAAa,CAAC,GACd,eAAe,CAAC,GAChB,cAAc,CAAC,GACf,GACA,GACA,UACA,UACA,YACA,OAAO,KAAK,KAAK;AAErB,OAAK,IAAI,GAAG,IAAI,KAAK,QAAQ,IAAI,GAAG,EAAE,GAAG;AACrC,eAAW,YAAY,KAAK,CAAC,EAAE,IAAI;AACnC,eAAW,YAAY,KAAK,CAAC,EAAE,IAAI;AACnC,iBAAa,YAAY,KAAK,CAAC,EAAE,MAAM;AAEvC,eAAW,KAAK,QAAQ;AACxB,eAAW,KAAK,QAAQ;AACxB,iBAAa,KAAK,UAAU;AAC5B,gBAAY,KAAK,QAAQ;AACzB,gBAAY,KAAK,QAAQ;AACzB,gBAAY,KAAK,UAAU;AAAA,EAC/B;AAEA,OAAK,aAAa,IAAI,OAAO,OAAO,YAAY,KAAK,GAAG,IAAI,KAAK,GAAG;AACpE,OAAK,iBAAiB,IAAI,OAAO,OAAO,WAAW,KAAK,GAAG,IAAI,KAAK,GAAG;AACvE,OAAK,iBAAiB,IAAI,OAAO,OAAO,WAAW,KAAK,GAAG,IAAI,KAAK,GAAG;AACvE,OAAK,mBAAmB,IAAI;AAAA,IACxB,OAAO,aAAa,KAAK,GAAG,IAAI;AAAA,IAChC;AAAA,EACJ;AACJ;AAIA,eAAe,GAAG,CAAC,MAAM,CAAC,GAAG,GAAG,WAAY;AACxC,SAAO,KAAK,SAAS,IAAI;AAC7B,CAAC;AAED,eAAe,GAAG,CAAC,MAAM,CAAC,GAAG,GAAG,WAAY;AACxC,SAAO,KAAK,YAAY,IAAI;AAChC,CAAC;AAED,SAAS,uBAAuBK,QAAO,QAAQ;AAC3C,iBAAe,GAAG,CAACA,QAAOA,OAAM,MAAM,GAAG,GAAG,MAAM;AACtD;AAEA,uBAAuB,QAAQ,UAAU;AACzC,uBAAuB,SAAS,UAAU;AAC1C,uBAAuB,QAAQ,aAAa;AAC5C,uBAAuB,SAAS,aAAa;AAM7C,cAAc,KAAK,WAAW;AAC9B,cAAc,KAAK,WAAW;AAC9B,cAAc,MAAM,WAAW,MAAM;AACrC,cAAc,MAAM,WAAW,MAAM;AACrC,cAAc,QAAQ,WAAW,MAAM;AACvC,cAAc,QAAQ,WAAW,MAAM;AACvC,cAAc,SAAS,WAAW,MAAM;AACxC,cAAc,SAAS,WAAW,MAAM;AAExC;AAAA,EACI,CAAC,QAAQ,SAAS,QAAQ,OAAO;AAAA,EACjC,SAAU,OAAO,MAAM,QAAQA,QAAO;AAClC,SAAKA,OAAM,OAAO,GAAG,CAAC,CAAC,IAAI,MAAM,KAAK;AAAA,EAC1C;AACJ;AAEA,kBAAkB,CAAC,MAAM,IAAI,GAAG,SAAU,OAAO,MAAM,QAAQA,QAAO;AAClE,OAAKA,MAAK,IAAI,MAAM,kBAAkB,KAAK;AAC/C,CAAC;AAID,SAAS,eAAe,OAAO;AAC3B,SAAO,qBAAqB;AAAA,IACxB;AAAA,IACA;AAAA,IACA,KAAK,KAAK;AAAA,IACV,KAAK,QAAQ,IAAI,KAAK,WAAW,EAAE,MAAM;AAAA,IACzC,KAAK,WAAW,EAAE,MAAM;AAAA,IACxB,KAAK,WAAW,EAAE,MAAM;AAAA,EAC5B;AACJ;AAEA,SAAS,kBAAkB,OAAO;AAC9B,SAAO,qBAAqB;AAAA,IACxB;AAAA,IACA;AAAA,IACA,KAAK,QAAQ;AAAA,IACb,KAAK,WAAW;AAAA,IAChB;AAAA,IACA;AAAA,EACJ;AACJ;AAEA,SAAS,oBAAoB;AACzB,SAAO,YAAY,KAAK,KAAK,GAAG,GAAG,CAAC;AACxC;AAEA,SAAS,2BAA2B;AAChC,SAAO,YAAY,KAAK,YAAY,GAAG,GAAG,CAAC;AAC/C;AAEA,SAAS,iBAAiB;AACtB,MAAI,WAAW,KAAK,WAAW,EAAE;AACjC,SAAO,YAAY,KAAK,KAAK,GAAG,SAAS,KAAK,SAAS,GAAG;AAC9D;AAEA,SAAS,qBAAqB;AAC1B,MAAI,WAAW,KAAK,WAAW,EAAE;AACjC,SAAO,YAAY,KAAK,SAAS,GAAG,SAAS,KAAK,SAAS,GAAG;AAClE;AAEA,SAAS,qBAAqB,OAAO,MAAM,SAAS,KAAK,KAAK;AAC1D,MAAI;AACJ,MAAI,SAAS,MAAM;AACf,WAAO,WAAW,MAAM,KAAK,GAAG,EAAE;AAAA,EACtC,OAAO;AACH,kBAAc,YAAY,OAAO,KAAK,GAAG;AACzC,QAAI,OAAO,aAAa;AACpB,aAAO;AAAA,IACX;AACA,WAAO,WAAW,KAAK,MAAM,OAAO,MAAM,SAAS,KAAK,GAAG;AAAA,EAC/D;AACJ;AAEA,SAAS,WAAW,UAAU,MAAM,SAAS,KAAK,KAAK;AACnD,MAAI,gBAAgB,mBAAmB,UAAU,MAAM,SAAS,KAAK,GAAG,GACpE,OAAO,cAAc,cAAc,MAAM,GAAG,cAAc,SAAS;AAEvE,OAAK,KAAK,KAAK,eAAe,CAAC;AAC/B,OAAK,MAAM,KAAK,YAAY,CAAC;AAC7B,OAAK,KAAK,KAAK,WAAW,CAAC;AAC3B,SAAO;AACX;AAIA,eAAe,KAAK,GAAG,MAAM,SAAS;AAItC,cAAc,KAAK,MAAM;AACzB,cAAc,KAAK,SAAU,OAAO,OAAO;AACvC,QAAM,KAAK,KAAK,MAAM,KAAK,IAAI,KAAK;AACxC,CAAC;AAID,SAAS,cAAc,OAAO;AAC1B,SAAO,SAAS,OACV,KAAK,MAAM,KAAK,MAAM,IAAI,KAAK,CAAC,IAChC,KAAK,OAAO,QAAQ,KAAK,IAAK,KAAK,MAAM,IAAI,CAAE;AACzD;AAIA,eAAe,KAAK,CAAC,MAAM,CAAC,GAAG,MAAM,MAAM;AAI3C,cAAc,KAAK,WAAW,sBAAsB;AACpD,cAAc,MAAM,WAAW,MAAM;AACrC,cAAc,MAAM,SAAU,UAAUL,SAAQ;AAE5C,SAAO,WACDA,QAAO,2BAA2BA,QAAO,gBACzCA,QAAO;AACjB,CAAC;AAED,cAAc,CAAC,KAAK,IAAI,GAAG,IAAI;AAC/B,cAAc,MAAM,SAAU,OAAO,OAAO;AACxC,QAAM,IAAI,IAAI,MAAM,MAAM,MAAM,SAAS,EAAE,CAAC,CAAC;AACjD,CAAC;AAID,IAAI,mBAAmB,WAAW,QAAQ,IAAI;AAI9C,eAAe,OAAO,CAAC,QAAQ,CAAC,GAAG,QAAQ,WAAW;AAItD,cAAc,OAAO,SAAS;AAC9B,cAAc,QAAQ,MAAM;AAC5B,cAAc,CAAC,OAAO,MAAM,GAAG,SAAU,OAAO,OAAO,QAAQ;AAC3D,SAAO,aAAa,MAAM,KAAK;AACnC,CAAC;AAMD,SAAS,gBAAgB,OAAO;AAC5B,MAAI,YACA,KAAK;AAAA,KACA,KAAK,MAAM,EAAE,QAAQ,KAAK,IAAI,KAAK,MAAM,EAAE,QAAQ,MAAM,KAAK;AAAA,EACnE,IAAI;AACR,SAAO,SAAS,OAAO,YAAY,KAAK,IAAI,QAAQ,WAAW,GAAG;AACtE;AAIA,eAAe,KAAK,CAAC,MAAM,CAAC,GAAG,GAAG,QAAQ;AAI1C,cAAc,KAAK,WAAW,gBAAgB;AAC9C,cAAc,MAAM,WAAW,MAAM;AACrC,cAAc,CAAC,KAAK,IAAI,GAAG,MAAM;AAIjC,IAAI,eAAe,WAAW,WAAW,KAAK;AAI9C,eAAe,KAAK,CAAC,MAAM,CAAC,GAAG,GAAG,QAAQ;AAI1C,cAAc,KAAK,WAAW,gBAAgB;AAC9C,cAAc,MAAM,WAAW,MAAM;AACrC,cAAc,CAAC,KAAK,IAAI,GAAG,MAAM;AAIjC,IAAI,eAAe,WAAW,WAAW,KAAK;AAI9C,eAAe,KAAK,GAAG,GAAG,WAAY;AAClC,SAAO,CAAC,EAAE,KAAK,YAAY,IAAI;AACnC,CAAC;AAED,eAAe,GAAG,CAAC,MAAM,CAAC,GAAG,GAAG,WAAY;AACxC,SAAO,CAAC,EAAE,KAAK,YAAY,IAAI;AACnC,CAAC;AAED,eAAe,GAAG,CAAC,OAAO,CAAC,GAAG,GAAG,aAAa;AAC9C,eAAe,GAAG,CAAC,QAAQ,CAAC,GAAG,GAAG,WAAY;AAC1C,SAAO,KAAK,YAAY,IAAI;AAChC,CAAC;AACD,eAAe,GAAG,CAAC,SAAS,CAAC,GAAG,GAAG,WAAY;AAC3C,SAAO,KAAK,YAAY,IAAI;AAChC,CAAC;AACD,eAAe,GAAG,CAAC,UAAU,CAAC,GAAG,GAAG,WAAY;AAC5C,SAAO,KAAK,YAAY,IAAI;AAChC,CAAC;AACD,eAAe,GAAG,CAAC,WAAW,CAAC,GAAG,GAAG,WAAY;AAC7C,SAAO,KAAK,YAAY,IAAI;AAChC,CAAC;AACD,eAAe,GAAG,CAAC,YAAY,CAAC,GAAG,GAAG,WAAY;AAC9C,SAAO,KAAK,YAAY,IAAI;AAChC,CAAC;AACD,eAAe,GAAG,CAAC,aAAa,CAAC,GAAG,GAAG,WAAY;AAC/C,SAAO,KAAK,YAAY,IAAI;AAChC,CAAC;AAID,cAAc,KAAK,WAAW,MAAM;AACpC,cAAc,MAAM,WAAW,MAAM;AACrC,cAAc,OAAO,WAAW,MAAM;AAEtC,IAAI;AAAJ,IAAW;AACX,KAAK,QAAQ,QAAQ,MAAM,UAAU,GAAG,SAAS,KAAK;AAClD,gBAAc,OAAO,aAAa;AACtC;AAEA,SAAS,QAAQ,OAAO,OAAO;AAC3B,QAAM,WAAW,IAAI,OAAO,OAAO,SAAS,GAAI;AACpD;AAEA,KAAK,QAAQ,KAAK,MAAM,UAAU,GAAG,SAAS,KAAK;AAC/C,gBAAc,OAAO,OAAO;AAChC;AAEA,oBAAoB,WAAW,gBAAgB,KAAK;AAIpD,eAAe,KAAK,GAAG,GAAG,UAAU;AACpC,eAAe,MAAM,GAAG,GAAG,UAAU;AAIrC,SAAS,cAAc;AACnB,SAAO,KAAK,SAAS,QAAQ;AACjC;AAEA,SAAS,cAAc;AACnB,SAAO,KAAK,SAAS,+BAA+B;AACxD;AAEA,IAAI,QAAQ,OAAO;AAEnB,MAAM,MAAM;AACZ,MAAM,WAAW;AACjB,MAAM,QAAQ;AACd,MAAM,OAAO;AACb,MAAM,QAAQ;AACd,MAAM,SAAS;AACf,MAAM,OAAO;AACb,MAAM,UAAU;AAChB,MAAM,KAAK;AACX,MAAM,QAAQ;AACd,MAAM,MAAM;AACZ,MAAM,YAAY;AAClB,MAAM,UAAU;AAChB,MAAM,WAAW;AACjB,MAAM,YAAY;AAClB,MAAM,SAAS;AACf,MAAM,gBAAgB;AACtB,MAAM,iBAAiB;AACvB,MAAM,UAAU;AAChB,MAAM,OAAO;AACb,MAAM,SAAS;AACf,MAAM,aAAa;AACnB,MAAM,MAAM;AACZ,MAAM,MAAM;AACZ,MAAM,eAAe;AACrB,MAAM,MAAM;AACZ,MAAM,UAAU;AAChB,MAAM,WAAW;AACjB,MAAM,UAAU;AAChB,MAAM,WAAW;AACjB,MAAM,SAAS;AACf,MAAM,cAAc;AACpB,MAAM,UAAU;AAChB,IAAI,OAAO,WAAW,eAAe,OAAO,OAAO,MAAM;AACrD,QAAM,OAAO,IAAI,4BAA4B,CAAC,IAAI,WAAY;AAC1D,WAAO,YAAY,KAAK,OAAO,IAAI;AAAA,EACvC;AACJ;AACA,MAAM,SAAS;AACf,MAAM,WAAW;AACjB,MAAM,OAAO;AACb,MAAM,UAAU;AAChB,MAAM,eAAe;AACrB,MAAM,UAAU;AAChB,MAAM,YAAY;AAClB,MAAM,UAAU;AAChB,MAAM,UAAU;AAChB,MAAM,OAAO;AACb,MAAM,aAAa;AACnB,MAAM,WAAW;AACjB,MAAM,cAAc;AACpB,MAAM,UAAU,MAAM,WAAW;AACjC,MAAM,QAAQ;AACd,MAAM,cAAc;AACpB,MAAM,OAAO,MAAM,QAAQ;AAC3B,MAAM,UAAU,MAAM,WAAW;AACjC,MAAM,cAAc;AACpB,MAAM,kBAAkB;AACxB,MAAM,iBAAiB;AACvB,MAAM,wBAAwB;AAC9B,MAAM,OAAO;AACb,MAAM,MAAM,MAAM,OAAO;AACzB,MAAM,UAAU;AAChB,MAAM,aAAa;AACnB,MAAM,YAAY;AAClB,MAAM,OAAO,MAAM,QAAQ;AAC3B,MAAM,SAAS,MAAM,UAAU;AAC/B,MAAM,SAAS,MAAM,UAAU;AAC/B,MAAM,cAAc,MAAM,eAAe;AACzC,MAAM,YAAY;AAClB,MAAM,MAAM;AACZ,MAAM,QAAQ;AACd,MAAM,YAAY;AAClB,MAAM,uBAAuB;AAC7B,MAAM,QAAQ;AACd,MAAM,UAAU;AAChB,MAAM,cAAc;AACpB,MAAM,QAAQ;AACd,MAAM,QAAQ;AACd,MAAM,WAAW;AACjB,MAAM,WAAW;AACjB,MAAM,QAAQ;AAAA,EACV;AAAA,EACA;AACJ;AACA,MAAM,SAAS;AAAA,EACX;AAAA,EACA;AACJ;AACA,MAAM,QAAQ;AAAA,EACV;AAAA,EACA;AACJ;AACA,MAAM,OAAO;AAAA,EACT;AAAA,EACA;AACJ;AACA,MAAM,eAAe;AAAA,EACjB;AAAA,EACA;AACJ;AAEA,SAAS,WAAW,OAAO;AACvB,SAAO,YAAY,QAAQ,GAAI;AACnC;AAEA,SAAS,eAAe;AACpB,SAAO,YAAY,MAAM,MAAM,SAAS,EAAE,UAAU;AACxD;AAEA,SAAS,mBAAmB,QAAQ;AAChC,SAAO;AACX;AAEA,IAAI,UAAU,OAAO;AAErB,QAAQ,WAAW;AACnB,QAAQ,iBAAiB;AACzB,QAAQ,cAAc;AACtB,QAAQ,UAAU;AAClB,QAAQ,WAAW;AACnB,QAAQ,aAAa;AACrB,QAAQ,eAAe;AACvB,QAAQ,aAAa;AACrB,QAAQ,MAAM;AACd,QAAQ,OAAO;AACf,QAAQ,YAAY;AACpB,QAAQ,kBAAkB;AAC1B,QAAQ,gBAAgB;AACxB,QAAQ,gBAAgB;AACxB,QAAQ,kBAAkB;AAE1B,QAAQ,SAAS;AACjB,QAAQ,cAAc;AACtB,QAAQ,cAAc;AACtB,QAAQ,cAAc;AACtB,QAAQ,mBAAmB;AAC3B,QAAQ,OAAO;AACf,QAAQ,iBAAiB;AACzB,QAAQ,iBAAiB;AAEzB,QAAQ,WAAW;AACnB,QAAQ,cAAc;AACtB,QAAQ,gBAAgB;AACxB,QAAQ,gBAAgB;AAExB,QAAQ,gBAAgB;AACxB,QAAQ,qBAAqB;AAC7B,QAAQ,mBAAmB;AAE3B,QAAQ,OAAO;AACf,QAAQ,WAAW;AAEnB,SAAS,MAAMD,SAAQ,OAAO,OAAO,QAAQ;AACzC,MAAIC,UAAS,UAAU,GACnB,MAAM,UAAU,EAAE,IAAI,QAAQ,KAAK;AACvC,SAAOA,QAAO,KAAK,EAAE,KAAKD,OAAM;AACpC;AAEA,SAAS,eAAeA,SAAQ,OAAO,OAAO;AAC1C,MAAI,SAASA,OAAM,GAAG;AAClB,YAAQA;AACR,IAAAA,UAAS;AAAA,EACb;AAEA,EAAAA,UAASA,WAAU;AAEnB,MAAI,SAAS,MAAM;AACf,WAAO,MAAMA,SAAQ,OAAO,OAAO,OAAO;AAAA,EAC9C;AAEA,MAAI,GACA,MAAM,CAAC;AACX,OAAK,IAAI,GAAG,IAAI,IAAI,KAAK;AACrB,QAAI,CAAC,IAAI,MAAMA,SAAQ,GAAG,OAAO,OAAO;AAAA,EAC5C;AACA,SAAO;AACX;AAUA,SAAS,iBAAiB,cAAcA,SAAQ,OAAO,OAAO;AAC1D,MAAI,OAAO,iBAAiB,WAAW;AACnC,QAAI,SAASA,OAAM,GAAG;AAClB,cAAQA;AACR,MAAAA,UAAS;AAAA,IACb;AAEA,IAAAA,UAASA,WAAU;AAAA,EACvB,OAAO;AACH,IAAAA,UAAS;AACT,YAAQA;AACR,mBAAe;AAEf,QAAI,SAASA,OAAM,GAAG;AAClB,cAAQA;AACR,MAAAA,UAAS;AAAA,IACb;AAEA,IAAAA,UAASA,WAAU;AAAA,EACvB;AAEA,MAAIC,UAAS,UAAU,GACnB,QAAQ,eAAeA,QAAO,MAAM,MAAM,GAC1C,GACA,MAAM,CAAC;AAEX,MAAI,SAAS,MAAM;AACf,WAAO,MAAMD,UAAS,QAAQ,SAAS,GAAG,OAAO,KAAK;AAAA,EAC1D;AAEA,OAAK,IAAI,GAAG,IAAI,GAAG,KAAK;AACpB,QAAI,CAAC,IAAI,MAAMA,UAAS,IAAI,SAAS,GAAG,OAAO,KAAK;AAAA,EACxD;AACA,SAAO;AACX;AAEA,SAAS,WAAWA,SAAQ,OAAO;AAC/B,SAAO,eAAeA,SAAQ,OAAO,QAAQ;AACjD;AAEA,SAAS,gBAAgBA,SAAQ,OAAO;AACpC,SAAO,eAAeA,SAAQ,OAAO,aAAa;AACtD;AAEA,SAAS,aAAa,cAAcA,SAAQ,OAAO;AAC/C,SAAO,iBAAiB,cAAcA,SAAQ,OAAO,UAAU;AACnE;AAEA,SAAS,kBAAkB,cAAcA,SAAQ,OAAO;AACpD,SAAO,iBAAiB,cAAcA,SAAQ,OAAO,eAAe;AACxE;AAEA,SAAS,gBAAgB,cAAcA,SAAQ,OAAO;AAClD,SAAO,iBAAiB,cAAcA,SAAQ,OAAO,aAAa;AACtE;AAEA,mBAAmB,MAAM;AAAA,EACrB,MAAM;AAAA,IACF;AAAA,MACI,OAAO;AAAA,MACP,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,MAAM;AAAA,IACV;AAAA,IACA;AAAA,MACI,OAAO;AAAA,MACP,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,MAAM;AAAA,IACV;AAAA,EACJ;AAAA,EACA,wBAAwB;AAAA,EACxB,SAAS,SAAU,QAAQ;AACvB,QAAI,IAAI,SAAS,IACb,SACI,MAAO,SAAS,MAAO,EAAE,MAAM,IACzB,OACA,MAAM,IACJ,OACA,MAAM,IACJ,OACA,MAAM,IACJ,OACA;AACpB,WAAO,SAAS;AAAA,EACpB;AACJ,CAAC;AAID,MAAM,OAAO;AAAA,EACT;AAAA,EACA;AACJ;AACA,MAAM,WAAW;AAAA,EACb;AAAA,EACA;AACJ;AAEA,IAAI,UAAU,KAAK;AAEnB,SAAS,MAAM;AACX,MAAI,OAAO,KAAK;AAEhB,OAAK,gBAAgB,QAAQ,KAAK,aAAa;AAC/C,OAAK,QAAQ,QAAQ,KAAK,KAAK;AAC/B,OAAK,UAAU,QAAQ,KAAK,OAAO;AAEnC,OAAK,eAAe,QAAQ,KAAK,YAAY;AAC7C,OAAK,UAAU,QAAQ,KAAK,OAAO;AACnC,OAAK,UAAU,QAAQ,KAAK,OAAO;AACnC,OAAK,QAAQ,QAAQ,KAAK,KAAK;AAC/B,OAAK,SAAS,QAAQ,KAAK,MAAM;AACjC,OAAK,QAAQ,QAAQ,KAAK,KAAK;AAE/B,SAAO;AACX;AAEA,SAAS,cAAc,UAAU,OAAO,OAAO,WAAW;AACtD,MAAI,QAAQ,eAAe,OAAO,KAAK;AAEvC,WAAS,iBAAiB,YAAY,MAAM;AAC5C,WAAS,SAAS,YAAY,MAAM;AACpC,WAAS,WAAW,YAAY,MAAM;AAEtC,SAAO,SAAS,QAAQ;AAC5B;AAGA,SAAS,MAAM,OAAO,OAAO;AACzB,SAAO,cAAc,MAAM,OAAO,OAAO,CAAC;AAC9C;AAGA,SAAS,WAAW,OAAO,OAAO;AAC9B,SAAO,cAAc,MAAM,OAAO,OAAO,EAAE;AAC/C;AAEA,SAAS,QAAQ,QAAQ;AACrB,MAAI,SAAS,GAAG;AACZ,WAAO,KAAK,MAAM,MAAM;AAAA,EAC5B,OAAO;AACH,WAAO,KAAK,KAAK,MAAM;AAAA,EAC3B;AACJ;AAEA,SAAS,SAAS;AACd,MAAIoB,gBAAe,KAAK,eACpBF,QAAO,KAAK,OACZF,UAAS,KAAK,SACd,OAAO,KAAK,OACZG,UACAP,UACAD,QACAI,QACA;AAIJ,MACI,EACKK,iBAAgB,KAAKF,SAAQ,KAAKF,WAAU,KAC5CI,iBAAgB,KAAKF,SAAQ,KAAKF,WAAU,IAEnD;AACE,IAAAI,iBAAgB,QAAQ,aAAaJ,OAAM,IAAIE,KAAI,IAAI;AACvD,IAAAA,QAAO;AACP,IAAAF,UAAS;AAAA,EACb;AAIA,OAAK,eAAeI,gBAAe;AAEnC,EAAAD,WAAU,SAASC,gBAAe,GAAI;AACtC,OAAK,UAAUD,WAAU;AAEzB,EAAAP,WAAU,SAASO,WAAU,EAAE;AAC/B,OAAK,UAAUP,WAAU;AAEzB,EAAAD,SAAQ,SAASC,WAAU,EAAE;AAC7B,OAAK,QAAQD,SAAQ;AAErB,EAAAO,SAAQ,SAASP,SAAQ,EAAE;AAG3B,mBAAiB,SAAS,aAAaO,KAAI,CAAC;AAC5C,EAAAF,WAAU;AACV,EAAAE,SAAQ,QAAQ,aAAa,cAAc,CAAC;AAG5C,EAAAH,SAAQ,SAASC,UAAS,EAAE;AAC5B,EAAAA,WAAU;AAEV,OAAK,OAAOE;AACZ,OAAK,SAASF;AACd,OAAK,QAAQD;AAEb,SAAO;AACX;AAEA,SAAS,aAAaG,OAAM;AAGxB,SAAQA,QAAO,OAAQ;AAC3B;AAEA,SAAS,aAAaF,SAAQ;AAE1B,SAAQA,UAAS,SAAU;AAC/B;AAEA,SAAS,GAAG,OAAO;AACf,MAAI,CAAC,KAAK,QAAQ,GAAG;AACjB,WAAO;AAAA,EACX;AACA,MAAIE,OACAF,SACAI,gBAAe,KAAK;AAExB,UAAQ,eAAe,KAAK;AAE5B,MAAI,UAAU,WAAW,UAAU,aAAa,UAAU,QAAQ;AAC9D,IAAAF,QAAO,KAAK,QAAQE,gBAAe;AACnC,IAAAJ,UAAS,KAAK,UAAU,aAAaE,KAAI;AACzC,YAAQ,OAAO;AAAA,MACX,KAAK;AACD,eAAOF;AAAA,MACX,KAAK;AACD,eAAOA,UAAS;AAAA,MACpB,KAAK;AACD,eAAOA,UAAS;AAAA,IACxB;AAAA,EACJ,OAAO;AAEH,IAAAE,QAAO,KAAK,QAAQ,KAAK,MAAM,aAAa,KAAK,OAAO,CAAC;AACzD,YAAQ,OAAO;AAAA,MACX,KAAK;AACD,eAAOA,QAAO,IAAIE,gBAAe;AAAA,MACrC,KAAK;AACD,eAAOF,QAAOE,gBAAe;AAAA,MACjC,KAAK;AACD,eAAOF,QAAO,KAAKE,gBAAe;AAAA,MACtC,KAAK;AACD,eAAOF,QAAO,OAAOE,gBAAe;AAAA,MACxC,KAAK;AACD,eAAOF,QAAO,QAAQE,gBAAe;AAAA,MAEzC,KAAK;AACD,eAAO,KAAK,MAAMF,QAAO,KAAK,IAAIE;AAAA,MACtC;AACI,cAAM,IAAI,MAAM,kBAAkB,KAAK;AAAA,IAC/C;AAAA,EACJ;AACJ;AAEA,SAAS,OAAO,OAAO;AACnB,SAAO,WAAY;AACf,WAAO,KAAK,GAAG,KAAK;AAAA,EACxB;AACJ;AAEA,IAAI,iBAAiB,OAAO,IAAI;AAAhC,IACI,YAAY,OAAO,GAAG;AAD1B,IAEI,YAAY,OAAO,GAAG;AAF1B,IAGI,UAAU,OAAO,GAAG;AAHxB,IAII,SAAS,OAAO,GAAG;AAJvB,IAKI,UAAU,OAAO,GAAG;AALxB,IAMI,WAAW,OAAO,GAAG;AANzB,IAOI,aAAa,OAAO,GAAG;AAP3B,IAQI,UAAU,OAAO,GAAG;AARxB,IASI,YAAY;AAEhB,SAAS,UAAU;AACf,SAAO,eAAe,IAAI;AAC9B;AAEA,SAAS,MAAM,OAAO;AAClB,UAAQ,eAAe,KAAK;AAC5B,SAAO,KAAK,QAAQ,IAAI,KAAK,QAAQ,GAAG,EAAE,IAAI;AAClD;AAEA,SAAS,WAAW,MAAM;AACtB,SAAO,WAAY;AACf,WAAO,KAAK,QAAQ,IAAI,KAAK,MAAM,IAAI,IAAI;AAAA,EAC/C;AACJ;AAEA,IAAI,eAAe,WAAW,cAAc;AAA5C,IACI,UAAU,WAAW,SAAS;AADlC,IAEI,UAAU,WAAW,SAAS;AAFlC,IAGI,QAAQ,WAAW,OAAO;AAH9B,IAII,OAAO,WAAW,MAAM;AAJ5B,IAKI,SAAS,WAAW,QAAQ;AALhC,IAMI,QAAQ,WAAW,OAAO;AAE9B,SAAS,QAAQ;AACb,SAAO,SAAS,KAAK,KAAK,IAAI,CAAC;AACnC;AAEA,IAAI,QAAQ,KAAK;AAAjB,IACI,aAAa;AAAA,EACT,IAAI;AAAA;AAAA,EACJ,GAAG;AAAA;AAAA,EACH,GAAG;AAAA;AAAA,EACH,GAAG;AAAA;AAAA,EACH,GAAG;AAAA;AAAA,EACH,GAAG;AAAA;AAAA,EACH,GAAG;AAAA;AACP;AAGJ,SAAS,kBAAkB,QAAQ,QAAQ,eAAe,UAAUnB,SAAQ;AACxE,SAAOA,QAAO,aAAa,UAAU,GAAG,CAAC,CAAC,eAAe,QAAQ,QAAQ;AAC7E;AAEA,SAAS,eAAe,gBAAgB,eAAeqB,aAAYrB,SAAQ;AACvE,MAAI,WAAW,eAAe,cAAc,EAAE,IAAI,GAC9CkB,WAAU,MAAM,SAAS,GAAG,GAAG,CAAC,GAChCP,WAAU,MAAM,SAAS,GAAG,GAAG,CAAC,GAChCD,SAAQ,MAAM,SAAS,GAAG,GAAG,CAAC,GAC9BO,QAAO,MAAM,SAAS,GAAG,GAAG,CAAC,GAC7BF,UAAS,MAAM,SAAS,GAAG,GAAG,CAAC,GAC/BC,SAAQ,MAAM,SAAS,GAAG,GAAG,CAAC,GAC9BF,SAAQ,MAAM,SAAS,GAAG,GAAG,CAAC,GAC9B,IACKI,YAAWG,YAAW,MAAM,CAAC,KAAKH,QAAO,KACzCA,WAAUG,YAAW,KAAK,CAAC,MAAMH,QAAO,KACxCP,YAAW,KAAK,CAAC,GAAG,KACpBA,WAAUU,YAAW,KAAK,CAAC,MAAMV,QAAO,KACxCD,UAAS,KAAK,CAAC,GAAG,KAClBA,SAAQW,YAAW,KAAK,CAAC,MAAMX,MAAK,KACpCO,SAAQ,KAAK,CAAC,GAAG,KACjBA,QAAOI,YAAW,KAAK,CAAC,MAAMJ,KAAI;AAE3C,MAAII,YAAW,KAAK,MAAM;AACtB,QACI,KACCL,UAAS,KAAK,CAAC,GAAG,KAClBA,SAAQK,YAAW,KAAK,CAAC,MAAML,MAAK;AAAA,EAC7C;AACA,MAAI,KACCD,WAAU,KAAK,CAAC,GAAG,KACnBA,UAASM,YAAW,KAAK,CAAC,MAAMN,OAAM,KACtCD,UAAS,KAAK,CAAC,GAAG,KAAM,CAAC,MAAMA,MAAK;AAEzC,IAAE,CAAC,IAAI;AACP,IAAE,CAAC,IAAI,CAAC,iBAAiB;AACzB,IAAE,CAAC,IAAId;AACP,SAAO,kBAAkB,MAAM,MAAM,CAAC;AAC1C;AAGA,SAAS,2BAA2B,kBAAkB;AAClD,MAAI,qBAAqB,QAAW;AAChC,WAAO;AAAA,EACX;AACA,MAAI,OAAO,qBAAqB,YAAY;AACxC,YAAQ;AACR,WAAO;AAAA,EACX;AACA,SAAO;AACX;AAGA,SAAS,4BAA4B,WAAW,OAAO;AACnD,MAAI,WAAW,SAAS,MAAM,QAAW;AACrC,WAAO;AAAA,EACX;AACA,MAAI,UAAU,QAAW;AACrB,WAAO,WAAW,SAAS;AAAA,EAC/B;AACA,aAAW,SAAS,IAAI;AACxB,MAAI,cAAc,KAAK;AACnB,eAAW,KAAK,QAAQ;AAAA,EAC5B;AACA,SAAO;AACX;AAEA,SAAS,SAAS,eAAe,eAAe;AAC5C,MAAI,CAAC,KAAK,QAAQ,GAAG;AACjB,WAAO,KAAK,WAAW,EAAE,YAAY;AAAA,EACzC;AAEA,MAAI,aAAa,OACb,KAAK,YACLA,SACA;AAEJ,MAAI,OAAO,kBAAkB,UAAU;AACnC,oBAAgB;AAChB,oBAAgB;AAAA,EACpB;AACA,MAAI,OAAO,kBAAkB,WAAW;AACpC,iBAAa;AAAA,EACjB;AACA,MAAI,OAAO,kBAAkB,UAAU;AACnC,SAAK,OAAO,OAAO,CAAC,GAAG,YAAY,aAAa;AAChD,QAAI,cAAc,KAAK,QAAQ,cAAc,MAAM,MAAM;AACrD,SAAG,KAAK,cAAc,IAAI;AAAA,IAC9B;AAAA,EACJ;AAEA,EAAAA,UAAS,KAAK,WAAW;AACzB,WAAS,eAAe,MAAM,CAAC,YAAY,IAAIA,OAAM;AAErD,MAAI,YAAY;AACZ,aAASA,QAAO,WAAW,CAAC,MAAM,MAAM;AAAA,EAC5C;AAEA,SAAOA,QAAO,WAAW,MAAM;AACnC;AAEA,IAAI,QAAQ,KAAK;AAEjB,SAAS,KAAK,GAAG;AACb,UAAQ,IAAI,MAAM,IAAI,MAAM,CAAC;AACjC;AAEA,SAAS,gBAAgB;AAQrB,MAAI,CAAC,KAAK,QAAQ,GAAG;AACjB,WAAO,KAAK,WAAW,EAAE,YAAY;AAAA,EACzC;AAEA,MAAIkB,WAAU,MAAM,KAAK,aAAa,IAAI,KACtCD,QAAO,MAAM,KAAK,KAAK,GACvBF,UAAS,MAAM,KAAK,OAAO,GAC3BJ,UACAD,QACAI,QACA,GACA,QAAQ,KAAK,UAAU,GACvB,WACA,QACA,UACA;AAEJ,MAAI,CAAC,OAAO;AAGR,WAAO;AAAA,EACX;AAGA,EAAAH,WAAU,SAASO,WAAU,EAAE;AAC/B,EAAAR,SAAQ,SAASC,WAAU,EAAE;AAC7B,EAAAO,YAAW;AACX,EAAAP,YAAW;AAGX,EAAAG,SAAQ,SAASC,UAAS,EAAE;AAC5B,EAAAA,WAAU;AAGV,MAAIG,WAAUA,SAAQ,QAAQ,CAAC,EAAE,QAAQ,UAAU,EAAE,IAAI;AAEzD,cAAY,QAAQ,IAAI,MAAM;AAC9B,WAAS,KAAK,KAAK,OAAO,MAAM,KAAK,KAAK,IAAI,MAAM;AACpD,aAAW,KAAK,KAAK,KAAK,MAAM,KAAK,KAAK,IAAI,MAAM;AACpD,YAAU,KAAK,KAAK,aAAa,MAAM,KAAK,KAAK,IAAI,MAAM;AAE3D,SACI,YACA,OACCJ,SAAQ,SAASA,SAAQ,MAAM,OAC/BC,UAAS,SAASA,UAAS,MAAM,OACjCE,QAAO,WAAWA,QAAO,MAAM,OAC/BP,UAASC,YAAWO,WAAU,MAAM,OACpCR,SAAQ,UAAUA,SAAQ,MAAM,OAChCC,WAAU,UAAUA,WAAU,MAAM,OACpCO,WAAU,UAAU,IAAI,MAAM;AAEvC;AAEA,IAAI,UAAU,SAAS;AAEvB,QAAQ,UAAU;AAClB,QAAQ,MAAM;AACd,QAAQ,MAAM;AACd,QAAQ,WAAW;AACnB,QAAQ,KAAK;AACb,QAAQ,iBAAiB;AACzB,QAAQ,YAAY;AACpB,QAAQ,YAAY;AACpB,QAAQ,UAAU;AAClB,QAAQ,SAAS;AACjB,QAAQ,UAAU;AAClB,QAAQ,WAAW;AACnB,QAAQ,aAAa;AACrB,QAAQ,UAAU;AAClB,QAAQ,UAAU;AAClB,QAAQ,UAAU;AAClB,QAAQ,QAAQ;AAChB,QAAQ,MAAM;AACd,QAAQ,eAAe;AACvB,QAAQ,UAAU;AAClB,QAAQ,UAAU;AAClB,QAAQ,QAAQ;AAChB,QAAQ,OAAO;AACf,QAAQ,QAAQ;AAChB,QAAQ,SAAS;AACjB,QAAQ,QAAQ;AAChB,QAAQ,WAAW;AACnB,QAAQ,cAAc;AACtB,QAAQ,WAAW;AACnB,QAAQ,SAAS;AACjB,QAAQ,SAAS;AACjB,QAAQ,aAAa;AAErB,QAAQ,cAAc;AAAA,EAClB;AAAA,EACA;AACJ;AACA,QAAQ,OAAO;AAIf,eAAe,KAAK,GAAG,GAAG,MAAM;AAChC,eAAe,KAAK,GAAG,GAAG,SAAS;AAInC,cAAc,KAAK,WAAW;AAC9B,cAAc,KAAK,cAAc;AACjC,cAAc,KAAK,SAAU,OAAO,OAAO,QAAQ;AAC/C,SAAO,KAAK,IAAI,KAAK,WAAW,KAAK,IAAI,GAAI;AACjD,CAAC;AACD,cAAc,KAAK,SAAU,OAAO,OAAO,QAAQ;AAC/C,SAAO,KAAK,IAAI,KAAK,MAAM,KAAK,CAAC;AACrC,CAAC;AAID,MAAM,UAAU;AAEhB,gBAAgB,WAAW;AAE3B,MAAM,KAAK;AACX,MAAM,MAAM;AACZ,MAAM,MAAM;AACZ,MAAM,MAAM;AACZ,MAAM,MAAM;AACZ,MAAM,OAAO;AACb,MAAM,SAAS;AACf,MAAM,SAAS;AACf,MAAM,SAAS;AACf,MAAM,UAAU;AAChB,MAAM,WAAW;AACjB,MAAM,WAAW;AACjB,MAAM,WAAW;AACjB,MAAM,YAAY;AAClB,MAAM,aAAa;AACnB,MAAM,aAAa;AACnB,MAAM,cAAc;AACpB,MAAM,cAAc;AACpB,MAAM,eAAe;AACrB,MAAM,eAAe;AACrB,MAAM,UAAU;AAChB,MAAM,gBAAgB;AACtB,MAAM,iBAAiB;AACvB,MAAM,uBAAuB;AAC7B,MAAM,wBAAwB;AAC9B,MAAM,iBAAiB;AACvB,MAAM,YAAY;AAGlB,MAAM,YAAY;AAAA,EACd,gBAAgB;AAAA;AAAA,EAChB,wBAAwB;AAAA;AAAA,EACxB,mBAAmB;AAAA;AAAA,EACnB,MAAM;AAAA;AAAA,EACN,MAAM;AAAA;AAAA,EACN,cAAc;AAAA;AAAA,EACd,SAAS;AAAA;AAAA,EACT,MAAM;AAAA;AAAA,EACN,OAAO;AAAA;AACX;AAEA,IAAO,iBAAQ;", "names": ["format", "locale", "to", "from", "now", "sign", "token", "ordinal", "i", "diff", "localeData", "hours", "minutes", "tokens", "meridiem", "years", "months", "weeks", "days", "seconds", "milliseconds", "offset", "thresholds"]}