{"version": 3, "sources": ["../../rbush/rbush.js"], "sourcesContent": ["/*\n (c) 2015, <PERSON>, a JavaScript library for high-performance 2D spatial indexing of points and rectangles.\n https://github.com/mourner/rbush\n*/\n\n(function () {\n'use strict';\n\nfunction rbush(maxEntries, format) {\n    if (!(this instanceof rbush)) return new rbush(maxEntries, format);\n\n    // max entries in a node is 9 by default; min node fill is 40% for best performance\n    this._maxEntries = Math.max(4, maxEntries || 9);\n    this._minEntries = Math.max(2, Math.ceil(this._maxEntries * 0.4));\n\n    if (format) {\n        this._initFormat(format);\n    }\n\n    this.clear();\n}\n\nrbush.prototype = {\n\n    all: function () {\n        return this._all(this.data, []);\n    },\n\n    search: function (bbox) {\n\n        var node = this.data,\n            result = [],\n            toBBox = this.toBBox;\n\n        if (!intersects(bbox, node.bbox)) return result;\n\n        var nodesToSearch = [],\n            i, len, child, childBBox;\n\n        while (node) {\n            for (i = 0, len = node.children.length; i < len; i++) {\n\n                child = node.children[i];\n                childBBox = node.leaf ? toBBox(child) : child.bbox;\n\n                if (intersects(bbox, childBBox)) {\n                    if (node.leaf) result.push(child);\n                    else if (contains(bbox, childBBox)) this._all(child, result);\n                    else nodesToSearch.push(child);\n                }\n            }\n            node = nodesToSearch.pop();\n        }\n\n        return result;\n    },\n\n    collides: function (bbox) {\n\n        var node = this.data,\n            toBBox = this.toBBox;\n\n        if (!intersects(bbox, node.bbox)) return false;\n\n        var nodesToSearch = [],\n            i, len, child, childBBox;\n\n        while (node) {\n            for (i = 0, len = node.children.length; i < len; i++) {\n\n                child = node.children[i];\n                childBBox = node.leaf ? toBBox(child) : child.bbox;\n\n                if (intersects(bbox, childBBox)) {\n                    if (node.leaf || contains(bbox, childBBox)) return true;\n                    nodesToSearch.push(child);\n                }\n            }\n            node = nodesToSearch.pop();\n        }\n\n        return false;\n    },\n\n    load: function (data) {\n        if (!(data && data.length)) return this;\n\n        if (data.length < this._minEntries) {\n            for (var i = 0, len = data.length; i < len; i++) {\n                this.insert(data[i]);\n            }\n            return this;\n        }\n\n        // recursively build the tree with the given data from stratch using OMT algorithm\n        var node = this._build(data.slice(), 0, data.length - 1, 0);\n\n        if (!this.data.children.length) {\n            // save as is if tree is empty\n            this.data = node;\n\n        } else if (this.data.height === node.height) {\n            // split root if trees have the same height\n            this._splitRoot(this.data, node);\n\n        } else {\n            if (this.data.height < node.height) {\n                // swap trees if inserted one is bigger\n                var tmpNode = this.data;\n                this.data = node;\n                node = tmpNode;\n            }\n\n            // insert the small tree into the large tree at appropriate level\n            this._insert(node, this.data.height - node.height - 1, true);\n        }\n\n        return this;\n    },\n\n    insert: function (item) {\n        if (item) this._insert(item, this.data.height - 1);\n        return this;\n    },\n\n    clear: function () {\n        this.data = {\n            children: [],\n            height: 1,\n            bbox: empty(),\n            leaf: true\n        };\n        return this;\n    },\n\n    remove: function (item) {\n        if (!item) return this;\n\n        var node = this.data,\n            bbox = this.toBBox(item),\n            path = [],\n            indexes = [],\n            i, parent, index, goingUp;\n\n        // depth-first iterative tree traversal\n        while (node || path.length) {\n\n            if (!node) { // go up\n                node = path.pop();\n                parent = path[path.length - 1];\n                i = indexes.pop();\n                goingUp = true;\n            }\n\n            if (node.leaf) { // check current node\n                index = node.children.indexOf(item);\n\n                if (index !== -1) {\n                    // item found, remove the item and condense tree upwards\n                    node.children.splice(index, 1);\n                    path.push(node);\n                    this._condense(path);\n                    return this;\n                }\n            }\n\n            if (!goingUp && !node.leaf && contains(node.bbox, bbox)) { // go down\n                path.push(node);\n                indexes.push(i);\n                i = 0;\n                parent = node;\n                node = node.children[0];\n\n            } else if (parent) { // go right\n                i++;\n                node = parent.children[i];\n                goingUp = false;\n\n            } else node = null; // nothing found\n        }\n\n        return this;\n    },\n\n    toBBox: function (item) { return item; },\n\n    compareMinX: function (a, b) { return a[0] - b[0]; },\n    compareMinY: function (a, b) { return a[1] - b[1]; },\n\n    toJSON: function () { return this.data; },\n\n    fromJSON: function (data) {\n        this.data = data;\n        return this;\n    },\n\n    _all: function (node, result) {\n        var nodesToSearch = [];\n        while (node) {\n            if (node.leaf) result.push.apply(result, node.children);\n            else nodesToSearch.push.apply(nodesToSearch, node.children);\n\n            node = nodesToSearch.pop();\n        }\n        return result;\n    },\n\n    _build: function (items, left, right, height) {\n\n        var N = right - left + 1,\n            M = this._maxEntries,\n            node;\n\n        if (N <= M) {\n            // reached leaf level; return leaf\n            node = {\n                children: items.slice(left, right + 1),\n                height: 1,\n                bbox: null,\n                leaf: true\n            };\n            calcBBox(node, this.toBBox);\n            return node;\n        }\n\n        if (!height) {\n            // target height of the bulk-loaded tree\n            height = Math.ceil(Math.log(N) / Math.log(M));\n\n            // target number of root entries to maximize storage utilization\n            M = Math.ceil(N / Math.pow(M, height - 1));\n        }\n\n        node = {\n            children: [],\n            height: height,\n            bbox: null,\n            leaf: false\n        };\n\n        // split the items into M mostly square tiles\n\n        var N2 = Math.ceil(N / M),\n            N1 = N2 * Math.ceil(Math.sqrt(M)),\n            i, j, right2, right3;\n\n        multiSelect(items, left, right, N1, this.compareMinX);\n\n        for (i = left; i <= right; i += N1) {\n\n            right2 = Math.min(i + N1 - 1, right);\n\n            multiSelect(items, i, right2, N2, this.compareMinY);\n\n            for (j = i; j <= right2; j += N2) {\n\n                right3 = Math.min(j + N2 - 1, right2);\n\n                // pack each entry recursively\n                node.children.push(this._build(items, j, right3, height - 1));\n            }\n        }\n\n        calcBBox(node, this.toBBox);\n\n        return node;\n    },\n\n    _chooseSubtree: function (bbox, node, level, path) {\n\n        var i, len, child, targetNode, area, enlargement, minArea, minEnlargement;\n\n        while (true) {\n            path.push(node);\n\n            if (node.leaf || path.length - 1 === level) break;\n\n            minArea = minEnlargement = Infinity;\n\n            for (i = 0, len = node.children.length; i < len; i++) {\n                child = node.children[i];\n                area = bboxArea(child.bbox);\n                enlargement = enlargedArea(bbox, child.bbox) - area;\n\n                // choose entry with the least area enlargement\n                if (enlargement < minEnlargement) {\n                    minEnlargement = enlargement;\n                    minArea = area < minArea ? area : minArea;\n                    targetNode = child;\n\n                } else if (enlargement === minEnlargement) {\n                    // otherwise choose one with the smallest area\n                    if (area < minArea) {\n                        minArea = area;\n                        targetNode = child;\n                    }\n                }\n            }\n\n            node = targetNode || node.children[0];\n        }\n\n        return node;\n    },\n\n    _insert: function (item, level, isNode) {\n\n        var toBBox = this.toBBox,\n            bbox = isNode ? item.bbox : toBBox(item),\n            insertPath = [];\n\n        // find the best node for accommodating the item, saving all nodes along the path too\n        var node = this._chooseSubtree(bbox, this.data, level, insertPath);\n\n        // put the item into the node\n        node.children.push(item);\n        extend(node.bbox, bbox);\n\n        // split on node overflow; propagate upwards if necessary\n        while (level >= 0) {\n            if (insertPath[level].children.length > this._maxEntries) {\n                this._split(insertPath, level);\n                level--;\n            } else break;\n        }\n\n        // adjust bboxes along the insertion path\n        this._adjustParentBBoxes(bbox, insertPath, level);\n    },\n\n    // split overflowed node into two\n    _split: function (insertPath, level) {\n\n        var node = insertPath[level],\n            M = node.children.length,\n            m = this._minEntries;\n\n        this._chooseSplitAxis(node, m, M);\n\n        var splitIndex = this._chooseSplitIndex(node, m, M);\n\n        var newNode = {\n            children: node.children.splice(splitIndex, node.children.length - splitIndex),\n            height: node.height,\n            bbox: null,\n            leaf: false\n        };\n\n        if (node.leaf) newNode.leaf = true;\n\n        calcBBox(node, this.toBBox);\n        calcBBox(newNode, this.toBBox);\n\n        if (level) insertPath[level - 1].children.push(newNode);\n        else this._splitRoot(node, newNode);\n    },\n\n    _splitRoot: function (node, newNode) {\n        // split root node\n        this.data = {\n            children: [node, newNode],\n            height: node.height + 1,\n            bbox: null,\n            leaf: false\n        };\n        calcBBox(this.data, this.toBBox);\n    },\n\n    _chooseSplitIndex: function (node, m, M) {\n\n        var i, bbox1, bbox2, overlap, area, minOverlap, minArea, index;\n\n        minOverlap = minArea = Infinity;\n\n        for (i = m; i <= M - m; i++) {\n            bbox1 = distBBox(node, 0, i, this.toBBox);\n            bbox2 = distBBox(node, i, M, this.toBBox);\n\n            overlap = intersectionArea(bbox1, bbox2);\n            area = bboxArea(bbox1) + bboxArea(bbox2);\n\n            // choose distribution with minimum overlap\n            if (overlap < minOverlap) {\n                minOverlap = overlap;\n                index = i;\n\n                minArea = area < minArea ? area : minArea;\n\n            } else if (overlap === minOverlap) {\n                // otherwise choose distribution with minimum area\n                if (area < minArea) {\n                    minArea = area;\n                    index = i;\n                }\n            }\n        }\n\n        return index;\n    },\n\n    // sorts node children by the best axis for split\n    _chooseSplitAxis: function (node, m, M) {\n\n        var compareMinX = node.leaf ? this.compareMinX : compareNodeMinX,\n            compareMinY = node.leaf ? this.compareMinY : compareNodeMinY,\n            xMargin = this._allDistMargin(node, m, M, compareMinX),\n            yMargin = this._allDistMargin(node, m, M, compareMinY);\n\n        // if total distributions margin value is minimal for x, sort by minX,\n        // otherwise it's already sorted by minY\n        if (xMargin < yMargin) node.children.sort(compareMinX);\n    },\n\n    // total margin of all possible split distributions where each node is at least m full\n    _allDistMargin: function (node, m, M, compare) {\n\n        node.children.sort(compare);\n\n        var toBBox = this.toBBox,\n            leftBBox = distBBox(node, 0, m, toBBox),\n            rightBBox = distBBox(node, M - m, M, toBBox),\n            margin = bboxMargin(leftBBox) + bboxMargin(rightBBox),\n            i, child;\n\n        for (i = m; i < M - m; i++) {\n            child = node.children[i];\n            extend(leftBBox, node.leaf ? toBBox(child) : child.bbox);\n            margin += bboxMargin(leftBBox);\n        }\n\n        for (i = M - m - 1; i >= m; i--) {\n            child = node.children[i];\n            extend(rightBBox, node.leaf ? toBBox(child) : child.bbox);\n            margin += bboxMargin(rightBBox);\n        }\n\n        return margin;\n    },\n\n    _adjustParentBBoxes: function (bbox, path, level) {\n        // adjust bboxes along the given tree path\n        for (var i = level; i >= 0; i--) {\n            extend(path[i].bbox, bbox);\n        }\n    },\n\n    _condense: function (path) {\n        // go through the path, removing empty nodes and updating bboxes\n        for (var i = path.length - 1, siblings; i >= 0; i--) {\n            if (path[i].children.length === 0) {\n                if (i > 0) {\n                    siblings = path[i - 1].children;\n                    siblings.splice(siblings.indexOf(path[i]), 1);\n\n                } else this.clear();\n\n            } else calcBBox(path[i], this.toBBox);\n        }\n    },\n\n    _initFormat: function (format) {\n        // data format (minX, minY, maxX, maxY accessors)\n\n        // uses eval-type function compilation instead of just accepting a toBBox function\n        // because the algorithms are very sensitive to sorting functions performance,\n        // so they should be dead simple and without inner calls\n\n        var compareArr = ['return a', ' - b', ';'];\n\n        this.compareMinX = new Function('a', 'b', compareArr.join(format[0]));\n        this.compareMinY = new Function('a', 'b', compareArr.join(format[1]));\n\n        this.toBBox = new Function('a', 'return [a' + format.join(', a') + '];');\n    }\n};\n\n\n// calculate node's bbox from bboxes of its children\nfunction calcBBox(node, toBBox) {\n    node.bbox = distBBox(node, 0, node.children.length, toBBox);\n}\n\n// min bounding rectangle of node children from k to p-1\nfunction distBBox(node, k, p, toBBox) {\n    var bbox = empty();\n\n    for (var i = k, child; i < p; i++) {\n        child = node.children[i];\n        extend(bbox, node.leaf ? toBBox(child) : child.bbox);\n    }\n\n    return bbox;\n}\n\nfunction empty() { return [Infinity, Infinity, -Infinity, -Infinity]; }\n\nfunction extend(a, b) {\n    a[0] = Math.min(a[0], b[0]);\n    a[1] = Math.min(a[1], b[1]);\n    a[2] = Math.max(a[2], b[2]);\n    a[3] = Math.max(a[3], b[3]);\n    return a;\n}\n\nfunction compareNodeMinX(a, b) { return a.bbox[0] - b.bbox[0]; }\nfunction compareNodeMinY(a, b) { return a.bbox[1] - b.bbox[1]; }\n\nfunction bboxArea(a)   { return (a[2] - a[0]) * (a[3] - a[1]); }\nfunction bboxMargin(a) { return (a[2] - a[0]) + (a[3] - a[1]); }\n\nfunction enlargedArea(a, b) {\n    return (Math.max(b[2], a[2]) - Math.min(b[0], a[0])) *\n           (Math.max(b[3], a[3]) - Math.min(b[1], a[1]));\n}\n\nfunction intersectionArea(a, b) {\n    var minX = Math.max(a[0], b[0]),\n        minY = Math.max(a[1], b[1]),\n        maxX = Math.min(a[2], b[2]),\n        maxY = Math.min(a[3], b[3]);\n\n    return Math.max(0, maxX - minX) *\n           Math.max(0, maxY - minY);\n}\n\nfunction contains(a, b) {\n    return a[0] <= b[0] &&\n           a[1] <= b[1] &&\n           b[2] <= a[2] &&\n           b[3] <= a[3];\n}\n\nfunction intersects(a, b) {\n    return b[0] <= a[2] &&\n           b[1] <= a[3] &&\n           b[2] >= a[0] &&\n           b[3] >= a[1];\n}\n\n// sort an array so that items come in groups of n unsorted items, with groups sorted between each other;\n// combines selection algorithm with binary divide & conquer approach\n\nfunction multiSelect(arr, left, right, n, compare) {\n    var stack = [left, right],\n        mid;\n\n    while (stack.length) {\n        right = stack.pop();\n        left = stack.pop();\n\n        if (right - left <= n) continue;\n\n        mid = left + Math.ceil((right - left) / n / 2) * n;\n        select(arr, left, right, mid, compare);\n\n        stack.push(left, mid, mid, right);\n    }\n}\n\n// Floyd-Rivest selection algorithm:\n// sort an array between left and right (inclusive) so that the smallest k elements come first (unordered)\nfunction select(arr, left, right, k, compare) {\n    var n, i, z, s, sd, newLeft, newRight, t, j;\n\n    while (right > left) {\n        if (right - left > 600) {\n            n = right - left + 1;\n            i = k - left + 1;\n            z = Math.log(n);\n            s = 0.5 * Math.exp(2 * z / 3);\n            sd = 0.5 * Math.sqrt(z * s * (n - s) / n) * (i - n / 2 < 0 ? -1 : 1);\n            newLeft = Math.max(left, Math.floor(k - i * s / n + sd));\n            newRight = Math.min(right, Math.floor(k + (n - i) * s / n + sd));\n            select(arr, newLeft, newRight, k, compare);\n        }\n\n        t = arr[k];\n        i = left;\n        j = right;\n\n        swap(arr, left, k);\n        if (compare(arr[right], t) > 0) swap(arr, left, right);\n\n        while (i < j) {\n            swap(arr, i, j);\n            i++;\n            j--;\n            while (compare(arr[i], t) < 0) i++;\n            while (compare(arr[j], t) > 0) j--;\n        }\n\n        if (compare(arr[left], t) === 0) swap(arr, left, j);\n        else {\n            j++;\n            swap(arr, j, right);\n        }\n\n        if (j <= k) left = j + 1;\n        if (k <= j) right = j - 1;\n    }\n}\n\nfunction swap(arr, i, j) {\n    var tmp = arr[i];\n    arr[i] = arr[j];\n    arr[j] = tmp;\n}\n\n\n// export as AMD/CommonJS module or global variable\nif (typeof define === 'function' && define.amd) define('rbush', function () { return rbush; });\nelse if (typeof module !== 'undefined') module.exports = rbush;\nelse if (typeof self !== 'undefined') self.rbush = rbush;\nelse window.rbush = rbush;\n\n})();\n"], "mappings": ";;;;;AAAA;AAAA;AAMA,KAAC,WAAY;AACb;AAEA,eAAS,MAAM,YAAY,QAAQ;AAC/B,YAAI,EAAE,gBAAgB;AAAQ,iBAAO,IAAI,MAAM,YAAY,MAAM;AAGjE,aAAK,cAAc,KAAK,IAAI,GAAG,cAAc,CAAC;AAC9C,aAAK,cAAc,KAAK,IAAI,GAAG,KAAK,KAAK,KAAK,cAAc,GAAG,CAAC;AAEhE,YAAI,QAAQ;AACR,eAAK,YAAY,MAAM;AAAA,QAC3B;AAEA,aAAK,MAAM;AAAA,MACf;AAEA,YAAM,YAAY;AAAA,QAEd,KAAK,WAAY;AACb,iBAAO,KAAK,KAAK,KAAK,MAAM,CAAC,CAAC;AAAA,QAClC;AAAA,QAEA,QAAQ,SAAU,MAAM;AAEpB,cAAI,OAAO,KAAK,MACZ,SAAS,CAAC,GACV,SAAS,KAAK;AAElB,cAAI,CAAC,WAAW,MAAM,KAAK,IAAI;AAAG,mBAAO;AAEzC,cAAI,gBAAgB,CAAC,GACjB,GAAG,KAAK,OAAO;AAEnB,iBAAO,MAAM;AACT,iBAAK,IAAI,GAAG,MAAM,KAAK,SAAS,QAAQ,IAAI,KAAK,KAAK;AAElD,sBAAQ,KAAK,SAAS,CAAC;AACvB,0BAAY,KAAK,OAAO,OAAO,KAAK,IAAI,MAAM;AAE9C,kBAAI,WAAW,MAAM,SAAS,GAAG;AAC7B,oBAAI,KAAK;AAAM,yBAAO,KAAK,KAAK;AAAA,yBACvB,SAAS,MAAM,SAAS;AAAG,uBAAK,KAAK,OAAO,MAAM;AAAA;AACtD,gCAAc,KAAK,KAAK;AAAA,cACjC;AAAA,YACJ;AACA,mBAAO,cAAc,IAAI;AAAA,UAC7B;AAEA,iBAAO;AAAA,QACX;AAAA,QAEA,UAAU,SAAU,MAAM;AAEtB,cAAI,OAAO,KAAK,MACZ,SAAS,KAAK;AAElB,cAAI,CAAC,WAAW,MAAM,KAAK,IAAI;AAAG,mBAAO;AAEzC,cAAI,gBAAgB,CAAC,GACjB,GAAG,KAAK,OAAO;AAEnB,iBAAO,MAAM;AACT,iBAAK,IAAI,GAAG,MAAM,KAAK,SAAS,QAAQ,IAAI,KAAK,KAAK;AAElD,sBAAQ,KAAK,SAAS,CAAC;AACvB,0BAAY,KAAK,OAAO,OAAO,KAAK,IAAI,MAAM;AAE9C,kBAAI,WAAW,MAAM,SAAS,GAAG;AAC7B,oBAAI,KAAK,QAAQ,SAAS,MAAM,SAAS;AAAG,yBAAO;AACnD,8BAAc,KAAK,KAAK;AAAA,cAC5B;AAAA,YACJ;AACA,mBAAO,cAAc,IAAI;AAAA,UAC7B;AAEA,iBAAO;AAAA,QACX;AAAA,QAEA,MAAM,SAAU,MAAM;AAClB,cAAI,EAAE,QAAQ,KAAK;AAAS,mBAAO;AAEnC,cAAI,KAAK,SAAS,KAAK,aAAa;AAChC,qBAAS,IAAI,GAAG,MAAM,KAAK,QAAQ,IAAI,KAAK,KAAK;AAC7C,mBAAK,OAAO,KAAK,CAAC,CAAC;AAAA,YACvB;AACA,mBAAO;AAAA,UACX;AAGA,cAAI,OAAO,KAAK,OAAO,KAAK,MAAM,GAAG,GAAG,KAAK,SAAS,GAAG,CAAC;AAE1D,cAAI,CAAC,KAAK,KAAK,SAAS,QAAQ;AAE5B,iBAAK,OAAO;AAAA,UAEhB,WAAW,KAAK,KAAK,WAAW,KAAK,QAAQ;AAEzC,iBAAK,WAAW,KAAK,MAAM,IAAI;AAAA,UAEnC,OAAO;AACH,gBAAI,KAAK,KAAK,SAAS,KAAK,QAAQ;AAEhC,kBAAI,UAAU,KAAK;AACnB,mBAAK,OAAO;AACZ,qBAAO;AAAA,YACX;AAGA,iBAAK,QAAQ,MAAM,KAAK,KAAK,SAAS,KAAK,SAAS,GAAG,IAAI;AAAA,UAC/D;AAEA,iBAAO;AAAA,QACX;AAAA,QAEA,QAAQ,SAAU,MAAM;AACpB,cAAI;AAAM,iBAAK,QAAQ,MAAM,KAAK,KAAK,SAAS,CAAC;AACjD,iBAAO;AAAA,QACX;AAAA,QAEA,OAAO,WAAY;AACf,eAAK,OAAO;AAAA,YACR,UAAU,CAAC;AAAA,YACX,QAAQ;AAAA,YACR,MAAM,MAAM;AAAA,YACZ,MAAM;AAAA,UACV;AACA,iBAAO;AAAA,QACX;AAAA,QAEA,QAAQ,SAAU,MAAM;AACpB,cAAI,CAAC;AAAM,mBAAO;AAElB,cAAI,OAAO,KAAK,MACZ,OAAO,KAAK,OAAO,IAAI,GACvB,OAAO,CAAC,GACR,UAAU,CAAC,GACX,GAAG,QAAQ,OAAO;AAGtB,iBAAO,QAAQ,KAAK,QAAQ;AAExB,gBAAI,CAAC,MAAM;AACP,qBAAO,KAAK,IAAI;AAChB,uBAAS,KAAK,KAAK,SAAS,CAAC;AAC7B,kBAAI,QAAQ,IAAI;AAChB,wBAAU;AAAA,YACd;AAEA,gBAAI,KAAK,MAAM;AACX,sBAAQ,KAAK,SAAS,QAAQ,IAAI;AAElC,kBAAI,UAAU,IAAI;AAEd,qBAAK,SAAS,OAAO,OAAO,CAAC;AAC7B,qBAAK,KAAK,IAAI;AACd,qBAAK,UAAU,IAAI;AACnB,uBAAO;AAAA,cACX;AAAA,YACJ;AAEA,gBAAI,CAAC,WAAW,CAAC,KAAK,QAAQ,SAAS,KAAK,MAAM,IAAI,GAAG;AACrD,mBAAK,KAAK,IAAI;AACd,sBAAQ,KAAK,CAAC;AACd,kBAAI;AACJ,uBAAS;AACT,qBAAO,KAAK,SAAS,CAAC;AAAA,YAE1B,WAAW,QAAQ;AACf;AACA,qBAAO,OAAO,SAAS,CAAC;AACxB,wBAAU;AAAA,YAEd;AAAO,qBAAO;AAAA,UAClB;AAEA,iBAAO;AAAA,QACX;AAAA,QAEA,QAAQ,SAAU,MAAM;AAAE,iBAAO;AAAA,QAAM;AAAA,QAEvC,aAAa,SAAU,GAAG,GAAG;AAAE,iBAAO,EAAE,CAAC,IAAI,EAAE,CAAC;AAAA,QAAG;AAAA,QACnD,aAAa,SAAU,GAAG,GAAG;AAAE,iBAAO,EAAE,CAAC,IAAI,EAAE,CAAC;AAAA,QAAG;AAAA,QAEnD,QAAQ,WAAY;AAAE,iBAAO,KAAK;AAAA,QAAM;AAAA,QAExC,UAAU,SAAU,MAAM;AACtB,eAAK,OAAO;AACZ,iBAAO;AAAA,QACX;AAAA,QAEA,MAAM,SAAU,MAAM,QAAQ;AAC1B,cAAI,gBAAgB,CAAC;AACrB,iBAAO,MAAM;AACT,gBAAI,KAAK;AAAM,qBAAO,KAAK,MAAM,QAAQ,KAAK,QAAQ;AAAA;AACjD,4BAAc,KAAK,MAAM,eAAe,KAAK,QAAQ;AAE1D,mBAAO,cAAc,IAAI;AAAA,UAC7B;AACA,iBAAO;AAAA,QACX;AAAA,QAEA,QAAQ,SAAU,OAAO,MAAM,OAAO,QAAQ;AAE1C,cAAI,IAAI,QAAQ,OAAO,GACnB,IAAI,KAAK,aACT;AAEJ,cAAI,KAAK,GAAG;AAER,mBAAO;AAAA,cACH,UAAU,MAAM,MAAM,MAAM,QAAQ,CAAC;AAAA,cACrC,QAAQ;AAAA,cACR,MAAM;AAAA,cACN,MAAM;AAAA,YACV;AACA,qBAAS,MAAM,KAAK,MAAM;AAC1B,mBAAO;AAAA,UACX;AAEA,cAAI,CAAC,QAAQ;AAET,qBAAS,KAAK,KAAK,KAAK,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC;AAG5C,gBAAI,KAAK,KAAK,IAAI,KAAK,IAAI,GAAG,SAAS,CAAC,CAAC;AAAA,UAC7C;AAEA,iBAAO;AAAA,YACH,UAAU,CAAC;AAAA,YACX;AAAA,YACA,MAAM;AAAA,YACN,MAAM;AAAA,UACV;AAIA,cAAI,KAAK,KAAK,KAAK,IAAI,CAAC,GACpB,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,CAAC,CAAC,GAChC,GAAG,GAAG,QAAQ;AAElB,sBAAY,OAAO,MAAM,OAAO,IAAI,KAAK,WAAW;AAEpD,eAAK,IAAI,MAAM,KAAK,OAAO,KAAK,IAAI;AAEhC,qBAAS,KAAK,IAAI,IAAI,KAAK,GAAG,KAAK;AAEnC,wBAAY,OAAO,GAAG,QAAQ,IAAI,KAAK,WAAW;AAElD,iBAAK,IAAI,GAAG,KAAK,QAAQ,KAAK,IAAI;AAE9B,uBAAS,KAAK,IAAI,IAAI,KAAK,GAAG,MAAM;AAGpC,mBAAK,SAAS,KAAK,KAAK,OAAO,OAAO,GAAG,QAAQ,SAAS,CAAC,CAAC;AAAA,YAChE;AAAA,UACJ;AAEA,mBAAS,MAAM,KAAK,MAAM;AAE1B,iBAAO;AAAA,QACX;AAAA,QAEA,gBAAgB,SAAU,MAAM,MAAM,OAAO,MAAM;AAE/C,cAAI,GAAG,KAAK,OAAO,YAAY,MAAM,aAAa,SAAS;AAE3D,iBAAO,MAAM;AACT,iBAAK,KAAK,IAAI;AAEd,gBAAI,KAAK,QAAQ,KAAK,SAAS,MAAM;AAAO;AAE5C,sBAAU,iBAAiB;AAE3B,iBAAK,IAAI,GAAG,MAAM,KAAK,SAAS,QAAQ,IAAI,KAAK,KAAK;AAClD,sBAAQ,KAAK,SAAS,CAAC;AACvB,qBAAO,SAAS,MAAM,IAAI;AAC1B,4BAAc,aAAa,MAAM,MAAM,IAAI,IAAI;AAG/C,kBAAI,cAAc,gBAAgB;AAC9B,iCAAiB;AACjB,0BAAU,OAAO,UAAU,OAAO;AAClC,6BAAa;AAAA,cAEjB,WAAW,gBAAgB,gBAAgB;AAEvC,oBAAI,OAAO,SAAS;AAChB,4BAAU;AACV,+BAAa;AAAA,gBACjB;AAAA,cACJ;AAAA,YACJ;AAEA,mBAAO,cAAc,KAAK,SAAS,CAAC;AAAA,UACxC;AAEA,iBAAO;AAAA,QACX;AAAA,QAEA,SAAS,SAAU,MAAM,OAAO,QAAQ;AAEpC,cAAI,SAAS,KAAK,QACd,OAAO,SAAS,KAAK,OAAO,OAAO,IAAI,GACvC,aAAa,CAAC;AAGlB,cAAI,OAAO,KAAK,eAAe,MAAM,KAAK,MAAM,OAAO,UAAU;AAGjE,eAAK,SAAS,KAAK,IAAI;AACvB,iBAAO,KAAK,MAAM,IAAI;AAGtB,iBAAO,SAAS,GAAG;AACf,gBAAI,WAAW,KAAK,EAAE,SAAS,SAAS,KAAK,aAAa;AACtD,mBAAK,OAAO,YAAY,KAAK;AAC7B;AAAA,YACJ;AAAO;AAAA,UACX;AAGA,eAAK,oBAAoB,MAAM,YAAY,KAAK;AAAA,QACpD;AAAA;AAAA,QAGA,QAAQ,SAAU,YAAY,OAAO;AAEjC,cAAI,OAAO,WAAW,KAAK,GACvB,IAAI,KAAK,SAAS,QAClB,IAAI,KAAK;AAEb,eAAK,iBAAiB,MAAM,GAAG,CAAC;AAEhC,cAAI,aAAa,KAAK,kBAAkB,MAAM,GAAG,CAAC;AAElD,cAAI,UAAU;AAAA,YACV,UAAU,KAAK,SAAS,OAAO,YAAY,KAAK,SAAS,SAAS,UAAU;AAAA,YAC5E,QAAQ,KAAK;AAAA,YACb,MAAM;AAAA,YACN,MAAM;AAAA,UACV;AAEA,cAAI,KAAK;AAAM,oBAAQ,OAAO;AAE9B,mBAAS,MAAM,KAAK,MAAM;AAC1B,mBAAS,SAAS,KAAK,MAAM;AAE7B,cAAI;AAAO,uBAAW,QAAQ,CAAC,EAAE,SAAS,KAAK,OAAO;AAAA;AACjD,iBAAK,WAAW,MAAM,OAAO;AAAA,QACtC;AAAA,QAEA,YAAY,SAAU,MAAM,SAAS;AAEjC,eAAK,OAAO;AAAA,YACR,UAAU,CAAC,MAAM,OAAO;AAAA,YACxB,QAAQ,KAAK,SAAS;AAAA,YACtB,MAAM;AAAA,YACN,MAAM;AAAA,UACV;AACA,mBAAS,KAAK,MAAM,KAAK,MAAM;AAAA,QACnC;AAAA,QAEA,mBAAmB,SAAU,MAAM,GAAG,GAAG;AAErC,cAAI,GAAG,OAAO,OAAO,SAAS,MAAM,YAAY,SAAS;AAEzD,uBAAa,UAAU;AAEvB,eAAK,IAAI,GAAG,KAAK,IAAI,GAAG,KAAK;AACzB,oBAAQ,SAAS,MAAM,GAAG,GAAG,KAAK,MAAM;AACxC,oBAAQ,SAAS,MAAM,GAAG,GAAG,KAAK,MAAM;AAExC,sBAAU,iBAAiB,OAAO,KAAK;AACvC,mBAAO,SAAS,KAAK,IAAI,SAAS,KAAK;AAGvC,gBAAI,UAAU,YAAY;AACtB,2BAAa;AACb,sBAAQ;AAER,wBAAU,OAAO,UAAU,OAAO;AAAA,YAEtC,WAAW,YAAY,YAAY;AAE/B,kBAAI,OAAO,SAAS;AAChB,0BAAU;AACV,wBAAQ;AAAA,cACZ;AAAA,YACJ;AAAA,UACJ;AAEA,iBAAO;AAAA,QACX;AAAA;AAAA,QAGA,kBAAkB,SAAU,MAAM,GAAG,GAAG;AAEpC,cAAI,cAAc,KAAK,OAAO,KAAK,cAAc,iBAC7C,cAAc,KAAK,OAAO,KAAK,cAAc,iBAC7C,UAAU,KAAK,eAAe,MAAM,GAAG,GAAG,WAAW,GACrD,UAAU,KAAK,eAAe,MAAM,GAAG,GAAG,WAAW;AAIzD,cAAI,UAAU;AAAS,iBAAK,SAAS,KAAK,WAAW;AAAA,QACzD;AAAA;AAAA,QAGA,gBAAgB,SAAU,MAAM,GAAG,GAAG,SAAS;AAE3C,eAAK,SAAS,KAAK,OAAO;AAE1B,cAAI,SAAS,KAAK,QACd,WAAW,SAAS,MAAM,GAAG,GAAG,MAAM,GACtC,YAAY,SAAS,MAAM,IAAI,GAAG,GAAG,MAAM,GAC3C,SAAS,WAAW,QAAQ,IAAI,WAAW,SAAS,GACpD,GAAG;AAEP,eAAK,IAAI,GAAG,IAAI,IAAI,GAAG,KAAK;AACxB,oBAAQ,KAAK,SAAS,CAAC;AACvB,mBAAO,UAAU,KAAK,OAAO,OAAO,KAAK,IAAI,MAAM,IAAI;AACvD,sBAAU,WAAW,QAAQ;AAAA,UACjC;AAEA,eAAK,IAAI,IAAI,IAAI,GAAG,KAAK,GAAG,KAAK;AAC7B,oBAAQ,KAAK,SAAS,CAAC;AACvB,mBAAO,WAAW,KAAK,OAAO,OAAO,KAAK,IAAI,MAAM,IAAI;AACxD,sBAAU,WAAW,SAAS;AAAA,UAClC;AAEA,iBAAO;AAAA,QACX;AAAA,QAEA,qBAAqB,SAAU,MAAM,MAAM,OAAO;AAE9C,mBAAS,IAAI,OAAO,KAAK,GAAG,KAAK;AAC7B,mBAAO,KAAK,CAAC,EAAE,MAAM,IAAI;AAAA,UAC7B;AAAA,QACJ;AAAA,QAEA,WAAW,SAAU,MAAM;AAEvB,mBAAS,IAAI,KAAK,SAAS,GAAG,UAAU,KAAK,GAAG,KAAK;AACjD,gBAAI,KAAK,CAAC,EAAE,SAAS,WAAW,GAAG;AAC/B,kBAAI,IAAI,GAAG;AACP,2BAAW,KAAK,IAAI,CAAC,EAAE;AACvB,yBAAS,OAAO,SAAS,QAAQ,KAAK,CAAC,CAAC,GAAG,CAAC;AAAA,cAEhD;AAAO,qBAAK,MAAM;AAAA,YAEtB;AAAO,uBAAS,KAAK,CAAC,GAAG,KAAK,MAAM;AAAA,UACxC;AAAA,QACJ;AAAA,QAEA,aAAa,SAAU,QAAQ;AAO3B,cAAI,aAAa,CAAC,YAAY,QAAQ,GAAG;AAEzC,eAAK,cAAc,IAAI,SAAS,KAAK,KAAK,WAAW,KAAK,OAAO,CAAC,CAAC,CAAC;AACpE,eAAK,cAAc,IAAI,SAAS,KAAK,KAAK,WAAW,KAAK,OAAO,CAAC,CAAC,CAAC;AAEpE,eAAK,SAAS,IAAI,SAAS,KAAK,cAAc,OAAO,KAAK,KAAK,IAAI,IAAI;AAAA,QAC3E;AAAA,MACJ;AAIA,eAAS,SAAS,MAAM,QAAQ;AAC5B,aAAK,OAAO,SAAS,MAAM,GAAG,KAAK,SAAS,QAAQ,MAAM;AAAA,MAC9D;AAGA,eAAS,SAAS,MAAM,GAAG,GAAG,QAAQ;AAClC,YAAI,OAAO,MAAM;AAEjB,iBAAS,IAAI,GAAG,OAAO,IAAI,GAAG,KAAK;AAC/B,kBAAQ,KAAK,SAAS,CAAC;AACvB,iBAAO,MAAM,KAAK,OAAO,OAAO,KAAK,IAAI,MAAM,IAAI;AAAA,QACvD;AAEA,eAAO;AAAA,MACX;AAEA,eAAS,QAAQ;AAAE,eAAO,CAAC,UAAU,UAAU,WAAW,SAAS;AAAA,MAAG;AAEtE,eAAS,OAAO,GAAG,GAAG;AAClB,UAAE,CAAC,IAAI,KAAK,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAC1B,UAAE,CAAC,IAAI,KAAK,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAC1B,UAAE,CAAC,IAAI,KAAK,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAC1B,UAAE,CAAC,IAAI,KAAK,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAC1B,eAAO;AAAA,MACX;AAEA,eAAS,gBAAgB,GAAG,GAAG;AAAE,eAAO,EAAE,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC;AAAA,MAAG;AAC/D,eAAS,gBAAgB,GAAG,GAAG;AAAE,eAAO,EAAE,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC;AAAA,MAAG;AAE/D,eAAS,SAAS,GAAK;AAAE,gBAAQ,EAAE,CAAC,IAAI,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC;AAAA,MAAI;AAC/D,eAAS,WAAW,GAAG;AAAE,eAAQ,EAAE,CAAC,IAAI,EAAE,CAAC,KAAM,EAAE,CAAC,IAAI,EAAE,CAAC;AAAA,MAAI;AAE/D,eAAS,aAAa,GAAG,GAAG;AACxB,gBAAQ,KAAK,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,IAAI,KAAK,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,MAC1C,KAAK,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,IAAI,KAAK,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAAA,MACtD;AAEA,eAAS,iBAAiB,GAAG,GAAG;AAC5B,YAAI,OAAO,KAAK,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,GAC1B,OAAO,KAAK,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,GAC1B,OAAO,KAAK,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,GAC1B,OAAO,KAAK,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAE9B,eAAO,KAAK,IAAI,GAAG,OAAO,IAAI,IACvB,KAAK,IAAI,GAAG,OAAO,IAAI;AAAA,MAClC;AAEA,eAAS,SAAS,GAAG,GAAG;AACpB,eAAO,EAAE,CAAC,KAAK,EAAE,CAAC,KACX,EAAE,CAAC,KAAK,EAAE,CAAC,KACX,EAAE,CAAC,KAAK,EAAE,CAAC,KACX,EAAE,CAAC,KAAK,EAAE,CAAC;AAAA,MACtB;AAEA,eAAS,WAAW,GAAG,GAAG;AACtB,eAAO,EAAE,CAAC,KAAK,EAAE,CAAC,KACX,EAAE,CAAC,KAAK,EAAE,CAAC,KACX,EAAE,CAAC,KAAK,EAAE,CAAC,KACX,EAAE,CAAC,KAAK,EAAE,CAAC;AAAA,MACtB;AAKA,eAAS,YAAY,KAAK,MAAM,OAAO,GAAG,SAAS;AAC/C,YAAI,QAAQ,CAAC,MAAM,KAAK,GACpB;AAEJ,eAAO,MAAM,QAAQ;AACjB,kBAAQ,MAAM,IAAI;AAClB,iBAAO,MAAM,IAAI;AAEjB,cAAI,QAAQ,QAAQ;AAAG;AAEvB,gBAAM,OAAO,KAAK,MAAM,QAAQ,QAAQ,IAAI,CAAC,IAAI;AACjD,iBAAO,KAAK,MAAM,OAAO,KAAK,OAAO;AAErC,gBAAM,KAAK,MAAM,KAAK,KAAK,KAAK;AAAA,QACpC;AAAA,MACJ;AAIA,eAAS,OAAO,KAAK,MAAM,OAAO,GAAG,SAAS;AAC1C,YAAI,GAAG,GAAG,GAAG,GAAG,IAAI,SAAS,UAAU,GAAG;AAE1C,eAAO,QAAQ,MAAM;AACjB,cAAI,QAAQ,OAAO,KAAK;AACpB,gBAAI,QAAQ,OAAO;AACnB,gBAAI,IAAI,OAAO;AACf,gBAAI,KAAK,IAAI,CAAC;AACd,gBAAI,MAAM,KAAK,IAAI,IAAI,IAAI,CAAC;AAC5B,iBAAK,MAAM,KAAK,KAAK,IAAI,KAAK,IAAI,KAAK,CAAC,KAAK,IAAI,IAAI,IAAI,IAAI,KAAK;AAClE,sBAAU,KAAK,IAAI,MAAM,KAAK,MAAM,IAAI,IAAI,IAAI,IAAI,EAAE,CAAC;AACvD,uBAAW,KAAK,IAAI,OAAO,KAAK,MAAM,KAAK,IAAI,KAAK,IAAI,IAAI,EAAE,CAAC;AAC/D,mBAAO,KAAK,SAAS,UAAU,GAAG,OAAO;AAAA,UAC7C;AAEA,cAAI,IAAI,CAAC;AACT,cAAI;AACJ,cAAI;AAEJ,eAAK,KAAK,MAAM,CAAC;AACjB,cAAI,QAAQ,IAAI,KAAK,GAAG,CAAC,IAAI;AAAG,iBAAK,KAAK,MAAM,KAAK;AAErD,iBAAO,IAAI,GAAG;AACV,iBAAK,KAAK,GAAG,CAAC;AACd;AACA;AACA,mBAAO,QAAQ,IAAI,CAAC,GAAG,CAAC,IAAI;AAAG;AAC/B,mBAAO,QAAQ,IAAI,CAAC,GAAG,CAAC,IAAI;AAAG;AAAA,UACnC;AAEA,cAAI,QAAQ,IAAI,IAAI,GAAG,CAAC,MAAM;AAAG,iBAAK,KAAK,MAAM,CAAC;AAAA,eAC7C;AACD;AACA,iBAAK,KAAK,GAAG,KAAK;AAAA,UACtB;AAEA,cAAI,KAAK;AAAG,mBAAO,IAAI;AACvB,cAAI,KAAK;AAAG,oBAAQ,IAAI;AAAA,QAC5B;AAAA,MACJ;AAEA,eAAS,KAAK,KAAK,GAAG,GAAG;AACrB,YAAI,MAAM,IAAI,CAAC;AACf,YAAI,CAAC,IAAI,IAAI,CAAC;AACd,YAAI,CAAC,IAAI;AAAA,MACb;AAIA,UAAI,OAAO,WAAW,cAAc,OAAO;AAAK,eAAO,SAAS,WAAY;AAAE,iBAAO;AAAA,QAAO,CAAC;AAAA,eACpF,OAAO,WAAW;AAAa,eAAO,UAAU;AAAA,eAChD,OAAO,SAAS;AAAa,aAAK,QAAQ;AAAA;AAC9C,eAAO,QAAQ;AAAA,IAEpB,GAAG;AAAA;AAAA;", "names": []}