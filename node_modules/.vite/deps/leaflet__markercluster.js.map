{"version": 3, "sources": ["../../leaflet.markercluster/src/MarkerClusterGroup.js", "../../leaflet.markercluster/src/MarkerCluster.js", "../../leaflet.markercluster/src/MarkerOpacity.js", "../../leaflet.markercluster/src/DistanceGrid.js", "../../leaflet.markercluster/src/MarkerCluster.QuickHull.js", "../../leaflet.markercluster/src/MarkerCluster.Spiderfier.js", "../../leaflet.markercluster/src/MarkerClusterGroup.Refresh.js"], "sourcesContent": ["/*\n * L.MarkerClusterGroup extends L.FeatureGroup by clustering the markers contained within\n */\n\nexport var MarkerClusterGroup = L.MarkerClusterGroup = L.FeatureGroup.extend({\n\n\toptions: {\n\t\tmaxClusterRadius: 80, //A cluster will cover at most this many pixels from its center\n\t\ticonCreateFunction: null,\n\t\tclusterPane: L.Marker.prototype.options.pane,\n\n\t\tspiderfyOnEveryZoom: false,\n\t\tspiderfyOnMaxZoom: true,\n\t\tshowCoverageOnHover: true,\n\t\tzoomToBoundsOnClick: true,\n\t\tsingleMarkerMode: false,\n\n\t\tdisableClusteringAtZoom: null,\n\n\t\t// Setting this to false prevents the removal of any clusters outside of the viewpoint, which\n\t\t// is the default behaviour for performance reasons.\n\t\tremoveOutsideVisibleBounds: true,\n\n\t\t// Set to false to disable all animations (zoom and spiderfy).\n\t\t// If false, option animateAddingMarkers below has no effect.\n\t\t// If L.DomUtil.TRANSITION is falsy, this option has no effect.\n\t\tanimate: true,\n\n\t\t//Whether to animate adding markers after adding the MarkerClusterGroup to the map\n\t\t// If you are adding individual markers set to true, if adding bulk markers leave false for massive performance gains.\n\t\tanimateAddingMarkers: false,\n\n\t\t// Make it possible to provide custom function to calculate spiderfy shape positions\n\t\tspiderfyShapePositions: null,\n\n\t\t//Increase to increase the distance away that spiderfied markers appear from the center\n\t\tspiderfyDistanceMultiplier: 1,\n\n\t\t// Make it possible to specify a polyline options on a spider leg\n\t\tspiderLegPolylineOptions: { weight: 1.5, color: '#222', opacity: 0.5 },\n\n\t\t// When bulk adding layers, adds markers in chunks. Means addLayers may not add all the layers in the call, others will be loaded during setTimeouts\n\t\tchunkedLoading: false,\n\t\tchunkInterval: 200, // process markers for a maximum of ~ n milliseconds (then trigger the chunkProgress callback)\n\t\tchunkDelay: 50, // at the end of each interval, give n milliseconds back to system/browser\n\t\tchunkProgress: null, // progress callback: function(processed, total, elapsed) (e.g. for a progress indicator)\n\n\t\t//Options to pass to the L.Polygon constructor\n\t\tpolygonOptions: {}\n\t},\n\n\tinitialize: function (options) {\n\t\tL.Util.setOptions(this, options);\n\t\tif (!this.options.iconCreateFunction) {\n\t\t\tthis.options.iconCreateFunction = this._defaultIconCreateFunction;\n\t\t}\n\n\t\tthis._featureGroup = L.featureGroup();\n\t\tthis._featureGroup.addEventParent(this);\n\n\t\tthis._nonPointGroup = L.featureGroup();\n\t\tthis._nonPointGroup.addEventParent(this);\n\n\t\tthis._inZoomAnimation = 0;\n\t\tthis._needsClustering = [];\n\t\tthis._needsRemoving = []; //Markers removed while we aren't on the map need to be kept track of\n\t\t//The bounds of the currently shown area (from _getExpandedVisibleBounds) Updated on zoom/move\n\t\tthis._currentShownBounds = null;\n\n\t\tthis._queue = [];\n\n\t\tthis._childMarkerEventHandlers = {\n\t\t\t'dragstart': this._childMarkerDragStart,\n\t\t\t'move': this._childMarkerMoved,\n\t\t\t'dragend': this._childMarkerDragEnd,\n\t\t};\n\n\t\t// Hook the appropriate animation methods.\n\t\tvar animate = L.DomUtil.TRANSITION && this.options.animate;\n\t\tL.extend(this, animate ? this._withAnimation : this._noAnimation);\n\t\t// Remember which MarkerCluster class to instantiate (animated or not).\n\t\tthis._markerCluster = animate ? L.MarkerCluster : L.MarkerClusterNonAnimated;\n\t},\n\n\taddLayer: function (layer) {\n\n\t\tif (layer instanceof L.LayerGroup) {\n\t\t\treturn this.addLayers([layer]);\n\t\t}\n\n\t\t//Don't cluster non point data\n\t\tif (!layer.getLatLng) {\n\t\t\tthis._nonPointGroup.addLayer(layer);\n\t\t\tthis.fire('layeradd', { layer: layer });\n\t\t\treturn this;\n\t\t}\n\n\t\tif (!this._map) {\n\t\t\tthis._needsClustering.push(layer);\n\t\t\tthis.fire('layeradd', { layer: layer });\n\t\t\treturn this;\n\t\t}\n\n\t\tif (this.hasLayer(layer)) {\n\t\t\treturn this;\n\t\t}\n\n\n\t\t//If we have already clustered we'll need to add this one to a cluster\n\n\t\tif (this._unspiderfy) {\n\t\t\tthis._unspiderfy();\n\t\t}\n\n\t\tthis._addLayer(layer, this._maxZoom);\n\t\tthis.fire('layeradd', { layer: layer });\n\n\t\t// Refresh bounds and weighted positions.\n\t\tthis._topClusterLevel._recalculateBounds();\n\n\t\tthis._refreshClustersIcons();\n\n\t\t//Work out what is visible\n\t\tvar visibleLayer = layer,\n\t\t    currentZoom = this._zoom;\n\t\tif (layer.__parent) {\n\t\t\twhile (visibleLayer.__parent._zoom >= currentZoom) {\n\t\t\t\tvisibleLayer = visibleLayer.__parent;\n\t\t\t}\n\t\t}\n\n\t\tif (this._currentShownBounds.contains(visibleLayer.getLatLng())) {\n\t\t\tif (this.options.animateAddingMarkers) {\n\t\t\t\tthis._animationAddLayer(layer, visibleLayer);\n\t\t\t} else {\n\t\t\t\tthis._animationAddLayerNonAnimated(layer, visibleLayer);\n\t\t\t}\n\t\t}\n\t\treturn this;\n\t},\n\n\tremoveLayer: function (layer) {\n\n\t\tif (layer instanceof L.LayerGroup) {\n\t\t\treturn this.removeLayers([layer]);\n\t\t}\n\n\t\t//Non point layers\n\t\tif (!layer.getLatLng) {\n\t\t\tthis._nonPointGroup.removeLayer(layer);\n\t\t\tthis.fire('layerremove', { layer: layer });\n\t\t\treturn this;\n\t\t}\n\n\t\tif (!this._map) {\n\t\t\tif (!this._arraySplice(this._needsClustering, layer) && this.hasLayer(layer)) {\n\t\t\t\tthis._needsRemoving.push({ layer: layer, latlng: layer._latlng });\n\t\t\t}\n\t\t\tthis.fire('layerremove', { layer: layer });\n\t\t\treturn this;\n\t\t}\n\n\t\tif (!layer.__parent) {\n\t\t\treturn this;\n\t\t}\n\n\t\tif (this._unspiderfy) {\n\t\t\tthis._unspiderfy();\n\t\t\tthis._unspiderfyLayer(layer);\n\t\t}\n\n\t\t//Remove the marker from clusters\n\t\tthis._removeLayer(layer, true);\n\t\tthis.fire('layerremove', { layer: layer });\n\n\t\t// Refresh bounds and weighted positions.\n\t\tthis._topClusterLevel._recalculateBounds();\n\n\t\tthis._refreshClustersIcons();\n\n\t\tlayer.off(this._childMarkerEventHandlers, this);\n\n\t\tif (this._featureGroup.hasLayer(layer)) {\n\t\t\tthis._featureGroup.removeLayer(layer);\n\t\t\tif (layer.clusterShow) {\n\t\t\t\tlayer.clusterShow();\n\t\t\t}\n\t\t}\n\n\t\treturn this;\n\t},\n\n\t//Takes an array of markers and adds them in bulk\n\taddLayers: function (layersArray, skipLayerAddEvent) {\n\t\tif (!L.Util.isArray(layersArray)) {\n\t\t\treturn this.addLayer(layersArray);\n\t\t}\n\n\t\tvar fg = this._featureGroup,\n\t\t    npg = this._nonPointGroup,\n\t\t    chunked = this.options.chunkedLoading,\n\t\t    chunkInterval = this.options.chunkInterval,\n\t\t    chunkProgress = this.options.chunkProgress,\n\t\t    l = layersArray.length,\n\t\t    offset = 0,\n\t\t    originalArray = true,\n\t\t    m;\n\n\t\tif (this._map) {\n\t\t\tvar started = (new Date()).getTime();\n\t\t\tvar process = L.bind(function () {\n\t\t\t\tvar start = (new Date()).getTime();\n\n\t\t\t\t// Make sure to unspiderfy before starting to add some layers\n\t\t\t\tif (this._map && this._unspiderfy) {\n\t\t\t\t\tthis._unspiderfy();\n\t\t\t\t}\n\n\t\t\t\tfor (; offset < l; offset++) {\n\t\t\t\t\tif (chunked && offset % 200 === 0) {\n\t\t\t\t\t\t// every couple hundred markers, instrument the time elapsed since processing started:\n\t\t\t\t\t\tvar elapsed = (new Date()).getTime() - start;\n\t\t\t\t\t\tif (elapsed > chunkInterval) {\n\t\t\t\t\t\t\tbreak; // been working too hard, time to take a break :-)\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\tm = layersArray[offset];\n\n\t\t\t\t\t// Group of layers, append children to layersArray and skip.\n\t\t\t\t\t// Side effects:\n\t\t\t\t\t// - Total increases, so chunkProgress ratio jumps backward.\n\t\t\t\t\t// - Groups are not included in this group, only their non-group child layers (hasLayer).\n\t\t\t\t\t// Changing array length while looping does not affect performance in current browsers:\n\t\t\t\t\t// http://jsperf.com/for-loop-changing-length/6\n\t\t\t\t\tif (m instanceof L.LayerGroup) {\n\t\t\t\t\t\tif (originalArray) {\n\t\t\t\t\t\t\tlayersArray = layersArray.slice();\n\t\t\t\t\t\t\toriginalArray = false;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tthis._extractNonGroupLayers(m, layersArray);\n\t\t\t\t\t\tl = layersArray.length;\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t}\n\n\t\t\t\t\t//Not point data, can't be clustered\n\t\t\t\t\tif (!m.getLatLng) {\n\t\t\t\t\t\tnpg.addLayer(m);\n\t\t\t\t\t\tif (!skipLayerAddEvent) {\n\t\t\t\t\t\t\tthis.fire('layeradd', { layer: m });\n\t\t\t\t\t\t}\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t}\n\n\t\t\t\t\tif (this.hasLayer(m)) {\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t}\n\n\t\t\t\t\tthis._addLayer(m, this._maxZoom);\n\t\t\t\t\tif (!skipLayerAddEvent) {\n\t\t\t\t\t\tthis.fire('layeradd', { layer: m });\n\t\t\t\t\t}\n\n\t\t\t\t\t//If we just made a cluster of size 2 then we need to remove the other marker from the map (if it is) or we never will\n\t\t\t\t\tif (m.__parent) {\n\t\t\t\t\t\tif (m.__parent.getChildCount() === 2) {\n\t\t\t\t\t\t\tvar markers = m.__parent.getAllChildMarkers(),\n\t\t\t\t\t\t\t    otherMarker = markers[0] === m ? markers[1] : markers[0];\n\t\t\t\t\t\t\tfg.removeLayer(otherMarker);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tif (chunkProgress) {\n\t\t\t\t\t// report progress and time elapsed:\n\t\t\t\t\tchunkProgress(offset, l, (new Date()).getTime() - started);\n\t\t\t\t}\n\n\t\t\t\t// Completed processing all markers.\n\t\t\t\tif (offset === l) {\n\n\t\t\t\t\t// Refresh bounds and weighted positions.\n\t\t\t\t\tthis._topClusterLevel._recalculateBounds();\n\n\t\t\t\t\tthis._refreshClustersIcons();\n\n\t\t\t\t\tthis._topClusterLevel._recursivelyAddChildrenToMap(null, this._zoom, this._currentShownBounds);\n\t\t\t\t} else {\n\t\t\t\t\tsetTimeout(process, this.options.chunkDelay);\n\t\t\t\t}\n\t\t\t}, this);\n\n\t\t\tprocess();\n\t\t} else {\n\t\t\tvar needsClustering = this._needsClustering;\n\n\t\t\tfor (; offset < l; offset++) {\n\t\t\t\tm = layersArray[offset];\n\n\t\t\t\t// Group of layers, append children to layersArray and skip.\n\t\t\t\tif (m instanceof L.LayerGroup) {\n\t\t\t\t\tif (originalArray) {\n\t\t\t\t\t\tlayersArray = layersArray.slice();\n\t\t\t\t\t\toriginalArray = false;\n\t\t\t\t\t}\n\t\t\t\t\tthis._extractNonGroupLayers(m, layersArray);\n\t\t\t\t\tl = layersArray.length;\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\n\t\t\t\t//Not point data, can't be clustered\n\t\t\t\tif (!m.getLatLng) {\n\t\t\t\t\tnpg.addLayer(m);\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\n\t\t\t\tif (this.hasLayer(m)) {\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\n\t\t\t\tneedsClustering.push(m);\n\t\t\t}\n\t\t}\n\t\treturn this;\n\t},\n\n\t//Takes an array of markers and removes them in bulk\n\tremoveLayers: function (layersArray) {\n\t\tvar i, m,\n\t\t    l = layersArray.length,\n\t\t    fg = this._featureGroup,\n\t\t    npg = this._nonPointGroup,\n\t\t    originalArray = true;\n\n\t\tif (!this._map) {\n\t\t\tfor (i = 0; i < l; i++) {\n\t\t\t\tm = layersArray[i];\n\n\t\t\t\t// Group of layers, append children to layersArray and skip.\n\t\t\t\tif (m instanceof L.LayerGroup) {\n\t\t\t\t\tif (originalArray) {\n\t\t\t\t\t\tlayersArray = layersArray.slice();\n\t\t\t\t\t\toriginalArray = false;\n\t\t\t\t\t}\n\t\t\t\t\tthis._extractNonGroupLayers(m, layersArray);\n\t\t\t\t\tl = layersArray.length;\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\n\t\t\t\tthis._arraySplice(this._needsClustering, m);\n\t\t\t\tnpg.removeLayer(m);\n\t\t\t\tif (this.hasLayer(m)) {\n\t\t\t\t\tthis._needsRemoving.push({ layer: m, latlng: m._latlng });\n\t\t\t\t}\n\t\t\t\tthis.fire('layerremove', { layer: m });\n\t\t\t}\n\t\t\treturn this;\n\t\t}\n\n\t\tif (this._unspiderfy) {\n\t\t\tthis._unspiderfy();\n\n\t\t\t// Work on a copy of the array, so that next loop is not affected.\n\t\t\tvar layersArray2 = layersArray.slice(),\n\t\t\t    l2 = l;\n\t\t\tfor (i = 0; i < l2; i++) {\n\t\t\t\tm = layersArray2[i];\n\n\t\t\t\t// Group of layers, append children to layersArray and skip.\n\t\t\t\tif (m instanceof L.LayerGroup) {\n\t\t\t\t\tthis._extractNonGroupLayers(m, layersArray2);\n\t\t\t\t\tl2 = layersArray2.length;\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\n\t\t\t\tthis._unspiderfyLayer(m);\n\t\t\t}\n\t\t}\n\n\t\tfor (i = 0; i < l; i++) {\n\t\t\tm = layersArray[i];\n\n\t\t\t// Group of layers, append children to layersArray and skip.\n\t\t\tif (m instanceof L.LayerGroup) {\n\t\t\t\tif (originalArray) {\n\t\t\t\t\tlayersArray = layersArray.slice();\n\t\t\t\t\toriginalArray = false;\n\t\t\t\t}\n\t\t\t\tthis._extractNonGroupLayers(m, layersArray);\n\t\t\t\tl = layersArray.length;\n\t\t\t\tcontinue;\n\t\t\t}\n\n\t\t\tif (!m.__parent) {\n\t\t\t\tnpg.removeLayer(m);\n\t\t\t\tthis.fire('layerremove', { layer: m });\n\t\t\t\tcontinue;\n\t\t\t}\n\n\t\t\tthis._removeLayer(m, true, true);\n\t\t\tthis.fire('layerremove', { layer: m });\n\n\t\t\tif (fg.hasLayer(m)) {\n\t\t\t\tfg.removeLayer(m);\n\t\t\t\tif (m.clusterShow) {\n\t\t\t\t\tm.clusterShow();\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t// Refresh bounds and weighted positions.\n\t\tthis._topClusterLevel._recalculateBounds();\n\n\t\tthis._refreshClustersIcons();\n\n\t\t//Fix up the clusters and markers on the map\n\t\tthis._topClusterLevel._recursivelyAddChildrenToMap(null, this._zoom, this._currentShownBounds);\n\n\t\treturn this;\n\t},\n\n\t//Removes all layers from the MarkerClusterGroup\n\tclearLayers: function () {\n\t\t//Need our own special implementation as the LayerGroup one doesn't work for us\n\n\t\t//If we aren't on the map (yet), blow away the markers we know of\n\t\tif (!this._map) {\n\t\t\tthis._needsClustering = [];\n\t\t\tthis._needsRemoving = [];\n\t\t\tdelete this._gridClusters;\n\t\t\tdelete this._gridUnclustered;\n\t\t}\n\n\t\tif (this._noanimationUnspiderfy) {\n\t\t\tthis._noanimationUnspiderfy();\n\t\t}\n\n\t\t//Remove all the visible layers\n\t\tthis._featureGroup.clearLayers();\n\t\tthis._nonPointGroup.clearLayers();\n\n\t\tthis.eachLayer(function (marker) {\n\t\t\tmarker.off(this._childMarkerEventHandlers, this);\n\t\t\tdelete marker.__parent;\n\t\t}, this);\n\n\t\tif (this._map) {\n\t\t\t//Reset _topClusterLevel and the DistanceGrids\n\t\t\tthis._generateInitialClusters();\n\t\t}\n\n\t\treturn this;\n\t},\n\n\t//Override FeatureGroup.getBounds as it doesn't work\n\tgetBounds: function () {\n\t\tvar bounds = new L.LatLngBounds();\n\n\t\tif (this._topClusterLevel) {\n\t\t\tbounds.extend(this._topClusterLevel._bounds);\n\t\t}\n\n\t\tfor (var i = this._needsClustering.length - 1; i >= 0; i--) {\n\t\t\tbounds.extend(this._needsClustering[i].getLatLng());\n\t\t}\n\n\t\tbounds.extend(this._nonPointGroup.getBounds());\n\n\t\treturn bounds;\n\t},\n\n\t//Overrides LayerGroup.eachLayer\n\teachLayer: function (method, context) {\n\t\tvar markers = this._needsClustering.slice(),\n\t\t\tneedsRemoving = this._needsRemoving,\n\t\t\tthisNeedsRemoving, i, j;\n\n\t\tif (this._topClusterLevel) {\n\t\t\tthis._topClusterLevel.getAllChildMarkers(markers);\n\t\t}\n\n\t\tfor (i = markers.length - 1; i >= 0; i--) {\n\t\t\tthisNeedsRemoving = true;\n\n\t\t\tfor (j = needsRemoving.length - 1; j >= 0; j--) {\n\t\t\t\tif (needsRemoving[j].layer === markers[i]) {\n\t\t\t\t\tthisNeedsRemoving = false;\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tif (thisNeedsRemoving) {\n\t\t\t\tmethod.call(context, markers[i]);\n\t\t\t}\n\t\t}\n\n\t\tthis._nonPointGroup.eachLayer(method, context);\n\t},\n\n\t//Overrides LayerGroup.getLayers\n\tgetLayers: function () {\n\t\tvar layers = [];\n\t\tthis.eachLayer(function (l) {\n\t\t\tlayers.push(l);\n\t\t});\n\t\treturn layers;\n\t},\n\n\t//Overrides LayerGroup.getLayer, WARNING: Really bad performance\n\tgetLayer: function (id) {\n\t\tvar result = null;\n\n\t\tid = parseInt(id, 10);\n\n\t\tthis.eachLayer(function (l) {\n\t\t\tif (L.stamp(l) === id) {\n\t\t\t\tresult = l;\n\t\t\t}\n\t\t});\n\n\t\treturn result;\n\t},\n\n\t//Returns true if the given layer is in this MarkerClusterGroup\n\thasLayer: function (layer) {\n\t\tif (!layer) {\n\t\t\treturn false;\n\t\t}\n\n\t\tvar i, anArray = this._needsClustering;\n\n\t\tfor (i = anArray.length - 1; i >= 0; i--) {\n\t\t\tif (anArray[i] === layer) {\n\t\t\t\treturn true;\n\t\t\t}\n\t\t}\n\n\t\tanArray = this._needsRemoving;\n\t\tfor (i = anArray.length - 1; i >= 0; i--) {\n\t\t\tif (anArray[i].layer === layer) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t}\n\n\t\treturn !!(layer.__parent && layer.__parent._group === this) || this._nonPointGroup.hasLayer(layer);\n\t},\n\n\t//Zoom down to show the given layer (spiderfying if necessary) then calls the callback\n\tzoomToShowLayer: function (layer, callback) {\n\n\t\tvar map = this._map;\n\n\t\tif (typeof callback !== 'function') {\n\t\t\tcallback = function () {};\n\t\t}\n\n\t\tvar showMarker = function () {\n\t\t\t// Assumes that map.hasLayer checks for direct appearance on map, not recursively calling\n\t\t\t// hasLayer on Layer Groups that are on map (typically not calling this MarkerClusterGroup.hasLayer, which would always return true)\n\t\t\tif ((map.hasLayer(layer) || map.hasLayer(layer.__parent)) && !this._inZoomAnimation) {\n\t\t\t\tthis._map.off('moveend', showMarker, this);\n\t\t\t\tthis.off('animationend', showMarker, this);\n\n\t\t\t\tif (map.hasLayer(layer)) {\n\t\t\t\t\tcallback();\n\t\t\t\t} else if (layer.__parent._icon) {\n\t\t\t\t\tthis.once('spiderfied', callback, this);\n\t\t\t\t\tlayer.__parent.spiderfy();\n\t\t\t\t}\n\t\t\t}\n\t\t};\n\n\t\tif (layer._icon && this._map.getBounds().contains(layer.getLatLng())) {\n\t\t\t//Layer is visible ond on screen, immediate return\n\t\t\tcallback();\n\t\t} else if (layer.__parent._zoom < Math.round(this._map._zoom)) {\n\t\t\t//Layer should be visible at this zoom level. It must not be on screen so just pan over to it\n\t\t\tthis._map.on('moveend', showMarker, this);\n\t\t\tthis._map.panTo(layer.getLatLng());\n\t\t} else {\n\t\t\tthis._map.on('moveend', showMarker, this);\n\t\t\tthis.on('animationend', showMarker, this);\n\t\t\tlayer.__parent.zoomToBounds();\n\t\t}\n\t},\n\n\t//Overrides FeatureGroup.onAdd\n\tonAdd: function (map) {\n\t\tthis._map = map;\n\t\tvar i, l, layer;\n\n\t\tif (!isFinite(this._map.getMaxZoom())) {\n\t\t\tthrow \"Map has no maxZoom specified\";\n\t\t}\n\n\t\tthis._featureGroup.addTo(map);\n\t\tthis._nonPointGroup.addTo(map);\n\n\t\tif (!this._gridClusters) {\n\t\t\tthis._generateInitialClusters();\n\t\t}\n\n\t\tthis._maxLat = map.options.crs.projection.MAX_LATITUDE;\n\n\t\t//Restore all the positions as they are in the MCG before removing them\n\t\tfor (i = 0, l = this._needsRemoving.length; i < l; i++) {\n\t\t\tlayer = this._needsRemoving[i];\n\t\t\tlayer.newlatlng = layer.layer._latlng;\n\t\t\tlayer.layer._latlng = layer.latlng;\n\t\t}\n\t\t//Remove them, then restore their new positions\n\t\tfor (i = 0, l = this._needsRemoving.length; i < l; i++) {\n\t\t\tlayer = this._needsRemoving[i];\n\t\t\tthis._removeLayer(layer.layer, true);\n\t\t\tlayer.layer._latlng = layer.newlatlng;\n\t\t}\n\t\tthis._needsRemoving = [];\n\n\t\t//Remember the current zoom level and bounds\n\t\tthis._zoom = Math.round(this._map._zoom);\n\t\tthis._currentShownBounds = this._getExpandedVisibleBounds();\n\n\t\tthis._map.on('zoomend', this._zoomEnd, this);\n\t\tthis._map.on('moveend', this._moveEnd, this);\n\n\t\tif (this._spiderfierOnAdd) { //TODO FIXME: Not sure how to have spiderfier add something on here nicely\n\t\t\tthis._spiderfierOnAdd();\n\t\t}\n\n\t\tthis._bindEvents();\n\n\t\t//Actually add our markers to the map:\n\t\tl = this._needsClustering;\n\t\tthis._needsClustering = [];\n\t\tthis.addLayers(l, true);\n\t},\n\n\t//Overrides FeatureGroup.onRemove\n\tonRemove: function (map) {\n\t\tmap.off('zoomend', this._zoomEnd, this);\n\t\tmap.off('moveend', this._moveEnd, this);\n\n\t\tthis._unbindEvents();\n\n\t\t//In case we are in a cluster animation\n\t\tthis._map._mapPane.className = this._map._mapPane.className.replace(' leaflet-cluster-anim', '');\n\n\t\tif (this._spiderfierOnRemove) { //TODO FIXME: Not sure how to have spiderfier add something on here nicely\n\t\t\tthis._spiderfierOnRemove();\n\t\t}\n\n\t\tdelete this._maxLat;\n\n\t\t//Clean up all the layers we added to the map\n\t\tthis._hideCoverage();\n\t\tthis._featureGroup.remove();\n\t\tthis._nonPointGroup.remove();\n\n\t\tthis._featureGroup.clearLayers();\n\n\t\tthis._map = null;\n\t},\n\n\tgetVisibleParent: function (marker) {\n\t\tvar vMarker = marker;\n\t\twhile (vMarker && !vMarker._icon) {\n\t\t\tvMarker = vMarker.__parent;\n\t\t}\n\t\treturn vMarker || null;\n\t},\n\n\t//Remove the given object from the given array\n\t_arraySplice: function (anArray, obj) {\n\t\tfor (var i = anArray.length - 1; i >= 0; i--) {\n\t\t\tif (anArray[i] === obj) {\n\t\t\t\tanArray.splice(i, 1);\n\t\t\t\treturn true;\n\t\t\t}\n\t\t}\n\t},\n\n\t/**\n\t * Removes a marker from all _gridUnclustered zoom levels, starting at the supplied zoom.\n\t * @param marker to be removed from _gridUnclustered.\n\t * @param z integer bottom start zoom level (included)\n\t * @private\n\t */\n\t_removeFromGridUnclustered: function (marker, z) {\n\t\tvar map = this._map,\n\t\t    gridUnclustered = this._gridUnclustered,\n\t\t\tminZoom = Math.floor(this._map.getMinZoom());\n\n\t\tfor (; z >= minZoom; z--) {\n\t\t\tif (!gridUnclustered[z].removeObject(marker, map.project(marker.getLatLng(), z))) {\n\t\t\t\tbreak;\n\t\t\t}\n\t\t}\n\t},\n\n\t_childMarkerDragStart: function (e) {\n\t\te.target.__dragStart = e.target._latlng;\n\t},\n\n\t_childMarkerMoved: function (e) {\n\t\tif (!this._ignoreMove && !e.target.__dragStart) {\n\t\t\tvar isPopupOpen = e.target._popup && e.target._popup.isOpen();\n\n\t\t\tthis._moveChild(e.target, e.oldLatLng, e.latlng);\n\n\t\t\tif (isPopupOpen) {\n\t\t\t\te.target.openPopup();\n\t\t\t}\n\t\t}\n\t},\n\n\t_moveChild: function (layer, from, to) {\n\t\tlayer._latlng = from;\n\t\tthis.removeLayer(layer);\n\n\t\tlayer._latlng = to;\n\t\tthis.addLayer(layer);\n\t},\n\n\t_childMarkerDragEnd: function (e) {\n\t\tvar dragStart = e.target.__dragStart;\n\t\tdelete e.target.__dragStart;\n\t\tif (dragStart) {\n\t\t\tthis._moveChild(e.target, dragStart, e.target._latlng);\n\t\t}\t\t\n\t},\n\n\n\t//Internal function for removing a marker from everything.\n\t//dontUpdateMap: set to true if you will handle updating the map manually (for bulk functions)\n\t_removeLayer: function (marker, removeFromDistanceGrid, dontUpdateMap) {\n\t\tvar gridClusters = this._gridClusters,\n\t\t\tgridUnclustered = this._gridUnclustered,\n\t\t\tfg = this._featureGroup,\n\t\t\tmap = this._map,\n\t\t\tminZoom = Math.floor(this._map.getMinZoom());\n\n\t\t//Remove the marker from distance clusters it might be in\n\t\tif (removeFromDistanceGrid) {\n\t\t\tthis._removeFromGridUnclustered(marker, this._maxZoom);\n\t\t}\n\n\t\t//Work our way up the clusters removing them as we go if required\n\t\tvar cluster = marker.__parent,\n\t\t\tmarkers = cluster._markers,\n\t\t\totherMarker;\n\n\t\t//Remove the marker from the immediate parents marker list\n\t\tthis._arraySplice(markers, marker);\n\n\t\twhile (cluster) {\n\t\t\tcluster._childCount--;\n\t\t\tcluster._boundsNeedUpdate = true;\n\n\t\t\tif (cluster._zoom < minZoom) {\n\t\t\t\t//Top level, do nothing\n\t\t\t\tbreak;\n\t\t\t} else if (removeFromDistanceGrid && cluster._childCount <= 1) { //Cluster no longer required\n\t\t\t\t//We need to push the other marker up to the parent\n\t\t\t\totherMarker = cluster._markers[0] === marker ? cluster._markers[1] : cluster._markers[0];\n\n\t\t\t\t//Update distance grid\n\t\t\t\tgridClusters[cluster._zoom].removeObject(cluster, map.project(cluster._cLatLng, cluster._zoom));\n\t\t\t\tgridUnclustered[cluster._zoom].addObject(otherMarker, map.project(otherMarker.getLatLng(), cluster._zoom));\n\n\t\t\t\t//Move otherMarker up to parent\n\t\t\t\tthis._arraySplice(cluster.__parent._childClusters, cluster);\n\t\t\t\tcluster.__parent._markers.push(otherMarker);\n\t\t\t\totherMarker.__parent = cluster.__parent;\n\n\t\t\t\tif (cluster._icon) {\n\t\t\t\t\t//Cluster is currently on the map, need to put the marker on the map instead\n\t\t\t\t\tfg.removeLayer(cluster);\n\t\t\t\t\tif (!dontUpdateMap) {\n\t\t\t\t\t\tfg.addLayer(otherMarker);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tcluster._iconNeedsUpdate = true;\n\t\t\t}\n\n\t\t\tcluster = cluster.__parent;\n\t\t}\n\n\t\tdelete marker.__parent;\n\t},\n\n\t_isOrIsParent: function (el, oel) {\n\t\twhile (oel) {\n\t\t\tif (el === oel) {\n\t\t\t\treturn true;\n\t\t\t}\n\t\t\toel = oel.parentNode;\n\t\t}\n\t\treturn false;\n\t},\n\n\t//Override L.Evented.fire\n\tfire: function (type, data, propagate) {\n\t\tif (data && data.layer instanceof L.MarkerCluster) {\n\t\t\t//Prevent multiple clustermouseover/off events if the icon is made up of stacked divs (Doesn't work in ie <= 8, no relatedTarget)\n\t\t\tif (data.originalEvent && this._isOrIsParent(data.layer._icon, data.originalEvent.relatedTarget)) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t\ttype = 'cluster' + type;\n\t\t}\n\n\t\tL.FeatureGroup.prototype.fire.call(this, type, data, propagate);\n\t},\n\n\t//Override L.Evented.listens\n\tlistens: function (type, propagate) {\n\t\treturn L.FeatureGroup.prototype.listens.call(this, type, propagate) || L.FeatureGroup.prototype.listens.call(this, 'cluster' + type, propagate);\n\t},\n\n\t//Default functionality\n\t_defaultIconCreateFunction: function (cluster) {\n\t\tvar childCount = cluster.getChildCount();\n\n\t\tvar c = ' marker-cluster-';\n\t\tif (childCount < 10) {\n\t\t\tc += 'small';\n\t\t} else if (childCount < 100) {\n\t\t\tc += 'medium';\n\t\t} else {\n\t\t\tc += 'large';\n\t\t}\n\n\t\treturn new L.DivIcon({ html: '<div><span>' + childCount + '</span></div>', className: 'marker-cluster' + c, iconSize: new L.Point(40, 40) });\n\t},\n\n\t_bindEvents: function () {\n\t\tvar map = this._map,\n\t\t    spiderfyOnMaxZoom = this.options.spiderfyOnMaxZoom,\n\t\t    showCoverageOnHover = this.options.showCoverageOnHover,\n\t\t    zoomToBoundsOnClick = this.options.zoomToBoundsOnClick,\n\t\t    spiderfyOnEveryZoom = this.options.spiderfyOnEveryZoom;\n\n\t\t//Zoom on cluster click or spiderfy if we are at the lowest level\n\t\tif (spiderfyOnMaxZoom || zoomToBoundsOnClick || spiderfyOnEveryZoom) {\n\t\t\tthis.on('clusterclick clusterkeypress', this._zoomOrSpiderfy, this);\n\t\t}\n\n\t\t//Show convex hull (boundary) polygon on mouse over\n\t\tif (showCoverageOnHover) {\n\t\t\tthis.on('clustermouseover', this._showCoverage, this);\n\t\t\tthis.on('clustermouseout', this._hideCoverage, this);\n\t\t\tmap.on('zoomend', this._hideCoverage, this);\n\t\t}\n\t},\n\n\t_zoomOrSpiderfy: function (e) {\n\t\tvar cluster = e.layer,\n\t\t    bottomCluster = cluster;\n\n\t\tif (e.type === 'clusterkeypress' && e.originalEvent && e.originalEvent.keyCode !== 13) {\n\t\t\treturn;\n\t\t}\n\n\t\twhile (bottomCluster._childClusters.length === 1) {\n\t\t\tbottomCluster = bottomCluster._childClusters[0];\n\t\t}\n\n\t\tif (bottomCluster._zoom === this._maxZoom &&\n\t\t\tbottomCluster._childCount === cluster._childCount &&\n\t\t\tthis.options.spiderfyOnMaxZoom) {\n\n\t\t\t// All child markers are contained in a single cluster from this._maxZoom to this cluster.\n\t\t\tcluster.spiderfy();\n\t\t} else if (this.options.zoomToBoundsOnClick) {\n\t\t\tcluster.zoomToBounds();\n\t\t}\n\n\t\tif (this.options.spiderfyOnEveryZoom) {\n\t\t\tcluster.spiderfy();\n\t\t}\n\n\t\t// Focus the map again for keyboard users.\n\t\tif (e.originalEvent && e.originalEvent.keyCode === 13) {\n\t\t\tthis._map._container.focus();\n\t\t}\n\t},\n\n\t_showCoverage: function (e) {\n\t\tvar map = this._map;\n\t\tif (this._inZoomAnimation) {\n\t\t\treturn;\n\t\t}\n\t\tif (this._shownPolygon) {\n\t\t\tmap.removeLayer(this._shownPolygon);\n\t\t}\n\t\tif (e.layer.getChildCount() > 2 && e.layer !== this._spiderfied) {\n\t\t\tthis._shownPolygon = new L.Polygon(e.layer.getConvexHull(), this.options.polygonOptions);\n\t\t\tmap.addLayer(this._shownPolygon);\n\t\t}\n\t},\n\n\t_hideCoverage: function () {\n\t\tif (this._shownPolygon) {\n\t\t\tthis._map.removeLayer(this._shownPolygon);\n\t\t\tthis._shownPolygon = null;\n\t\t}\n\t},\n\n\t_unbindEvents: function () {\n\t\tvar spiderfyOnMaxZoom = this.options.spiderfyOnMaxZoom,\n\t\t\tshowCoverageOnHover = this.options.showCoverageOnHover,\n\t\t\tzoomToBoundsOnClick = this.options.zoomToBoundsOnClick,\n\t\t\tspiderfyOnEveryZoom = this.options.spiderfyOnEveryZoom,\n\t\t\tmap = this._map;\n\n\t\tif (spiderfyOnMaxZoom || zoomToBoundsOnClick || spiderfyOnEveryZoom) {\n\t\t\tthis.off('clusterclick clusterkeypress', this._zoomOrSpiderfy, this);\n\t\t}\n\t\tif (showCoverageOnHover) {\n\t\t\tthis.off('clustermouseover', this._showCoverage, this);\n\t\t\tthis.off('clustermouseout', this._hideCoverage, this);\n\t\t\tmap.off('zoomend', this._hideCoverage, this);\n\t\t}\n\t},\n\n\t_zoomEnd: function () {\n\t\tif (!this._map) { //May have been removed from the map by a zoomEnd handler\n\t\t\treturn;\n\t\t}\n\t\tthis._mergeSplitClusters();\n\n\t\tthis._zoom = Math.round(this._map._zoom);\n\t\tthis._currentShownBounds = this._getExpandedVisibleBounds();\n\t},\n\n\t_moveEnd: function () {\n\t\tif (this._inZoomAnimation) {\n\t\t\treturn;\n\t\t}\n\n\t\tvar newBounds = this._getExpandedVisibleBounds();\n\n\t\tthis._topClusterLevel._recursivelyRemoveChildrenFromMap(this._currentShownBounds, Math.floor(this._map.getMinZoom()), this._zoom, newBounds);\n\t\tthis._topClusterLevel._recursivelyAddChildrenToMap(null, Math.round(this._map._zoom), newBounds);\n\n\t\tthis._currentShownBounds = newBounds;\n\t\treturn;\n\t},\n\n\t_generateInitialClusters: function () {\n\t\tvar maxZoom = Math.ceil(this._map.getMaxZoom()),\n\t\t\tminZoom = Math.floor(this._map.getMinZoom()),\n\t\t\tradius = this.options.maxClusterRadius,\n\t\t\tradiusFn = radius;\n\n\t\t//If we just set maxClusterRadius to a single number, we need to create\n\t\t//a simple function to return that number. Otherwise, we just have to\n\t\t//use the function we've passed in.\n\t\tif (typeof radius !== \"function\") {\n\t\t\tradiusFn = function () { return radius; };\n\t\t}\n\n\t\tif (this.options.disableClusteringAtZoom !== null) {\n\t\t\tmaxZoom = this.options.disableClusteringAtZoom - 1;\n\t\t}\n\t\tthis._maxZoom = maxZoom;\n\t\tthis._gridClusters = {};\n\t\tthis._gridUnclustered = {};\n\n\t\t//Set up DistanceGrids for each zoom\n\t\tfor (var zoom = maxZoom; zoom >= minZoom; zoom--) {\n\t\t\tthis._gridClusters[zoom] = new L.DistanceGrid(radiusFn(zoom));\n\t\t\tthis._gridUnclustered[zoom] = new L.DistanceGrid(radiusFn(zoom));\n\t\t}\n\n\t\t// Instantiate the appropriate L.MarkerCluster class (animated or not).\n\t\tthis._topClusterLevel = new this._markerCluster(this, minZoom - 1);\n\t},\n\n\t//Zoom: Zoom to start adding at (Pass this._maxZoom to start at the bottom)\n\t_addLayer: function (layer, zoom) {\n\t\tvar gridClusters = this._gridClusters,\n\t\t    gridUnclustered = this._gridUnclustered,\n\t\t\tminZoom = Math.floor(this._map.getMinZoom()),\n\t\t    markerPoint, z;\n\n\t\tif (this.options.singleMarkerMode) {\n\t\t\tthis._overrideMarkerIcon(layer);\n\t\t}\n\n\t\tlayer.on(this._childMarkerEventHandlers, this);\n\n\t\t//Find the lowest zoom level to slot this one in\n\t\tfor (; zoom >= minZoom; zoom--) {\n\t\t\tmarkerPoint = this._map.project(layer.getLatLng(), zoom); // calculate pixel position\n\n\t\t\t//Try find a cluster close by\n\t\t\tvar closest = gridClusters[zoom].getNearObject(markerPoint);\n\t\t\tif (closest) {\n\t\t\t\tclosest._addChild(layer);\n\t\t\t\tlayer.__parent = closest;\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t//Try find a marker close by to form a new cluster with\n\t\t\tclosest = gridUnclustered[zoom].getNearObject(markerPoint);\n\t\t\tif (closest) {\n\t\t\t\tvar parent = closest.__parent;\n\t\t\t\tif (parent) {\n\t\t\t\t\tthis._removeLayer(closest, false);\n\t\t\t\t}\n\n\t\t\t\t//Create new cluster with these 2 in it\n\n\t\t\t\tvar newCluster = new this._markerCluster(this, zoom, closest, layer);\n\t\t\t\tgridClusters[zoom].addObject(newCluster, this._map.project(newCluster._cLatLng, zoom));\n\t\t\t\tclosest.__parent = newCluster;\n\t\t\t\tlayer.__parent = newCluster;\n\n\t\t\t\t//First create any new intermediate parent clusters that don't exist\n\t\t\t\tvar lastParent = newCluster;\n\t\t\t\tfor (z = zoom - 1; z > parent._zoom; z--) {\n\t\t\t\t\tlastParent = new this._markerCluster(this, z, lastParent);\n\t\t\t\t\tgridClusters[z].addObject(lastParent, this._map.project(closest.getLatLng(), z));\n\t\t\t\t}\n\t\t\t\tparent._addChild(lastParent);\n\n\t\t\t\t//Remove closest from this zoom level and any above that it is in, replace with newCluster\n\t\t\t\tthis._removeFromGridUnclustered(closest, zoom);\n\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t//Didn't manage to cluster in at this zoom, record us as a marker here and continue upwards\n\t\t\tgridUnclustered[zoom].addObject(layer, markerPoint);\n\t\t}\n\n\t\t//Didn't get in anything, add us to the top\n\t\tthis._topClusterLevel._addChild(layer);\n\t\tlayer.__parent = this._topClusterLevel;\n\t\treturn;\n\t},\n\n\t/**\n\t * Refreshes the icon of all \"dirty\" visible clusters.\n\t * Non-visible \"dirty\" clusters will be updated when they are added to the map.\n\t * @private\n\t */\n\t_refreshClustersIcons: function () {\n\t\tthis._featureGroup.eachLayer(function (c) {\n\t\t\tif (c instanceof L.MarkerCluster && c._iconNeedsUpdate) {\n\t\t\t\tc._updateIcon();\n\t\t\t}\n\t\t});\n\t},\n\n\t//Enqueue code to fire after the marker expand/contract has happened\n\t_enqueue: function (fn) {\n\t\tthis._queue.push(fn);\n\t\tif (!this._queueTimeout) {\n\t\t\tthis._queueTimeout = setTimeout(L.bind(this._processQueue, this), 300);\n\t\t}\n\t},\n\t_processQueue: function () {\n\t\tfor (var i = 0; i < this._queue.length; i++) {\n\t\t\tthis._queue[i].call(this);\n\t\t}\n\t\tthis._queue.length = 0;\n\t\tclearTimeout(this._queueTimeout);\n\t\tthis._queueTimeout = null;\n\t},\n\n\t//Merge and split any existing clusters that are too big or small\n\t_mergeSplitClusters: function () {\n\t\tvar mapZoom = Math.round(this._map._zoom);\n\n\t\t//In case we are starting to split before the animation finished\n\t\tthis._processQueue();\n\n\t\tif (this._zoom < mapZoom && this._currentShownBounds.intersects(this._getExpandedVisibleBounds())) { //Zoom in, split\n\t\t\tthis._animationStart();\n\t\t\t//Remove clusters now off screen\n\t\t\tthis._topClusterLevel._recursivelyRemoveChildrenFromMap(this._currentShownBounds, Math.floor(this._map.getMinZoom()), this._zoom, this._getExpandedVisibleBounds());\n\n\t\t\tthis._animationZoomIn(this._zoom, mapZoom);\n\n\t\t} else if (this._zoom > mapZoom) { //Zoom out, merge\n\t\t\tthis._animationStart();\n\n\t\t\tthis._animationZoomOut(this._zoom, mapZoom);\n\t\t} else {\n\t\t\tthis._moveEnd();\n\t\t}\n\t},\n\n\t//Gets the maps visible bounds expanded in each direction by the size of the screen (so the user cannot see an area we do not cover in one pan)\n\t_getExpandedVisibleBounds: function () {\n\t\tif (!this.options.removeOutsideVisibleBounds) {\n\t\t\treturn this._mapBoundsInfinite;\n\t\t} else if (L.Browser.mobile) {\n\t\t\treturn this._checkBoundsMaxLat(this._map.getBounds());\n\t\t}\n\n\t\treturn this._checkBoundsMaxLat(this._map.getBounds().pad(1)); // Padding expands the bounds by its own dimensions but scaled with the given factor.\n\t},\n\n\t/**\n\t * Expands the latitude to Infinity (or -Infinity) if the input bounds reach the map projection maximum defined latitude\n\t * (in the case of Web/Spherical Mercator, it is 85.0511287798 / see https://en.wikipedia.org/wiki/Web_Mercator#Formulas).\n\t * Otherwise, the removeOutsideVisibleBounds option will remove markers beyond that limit, whereas the same markers without\n\t * this option (or outside MCG) will have their position floored (ceiled) by the projection and rendered at that limit,\n\t * making the user think that MCG \"eats\" them and never displays them again.\n\t * @param bounds L.LatLngBounds\n\t * @returns {L.LatLngBounds}\n\t * @private\n\t */\n\t_checkBoundsMaxLat: function (bounds) {\n\t\tvar maxLat = this._maxLat;\n\n\t\tif (maxLat !== undefined) {\n\t\t\tif (bounds.getNorth() >= maxLat) {\n\t\t\t\tbounds._northEast.lat = Infinity;\n\t\t\t}\n\t\t\tif (bounds.getSouth() <= -maxLat) {\n\t\t\t\tbounds._southWest.lat = -Infinity;\n\t\t\t}\n\t\t}\n\n\t\treturn bounds;\n\t},\n\n\t//Shared animation code\n\t_animationAddLayerNonAnimated: function (layer, newCluster) {\n\t\tif (newCluster === layer) {\n\t\t\tthis._featureGroup.addLayer(layer);\n\t\t} else if (newCluster._childCount === 2) {\n\t\t\tnewCluster._addToMap();\n\n\t\t\tvar markers = newCluster.getAllChildMarkers();\n\t\t\tthis._featureGroup.removeLayer(markers[0]);\n\t\t\tthis._featureGroup.removeLayer(markers[1]);\n\t\t} else {\n\t\t\tnewCluster._updateIcon();\n\t\t}\n\t},\n\n\t/**\n\t * Extracts individual (i.e. non-group) layers from a Layer Group.\n\t * @param group to extract layers from.\n\t * @param output {Array} in which to store the extracted layers.\n\t * @returns {*|Array}\n\t * @private\n\t */\n\t_extractNonGroupLayers: function (group, output) {\n\t\tvar layers = group.getLayers(),\n\t\t    i = 0,\n\t\t    layer;\n\n\t\toutput = output || [];\n\n\t\tfor (; i < layers.length; i++) {\n\t\t\tlayer = layers[i];\n\n\t\t\tif (layer instanceof L.LayerGroup) {\n\t\t\t\tthis._extractNonGroupLayers(layer, output);\n\t\t\t\tcontinue;\n\t\t\t}\n\n\t\t\toutput.push(layer);\n\t\t}\n\n\t\treturn output;\n\t},\n\n\t/**\n\t * Implements the singleMarkerMode option.\n\t * @param layer Marker to re-style using the Clusters iconCreateFunction.\n\t * @returns {L.Icon} The newly created icon.\n\t * @private\n\t */\n\t_overrideMarkerIcon: function (layer) {\n\t\tvar icon = layer.options.icon = this.options.iconCreateFunction({\n\t\t\tgetChildCount: function () {\n\t\t\t\treturn 1;\n\t\t\t},\n\t\t\tgetAllChildMarkers: function () {\n\t\t\t\treturn [layer];\n\t\t\t}\n\t\t});\n\n\t\treturn icon;\n\t}\n});\n\n// Constant bounds used in case option \"removeOutsideVisibleBounds\" is set to false.\nL.MarkerClusterGroup.include({\n\t_mapBoundsInfinite: new L.LatLngBounds(new L.LatLng(-Infinity, -Infinity), new L.LatLng(Infinity, Infinity))\n});\n\nL.MarkerClusterGroup.include({\n\t_noAnimation: {\n\t\t//Non Animated versions of everything\n\t\t_animationStart: function () {\n\t\t\t//Do nothing...\n\t\t},\n\t\t_animationZoomIn: function (previousZoomLevel, newZoomLevel) {\n\t\t\tthis._topClusterLevel._recursivelyRemoveChildrenFromMap(this._currentShownBounds, Math.floor(this._map.getMinZoom()), previousZoomLevel);\n\t\t\tthis._topClusterLevel._recursivelyAddChildrenToMap(null, newZoomLevel, this._getExpandedVisibleBounds());\n\n\t\t\t//We didn't actually animate, but we use this event to mean \"clustering animations have finished\"\n\t\t\tthis.fire('animationend');\n\t\t},\n\t\t_animationZoomOut: function (previousZoomLevel, newZoomLevel) {\n\t\t\tthis._topClusterLevel._recursivelyRemoveChildrenFromMap(this._currentShownBounds, Math.floor(this._map.getMinZoom()), previousZoomLevel);\n\t\t\tthis._topClusterLevel._recursivelyAddChildrenToMap(null, newZoomLevel, this._getExpandedVisibleBounds());\n\n\t\t\t//We didn't actually animate, but we use this event to mean \"clustering animations have finished\"\n\t\t\tthis.fire('animationend');\n\t\t},\n\t\t_animationAddLayer: function (layer, newCluster) {\n\t\t\tthis._animationAddLayerNonAnimated(layer, newCluster);\n\t\t}\n\t},\n\n\t_withAnimation: {\n\t\t//Animated versions here\n\t\t_animationStart: function () {\n\t\t\tthis._map._mapPane.className += ' leaflet-cluster-anim';\n\t\t\tthis._inZoomAnimation++;\n\t\t},\n\n\t\t_animationZoomIn: function (previousZoomLevel, newZoomLevel) {\n\t\t\tvar bounds = this._getExpandedVisibleBounds(),\n\t\t\t    fg = this._featureGroup,\n\t\t\t\tminZoom = Math.floor(this._map.getMinZoom()),\n\t\t\t    i;\n\n\t\t\tthis._ignoreMove = true;\n\n\t\t\t//Add all children of current clusters to map and remove those clusters from map\n\t\t\tthis._topClusterLevel._recursively(bounds, previousZoomLevel, minZoom, function (c) {\n\t\t\t\tvar startPos = c._latlng,\n\t\t\t\t    markers  = c._markers,\n\t\t\t\t    m;\n\n\t\t\t\tif (!bounds.contains(startPos)) {\n\t\t\t\t\tstartPos = null;\n\t\t\t\t}\n\n\t\t\t\tif (c._isSingleParent() && previousZoomLevel + 1 === newZoomLevel) { //Immediately add the new child and remove us\n\t\t\t\t\tfg.removeLayer(c);\n\t\t\t\t\tc._recursivelyAddChildrenToMap(null, newZoomLevel, bounds);\n\t\t\t\t} else {\n\t\t\t\t\t//Fade out old cluster\n\t\t\t\t\tc.clusterHide();\n\t\t\t\t\tc._recursivelyAddChildrenToMap(startPos, newZoomLevel, bounds);\n\t\t\t\t}\n\n\t\t\t\t//Remove all markers that aren't visible any more\n\t\t\t\t//TODO: Do we actually need to do this on the higher levels too?\n\t\t\t\tfor (i = markers.length - 1; i >= 0; i--) {\n\t\t\t\t\tm = markers[i];\n\t\t\t\t\tif (!bounds.contains(m._latlng)) {\n\t\t\t\t\t\tfg.removeLayer(m);\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t});\n\n\t\t\tthis._forceLayout();\n\n\t\t\t//Update opacities\n\t\t\tthis._topClusterLevel._recursivelyBecomeVisible(bounds, newZoomLevel);\n\t\t\t//TODO Maybe? Update markers in _recursivelyBecomeVisible\n\t\t\tfg.eachLayer(function (n) {\n\t\t\t\tif (!(n instanceof L.MarkerCluster) && n._icon) {\n\t\t\t\t\tn.clusterShow();\n\t\t\t\t}\n\t\t\t});\n\n\t\t\t//update the positions of the just added clusters/markers\n\t\t\tthis._topClusterLevel._recursively(bounds, previousZoomLevel, newZoomLevel, function (c) {\n\t\t\t\tc._recursivelyRestoreChildPositions(newZoomLevel);\n\t\t\t});\n\n\t\t\tthis._ignoreMove = false;\n\n\t\t\t//Remove the old clusters and close the zoom animation\n\t\t\tthis._enqueue(function () {\n\t\t\t\t//update the positions of the just added clusters/markers\n\t\t\t\tthis._topClusterLevel._recursively(bounds, previousZoomLevel, minZoom, function (c) {\n\t\t\t\t\tfg.removeLayer(c);\n\t\t\t\t\tc.clusterShow();\n\t\t\t\t});\n\n\t\t\t\tthis._animationEnd();\n\t\t\t});\n\t\t},\n\n\t\t_animationZoomOut: function (previousZoomLevel, newZoomLevel) {\n\t\t\tthis._animationZoomOutSingle(this._topClusterLevel, previousZoomLevel - 1, newZoomLevel);\n\n\t\t\t//Need to add markers for those that weren't on the map before but are now\n\t\t\tthis._topClusterLevel._recursivelyAddChildrenToMap(null, newZoomLevel, this._getExpandedVisibleBounds());\n\t\t\t//Remove markers that were on the map before but won't be now\n\t\t\tthis._topClusterLevel._recursivelyRemoveChildrenFromMap(this._currentShownBounds, Math.floor(this._map.getMinZoom()), previousZoomLevel, this._getExpandedVisibleBounds());\n\t\t},\n\n\t\t_animationAddLayer: function (layer, newCluster) {\n\t\t\tvar me = this,\n\t\t\t    fg = this._featureGroup;\n\n\t\t\tfg.addLayer(layer);\n\t\t\tif (newCluster !== layer) {\n\t\t\t\tif (newCluster._childCount > 2) { //Was already a cluster\n\n\t\t\t\t\tnewCluster._updateIcon();\n\t\t\t\t\tthis._forceLayout();\n\t\t\t\t\tthis._animationStart();\n\n\t\t\t\t\tlayer._setPos(this._map.latLngToLayerPoint(newCluster.getLatLng()));\n\t\t\t\t\tlayer.clusterHide();\n\n\t\t\t\t\tthis._enqueue(function () {\n\t\t\t\t\t\tfg.removeLayer(layer);\n\t\t\t\t\t\tlayer.clusterShow();\n\n\t\t\t\t\t\tme._animationEnd();\n\t\t\t\t\t});\n\n\t\t\t\t} else { //Just became a cluster\n\t\t\t\t\tthis._forceLayout();\n\n\t\t\t\t\tme._animationStart();\n\t\t\t\t\tme._animationZoomOutSingle(newCluster, this._map.getMaxZoom(), this._zoom);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t},\n\n\t// Private methods for animated versions.\n\t_animationZoomOutSingle: function (cluster, previousZoomLevel, newZoomLevel) {\n\t\tvar bounds = this._getExpandedVisibleBounds(),\n\t\t\tminZoom = Math.floor(this._map.getMinZoom());\n\n\t\t//Animate all of the markers in the clusters to move to their cluster center point\n\t\tcluster._recursivelyAnimateChildrenInAndAddSelfToMap(bounds, minZoom, previousZoomLevel + 1, newZoomLevel);\n\n\t\tvar me = this;\n\n\t\t//Update the opacity (If we immediately set it they won't animate)\n\t\tthis._forceLayout();\n\t\tcluster._recursivelyBecomeVisible(bounds, newZoomLevel);\n\n\t\t//TODO: Maybe use the transition timing stuff to make this more reliable\n\t\t//When the animations are done, tidy up\n\t\tthis._enqueue(function () {\n\n\t\t\t//This cluster stopped being a cluster before the timeout fired\n\t\t\tif (cluster._childCount === 1) {\n\t\t\t\tvar m = cluster._markers[0];\n\t\t\t\t//If we were in a cluster animation at the time then the opacity and position of our child could be wrong now, so fix it\n\t\t\t\tthis._ignoreMove = true;\n\t\t\t\tm.setLatLng(m.getLatLng());\n\t\t\t\tthis._ignoreMove = false;\n\t\t\t\tif (m.clusterShow) {\n\t\t\t\t\tm.clusterShow();\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tcluster._recursively(bounds, newZoomLevel, minZoom, function (c) {\n\t\t\t\t\tc._recursivelyRemoveChildrenFromMap(bounds, minZoom, previousZoomLevel + 1);\n\t\t\t\t});\n\t\t\t}\n\t\t\tme._animationEnd();\n\t\t});\n\t},\n\n\t_animationEnd: function () {\n\t\tif (this._map) {\n\t\t\tthis._map._mapPane.className = this._map._mapPane.className.replace(' leaflet-cluster-anim', '');\n\t\t}\n\t\tthis._inZoomAnimation--;\n\t\tthis.fire('animationend');\n\t},\n\n\t//Force a browser layout of stuff in the map\n\t// Should apply the current opacity and location to all elements so we can update them again for an animation\n\t_forceLayout: function () {\n\t\t//In my testing this works, infact offsetWidth of any element seems to work.\n\t\t//Could loop all this._layers and do this for each _icon if it stops working\n\n\t\tL.Util.falseFn(document.body.offsetWidth);\n\t}\n});\n\nL.markerClusterGroup = function (options) {\n\treturn new L.MarkerClusterGroup(options);\n};\n", "export var MarkerCluster = L.MarkerCluster = L.Marker.extend({\n\toptions: L.Icon.prototype.options,\n\n\tinitialize: function (group, zoom, a, b) {\n\n\t\tL.Marker.prototype.initialize.call(this, a ? (a._cLatLng || a.getLatLng()) : new L.LatLng(0, 0),\n            { icon: this, pane: group.options.clusterPane });\n\n\t\tthis._group = group;\n\t\tthis._zoom = zoom;\n\n\t\tthis._markers = [];\n\t\tthis._childClusters = [];\n\t\tthis._childCount = 0;\n\t\tthis._iconNeedsUpdate = true;\n\t\tthis._boundsNeedUpdate = true;\n\n\t\tthis._bounds = new L.LatLngBounds();\n\n\t\tif (a) {\n\t\t\tthis._addChild(a);\n\t\t}\n\t\tif (b) {\n\t\t\tthis._addChild(b);\n\t\t}\n\t},\n\n\t//Recursively retrieve all child markers of this cluster\n\tgetAllChildMarkers: function (storageArray, ignoreDraggedMarker) {\n\t\tstorageArray = storageArray || [];\n\n\t\tfor (var i = this._childClusters.length - 1; i >= 0; i--) {\n\t\t\tthis._childClusters[i].getAllChildMarkers(storageArray, ignoreDraggedMarker);\n\t\t}\n\n\t\tfor (var j = this._markers.length - 1; j >= 0; j--) {\n\t\t\tif (ignoreDraggedMarker && this._markers[j].__dragStart) {\n\t\t\t\tcontinue;\n\t\t\t}\n\t\t\tstorageArray.push(this._markers[j]);\n\t\t}\n\n\t\treturn storageArray;\n\t},\n\n\t//Returns the count of how many child markers we have\n\tgetChildCount: function () {\n\t\treturn this._childCount;\n\t},\n\n\t//Zoom to the minimum of showing all of the child markers, or the extents of this cluster\n\tzoomToBounds: function (fitBoundsOptions) {\n\t\tvar childClusters = this._childClusters.slice(),\n\t\t\tmap = this._group._map,\n\t\t\tboundsZoom = map.getBoundsZoom(this._bounds),\n\t\t\tzoom = this._zoom + 1,\n\t\t\tmapZoom = map.getZoom(),\n\t\t\ti;\n\n\t\t//calculate how far we need to zoom down to see all of the markers\n\t\twhile (childClusters.length > 0 && boundsZoom > zoom) {\n\t\t\tzoom++;\n\t\t\tvar newClusters = [];\n\t\t\tfor (i = 0; i < childClusters.length; i++) {\n\t\t\t\tnewClusters = newClusters.concat(childClusters[i]._childClusters);\n\t\t\t}\n\t\t\tchildClusters = newClusters;\n\t\t}\n\n\t\tif (boundsZoom > zoom) {\n\t\t\tthis._group._map.setView(this._latlng, zoom);\n\t\t} else if (boundsZoom <= mapZoom) { //If fitBounds wouldn't zoom us down, zoom us down instead\n\t\t\tthis._group._map.setView(this._latlng, mapZoom + 1);\n\t\t} else {\n\t\t\tthis._group._map.fitBounds(this._bounds, fitBoundsOptions);\n\t\t}\n\t},\n\n\tgetBounds: function () {\n\t\tvar bounds = new L.LatLngBounds();\n\t\tbounds.extend(this._bounds);\n\t\treturn bounds;\n\t},\n\n\t_updateIcon: function () {\n\t\tthis._iconNeedsUpdate = true;\n\t\tif (this._icon) {\n\t\t\tthis.setIcon(this);\n\t\t}\n\t},\n\n\t//Cludge for Icon, we pretend to be an icon for performance\n\tcreateIcon: function () {\n\t\tif (this._iconNeedsUpdate) {\n\t\t\tthis._iconObj = this._group.options.iconCreateFunction(this);\n\t\t\tthis._iconNeedsUpdate = false;\n\t\t}\n\t\treturn this._iconObj.createIcon();\n\t},\n\tcreateShadow: function () {\n\t\treturn this._iconObj.createShadow();\n\t},\n\n\n\t_addChild: function (new1, isNotificationFromChild) {\n\n\t\tthis._iconNeedsUpdate = true;\n\n\t\tthis._boundsNeedUpdate = true;\n\t\tthis._setClusterCenter(new1);\n\n\t\tif (new1 instanceof L.MarkerCluster) {\n\t\t\tif (!isNotificationFromChild) {\n\t\t\t\tthis._childClusters.push(new1);\n\t\t\t\tnew1.__parent = this;\n\t\t\t}\n\t\t\tthis._childCount += new1._childCount;\n\t\t} else {\n\t\t\tif (!isNotificationFromChild) {\n\t\t\t\tthis._markers.push(new1);\n\t\t\t}\n\t\t\tthis._childCount++;\n\t\t}\n\n\t\tif (this.__parent) {\n\t\t\tthis.__parent._addChild(new1, true);\n\t\t}\n\t},\n\n\t/**\n\t * Makes sure the cluster center is set. If not, uses the child center if it is a cluster, or the marker position.\n\t * @param child L.MarkerCluster|L.Marker that will be used as cluster center if not defined yet.\n\t * @private\n\t */\n\t_setClusterCenter: function (child) {\n\t\tif (!this._cLatLng) {\n\t\t\t// when clustering, take position of the first point as the cluster center\n\t\t\tthis._cLatLng = child._cLatLng || child._latlng;\n\t\t}\n\t},\n\n\t/**\n\t * Assigns impossible bounding values so that the next extend entirely determines the new bounds.\n\t * This method avoids having to trash the previous L.LatLngBounds object and to create a new one, which is much slower for this class.\n\t * As long as the bounds are not extended, most other methods would probably fail, as they would with bounds initialized but not extended.\n\t * @private\n\t */\n\t_resetBounds: function () {\n\t\tvar bounds = this._bounds;\n\n\t\tif (bounds._southWest) {\n\t\t\tbounds._southWest.lat = Infinity;\n\t\t\tbounds._southWest.lng = Infinity;\n\t\t}\n\t\tif (bounds._northEast) {\n\t\t\tbounds._northEast.lat = -Infinity;\n\t\t\tbounds._northEast.lng = -Infinity;\n\t\t}\n\t},\n\n\t_recalculateBounds: function () {\n\t\tvar markers = this._markers,\n\t\t    childClusters = this._childClusters,\n\t\t    latSum = 0,\n\t\t    lngSum = 0,\n\t\t    totalCount = this._childCount,\n\t\t    i, child, childLatLng, childCount;\n\n\t\t// Case where all markers are removed from the map and we are left with just an empty _topClusterLevel.\n\t\tif (totalCount === 0) {\n\t\t\treturn;\n\t\t}\n\n\t\t// Reset rather than creating a new object, for performance.\n\t\tthis._resetBounds();\n\n\t\t// Child markers.\n\t\tfor (i = 0; i < markers.length; i++) {\n\t\t\tchildLatLng = markers[i]._latlng;\n\n\t\t\tthis._bounds.extend(childLatLng);\n\n\t\t\tlatSum += childLatLng.lat;\n\t\t\tlngSum += childLatLng.lng;\n\t\t}\n\n\t\t// Child clusters.\n\t\tfor (i = 0; i < childClusters.length; i++) {\n\t\t\tchild = childClusters[i];\n\n\t\t\t// Re-compute child bounds and weighted position first if necessary.\n\t\t\tif (child._boundsNeedUpdate) {\n\t\t\t\tchild._recalculateBounds();\n\t\t\t}\n\n\t\t\tthis._bounds.extend(child._bounds);\n\n\t\t\tchildLatLng = child._wLatLng;\n\t\t\tchildCount = child._childCount;\n\n\t\t\tlatSum += childLatLng.lat * childCount;\n\t\t\tlngSum += childLatLng.lng * childCount;\n\t\t}\n\n\t\tthis._latlng = this._wLatLng = new L.LatLng(latSum / totalCount, lngSum / totalCount);\n\n\t\t// Reset dirty flag.\n\t\tthis._boundsNeedUpdate = false;\n\t},\n\n\t//Set our markers position as given and add it to the map\n\t_addToMap: function (startPos) {\n\t\tif (startPos) {\n\t\t\tthis._backupLatlng = this._latlng;\n\t\t\tthis.setLatLng(startPos);\n\t\t}\n\t\tthis._group._featureGroup.addLayer(this);\n\t},\n\n\t_recursivelyAnimateChildrenIn: function (bounds, center, maxZoom) {\n\t\tthis._recursively(bounds, this._group._map.getMinZoom(), maxZoom - 1,\n\t\t\tfunction (c) {\n\t\t\t\tvar markers = c._markers,\n\t\t\t\t\ti, m;\n\t\t\t\tfor (i = markers.length - 1; i >= 0; i--) {\n\t\t\t\t\tm = markers[i];\n\n\t\t\t\t\t//Only do it if the icon is still on the map\n\t\t\t\t\tif (m._icon) {\n\t\t\t\t\t\tm._setPos(center);\n\t\t\t\t\t\tm.clusterHide();\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t},\n\t\t\tfunction (c) {\n\t\t\t\tvar childClusters = c._childClusters,\n\t\t\t\t\tj, cm;\n\t\t\t\tfor (j = childClusters.length - 1; j >= 0; j--) {\n\t\t\t\t\tcm = childClusters[j];\n\t\t\t\t\tif (cm._icon) {\n\t\t\t\t\t\tcm._setPos(center);\n\t\t\t\t\t\tcm.clusterHide();\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t);\n\t},\n\n\t_recursivelyAnimateChildrenInAndAddSelfToMap: function (bounds, mapMinZoom, previousZoomLevel, newZoomLevel) {\n\t\tthis._recursively(bounds, newZoomLevel, mapMinZoom,\n\t\t\tfunction (c) {\n\t\t\t\tc._recursivelyAnimateChildrenIn(bounds, c._group._map.latLngToLayerPoint(c.getLatLng()).round(), previousZoomLevel);\n\n\t\t\t\t//TODO: depthToAnimateIn affects _isSingleParent, if there is a multizoom we may/may not be.\n\t\t\t\t//As a hack we only do a animation free zoom on a single level zoom, if someone does multiple levels then we always animate\n\t\t\t\tif (c._isSingleParent() && previousZoomLevel - 1 === newZoomLevel) {\n\t\t\t\t\tc.clusterShow();\n\t\t\t\t\tc._recursivelyRemoveChildrenFromMap(bounds, mapMinZoom, previousZoomLevel); //Immediately remove our children as we are replacing them. TODO previousBounds not bounds\n\t\t\t\t} else {\n\t\t\t\t\tc.clusterHide();\n\t\t\t\t}\n\n\t\t\t\tc._addToMap();\n\t\t\t}\n\t\t);\n\t},\n\n\t_recursivelyBecomeVisible: function (bounds, zoomLevel) {\n\t\tthis._recursively(bounds, this._group._map.getMinZoom(), zoomLevel, null, function (c) {\n\t\t\tc.clusterShow();\n\t\t});\n\t},\n\n\t_recursivelyAddChildrenToMap: function (startPos, zoomLevel, bounds) {\n\t\tthis._recursively(bounds, this._group._map.getMinZoom() - 1, zoomLevel,\n\t\t\tfunction (c) {\n\t\t\t\tif (zoomLevel === c._zoom) {\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\t//Add our child markers at startPos (so they can be animated out)\n\t\t\t\tfor (var i = c._markers.length - 1; i >= 0; i--) {\n\t\t\t\t\tvar nm = c._markers[i];\n\n\t\t\t\t\tif (!bounds.contains(nm._latlng)) {\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t}\n\n\t\t\t\t\tif (startPos) {\n\t\t\t\t\t\tnm._backupLatlng = nm.getLatLng();\n\n\t\t\t\t\t\tnm.setLatLng(startPos);\n\t\t\t\t\t\tif (nm.clusterHide) {\n\t\t\t\t\t\t\tnm.clusterHide();\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\tc._group._featureGroup.addLayer(nm);\n\t\t\t\t}\n\t\t\t},\n\t\t\tfunction (c) {\n\t\t\t\tc._addToMap(startPos);\n\t\t\t}\n\t\t);\n\t},\n\n\t_recursivelyRestoreChildPositions: function (zoomLevel) {\n\t\t//Fix positions of child markers\n\t\tfor (var i = this._markers.length - 1; i >= 0; i--) {\n\t\t\tvar nm = this._markers[i];\n\t\t\tif (nm._backupLatlng) {\n\t\t\t\tnm.setLatLng(nm._backupLatlng);\n\t\t\t\tdelete nm._backupLatlng;\n\t\t\t}\n\t\t}\n\n\t\tif (zoomLevel - 1 === this._zoom) {\n\t\t\t//Reposition child clusters\n\t\t\tfor (var j = this._childClusters.length - 1; j >= 0; j--) {\n\t\t\t\tthis._childClusters[j]._restorePosition();\n\t\t\t}\n\t\t} else {\n\t\t\tfor (var k = this._childClusters.length - 1; k >= 0; k--) {\n\t\t\t\tthis._childClusters[k]._recursivelyRestoreChildPositions(zoomLevel);\n\t\t\t}\n\t\t}\n\t},\n\n\t_restorePosition: function () {\n\t\tif (this._backupLatlng) {\n\t\t\tthis.setLatLng(this._backupLatlng);\n\t\t\tdelete this._backupLatlng;\n\t\t}\n\t},\n\n\t//exceptBounds: If set, don't remove any markers/clusters in it\n\t_recursivelyRemoveChildrenFromMap: function (previousBounds, mapMinZoom, zoomLevel, exceptBounds) {\n\t\tvar m, i;\n\t\tthis._recursively(previousBounds, mapMinZoom - 1, zoomLevel - 1,\n\t\t\tfunction (c) {\n\t\t\t\t//Remove markers at every level\n\t\t\t\tfor (i = c._markers.length - 1; i >= 0; i--) {\n\t\t\t\t\tm = c._markers[i];\n\t\t\t\t\tif (!exceptBounds || !exceptBounds.contains(m._latlng)) {\n\t\t\t\t\t\tc._group._featureGroup.removeLayer(m);\n\t\t\t\t\t\tif (m.clusterShow) {\n\t\t\t\t\t\t\tm.clusterShow();\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t},\n\t\t\tfunction (c) {\n\t\t\t\t//Remove child clusters at just the bottom level\n\t\t\t\tfor (i = c._childClusters.length - 1; i >= 0; i--) {\n\t\t\t\t\tm = c._childClusters[i];\n\t\t\t\t\tif (!exceptBounds || !exceptBounds.contains(m._latlng)) {\n\t\t\t\t\t\tc._group._featureGroup.removeLayer(m);\n\t\t\t\t\t\tif (m.clusterShow) {\n\t\t\t\t\t\t\tm.clusterShow();\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t);\n\t},\n\n\t//Run the given functions recursively to this and child clusters\n\t// boundsToApplyTo: a L.LatLngBounds representing the bounds of what clusters to recurse in to\n\t// zoomLevelToStart: zoom level to start running functions (inclusive)\n\t// zoomLevelToStop: zoom level to stop running functions (inclusive)\n\t// runAtEveryLevel: function that takes an L.MarkerCluster as an argument that should be applied on every level\n\t// runAtBottomLevel: function that takes an L.MarkerCluster as an argument that should be applied at only the bottom level\n\t_recursively: function (boundsToApplyTo, zoomLevelToStart, zoomLevelToStop, runAtEveryLevel, runAtBottomLevel) {\n\t\tvar childClusters = this._childClusters,\n\t\t    zoom = this._zoom,\n\t\t    i, c;\n\n\t\tif (zoomLevelToStart <= zoom) {\n\t\t\tif (runAtEveryLevel) {\n\t\t\t\trunAtEveryLevel(this);\n\t\t\t}\n\t\t\tif (runAtBottomLevel && zoom === zoomLevelToStop) {\n\t\t\t\trunAtBottomLevel(this);\n\t\t\t}\n\t\t}\n\n\t\tif (zoom < zoomLevelToStart || zoom < zoomLevelToStop) {\n\t\t\tfor (i = childClusters.length - 1; i >= 0; i--) {\n\t\t\t\tc = childClusters[i];\n\t\t\t\tif (c._boundsNeedUpdate) {\n\t\t\t\t\tc._recalculateBounds();\n\t\t\t\t}\n\t\t\t\tif (boundsToApplyTo.intersects(c._bounds)) {\n\t\t\t\t\tc._recursively(boundsToApplyTo, zoomLevelToStart, zoomLevelToStop, runAtEveryLevel, runAtBottomLevel);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t},\n\n\t//Returns true if we are the parent of only one cluster and that cluster is the same as us\n\t_isSingleParent: function () {\n\t\t//Don't need to check this._markers as the rest won't work if there are any\n\t\treturn this._childClusters.length > 0 && this._childClusters[0]._childCount === this._childCount;\n\t}\n});\n\n", "/*\n* Extends <PERSON><PERSON> to include two extra methods: clusterHide and clusterShow.\n* \n* They work as setOpacity(0) and setOpacity(1) respectively, but\n* don't overwrite the options.opacity\n* \n*/\n\nL.Marker.include({\n\tclusterHide: function () {\n\t\tvar backup = this.options.opacity;\n\t\tthis.setOpacity(0);\n\t\tthis.options.opacity = backup;\n\t\treturn this;\n\t},\n\t\n\tclusterShow: function () {\n\t\treturn this.setOpacity(this.options.opacity);\n\t}\n});\n\n\n", "\nL.DistanceGrid = function (cellSize) {\n\tthis._cellSize = cellSize;\n\tthis._sqCellSize = cellSize * cellSize;\n\tthis._grid = {};\n\tthis._objectPoint = { };\n};\n\nL.DistanceGrid.prototype = {\n\n\taddObject: function (obj, point) {\n\t\tvar x = this._getCoord(point.x),\n\t\t    y = this._getCoord(point.y),\n\t\t    grid = this._grid,\n\t\t    row = grid[y] = grid[y] || {},\n\t\t    cell = row[x] = row[x] || [],\n\t\t    stamp = L.Util.stamp(obj);\n\n\t\tthis._objectPoint[stamp] = point;\n\n\t\tcell.push(obj);\n\t},\n\n\tupdateObject: function (obj, point) {\n\t\tthis.removeObject(obj);\n\t\tthis.addObject(obj, point);\n\t},\n\n\t//Returns true if the object was found\n\tremoveObject: function (obj, point) {\n\t\tvar x = this._getCoord(point.x),\n\t\t    y = this._getCoord(point.y),\n\t\t    grid = this._grid,\n\t\t    row = grid[y] = grid[y] || {},\n\t\t    cell = row[x] = row[x] || [],\n\t\t    i, len;\n\n\t\tdelete this._objectPoint[L.Util.stamp(obj)];\n\n\t\tfor (i = 0, len = cell.length; i < len; i++) {\n\t\t\tif (cell[i] === obj) {\n\n\t\t\t\tcell.splice(i, 1);\n\n\t\t\t\tif (len === 1) {\n\t\t\t\t\tdelete row[x];\n\t\t\t\t}\n\n\t\t\t\treturn true;\n\t\t\t}\n\t\t}\n\n\t},\n\n\teachObject: function (fn, context) {\n\t\tvar i, j, k, len, row, cell, removed,\n\t\t    grid = this._grid;\n\n\t\tfor (i in grid) {\n\t\t\trow = grid[i];\n\n\t\t\tfor (j in row) {\n\t\t\t\tcell = row[j];\n\n\t\t\t\tfor (k = 0, len = cell.length; k < len; k++) {\n\t\t\t\t\tremoved = fn.call(context, cell[k]);\n\t\t\t\t\tif (removed) {\n\t\t\t\t\t\tk--;\n\t\t\t\t\t\tlen--;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t},\n\n\tgetNearObject: function (point) {\n\t\tvar x = this._getCoord(point.x),\n\t\t    y = this._getCoord(point.y),\n\t\t    i, j, k, row, cell, len, obj, dist,\n\t\t    objectPoint = this._objectPoint,\n\t\t    closestDistSq = this._sqCellSize,\n\t\t    closest = null;\n\n\t\tfor (i = y - 1; i <= y + 1; i++) {\n\t\t\trow = this._grid[i];\n\t\t\tif (row) {\n\n\t\t\t\tfor (j = x - 1; j <= x + 1; j++) {\n\t\t\t\t\tcell = row[j];\n\t\t\t\t\tif (cell) {\n\n\t\t\t\t\t\tfor (k = 0, len = cell.length; k < len; k++) {\n\t\t\t\t\t\t\tobj = cell[k];\n\t\t\t\t\t\t\tdist = this._sqDist(objectPoint[L.Util.stamp(obj)], point);\n\t\t\t\t\t\t\tif (dist < closestDistSq ||\n\t\t\t\t\t\t\t\tdist <= closestDistSq && closest === null) {\n\t\t\t\t\t\t\t\tclosestDistSq = dist;\n\t\t\t\t\t\t\t\tclosest = obj;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\treturn closest;\n\t},\n\n\t_getCoord: function (x) {\n\t\tvar coord = Math.floor(x / this._cellSize);\n\t\treturn isFinite(coord) ? coord : x;\n\t},\n\n\t_sqDist: function (p, p2) {\n\t\tvar dx = p2.x - p.x,\n\t\t    dy = p2.y - p.y;\n\t\treturn dx * dx + dy * dy;\n\t}\n};\n", "/* Copyright (c) 2012 the authors listed at the following URL, and/or\nthe authors of referenced articles or incorporated external code:\nhttp://en.literateprograms.org/Quickhull_(Javascript)?action=history&offset=20120410175256\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.\nIN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY\nCLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,\nTORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE\nSOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n\nRetrieved from: http://en.literateprograms.org/Quickhull_(Javascript)?oldid=18434\n*/\n\n(function () {\n\tL.QuickHull = {\n\n\t\t/*\n\t\t * @param {Object} cpt a point to be measured from the baseline\n\t\t * @param {Array} bl the baseline, as represented by a two-element\n\t\t *   array of latlng objects.\n\t\t * @returns {Number} an approximate distance measure\n\t\t */\n\t\tgetDistant: function (cpt, bl) {\n\t\t\tvar vY = bl[1].lat - bl[0].lat,\n\t\t\t\tvX = bl[0].lng - bl[1].lng;\n\t\t\treturn (vX * (cpt.lat - bl[0].lat) + vY * (cpt.lng - bl[0].lng));\n\t\t},\n\n\t\t/*\n\t\t * @param {Array} baseLine a two-element array of latlng objects\n\t\t *   representing the baseline to project from\n\t\t * @param {Array} latLngs an array of latlng objects\n\t\t * @returns {Object} the maximum point and all new points to stay\n\t\t *   in consideration for the hull.\n\t\t */\n\t\tfindMostDistantPointFromBaseLine: function (baseLine, latLngs) {\n\t\t\tvar maxD = 0,\n\t\t\t\tmaxPt = null,\n\t\t\t\tnewPoints = [],\n\t\t\t\ti, pt, d;\n\n\t\t\tfor (i = latLngs.length - 1; i >= 0; i--) {\n\t\t\t\tpt = latLngs[i];\n\t\t\t\td = this.getDistant(pt, baseLine);\n\n\t\t\t\tif (d > 0) {\n\t\t\t\t\tnewPoints.push(pt);\n\t\t\t\t} else {\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\n\t\t\t\tif (d > maxD) {\n\t\t\t\t\tmaxD = d;\n\t\t\t\t\tmaxPt = pt;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\treturn { maxPoint: maxPt, newPoints: newPoints };\n\t\t},\n\n\n\t\t/*\n\t\t * Given a baseline, compute the convex hull of latLngs as an array\n\t\t * of latLngs.\n\t\t *\n\t\t * @param {Array} latLngs\n\t\t * @returns {Array}\n\t\t */\n\t\tbuildConvexHull: function (baseLine, latLngs) {\n\t\t\tvar convexHullBaseLines = [],\n\t\t\t\tt = this.findMostDistantPointFromBaseLine(baseLine, latLngs);\n\n\t\t\tif (t.maxPoint) { // if there is still a point \"outside\" the base line\n\t\t\t\tconvexHullBaseLines =\n\t\t\t\t\tconvexHullBaseLines.concat(\n\t\t\t\t\t\tthis.buildConvexHull([baseLine[0], t.maxPoint], t.newPoints)\n\t\t\t\t\t);\n\t\t\t\tconvexHullBaseLines =\n\t\t\t\t\tconvexHullBaseLines.concat(\n\t\t\t\t\t\tthis.buildConvexHull([t.maxPoint, baseLine[1]], t.newPoints)\n\t\t\t\t\t);\n\t\t\t\treturn convexHullBaseLines;\n\t\t\t} else {  // if there is no more point \"outside\" the base line, the current base line is part of the convex hull\n\t\t\t\treturn [baseLine[0]];\n\t\t\t}\n\t\t},\n\n\t\t/*\n\t\t * Given an array of latlngs, compute a convex hull as an array\n\t\t * of latlngs\n\t\t *\n\t\t * @param {Array} latLngs\n\t\t * @returns {Array}\n\t\t */\n\t\tgetConvexHull: function (latLngs) {\n\t\t\t// find first baseline\n\t\t\tvar maxLat = false, minLat = false,\n\t\t\t\tmaxLng = false, minLng = false,\n\t\t\t\tmaxLatPt = null, minLatPt = null,\n\t\t\t\tmaxLngPt = null, minLngPt = null,\n\t\t\t\tmaxPt = null, minPt = null,\n\t\t\t\ti;\n\n\t\t\tfor (i = latLngs.length - 1; i >= 0; i--) {\n\t\t\t\tvar pt = latLngs[i];\n\t\t\t\tif (maxLat === false || pt.lat > maxLat) {\n\t\t\t\t\tmaxLatPt = pt;\n\t\t\t\t\tmaxLat = pt.lat;\n\t\t\t\t}\n\t\t\t\tif (minLat === false || pt.lat < minLat) {\n\t\t\t\t\tminLatPt = pt;\n\t\t\t\t\tminLat = pt.lat;\n\t\t\t\t}\n\t\t\t\tif (maxLng === false || pt.lng > maxLng) {\n\t\t\t\t\tmaxLngPt = pt;\n\t\t\t\t\tmaxLng = pt.lng;\n\t\t\t\t}\n\t\t\t\tif (minLng === false || pt.lng < minLng) {\n\t\t\t\t\tminLngPt = pt;\n\t\t\t\t\tminLng = pt.lng;\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\tif (minLat !== maxLat) {\n\t\t\t\tminPt = minLatPt;\n\t\t\t\tmaxPt = maxLatPt;\n\t\t\t} else {\n\t\t\t\tminPt = minLngPt;\n\t\t\t\tmaxPt = maxLngPt;\n\t\t\t}\n\n\t\t\tvar ch = [].concat(this.buildConvexHull([minPt, maxPt], latLngs),\n\t\t\t\t\t\t\t\tthis.buildConvexHull([maxPt, minPt], latLngs));\n\t\t\treturn ch;\n\t\t}\n\t};\n}());\n\nL.MarkerCluster.include({\n\tgetConvexHull: function () {\n\t\tvar childMarkers = this.getAllChildMarkers(),\n\t\t\tpoints = [],\n\t\t\tp, i;\n\n\t\tfor (i = childMarkers.length - 1; i >= 0; i--) {\n\t\t\tp = childMarkers[i].getLatLng();\n\t\t\tpoints.push(p);\n\t\t}\n\n\t\treturn L.QuickHull.getConvexHull(points);\n\t}\n});\n", "//This code is 100% based on https://github.com/jawj/OverlappingMarkerSpiderfier-Leaflet\n//Huge thanks to jawj for implementing it first to make my job easy :-)\n\nL.MarkerCluster.include({\n\n\t_2PI: Math.PI * 2,\n\t_circleFootSeparation: 25, //related to circumference of circle\n\t_circleStartAngle: 0,\n\n\t_spiralFootSeparation:  28, //related to size of spiral (experiment!)\n\t_spiralLengthStart: 11,\n\t_spiralLengthFactor: 5,\n\n\t_circleSpiralSwitchover: 9, //show spiral instead of circle from this marker count upwards.\n\t\t\t\t\t\t\t\t// 0 -> always spiral; Infinity -> always circle\n\n\tspiderfy: function () {\n\t\tif (this._group._spiderfied === this || this._group._inZoomAnimation) {\n\t\t\treturn;\n\t\t}\n\n\t\tvar childMarkers = this.getAllChildMarkers(null, true),\n\t\t\tgroup = this._group,\n\t\t\tmap = group._map,\n\t\t\tcenter = map.latLngToLayerPoint(this._latlng),\n\t\t\tpositions;\n\n\t\tthis._group._unspiderfy();\n\t\tthis._group._spiderfied = this;\n\n\t\t//TODO Maybe: childMarkers order by distance to center\n\n\t\tif (this._group.options.spiderfyShapePositions) {\n\t\t\tpositions = this._group.options.spiderfyShapePositions(childMarkers.length, center);\n\t\t} else if (childMarkers.length >= this._circleSpiralSwitchover) {\n\t\t\tpositions = this._generatePointsSpiral(childMarkers.length, center);\n\t\t} else {\n\t\t\tcenter.y += 10; // Otherwise circles look wrong => hack for standard blue icon, renders differently for other icons.\n\t\t\tpositions = this._generatePointsCircle(childMarkers.length, center);\n\t\t}\n\n\t\tthis._animationSpiderfy(childMarkers, positions);\n\t},\n\n\tunspiderfy: function (zoomDetails) {\n\t\t/// <param Name=\"zoomDetails\">Argument from zoomanim if being called in a zoom animation or null otherwise</param>\n\t\tif (this._group._inZoomAnimation) {\n\t\t\treturn;\n\t\t}\n\t\tthis._animationUnspiderfy(zoomDetails);\n\n\t\tthis._group._spiderfied = null;\n\t},\n\n\t_generatePointsCircle: function (count, centerPt) {\n\t\tvar circumference = this._group.options.spiderfyDistanceMultiplier * this._circleFootSeparation * (2 + count),\n\t\t\tlegLength = circumference / this._2PI,  //radius from circumference\n\t\t\tangleStep = this._2PI / count,\n\t\t\tres = [],\n\t\t\ti, angle;\n\n\t\tlegLength = Math.max(legLength, 35); // Minimum distance to get outside the cluster icon.\n\n\t\tres.length = count;\n\n\t\tfor (i = 0; i < count; i++) { // Clockwise, like spiral.\n\t\t\tangle = this._circleStartAngle + i * angleStep;\n\t\t\tres[i] = new L.Point(centerPt.x + legLength * Math.cos(angle), centerPt.y + legLength * Math.sin(angle))._round();\n\t\t}\n\n\t\treturn res;\n\t},\n\n\t_generatePointsSpiral: function (count, centerPt) {\n\t\tvar spiderfyDistanceMultiplier = this._group.options.spiderfyDistanceMultiplier,\n\t\t\tlegLength = spiderfyDistanceMultiplier * this._spiralLengthStart,\n\t\t\tseparation = spiderfyDistanceMultiplier * this._spiralFootSeparation,\n\t\t\tlengthFactor = spiderfyDistanceMultiplier * this._spiralLengthFactor * this._2PI,\n\t\t\tangle = 0,\n\t\t\tres = [],\n\t\t\ti;\n\n\t\tres.length = count;\n\n\t\t// Higher index, closer position to cluster center.\n\t\tfor (i = count; i >= 0; i--) {\n\t\t\t// Skip the first position, so that we are already farther from center and we avoid\n\t\t\t// being under the default cluster icon (especially important for Circle Markers).\n\t\t\tif (i < count) {\n\t\t\t\tres[i] = new L.Point(centerPt.x + legLength * Math.cos(angle), centerPt.y + legLength * Math.sin(angle))._round();\n\t\t\t}\n\t\t\tangle += separation / legLength + i * 0.0005;\n\t\t\tlegLength += lengthFactor / angle;\n\t\t}\n\t\treturn res;\n\t},\n\n\t_noanimationUnspiderfy: function () {\n\t\tvar group = this._group,\n\t\t\tmap = group._map,\n\t\t\tfg = group._featureGroup,\n\t\t\tchildMarkers = this.getAllChildMarkers(null, true),\n\t\t\tm, i;\n\n\t\tgroup._ignoreMove = true;\n\n\t\tthis.setOpacity(1);\n\t\tfor (i = childMarkers.length - 1; i >= 0; i--) {\n\t\t\tm = childMarkers[i];\n\n\t\t\tfg.removeLayer(m);\n\n\t\t\tif (m._preSpiderfyLatlng) {\n\t\t\t\tm.setLatLng(m._preSpiderfyLatlng);\n\t\t\t\tdelete m._preSpiderfyLatlng;\n\t\t\t}\n\t\t\tif (m.setZIndexOffset) {\n\t\t\t\tm.setZIndexOffset(0);\n\t\t\t}\n\n\t\t\tif (m._spiderLeg) {\n\t\t\t\tmap.removeLayer(m._spiderLeg);\n\t\t\t\tdelete m._spiderLeg;\n\t\t\t}\n\t\t}\n\n\t\tgroup.fire('unspiderfied', {\n\t\t\tcluster: this,\n\t\t\tmarkers: childMarkers\n\t\t});\n\t\tgroup._ignoreMove = false;\n\t\tgroup._spiderfied = null;\n\t}\n});\n\n//Non Animated versions of everything\nL.MarkerClusterNonAnimated = L.MarkerCluster.extend({\n\t_animationSpiderfy: function (childMarkers, positions) {\n\t\tvar group = this._group,\n\t\t\tmap = group._map,\n\t\t\tfg = group._featureGroup,\n\t\t\tlegOptions = this._group.options.spiderLegPolylineOptions,\n\t\t\ti, m, leg, newPos;\n\n\t\tgroup._ignoreMove = true;\n\n\t\t// Traverse in ascending order to make sure that inner circleMarkers are on top of further legs. Normal markers are re-ordered by newPosition.\n\t\t// The reverse order trick no longer improves performance on modern browsers.\n\t\tfor (i = 0; i < childMarkers.length; i++) {\n\t\t\tnewPos = map.layerPointToLatLng(positions[i]);\n\t\t\tm = childMarkers[i];\n\n\t\t\t// Add the leg before the marker, so that in case the latter is a circleMarker, the leg is behind it.\n\t\t\tleg = new L.Polyline([this._latlng, newPos], legOptions);\n\t\t\tmap.addLayer(leg);\n\t\t\tm._spiderLeg = leg;\n\n\t\t\t// Now add the marker.\n\t\t\tm._preSpiderfyLatlng = m._latlng;\n\t\t\tm.setLatLng(newPos);\n\t\t\tif (m.setZIndexOffset) {\n\t\t\t\tm.setZIndexOffset(1000000); //Make these appear on top of EVERYTHING\n\t\t\t}\n\n\t\t\tfg.addLayer(m);\n\t\t}\n\t\tthis.setOpacity(0.3);\n\n\t\tgroup._ignoreMove = false;\n\t\tgroup.fire('spiderfied', {\n\t\t\tcluster: this,\n\t\t\tmarkers: childMarkers\n\t\t});\n\t},\n\n\t_animationUnspiderfy: function () {\n\t\tthis._noanimationUnspiderfy();\n\t}\n});\n\n//Animated versions here\nL.MarkerCluster.include({\n\n\t_animationSpiderfy: function (childMarkers, positions) {\n\t\tvar me = this,\n\t\t\tgroup = this._group,\n\t\t\tmap = group._map,\n\t\t\tfg = group._featureGroup,\n\t\t\tthisLayerLatLng = this._latlng,\n\t\t\tthisLayerPos = map.latLngToLayerPoint(thisLayerLatLng),\n\t\t\tsvg = L.Path.SVG,\n\t\t\tlegOptions = L.extend({}, this._group.options.spiderLegPolylineOptions), // Copy the options so that we can modify them for animation.\n\t\t\tfinalLegOpacity = legOptions.opacity,\n\t\t\ti, m, leg, legPath, legLength, newPos;\n\n\t\tif (finalLegOpacity === undefined) {\n\t\t\tfinalLegOpacity = L.MarkerClusterGroup.prototype.options.spiderLegPolylineOptions.opacity;\n\t\t}\n\n\t\tif (svg) {\n\t\t\t// If the initial opacity of the spider leg is not 0 then it appears before the animation starts.\n\t\t\tlegOptions.opacity = 0;\n\n\t\t\t// Add the class for CSS transitions.\n\t\t\tlegOptions.className = (legOptions.className || '') + ' leaflet-cluster-spider-leg';\n\t\t} else {\n\t\t\t// Make sure we have a defined opacity.\n\t\t\tlegOptions.opacity = finalLegOpacity;\n\t\t}\n\n\t\tgroup._ignoreMove = true;\n\n\t\t// Add markers and spider legs to map, hidden at our center point.\n\t\t// Traverse in ascending order to make sure that inner circleMarkers are on top of further legs. Normal markers are re-ordered by newPosition.\n\t\t// The reverse order trick no longer improves performance on modern browsers.\n\t\tfor (i = 0; i < childMarkers.length; i++) {\n\t\t\tm = childMarkers[i];\n\n\t\t\tnewPos = map.layerPointToLatLng(positions[i]);\n\n\t\t\t// Add the leg before the marker, so that in case the latter is a circleMarker, the leg is behind it.\n\t\t\tleg = new L.Polyline([thisLayerLatLng, newPos], legOptions);\n\t\t\tmap.addLayer(leg);\n\t\t\tm._spiderLeg = leg;\n\n\t\t\t// Explanations: https://jakearchibald.com/2013/animated-line-drawing-svg/\n\t\t\t// In our case the transition property is declared in the CSS file.\n\t\t\tif (svg) {\n\t\t\t\tlegPath = leg._path;\n\t\t\t\tlegLength = legPath.getTotalLength() + 0.1; // Need a small extra length to avoid remaining dot in Firefox.\n\t\t\t\tlegPath.style.strokeDasharray = legLength; // Just 1 length is enough, it will be duplicated.\n\t\t\t\tlegPath.style.strokeDashoffset = legLength;\n\t\t\t}\n\n\t\t\t// If it is a marker, add it now and we'll animate it out\n\t\t\tif (m.setZIndexOffset) {\n\t\t\t\tm.setZIndexOffset(1000000); // Make normal markers appear on top of EVERYTHING\n\t\t\t}\n\t\t\tif (m.clusterHide) {\n\t\t\t\tm.clusterHide();\n\t\t\t}\n\t\t\t\n\t\t\t// Vectors just get immediately added\n\t\t\tfg.addLayer(m);\n\n\t\t\tif (m._setPos) {\n\t\t\t\tm._setPos(thisLayerPos);\n\t\t\t}\n\t\t}\n\n\t\tgroup._forceLayout();\n\t\tgroup._animationStart();\n\n\t\t// Reveal markers and spider legs.\n\t\tfor (i = childMarkers.length - 1; i >= 0; i--) {\n\t\t\tnewPos = map.layerPointToLatLng(positions[i]);\n\t\t\tm = childMarkers[i];\n\n\t\t\t//Move marker to new position\n\t\t\tm._preSpiderfyLatlng = m._latlng;\n\t\t\tm.setLatLng(newPos);\n\t\t\t\n\t\t\tif (m.clusterShow) {\n\t\t\t\tm.clusterShow();\n\t\t\t}\n\n\t\t\t// Animate leg (animation is actually delegated to CSS transition).\n\t\t\tif (svg) {\n\t\t\t\tleg = m._spiderLeg;\n\t\t\t\tlegPath = leg._path;\n\t\t\t\tlegPath.style.strokeDashoffset = 0;\n\t\t\t\t//legPath.style.strokeOpacity = finalLegOpacity;\n\t\t\t\tleg.setStyle({opacity: finalLegOpacity});\n\t\t\t}\n\t\t}\n\t\tthis.setOpacity(0.3);\n\n\t\tgroup._ignoreMove = false;\n\n\t\tsetTimeout(function () {\n\t\t\tgroup._animationEnd();\n\t\t\tgroup.fire('spiderfied', {\n\t\t\t\tcluster: me,\n\t\t\t\tmarkers: childMarkers\n\t\t\t});\n\t\t}, 200);\n\t},\n\n\t_animationUnspiderfy: function (zoomDetails) {\n\t\tvar me = this,\n\t\t\tgroup = this._group,\n\t\t\tmap = group._map,\n\t\t\tfg = group._featureGroup,\n\t\t\tthisLayerPos = zoomDetails ? map._latLngToNewLayerPoint(this._latlng, zoomDetails.zoom, zoomDetails.center) : map.latLngToLayerPoint(this._latlng),\n\t\t\tchildMarkers = this.getAllChildMarkers(null, true),\n\t\t\tsvg = L.Path.SVG,\n\t\t\tm, i, leg, legPath, legLength, nonAnimatable;\n\n\t\tgroup._ignoreMove = true;\n\t\tgroup._animationStart();\n\n\t\t//Make us visible and bring the child markers back in\n\t\tthis.setOpacity(1);\n\t\tfor (i = childMarkers.length - 1; i >= 0; i--) {\n\t\t\tm = childMarkers[i];\n\n\t\t\t//Marker was added to us after we were spiderfied\n\t\t\tif (!m._preSpiderfyLatlng) {\n\t\t\t\tcontinue;\n\t\t\t}\n\n\t\t\t//Close any popup on the marker first, otherwise setting the location of the marker will make the map scroll\n\t\t\tm.closePopup();\n\n\t\t\t//Fix up the location to the real one\n\t\t\tm.setLatLng(m._preSpiderfyLatlng);\n\t\t\tdelete m._preSpiderfyLatlng;\n\n\t\t\t//Hack override the location to be our center\n\t\t\tnonAnimatable = true;\n\t\t\tif (m._setPos) {\n\t\t\t\tm._setPos(thisLayerPos);\n\t\t\t\tnonAnimatable = false;\n\t\t\t}\n\t\t\tif (m.clusterHide) {\n\t\t\t\tm.clusterHide();\n\t\t\t\tnonAnimatable = false;\n\t\t\t}\n\t\t\tif (nonAnimatable) {\n\t\t\t\tfg.removeLayer(m);\n\t\t\t}\n\n\t\t\t// Animate the spider leg back in (animation is actually delegated to CSS transition).\n\t\t\tif (svg) {\n\t\t\t\tleg = m._spiderLeg;\n\t\t\t\tlegPath = leg._path;\n\t\t\t\tlegLength = legPath.getTotalLength() + 0.1;\n\t\t\t\tlegPath.style.strokeDashoffset = legLength;\n\t\t\t\tleg.setStyle({opacity: 0});\n\t\t\t}\n\t\t}\n\n\t\tgroup._ignoreMove = false;\n\n\t\tsetTimeout(function () {\n\t\t\t//If we have only <= one child left then that marker will be shown on the map so don't remove it!\n\t\t\tvar stillThereChildCount = 0;\n\t\t\tfor (i = childMarkers.length - 1; i >= 0; i--) {\n\t\t\t\tm = childMarkers[i];\n\t\t\t\tif (m._spiderLeg) {\n\t\t\t\t\tstillThereChildCount++;\n\t\t\t\t}\n\t\t\t}\n\n\n\t\t\tfor (i = childMarkers.length - 1; i >= 0; i--) {\n\t\t\t\tm = childMarkers[i];\n\n\t\t\t\tif (!m._spiderLeg) { //Has already been unspiderfied\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\n\t\t\t\tif (m.clusterShow) {\n\t\t\t\t\tm.clusterShow();\n\t\t\t\t}\n\t\t\t\tif (m.setZIndexOffset) {\n\t\t\t\t\tm.setZIndexOffset(0);\n\t\t\t\t}\n\n\t\t\t\tif (stillThereChildCount > 1) {\n\t\t\t\t\tfg.removeLayer(m);\n\t\t\t\t}\n\n\t\t\t\tmap.removeLayer(m._spiderLeg);\n\t\t\t\tdelete m._spiderLeg;\n\t\t\t}\n\t\t\tgroup._animationEnd();\n\t\t\tgroup.fire('unspiderfied', {\n\t\t\t\tcluster: me,\n\t\t\t\tmarkers: childMarkers\n\t\t\t});\n\t\t}, 200);\n\t}\n});\n\n\nL.MarkerClusterGroup.include({\n\t//The MarkerCluster currently spiderfied (if any)\n\t_spiderfied: null,\n\n\tunspiderfy: function () {\n\t\tthis._unspiderfy.apply(this, arguments);\n\t},\n\n\t_spiderfierOnAdd: function () {\n\t\tthis._map.on('click', this._unspiderfyWrapper, this);\n\n\t\tif (this._map.options.zoomAnimation) {\n\t\t\tthis._map.on('zoomstart', this._unspiderfyZoomStart, this);\n\t\t}\n\t\t//Browsers without zoomAnimation or a big zoom don't fire zoomstart\n\t\tthis._map.on('zoomend', this._noanimationUnspiderfy, this);\n\n\t\tif (!L.Browser.touch) {\n\t\t\tthis._map.getRenderer(this);\n\t\t\t//Needs to happen in the pageload, not after, or animations don't work in webkit\n\t\t\t//  http://stackoverflow.com/questions/8455200/svg-animate-with-dynamically-added-elements\n\t\t\t//Disable on touch browsers as the animation messes up on a touch zoom and isn't very noticable\n\t\t}\n\t},\n\n\t_spiderfierOnRemove: function () {\n\t\tthis._map.off('click', this._unspiderfyWrapper, this);\n\t\tthis._map.off('zoomstart', this._unspiderfyZoomStart, this);\n\t\tthis._map.off('zoomanim', this._unspiderfyZoomAnim, this);\n\t\tthis._map.off('zoomend', this._noanimationUnspiderfy, this);\n\n\t\t//Ensure that markers are back where they should be\n\t\t// Use no animation to avoid a sticky leaflet-cluster-anim class on mapPane\n\t\tthis._noanimationUnspiderfy();\n\t},\n\n\t//On zoom start we add a zoomanim handler so that we are guaranteed to be last (after markers are animated)\n\t//This means we can define the animation they do rather than Markers doing an animation to their actual location\n\t_unspiderfyZoomStart: function () {\n\t\tif (!this._map) { //May have been removed from the map by a zoomEnd handler\n\t\t\treturn;\n\t\t}\n\n\t\tthis._map.on('zoomanim', this._unspiderfyZoomAnim, this);\n\t},\n\n\t_unspiderfyZoomAnim: function (zoomDetails) {\n\t\t//Wait until the first zoomanim after the user has finished touch-zooming before running the animation\n\t\tif (L.DomUtil.hasClass(this._map._mapPane, 'leaflet-touching')) {\n\t\t\treturn;\n\t\t}\n\n\t\tthis._map.off('zoomanim', this._unspiderfyZoomAnim, this);\n\t\tthis._unspiderfy(zoomDetails);\n\t},\n\n\t_unspiderfyWrapper: function () {\n\t\t/// <summary>_unspiderfy but passes no arguments</summary>\n\t\tthis._unspiderfy();\n\t},\n\n\t_unspiderfy: function (zoomDetails) {\n\t\tif (this._spiderfied) {\n\t\t\tthis._spiderfied.unspiderfy(zoomDetails);\n\t\t}\n\t},\n\n\t_noanimationUnspiderfy: function () {\n\t\tif (this._spiderfied) {\n\t\t\tthis._spiderfied._noanimationUnspiderfy();\n\t\t}\n\t},\n\n\t//If the given layer is currently being spiderfied then we unspiderfy it so it isn't on the map anymore etc\n\t_unspiderfyLayer: function (layer) {\n\t\tif (layer._spiderLeg) {\n\t\t\tthis._featureGroup.removeLayer(layer);\n\n\t\t\tif (layer.clusterShow) {\n\t\t\t\tlayer.clusterShow();\n\t\t\t}\n\t\t\t\t//Position will be fixed up immediately in _animationUnspiderfy\n\t\t\tif (layer.setZIndexOffset) {\n\t\t\t\tlayer.setZIndexOffset(0);\n\t\t\t}\n\n\t\t\tthis._map.removeLayer(layer._spiderLeg);\n\t\t\tdelete layer._spiderLeg;\n\t\t}\n\t}\n});\n", "/**\n * Adds 1 public method to MC<PERSON> and 1 to <PERSON><PERSON> to facilitate changing\n * markers' icon options and refreshing their icon and their parent clusters\n * accordingly (case where their iconCreateFunction uses data of childMarkers\n * to make up the cluster icon).\n */\n\n\nL.MarkerClusterGroup.include({\n\t/**\n\t * Updates the icon of all clusters which are parents of the given marker(s).\n\t * In singleMarkerMode, also updates the given marker(s) icon.\n\t * @param layers L.MarkerClusterGroup|L.LayerGroup|Array(L.Marker)|Map(L.Marker)|\n\t * L.MarkerCluster|L.Marker (optional) list of markers (or single marker) whose parent\n\t * clusters need to be updated. If not provided, retrieves all child markers of this.\n\t * @returns {L.MarkerClusterGroup}\n\t */\n\trefreshClusters: function (layers) {\n\t\tif (!layers) {\n\t\t\tlayers = this._topClusterLevel.getAllChildMarkers();\n\t\t} else if (layers instanceof L.MarkerClusterGroup) {\n\t\t\tlayers = layers._topClusterLevel.getAllChildMarkers();\n\t\t} else if (layers instanceof L.LayerGroup) {\n\t\t\tlayers = layers._layers;\n\t\t} else if (layers instanceof L.MarkerCluster) {\n\t\t\tlayers = layers.getAllChildMarkers();\n\t\t} else if (layers instanceof L.Marker) {\n\t\t\tlayers = [layers];\n\t\t} // else: must be an Array(L.Marker)|Map(L.Marker)\n\t\tthis._flagParentsIconsNeedUpdate(layers);\n\t\tthis._refreshClustersIcons();\n\n\t\t// In case of singleMarkerMode, also re-draw the markers.\n\t\tif (this.options.singleMarkerMode) {\n\t\t\tthis._refreshSingleMarkerModeMarkers(layers);\n\t\t}\n\n\t\treturn this;\n\t},\n\n\t/**\n\t * Simply flags all parent clusters of the given markers as having a \"dirty\" icon.\n\t * @param layers Array(L.Marker)|Map(L.Marker) list of markers.\n\t * @private\n\t */\n\t_flagParentsIconsNeedUpdate: function (layers) {\n\t\tvar id, parent;\n\n\t\t// Assumes layers is an Array or an Object whose prototype is non-enumerable.\n\t\tfor (id in layers) {\n\t\t\t// Flag parent clusters' icon as \"dirty\", all the way up.\n\t\t\t// Dumb process that flags multiple times upper parents, but still\n\t\t\t// much more efficient than trying to be smart and make short lists,\n\t\t\t// at least in the case of a hierarchy following a power law:\n\t\t\t// http://jsperf.com/flag-nodes-in-power-hierarchy/2\n\t\t\tparent = layers[id].__parent;\n\t\t\twhile (parent) {\n\t\t\t\tparent._iconNeedsUpdate = true;\n\t\t\t\tparent = parent.__parent;\n\t\t\t}\n\t\t}\n\t},\n\n\t/**\n\t * Re-draws the icon of the supplied markers.\n\t * To be used in singleMarkerMode only.\n\t * @param layers Array(L.Marker)|Map(L.Marker) list of markers.\n\t * @private\n\t */\n\t_refreshSingleMarkerModeMarkers: function (layers) {\n\t\tvar id, layer;\n\n\t\tfor (id in layers) {\n\t\t\tlayer = layers[id];\n\n\t\t\t// Make sure we do not override markers that do not belong to THIS group.\n\t\t\tif (this.hasLayer(layer)) {\n\t\t\t\t// Need to re-create the icon first, then re-draw the marker.\n\t\t\t\tlayer.setIcon(this._overrideMarkerIcon(layer));\n\t\t\t}\n\t\t}\n\t}\n});\n\nL.Marker.include({\n\t/**\n\t * Updates the given options in the marker's icon and refreshes the marker.\n\t * @param options map object of icon options.\n\t * @param directlyRefreshClusters boolean (optional) true to trigger\n\t * MCG.refreshClustersOf() right away with this single marker.\n\t * @returns {L.Marker}\n\t */\n\trefreshIconOptions: function (options, directlyRefreshClusters) {\n\t\tvar icon = this.options.icon;\n\n\t\tL.setOptions(icon, options);\n\n\t\tthis.setIcon(icon);\n\n\t\t// Shortcut to refresh the associated MCG clusters right away.\n\t\t// To be used when refreshing a single marker.\n\t\t// Otherwise, better use MCG.refreshClusters() once at the end with\n\t\t// the list of modified markers.\n\t\tif (directlyRefreshClusters && this.__parent) {\n\t\t\tthis.__parent._group.refreshClusters(this);\n\t\t}\n\n\t\treturn this;\n\t}\n});\n"], "mappings": ";;;;;;;;;;;AAIU,UAAC,qBAAqB,EAAE,qBAAqB,EAAE,aAAa,OAAO;QAE5E,SAAS;UACR,kBAAkB;;UAClB,oBAAoB;UACpB,aAAa,EAAE,OAAO,UAAU,QAAQ;UAExC,qBAAqB;UACrB,mBAAmB;UACnB,qBAAqB;UACrB,qBAAqB;UACrB,kBAAkB;UAElB,yBAAyB;;;UAIzB,4BAA4B;;;;UAK5B,SAAS;;;UAIT,sBAAsB;;UAGtB,wBAAwB;;UAGxB,4BAA4B;;UAG5B,0BAA0B,EAAE,QAAQ,KAAK,OAAO,QAAQ,SAAS,IAAG;;UAGpE,gBAAgB;UAChB,eAAe;;UACf,YAAY;;UACZ,eAAe;;;UAGf,gBAAgB,CAAA;QAClB;QAEC,YAAY,SAAU,SAAS;AAC9B,YAAE,KAAK,WAAW,MAAM,OAAO;AAC/B,cAAI,CAAC,KAAK,QAAQ,oBAAoB;AACrC,iBAAK,QAAQ,qBAAqB,KAAK;UAC1C;AAEE,eAAK,gBAAgB,EAAE,aAAY;AACnC,eAAK,cAAc,eAAe,IAAI;AAEtC,eAAK,iBAAiB,EAAE,aAAY;AACpC,eAAK,eAAe,eAAe,IAAI;AAEvC,eAAK,mBAAmB;AACxB,eAAK,mBAAmB,CAAA;AACxB,eAAK,iBAAiB,CAAA;AAEtB,eAAK,sBAAsB;AAE3B,eAAK,SAAS,CAAA;AAEd,eAAK,4BAA4B;YAChC,aAAa,KAAK;YAClB,QAAQ,KAAK;YACb,WAAW,KAAK;UACnB;AAGE,cAAI,UAAU,EAAE,QAAQ,cAAc,KAAK,QAAQ;AACnD,YAAE,OAAO,MAAM,UAAU,KAAK,iBAAiB,KAAK,YAAY;AAEhE,eAAK,iBAAiB,UAAU,EAAE,gBAAgB,EAAE;QACtD;QAEC,UAAU,SAAU,OAAO;AAE1B,cAAI,iBAAiB,EAAE,YAAY;AAClC,mBAAO,KAAK,UAAU,CAAC,KAAK,CAAC;UAChC;AAGE,cAAI,CAAC,MAAM,WAAW;AACrB,iBAAK,eAAe,SAAS,KAAK;AAClC,iBAAK,KAAK,YAAY,EAAE,MAAY,CAAE;AACtC,mBAAO;UACV;AAEE,cAAI,CAAC,KAAK,MAAM;AACf,iBAAK,iBAAiB,KAAK,KAAK;AAChC,iBAAK,KAAK,YAAY,EAAE,MAAY,CAAE;AACtC,mBAAO;UACV;AAEE,cAAI,KAAK,SAAS,KAAK,GAAG;AACzB,mBAAO;UACV;AAKE,cAAI,KAAK,aAAa;AACrB,iBAAK,YAAW;UACnB;AAEE,eAAK,UAAU,OAAO,KAAK,QAAQ;AACnC,eAAK,KAAK,YAAY,EAAE,MAAY,CAAE;AAGtC,eAAK,iBAAiB,mBAAkB;AAExC,eAAK,sBAAqB;AAG1B,cAAI,eAAe,OACf,cAAc,KAAK;AACvB,cAAI,MAAM,UAAU;AACnB,mBAAO,aAAa,SAAS,SAAS,aAAa;AAClD,6BAAe,aAAa;YAChC;UACA;AAEE,cAAI,KAAK,oBAAoB,SAAS,aAAa,UAAS,CAAE,GAAG;AAChE,gBAAI,KAAK,QAAQ,sBAAsB;AACtC,mBAAK,mBAAmB,OAAO,YAAY;YAC/C,OAAU;AACN,mBAAK,8BAA8B,OAAO,YAAY;YAC1D;UACA;AACE,iBAAO;QACT;QAEC,aAAa,SAAU,OAAO;AAE7B,cAAI,iBAAiB,EAAE,YAAY;AAClC,mBAAO,KAAK,aAAa,CAAC,KAAK,CAAC;UACnC;AAGE,cAAI,CAAC,MAAM,WAAW;AACrB,iBAAK,eAAe,YAAY,KAAK;AACrC,iBAAK,KAAK,eAAe,EAAE,MAAY,CAAE;AACzC,mBAAO;UACV;AAEE,cAAI,CAAC,KAAK,MAAM;AACf,gBAAI,CAAC,KAAK,aAAa,KAAK,kBAAkB,KAAK,KAAK,KAAK,SAAS,KAAK,GAAG;AAC7E,mBAAK,eAAe,KAAK,EAAE,OAAc,QAAQ,MAAM,QAAO,CAAE;YACpE;AACG,iBAAK,KAAK,eAAe,EAAE,MAAY,CAAE;AACzC,mBAAO;UACV;AAEE,cAAI,CAAC,MAAM,UAAU;AACpB,mBAAO;UACV;AAEE,cAAI,KAAK,aAAa;AACrB,iBAAK,YAAW;AAChB,iBAAK,iBAAiB,KAAK;UAC9B;AAGE,eAAK,aAAa,OAAO,IAAI;AAC7B,eAAK,KAAK,eAAe,EAAE,MAAY,CAAE;AAGzC,eAAK,iBAAiB,mBAAkB;AAExC,eAAK,sBAAqB;AAE1B,gBAAM,IAAI,KAAK,2BAA2B,IAAI;AAE9C,cAAI,KAAK,cAAc,SAAS,KAAK,GAAG;AACvC,iBAAK,cAAc,YAAY,KAAK;AACpC,gBAAI,MAAM,aAAa;AACtB,oBAAM,YAAW;YACrB;UACA;AAEE,iBAAO;QACT;;QAGC,WAAW,SAAU,aAAa,mBAAmB;AACpD,cAAI,CAAC,EAAE,KAAK,QAAQ,WAAW,GAAG;AACjC,mBAAO,KAAK,SAAS,WAAW;UACnC;AAEE,cAAI,KAAK,KAAK,eACV,MAAM,KAAK,gBACX,UAAU,KAAK,QAAQ,gBACvB,gBAAgB,KAAK,QAAQ,eAC7B,gBAAgB,KAAK,QAAQ,eAC7B,IAAI,YAAY,QAChB,SAAS,GACT,gBAAgB,MAChB;AAEJ,cAAI,KAAK,MAAM;AACd,gBAAI,WAAW,oBAAI,KAAI,GAAI,QAAO;AAClC,gBAAI,UAAU,EAAE,KAAK,WAAY;AAChC,kBAAI,SAAS,oBAAI,KAAI,GAAI,QAAO;AAGhC,kBAAI,KAAK,QAAQ,KAAK,aAAa;AAClC,qBAAK,YAAW;cACrB;AAEI,qBAAO,SAAS,GAAG,UAAU;AAC5B,oBAAI,WAAW,SAAS,QAAQ,GAAG;AAElC,sBAAI,WAAW,oBAAI,KAAI,GAAI,QAAO,IAAK;AACvC,sBAAI,UAAU,eAAe;AAC5B;kBACP;gBACA;AAEK,oBAAI,YAAY,MAAM;AAQtB,oBAAI,aAAa,EAAE,YAAY;AAC9B,sBAAI,eAAe;AAClB,kCAAc,YAAY,MAAK;AAC/B,oCAAgB;kBACvB;AACM,uBAAK,uBAAuB,GAAG,WAAW;AAC1C,sBAAI,YAAY;AAChB;gBACN;AAGK,oBAAI,CAAC,EAAE,WAAW;AACjB,sBAAI,SAAS,CAAC;AACd,sBAAI,CAAC,mBAAmB;AACvB,yBAAK,KAAK,YAAY,EAAE,OAAO,EAAC,CAAE;kBACzC;AACM;gBACN;AAEK,oBAAI,KAAK,SAAS,CAAC,GAAG;AACrB;gBACN;AAEK,qBAAK,UAAU,GAAG,KAAK,QAAQ;AAC/B,oBAAI,CAAC,mBAAmB;AACvB,uBAAK,KAAK,YAAY,EAAE,OAAO,EAAC,CAAE;gBACxC;AAGK,oBAAI,EAAE,UAAU;AACf,sBAAI,EAAE,SAAS,cAAa,MAAO,GAAG;AACrC,wBAAI,UAAU,EAAE,SAAS,mBAAkB,GACvC,cAAc,QAAQ,CAAC,MAAM,IAAI,QAAQ,CAAC,IAAI,QAAQ,CAAC;AAC3D,uBAAG,YAAY,WAAW;kBACjC;gBACA;cACA;AAEI,kBAAI,eAAe;AAElB,8BAAc,QAAQ,IAAI,oBAAI,KAAI,GAAI,QAAO,IAAK,OAAO;cAC9D;AAGI,kBAAI,WAAW,GAAG;AAGjB,qBAAK,iBAAiB,mBAAkB;AAExC,qBAAK,sBAAqB;AAE1B,qBAAK,iBAAiB,6BAA6B,MAAM,KAAK,OAAO,KAAK,mBAAmB;cAClG,OAAW;AACN,2BAAW,SAAS,KAAK,QAAQ,UAAU;cAChD;YACA,GAAM,IAAI;AAEP,oBAAO;UACV,OAAS;AACN,gBAAI,kBAAkB,KAAK;AAE3B,mBAAO,SAAS,GAAG,UAAU;AAC5B,kBAAI,YAAY,MAAM;AAGtB,kBAAI,aAAa,EAAE,YAAY;AAC9B,oBAAI,eAAe;AAClB,gCAAc,YAAY,MAAK;AAC/B,kCAAgB;gBACtB;AACK,qBAAK,uBAAuB,GAAG,WAAW;AAC1C,oBAAI,YAAY;AAChB;cACL;AAGI,kBAAI,CAAC,EAAE,WAAW;AACjB,oBAAI,SAAS,CAAC;AACd;cACL;AAEI,kBAAI,KAAK,SAAS,CAAC,GAAG;AACrB;cACL;AAEI,8BAAgB,KAAK,CAAC;YAC1B;UACA;AACE,iBAAO;QACT;;QAGC,cAAc,SAAU,aAAa;AACpC,cAAI,GAAG,GACH,IAAI,YAAY,QAChB,KAAK,KAAK,eACV,MAAM,KAAK,gBACX,gBAAgB;AAEpB,cAAI,CAAC,KAAK,MAAM;AACf,iBAAK,IAAI,GAAG,IAAI,GAAG,KAAK;AACvB,kBAAI,YAAY,CAAC;AAGjB,kBAAI,aAAa,EAAE,YAAY;AAC9B,oBAAI,eAAe;AAClB,gCAAc,YAAY,MAAK;AAC/B,kCAAgB;gBACtB;AACK,qBAAK,uBAAuB,GAAG,WAAW;AAC1C,oBAAI,YAAY;AAChB;cACL;AAEI,mBAAK,aAAa,KAAK,kBAAkB,CAAC;AAC1C,kBAAI,YAAY,CAAC;AACjB,kBAAI,KAAK,SAAS,CAAC,GAAG;AACrB,qBAAK,eAAe,KAAK,EAAE,OAAO,GAAG,QAAQ,EAAE,QAAO,CAAE;cAC7D;AACI,mBAAK,KAAK,eAAe,EAAE,OAAO,EAAC,CAAE;YACzC;AACG,mBAAO;UACV;AAEE,cAAI,KAAK,aAAa;AACrB,iBAAK,YAAW;AAGhB,gBAAI,eAAe,YAAY,MAAK,GAChC,KAAK;AACT,iBAAK,IAAI,GAAG,IAAI,IAAI,KAAK;AACxB,kBAAI,aAAa,CAAC;AAGlB,kBAAI,aAAa,EAAE,YAAY;AAC9B,qBAAK,uBAAuB,GAAG,YAAY;AAC3C,qBAAK,aAAa;AAClB;cACL;AAEI,mBAAK,iBAAiB,CAAC;YAC3B;UACA;AAEE,eAAK,IAAI,GAAG,IAAI,GAAG,KAAK;AACvB,gBAAI,YAAY,CAAC;AAGjB,gBAAI,aAAa,EAAE,YAAY;AAC9B,kBAAI,eAAe;AAClB,8BAAc,YAAY,MAAK;AAC/B,gCAAgB;cACrB;AACI,mBAAK,uBAAuB,GAAG,WAAW;AAC1C,kBAAI,YAAY;AAChB;YACJ;AAEG,gBAAI,CAAC,EAAE,UAAU;AAChB,kBAAI,YAAY,CAAC;AACjB,mBAAK,KAAK,eAAe,EAAE,OAAO,EAAC,CAAE;AACrC;YACJ;AAEG,iBAAK,aAAa,GAAG,MAAM,IAAI;AAC/B,iBAAK,KAAK,eAAe,EAAE,OAAO,EAAC,CAAE;AAErC,gBAAI,GAAG,SAAS,CAAC,GAAG;AACnB,iBAAG,YAAY,CAAC;AAChB,kBAAI,EAAE,aAAa;AAClB,kBAAE,YAAW;cAClB;YACA;UACA;AAGE,eAAK,iBAAiB,mBAAkB;AAExC,eAAK,sBAAqB;AAG1B,eAAK,iBAAiB,6BAA6B,MAAM,KAAK,OAAO,KAAK,mBAAmB;AAE7F,iBAAO;QACT;;QAGC,aAAa,WAAY;AAIxB,cAAI,CAAC,KAAK,MAAM;AACf,iBAAK,mBAAmB,CAAA;AACxB,iBAAK,iBAAiB,CAAA;AACtB,mBAAO,KAAK;AACZ,mBAAO,KAAK;UACf;AAEE,cAAI,KAAK,wBAAwB;AAChC,iBAAK,uBAAsB;UAC9B;AAGE,eAAK,cAAc,YAAW;AAC9B,eAAK,eAAe,YAAW;AAE/B,eAAK,UAAU,SAAU,QAAQ;AAChC,mBAAO,IAAI,KAAK,2BAA2B,IAAI;AAC/C,mBAAO,OAAO;UACjB,GAAK,IAAI;AAEP,cAAI,KAAK,MAAM;AAEd,iBAAK,yBAAwB;UAChC;AAEE,iBAAO;QACT;;QAGC,WAAW,WAAY;AACtB,cAAI,SAAS,IAAI,EAAE,aAAY;AAE/B,cAAI,KAAK,kBAAkB;AAC1B,mBAAO,OAAO,KAAK,iBAAiB,OAAO;UAC9C;AAEE,mBAAS,IAAI,KAAK,iBAAiB,SAAS,GAAG,KAAK,GAAG,KAAK;AAC3D,mBAAO,OAAO,KAAK,iBAAiB,CAAC,EAAE,UAAS,CAAE;UACrD;AAEE,iBAAO,OAAO,KAAK,eAAe,UAAS,CAAE;AAE7C,iBAAO;QACT;;QAGC,WAAW,SAAU,QAAQ,SAAS;AACrC,cAAI,UAAU,KAAK,iBAAiB,MAAK,GACxC,gBAAgB,KAAK,gBACrB,mBAAmB,GAAG;AAEvB,cAAI,KAAK,kBAAkB;AAC1B,iBAAK,iBAAiB,mBAAmB,OAAO;UACnD;AAEE,eAAK,IAAI,QAAQ,SAAS,GAAG,KAAK,GAAG,KAAK;AACzC,gCAAoB;AAEpB,iBAAK,IAAI,cAAc,SAAS,GAAG,KAAK,GAAG,KAAK;AAC/C,kBAAI,cAAc,CAAC,EAAE,UAAU,QAAQ,CAAC,GAAG;AAC1C,oCAAoB;AACpB;cACL;YACA;AAEG,gBAAI,mBAAmB;AACtB,qBAAO,KAAK,SAAS,QAAQ,CAAC,CAAC;YACnC;UACA;AAEE,eAAK,eAAe,UAAU,QAAQ,OAAO;QAC/C;;QAGC,WAAW,WAAY;AACtB,cAAI,SAAS,CAAA;AACb,eAAK,UAAU,SAAU,GAAG;AAC3B,mBAAO,KAAK,CAAC;UAChB,CAAG;AACD,iBAAO;QACT;;QAGC,UAAU,SAAU,IAAI;AACvB,cAAI,SAAS;AAEb,eAAK,SAAS,IAAI,EAAE;AAEpB,eAAK,UAAU,SAAU,GAAG;AAC3B,gBAAI,EAAE,MAAM,CAAC,MAAM,IAAI;AACtB,uBAAS;YACb;UACA,CAAG;AAED,iBAAO;QACT;;QAGC,UAAU,SAAU,OAAO;AAC1B,cAAI,CAAC,OAAO;AACX,mBAAO;UACV;AAEE,cAAI,GAAG,UAAU,KAAK;AAEtB,eAAK,IAAI,QAAQ,SAAS,GAAG,KAAK,GAAG,KAAK;AACzC,gBAAI,QAAQ,CAAC,MAAM,OAAO;AACzB,qBAAO;YACX;UACA;AAEE,oBAAU,KAAK;AACf,eAAK,IAAI,QAAQ,SAAS,GAAG,KAAK,GAAG,KAAK;AACzC,gBAAI,QAAQ,CAAC,EAAE,UAAU,OAAO;AAC/B,qBAAO;YACX;UACA;AAEE,iBAAO,CAAC,EAAE,MAAM,YAAY,MAAM,SAAS,WAAW,SAAS,KAAK,eAAe,SAAS,KAAK;QACnG;;QAGC,iBAAiB,SAAU,OAAO,UAAU;AAE3C,cAAI,MAAM,KAAK;AAEf,cAAI,OAAO,aAAa,YAAY;AACnC,uBAAW,WAAY;YAAA;UAC1B;AAEE,cAAI,aAAa,WAAY;AAG5B,iBAAK,IAAI,SAAS,KAAK,KAAK,IAAI,SAAS,MAAM,QAAQ,MAAM,CAAC,KAAK,kBAAkB;AACpF,mBAAK,KAAK,IAAI,WAAW,YAAY,IAAI;AACzC,mBAAK,IAAI,gBAAgB,YAAY,IAAI;AAEzC,kBAAI,IAAI,SAAS,KAAK,GAAG;AACxB,yBAAQ;cACb,WAAe,MAAM,SAAS,OAAO;AAChC,qBAAK,KAAK,cAAc,UAAU,IAAI;AACtC,sBAAM,SAAS,SAAQ;cAC5B;YACA;UACA;AAEE,cAAI,MAAM,SAAS,KAAK,KAAK,UAAS,EAAG,SAAS,MAAM,UAAS,CAAE,GAAG;AAErE,qBAAQ;UACX,WAAa,MAAM,SAAS,QAAQ,KAAK,MAAM,KAAK,KAAK,KAAK,GAAG;AAE9D,iBAAK,KAAK,GAAG,WAAW,YAAY,IAAI;AACxC,iBAAK,KAAK,MAAM,MAAM,UAAS,CAAE;UACpC,OAAS;AACN,iBAAK,KAAK,GAAG,WAAW,YAAY,IAAI;AACxC,iBAAK,GAAG,gBAAgB,YAAY,IAAI;AACxC,kBAAM,SAAS,aAAY;UAC9B;QACA;;QAGC,OAAO,SAAU,KAAK;AACrB,eAAK,OAAO;AACZ,cAAI,GAAG,GAAG;AAEV,cAAI,CAAC,SAAS,KAAK,KAAK,WAAU,CAAE,GAAG;AACtC,kBAAM;UACT;AAEE,eAAK,cAAc,MAAM,GAAG;AAC5B,eAAK,eAAe,MAAM,GAAG;AAE7B,cAAI,CAAC,KAAK,eAAe;AACxB,iBAAK,yBAAwB;UAChC;AAEE,eAAK,UAAU,IAAI,QAAQ,IAAI,WAAW;AAG1C,eAAK,IAAI,GAAG,IAAI,KAAK,eAAe,QAAQ,IAAI,GAAG,KAAK;AACvD,oBAAQ,KAAK,eAAe,CAAC;AAC7B,kBAAM,YAAY,MAAM,MAAM;AAC9B,kBAAM,MAAM,UAAU,MAAM;UAC/B;AAEE,eAAK,IAAI,GAAG,IAAI,KAAK,eAAe,QAAQ,IAAI,GAAG,KAAK;AACvD,oBAAQ,KAAK,eAAe,CAAC;AAC7B,iBAAK,aAAa,MAAM,OAAO,IAAI;AACnC,kBAAM,MAAM,UAAU,MAAM;UAC/B;AACE,eAAK,iBAAiB,CAAA;AAGtB,eAAK,QAAQ,KAAK,MAAM,KAAK,KAAK,KAAK;AACvC,eAAK,sBAAsB,KAAK,0BAAyB;AAEzD,eAAK,KAAK,GAAG,WAAW,KAAK,UAAU,IAAI;AAC3C,eAAK,KAAK,GAAG,WAAW,KAAK,UAAU,IAAI;AAE3C,cAAI,KAAK,kBAAkB;AAC1B,iBAAK,iBAAgB;UACxB;AAEE,eAAK,YAAW;AAGhB,cAAI,KAAK;AACT,eAAK,mBAAmB,CAAA;AACxB,eAAK,UAAU,GAAG,IAAI;QACxB;;QAGC,UAAU,SAAU,KAAK;AACxB,cAAI,IAAI,WAAW,KAAK,UAAU,IAAI;AACtC,cAAI,IAAI,WAAW,KAAK,UAAU,IAAI;AAEtC,eAAK,cAAa;AAGlB,eAAK,KAAK,SAAS,YAAY,KAAK,KAAK,SAAS,UAAU,QAAQ,yBAAyB,EAAE;AAE/F,cAAI,KAAK,qBAAqB;AAC7B,iBAAK,oBAAmB;UAC3B;AAEE,iBAAO,KAAK;AAGZ,eAAK,cAAa;AAClB,eAAK,cAAc,OAAM;AACzB,eAAK,eAAe,OAAM;AAE1B,eAAK,cAAc,YAAW;AAE9B,eAAK,OAAO;QACd;QAEC,kBAAkB,SAAU,QAAQ;AACnC,cAAI,UAAU;AACd,iBAAO,WAAW,CAAC,QAAQ,OAAO;AACjC,sBAAU,QAAQ;UACrB;AACE,iBAAO,WAAW;QACpB;;QAGC,cAAc,SAAU,SAAS,KAAK;AACrC,mBAAS,IAAI,QAAQ,SAAS,GAAG,KAAK,GAAG,KAAK;AAC7C,gBAAI,QAAQ,CAAC,MAAM,KAAK;AACvB,sBAAQ,OAAO,GAAG,CAAC;AACnB,qBAAO;YACX;UACA;QACA;;;;;;;QAQC,4BAA4B,SAAU,QAAQ,GAAG;AAChD,cAAI,MAAM,KAAK,MACX,kBAAkB,KAAK,kBAC1B,UAAU,KAAK,MAAM,KAAK,KAAK,WAAU,CAAE;AAE5C,iBAAO,KAAK,SAAS,KAAK;AACzB,gBAAI,CAAC,gBAAgB,CAAC,EAAE,aAAa,QAAQ,IAAI,QAAQ,OAAO,UAAS,GAAI,CAAC,CAAC,GAAG;AACjF;YACJ;UACA;QACA;QAEC,uBAAuB,SAAU,GAAG;AACnC,YAAE,OAAO,cAAc,EAAE,OAAO;QAClC;QAEC,mBAAmB,SAAU,GAAG;AAC/B,cAAI,CAAC,KAAK,eAAe,CAAC,EAAE,OAAO,aAAa;AAC/C,gBAAI,cAAc,EAAE,OAAO,UAAU,EAAE,OAAO,OAAO,OAAM;AAE3D,iBAAK,WAAW,EAAE,QAAQ,EAAE,WAAW,EAAE,MAAM;AAE/C,gBAAI,aAAa;AAChB,gBAAE,OAAO,UAAS;YACtB;UACA;QACA;QAEC,YAAY,SAAU,OAAO,MAAM,IAAI;AACtC,gBAAM,UAAU;AAChB,eAAK,YAAY,KAAK;AAEtB,gBAAM,UAAU;AAChB,eAAK,SAAS,KAAK;QACrB;QAEC,qBAAqB,SAAU,GAAG;AACjC,cAAI,YAAY,EAAE,OAAO;AACzB,iBAAO,EAAE,OAAO;AAChB,cAAI,WAAW;AACd,iBAAK,WAAW,EAAE,QAAQ,WAAW,EAAE,OAAO,OAAO;UACxD;QACA;;;QAKC,cAAc,SAAU,QAAQ,wBAAwB,eAAe;AACtE,cAAI,eAAe,KAAK,eACvB,kBAAkB,KAAK,kBACvB,KAAK,KAAK,eACV,MAAM,KAAK,MACX,UAAU,KAAK,MAAM,KAAK,KAAK,WAAU,CAAE;AAG5C,cAAI,wBAAwB;AAC3B,iBAAK,2BAA2B,QAAQ,KAAK,QAAQ;UACxD;AAGE,cAAI,UAAU,OAAO,UACpB,UAAU,QAAQ,UAClB;AAGD,eAAK,aAAa,SAAS,MAAM;AAEjC,iBAAO,SAAS;AACf,oBAAQ;AACR,oBAAQ,oBAAoB;AAE5B,gBAAI,QAAQ,QAAQ,SAAS;AAE5B;YACJ,WAAc,0BAA0B,QAAQ,eAAe,GAAG;AAE9D,4BAAc,QAAQ,SAAS,CAAC,MAAM,SAAS,QAAQ,SAAS,CAAC,IAAI,QAAQ,SAAS,CAAC;AAGvF,2BAAa,QAAQ,KAAK,EAAE,aAAa,SAAS,IAAI,QAAQ,QAAQ,UAAU,QAAQ,KAAK,CAAC;AAC9F,8BAAgB,QAAQ,KAAK,EAAE,UAAU,aAAa,IAAI,QAAQ,YAAY,UAAS,GAAI,QAAQ,KAAK,CAAC;AAGzG,mBAAK,aAAa,QAAQ,SAAS,gBAAgB,OAAO;AAC1D,sBAAQ,SAAS,SAAS,KAAK,WAAW;AAC1C,0BAAY,WAAW,QAAQ;AAE/B,kBAAI,QAAQ,OAAO;AAElB,mBAAG,YAAY,OAAO;AACtB,oBAAI,CAAC,eAAe;AACnB,qBAAG,SAAS,WAAW;gBAC7B;cACA;YACA,OAAU;AACN,sBAAQ,mBAAmB;YAC/B;AAEG,sBAAU,QAAQ;UACrB;AAEE,iBAAO,OAAO;QAChB;QAEC,eAAe,SAAU,IAAI,KAAK;AACjC,iBAAO,KAAK;AACX,gBAAI,OAAO,KAAK;AACf,qBAAO;YACX;AACG,kBAAM,IAAI;UACb;AACE,iBAAO;QACT;;QAGC,MAAM,SAAU,MAAM,MAAM,WAAW;AACtC,cAAI,QAAQ,KAAK,iBAAiB,EAAE,eAAe;AAElD,gBAAI,KAAK,iBAAiB,KAAK,cAAc,KAAK,MAAM,OAAO,KAAK,cAAc,aAAa,GAAG;AACjG;YACJ;AACG,mBAAO,YAAY;UACtB;AAEE,YAAE,aAAa,UAAU,KAAK,KAAK,MAAM,MAAM,MAAM,SAAS;QAChE;;QAGC,SAAS,SAAU,MAAM,WAAW;AACnC,iBAAO,EAAE,aAAa,UAAU,QAAQ,KAAK,MAAM,MAAM,SAAS,KAAK,EAAE,aAAa,UAAU,QAAQ,KAAK,MAAM,YAAY,MAAM,SAAS;QAChJ;;QAGC,4BAA4B,SAAU,SAAS;AAC9C,cAAI,aAAa,QAAQ,cAAa;AAEtC,cAAI,IAAI;AACR,cAAI,aAAa,IAAI;AACpB,iBAAK;UACR,WAAa,aAAa,KAAK;AAC5B,iBAAK;UACR,OAAS;AACN,iBAAK;UACR;AAEE,iBAAO,IAAI,EAAE,QAAQ,EAAE,MAAM,gBAAgB,aAAa,iBAAiB,WAAW,mBAAmB,GAAG,UAAU,IAAI,EAAE,MAAM,IAAI,EAAE,EAAC,CAAE;QAC7I;QAEC,aAAa,WAAY;AACxB,cAAI,MAAM,KAAK,MACX,oBAAoB,KAAK,QAAQ,mBACjC,sBAAsB,KAAK,QAAQ,qBACnC,sBAAsB,KAAK,QAAQ,qBACnC,sBAAsB,KAAK,QAAQ;AAGvC,cAAI,qBAAqB,uBAAuB,qBAAqB;AACpE,iBAAK,GAAG,gCAAgC,KAAK,iBAAiB,IAAI;UACrE;AAGE,cAAI,qBAAqB;AACxB,iBAAK,GAAG,oBAAoB,KAAK,eAAe,IAAI;AACpD,iBAAK,GAAG,mBAAmB,KAAK,eAAe,IAAI;AACnD,gBAAI,GAAG,WAAW,KAAK,eAAe,IAAI;UAC7C;QACA;QAEC,iBAAiB,SAAU,GAAG;AAC7B,cAAI,UAAU,EAAE,OACZ,gBAAgB;AAEpB,cAAI,EAAE,SAAS,qBAAqB,EAAE,iBAAiB,EAAE,cAAc,YAAY,IAAI;AACtF;UACH;AAEE,iBAAO,cAAc,eAAe,WAAW,GAAG;AACjD,4BAAgB,cAAc,eAAe,CAAC;UACjD;AAEE,cAAI,cAAc,UAAU,KAAK,YAChC,cAAc,gBAAgB,QAAQ,eACtC,KAAK,QAAQ,mBAAmB;AAGhC,oBAAQ,SAAQ;UACnB,WAAa,KAAK,QAAQ,qBAAqB;AAC5C,oBAAQ,aAAY;UACvB;AAEE,cAAI,KAAK,QAAQ,qBAAqB;AACrC,oBAAQ,SAAQ;UACnB;AAGE,cAAI,EAAE,iBAAiB,EAAE,cAAc,YAAY,IAAI;AACtD,iBAAK,KAAK,WAAW,MAAK;UAC7B;QACA;QAEC,eAAe,SAAU,GAAG;AAC3B,cAAI,MAAM,KAAK;AACf,cAAI,KAAK,kBAAkB;AAC1B;UACH;AACE,cAAI,KAAK,eAAe;AACvB,gBAAI,YAAY,KAAK,aAAa;UACrC;AACE,cAAI,EAAE,MAAM,cAAa,IAAK,KAAK,EAAE,UAAU,KAAK,aAAa;AAChE,iBAAK,gBAAgB,IAAI,EAAE,QAAQ,EAAE,MAAM,cAAa,GAAI,KAAK,QAAQ,cAAc;AACvF,gBAAI,SAAS,KAAK,aAAa;UAClC;QACA;QAEC,eAAe,WAAY;AAC1B,cAAI,KAAK,eAAe;AACvB,iBAAK,KAAK,YAAY,KAAK,aAAa;AACxC,iBAAK,gBAAgB;UACxB;QACA;QAEC,eAAe,WAAY;AAC1B,cAAI,oBAAoB,KAAK,QAAQ,mBACpC,sBAAsB,KAAK,QAAQ,qBACnC,sBAAsB,KAAK,QAAQ,qBACnC,sBAAsB,KAAK,QAAQ,qBACnC,MAAM,KAAK;AAEZ,cAAI,qBAAqB,uBAAuB,qBAAqB;AACpE,iBAAK,IAAI,gCAAgC,KAAK,iBAAiB,IAAI;UACtE;AACE,cAAI,qBAAqB;AACxB,iBAAK,IAAI,oBAAoB,KAAK,eAAe,IAAI;AACrD,iBAAK,IAAI,mBAAmB,KAAK,eAAe,IAAI;AACpD,gBAAI,IAAI,WAAW,KAAK,eAAe,IAAI;UAC9C;QACA;QAEC,UAAU,WAAY;AACrB,cAAI,CAAC,KAAK,MAAM;AACf;UACH;AACE,eAAK,oBAAmB;AAExB,eAAK,QAAQ,KAAK,MAAM,KAAK,KAAK,KAAK;AACvC,eAAK,sBAAsB,KAAK,0BAAyB;QAC3D;QAEC,UAAU,WAAY;AACrB,cAAI,KAAK,kBAAkB;AAC1B;UACH;AAEE,cAAI,YAAY,KAAK,0BAAyB;AAE9C,eAAK,iBAAiB,kCAAkC,KAAK,qBAAqB,KAAK,MAAM,KAAK,KAAK,WAAU,CAAE,GAAG,KAAK,OAAO,SAAS;AAC3I,eAAK,iBAAiB,6BAA6B,MAAM,KAAK,MAAM,KAAK,KAAK,KAAK,GAAG,SAAS;AAE/F,eAAK,sBAAsB;AAC3B;QACF;QAEC,0BAA0B,WAAY;AACrC,cAAI,UAAU,KAAK,KAAK,KAAK,KAAK,WAAU,CAAE,GAC7C,UAAU,KAAK,MAAM,KAAK,KAAK,WAAU,CAAE,GAC3C,SAAS,KAAK,QAAQ,kBACtB,WAAW;AAKZ,cAAI,OAAO,WAAW,YAAY;AACjC,uBAAW,WAAY;AAAE,qBAAO;YAAO;UAC1C;AAEE,cAAI,KAAK,QAAQ,4BAA4B,MAAM;AAClD,sBAAU,KAAK,QAAQ,0BAA0B;UACpD;AACE,eAAK,WAAW;AAChB,eAAK,gBAAgB,CAAA;AACrB,eAAK,mBAAmB,CAAA;AAGxB,mBAAS,OAAO,SAAS,QAAQ,SAAS,QAAQ;AACjD,iBAAK,cAAc,IAAI,IAAI,IAAI,EAAE,aAAa,SAAS,IAAI,CAAC;AAC5D,iBAAK,iBAAiB,IAAI,IAAI,IAAI,EAAE,aAAa,SAAS,IAAI,CAAC;UAClE;AAGE,eAAK,mBAAmB,IAAI,KAAK,eAAe,MAAM,UAAU,CAAC;QACnE;;QAGC,WAAW,SAAU,OAAO,MAAM;AACjC,cAAI,eAAe,KAAK,eACpB,kBAAkB,KAAK,kBAC1B,UAAU,KAAK,MAAM,KAAK,KAAK,WAAU,CAAE,GACxC,aAAa;AAEjB,cAAI,KAAK,QAAQ,kBAAkB;AAClC,iBAAK,oBAAoB,KAAK;UACjC;AAEE,gBAAM,GAAG,KAAK,2BAA2B,IAAI;AAG7C,iBAAO,QAAQ,SAAS,QAAQ;AAC/B,0BAAc,KAAK,KAAK,QAAQ,MAAM,UAAS,GAAI,IAAI;AAGvD,gBAAI,UAAU,aAAa,IAAI,EAAE,cAAc,WAAW;AAC1D,gBAAI,SAAS;AACZ,sBAAQ,UAAU,KAAK;AACvB,oBAAM,WAAW;AACjB;YACJ;AAGG,sBAAU,gBAAgB,IAAI,EAAE,cAAc,WAAW;AACzD,gBAAI,SAAS;AACZ,kBAAI,SAAS,QAAQ;AACrB,kBAAI,QAAQ;AACX,qBAAK,aAAa,SAAS,KAAK;cACrC;AAII,kBAAI,aAAa,IAAI,KAAK,eAAe,MAAM,MAAM,SAAS,KAAK;AACnE,2BAAa,IAAI,EAAE,UAAU,YAAY,KAAK,KAAK,QAAQ,WAAW,UAAU,IAAI,CAAC;AACrF,sBAAQ,WAAW;AACnB,oBAAM,WAAW;AAGjB,kBAAI,aAAa;AACjB,mBAAK,IAAI,OAAO,GAAG,IAAI,OAAO,OAAO,KAAK;AACzC,6BAAa,IAAI,KAAK,eAAe,MAAM,GAAG,UAAU;AACxD,6BAAa,CAAC,EAAE,UAAU,YAAY,KAAK,KAAK,QAAQ,QAAQ,UAAS,GAAI,CAAC,CAAC;cACpF;AACI,qBAAO,UAAU,UAAU;AAG3B,mBAAK,2BAA2B,SAAS,IAAI;AAE7C;YACJ;AAGG,4BAAgB,IAAI,EAAE,UAAU,OAAO,WAAW;UACrD;AAGE,eAAK,iBAAiB,UAAU,KAAK;AACrC,gBAAM,WAAW,KAAK;AACtB;QACF;;;;;;QAOC,uBAAuB,WAAY;AAClC,eAAK,cAAc,UAAU,SAAU,GAAG;AACzC,gBAAI,aAAa,EAAE,iBAAiB,EAAE,kBAAkB;AACvD,gBAAE,YAAW;YACjB;UACA,CAAG;QACH;;QAGC,UAAU,SAAU,IAAI;AACvB,eAAK,OAAO,KAAK,EAAE;AACnB,cAAI,CAAC,KAAK,eAAe;AACxB,iBAAK,gBAAgB,WAAW,EAAE,KAAK,KAAK,eAAe,IAAI,GAAG,GAAG;UACxE;QACA;QACC,eAAe,WAAY;AAC1B,mBAAS,IAAI,GAAG,IAAI,KAAK,OAAO,QAAQ,KAAK;AAC5C,iBAAK,OAAO,CAAC,EAAE,KAAK,IAAI;UAC3B;AACE,eAAK,OAAO,SAAS;AACrB,uBAAa,KAAK,aAAa;AAC/B,eAAK,gBAAgB;QACvB;;QAGC,qBAAqB,WAAY;AAChC,cAAI,UAAU,KAAK,MAAM,KAAK,KAAK,KAAK;AAGxC,eAAK,cAAa;AAElB,cAAI,KAAK,QAAQ,WAAW,KAAK,oBAAoB,WAAW,KAAK,0BAAyB,CAAE,GAAG;AAClG,iBAAK,gBAAe;AAEpB,iBAAK,iBAAiB,kCAAkC,KAAK,qBAAqB,KAAK,MAAM,KAAK,KAAK,WAAU,CAAE,GAAG,KAAK,OAAO,KAAK,0BAAyB,CAAE;AAElK,iBAAK,iBAAiB,KAAK,OAAO,OAAO;UAE5C,WAAa,KAAK,QAAQ,SAAS;AAChC,iBAAK,gBAAe;AAEpB,iBAAK,kBAAkB,KAAK,OAAO,OAAO;UAC7C,OAAS;AACN,iBAAK,SAAQ;UAChB;QACA;;QAGC,2BAA2B,WAAY;AACtC,cAAI,CAAC,KAAK,QAAQ,4BAA4B;AAC7C,mBAAO,KAAK;UACf,WAAa,EAAE,QAAQ,QAAQ;AAC5B,mBAAO,KAAK,mBAAmB,KAAK,KAAK,UAAS,CAAE;UACvD;AAEE,iBAAO,KAAK,mBAAmB,KAAK,KAAK,UAAS,EAAG,IAAI,CAAC,CAAC;QAC7D;;;;;;;;;;;QAYC,oBAAoB,SAAU,QAAQ;AACrC,cAAI,SAAS,KAAK;AAElB,cAAI,WAAW,QAAW;AACzB,gBAAI,OAAO,SAAQ,KAAM,QAAQ;AAChC,qBAAO,WAAW,MAAM;YAC5B;AACG,gBAAI,OAAO,SAAQ,KAAM,CAAC,QAAQ;AACjC,qBAAO,WAAW,MAAM;YAC5B;UACA;AAEE,iBAAO;QACT;;QAGC,+BAA+B,SAAU,OAAO,YAAY;AAC3D,cAAI,eAAe,OAAO;AACzB,iBAAK,cAAc,SAAS,KAAK;UACpC,WAAa,WAAW,gBAAgB,GAAG;AACxC,uBAAW,UAAS;AAEpB,gBAAI,UAAU,WAAW,mBAAkB;AAC3C,iBAAK,cAAc,YAAY,QAAQ,CAAC,CAAC;AACzC,iBAAK,cAAc,YAAY,QAAQ,CAAC,CAAC;UAC5C,OAAS;AACN,uBAAW,YAAW;UACzB;QACA;;;;;;;;QASC,wBAAwB,SAAU,OAAO,QAAQ;AAChD,cAAI,SAAS,MAAM,UAAS,GACxB,IAAI,GACJ;AAEJ,mBAAS,UAAU,CAAA;AAEnB,iBAAO,IAAI,OAAO,QAAQ,KAAK;AAC9B,oBAAQ,OAAO,CAAC;AAEhB,gBAAI,iBAAiB,EAAE,YAAY;AAClC,mBAAK,uBAAuB,OAAO,MAAM;AACzC;YACJ;AAEG,mBAAO,KAAK,KAAK;UACpB;AAEE,iBAAO;QACT;;;;;;;QAQC,qBAAqB,SAAU,OAAO;AACrC,cAAI,OAAO,MAAM,QAAQ,OAAO,KAAK,QAAQ,mBAAmB;YAC/D,eAAe,WAAY;AAC1B,qBAAO;YACX;YACG,oBAAoB,WAAY;AAC/B,qBAAO,CAAC,KAAK;YACjB;UACA,CAAG;AAED,iBAAO;QACT;MACA,CAAC;AAGD,QAAE,mBAAmB,QAAQ;QAC5B,oBAAoB,IAAI,EAAE,aAAa,IAAI,EAAE,OAAO,WAAW,SAAS,GAAG,IAAI,EAAE,OAAO,UAAU,QAAQ,CAAC;MAC5G,CAAC;AAED,QAAE,mBAAmB,QAAQ;QAC5B,cAAc;;UAEb,iBAAiB,WAAY;UAE/B;UACE,kBAAkB,SAAU,mBAAmB,cAAc;AAC5D,iBAAK,iBAAiB,kCAAkC,KAAK,qBAAqB,KAAK,MAAM,KAAK,KAAK,WAAU,CAAE,GAAG,iBAAiB;AACvI,iBAAK,iBAAiB,6BAA6B,MAAM,cAAc,KAAK,0BAAyB,CAAE;AAGvG,iBAAK,KAAK,cAAc;UAC3B;UACE,mBAAmB,SAAU,mBAAmB,cAAc;AAC7D,iBAAK,iBAAiB,kCAAkC,KAAK,qBAAqB,KAAK,MAAM,KAAK,KAAK,WAAU,CAAE,GAAG,iBAAiB;AACvI,iBAAK,iBAAiB,6BAA6B,MAAM,cAAc,KAAK,0BAAyB,CAAE;AAGvG,iBAAK,KAAK,cAAc;UAC3B;UACE,oBAAoB,SAAU,OAAO,YAAY;AAChD,iBAAK,8BAA8B,OAAO,UAAU;UACvD;QACA;QAEC,gBAAgB;;UAEf,iBAAiB,WAAY;AAC5B,iBAAK,KAAK,SAAS,aAAa;AAChC,iBAAK;UACR;UAEE,kBAAkB,SAAU,mBAAmB,cAAc;AAC5D,gBAAI,SAAS,KAAK,0BAAyB,GACvC,KAAK,KAAK,eACb,UAAU,KAAK,MAAM,KAAK,KAAK,WAAU,CAAE,GACxC;AAEJ,iBAAK,cAAc;AAGnB,iBAAK,iBAAiB,aAAa,QAAQ,mBAAmB,SAAS,SAAU,GAAG;AACnF,kBAAI,WAAW,EAAE,SACb,UAAW,EAAE,UACb;AAEJ,kBAAI,CAAC,OAAO,SAAS,QAAQ,GAAG;AAC/B,2BAAW;cAChB;AAEI,kBAAI,EAAE,gBAAe,KAAM,oBAAoB,MAAM,cAAc;AAClE,mBAAG,YAAY,CAAC;AAChB,kBAAE,6BAA6B,MAAM,cAAc,MAAM;cAC9D,OAAW;AAEN,kBAAE,YAAW;AACb,kBAAE,6BAA6B,UAAU,cAAc,MAAM;cAClE;AAII,mBAAK,IAAI,QAAQ,SAAS,GAAG,KAAK,GAAG,KAAK;AACzC,oBAAI,QAAQ,CAAC;AACb,oBAAI,CAAC,OAAO,SAAS,EAAE,OAAO,GAAG;AAChC,qBAAG,YAAY,CAAC;gBACtB;cACA;YAEA,CAAI;AAED,iBAAK,aAAY;AAGjB,iBAAK,iBAAiB,0BAA0B,QAAQ,YAAY;AAEpE,eAAG,UAAU,SAAU,GAAG;AACzB,kBAAI,EAAE,aAAa,EAAE,kBAAkB,EAAE,OAAO;AAC/C,kBAAE,YAAW;cAClB;YACA,CAAI;AAGD,iBAAK,iBAAiB,aAAa,QAAQ,mBAAmB,cAAc,SAAU,GAAG;AACxF,gBAAE,kCAAkC,YAAY;YACpD,CAAI;AAED,iBAAK,cAAc;AAGnB,iBAAK,SAAS,WAAY;AAEzB,mBAAK,iBAAiB,aAAa,QAAQ,mBAAmB,SAAS,SAAU,GAAG;AACnF,mBAAG,YAAY,CAAC;AAChB,kBAAE,YAAW;cAClB,CAAK;AAED,mBAAK,cAAa;YACtB,CAAI;UACJ;UAEE,mBAAmB,SAAU,mBAAmB,cAAc;AAC7D,iBAAK,wBAAwB,KAAK,kBAAkB,oBAAoB,GAAG,YAAY;AAGvF,iBAAK,iBAAiB,6BAA6B,MAAM,cAAc,KAAK,0BAAyB,CAAE;AAEvG,iBAAK,iBAAiB,kCAAkC,KAAK,qBAAqB,KAAK,MAAM,KAAK,KAAK,WAAU,CAAE,GAAG,mBAAmB,KAAK,0BAAyB,CAAE;UAC5K;UAEE,oBAAoB,SAAU,OAAO,YAAY;AAChD,gBAAI,KAAK,MACL,KAAK,KAAK;AAEd,eAAG,SAAS,KAAK;AACjB,gBAAI,eAAe,OAAO;AACzB,kBAAI,WAAW,cAAc,GAAG;AAE/B,2BAAW,YAAW;AACtB,qBAAK,aAAY;AACjB,qBAAK,gBAAe;AAEpB,sBAAM,QAAQ,KAAK,KAAK,mBAAmB,WAAW,UAAS,CAAE,CAAC;AAClE,sBAAM,YAAW;AAEjB,qBAAK,SAAS,WAAY;AACzB,qBAAG,YAAY,KAAK;AACpB,wBAAM,YAAW;AAEjB,qBAAG,cAAa;gBACtB,CAAM;cAEN,OAAW;AACN,qBAAK,aAAY;AAEjB,mBAAG,gBAAe;AAClB,mBAAG,wBAAwB,YAAY,KAAK,KAAK,WAAU,GAAI,KAAK,KAAK;cAC9E;YACA;UACA;QACA;;QAGC,yBAAyB,SAAU,SAAS,mBAAmB,cAAc;AAC5E,cAAI,SAAS,KAAK,0BAAyB,GAC1C,UAAU,KAAK,MAAM,KAAK,KAAK,WAAU,CAAE;AAG5C,kBAAQ,6CAA6C,QAAQ,SAAS,oBAAoB,GAAG,YAAY;AAEzG,cAAI,KAAK;AAGT,eAAK,aAAY;AACjB,kBAAQ,0BAA0B,QAAQ,YAAY;AAItD,eAAK,SAAS,WAAY;AAGzB,gBAAI,QAAQ,gBAAgB,GAAG;AAC9B,kBAAI,IAAI,QAAQ,SAAS,CAAC;AAE1B,mBAAK,cAAc;AACnB,gBAAE,UAAU,EAAE,UAAS,CAAE;AACzB,mBAAK,cAAc;AACnB,kBAAI,EAAE,aAAa;AAClB,kBAAE,YAAW;cAClB;YACA,OAAU;AACN,sBAAQ,aAAa,QAAQ,cAAc,SAAS,SAAU,GAAG;AAChE,kBAAE,kCAAkC,QAAQ,SAAS,oBAAoB,CAAC;cAC/E,CAAK;YACL;AACG,eAAG,cAAa;UACnB,CAAG;QACH;QAEC,eAAe,WAAY;AAC1B,cAAI,KAAK,MAAM;AACd,iBAAK,KAAK,SAAS,YAAY,KAAK,KAAK,SAAS,UAAU,QAAQ,yBAAyB,EAAE;UAClG;AACE,eAAK;AACL,eAAK,KAAK,cAAc;QAC1B;;;QAIC,cAAc,WAAY;AAIzB,YAAE,KAAK,QAAQ,SAAS,KAAK,WAAW;QAC1C;MACA,CAAC;AAED,QAAE,qBAAqB,SAAU,SAAS;AACzC,eAAO,IAAI,EAAE,mBAAmB,OAAO;MACxC;ACr3CU,UAAC,gBAAgB,EAAE,gBAAgB,EAAE,OAAO,OAAO;QAC5D,SAAS,EAAE,KAAK,UAAU;QAE1B,YAAY,SAAU,OAAO,MAAM,GAAG,GAAG;AAExC,YAAE,OAAO,UAAU,WAAW;YAAK;YAAM,IAAK,EAAE,YAAY,EAAE,UAAS,IAAM,IAAI,EAAE,OAAO,GAAG,CAAC;YACpF,EAAE,MAAM,MAAM,MAAM,MAAM,QAAQ,YAAW;UAAE;AAEzD,eAAK,SAAS;AACd,eAAK,QAAQ;AAEb,eAAK,WAAW,CAAA;AAChB,eAAK,iBAAiB,CAAA;AACtB,eAAK,cAAc;AACnB,eAAK,mBAAmB;AACxB,eAAK,oBAAoB;AAEzB,eAAK,UAAU,IAAI,EAAE,aAAY;AAEjC,cAAI,GAAG;AACN,iBAAK,UAAU,CAAC;UACnB;AACE,cAAI,GAAG;AACN,iBAAK,UAAU,CAAC;UACnB;QACA;;QAGC,oBAAoB,SAAU,cAAc,qBAAqB;AAChE,yBAAe,gBAAgB,CAAA;AAE/B,mBAAS,IAAI,KAAK,eAAe,SAAS,GAAG,KAAK,GAAG,KAAK;AACzD,iBAAK,eAAe,CAAC,EAAE,mBAAmB,cAAc,mBAAmB;UAC9E;AAEE,mBAAS,IAAI,KAAK,SAAS,SAAS,GAAG,KAAK,GAAG,KAAK;AACnD,gBAAI,uBAAuB,KAAK,SAAS,CAAC,EAAE,aAAa;AACxD;YACJ;AACG,yBAAa,KAAK,KAAK,SAAS,CAAC,CAAC;UACrC;AAEE,iBAAO;QACT;;QAGC,eAAe,WAAY;AAC1B,iBAAO,KAAK;QACd;;QAGC,cAAc,SAAU,kBAAkB;AACzC,cAAI,gBAAgB,KAAK,eAAe,MAAK,GAC5C,MAAM,KAAK,OAAO,MAClB,aAAa,IAAI,cAAc,KAAK,OAAO,GAC3C,OAAO,KAAK,QAAQ,GACpB,UAAU,IAAI,QAAO,GACrB;AAGD,iBAAO,cAAc,SAAS,KAAK,aAAa,MAAM;AACrD;AACA,gBAAI,cAAc,CAAA;AAClB,iBAAK,IAAI,GAAG,IAAI,cAAc,QAAQ,KAAK;AAC1C,4BAAc,YAAY,OAAO,cAAc,CAAC,EAAE,cAAc;YACpE;AACG,4BAAgB;UACnB;AAEE,cAAI,aAAa,MAAM;AACtB,iBAAK,OAAO,KAAK,QAAQ,KAAK,SAAS,IAAI;UAC9C,WAAa,cAAc,SAAS;AACjC,iBAAK,OAAO,KAAK,QAAQ,KAAK,SAAS,UAAU,CAAC;UACrD,OAAS;AACN,iBAAK,OAAO,KAAK,UAAU,KAAK,SAAS,gBAAgB;UAC5D;QACA;QAEC,WAAW,WAAY;AACtB,cAAI,SAAS,IAAI,EAAE,aAAY;AAC/B,iBAAO,OAAO,KAAK,OAAO;AAC1B,iBAAO;QACT;QAEC,aAAa,WAAY;AACxB,eAAK,mBAAmB;AACxB,cAAI,KAAK,OAAO;AACf,iBAAK,QAAQ,IAAI;UACpB;QACA;;QAGC,YAAY,WAAY;AACvB,cAAI,KAAK,kBAAkB;AAC1B,iBAAK,WAAW,KAAK,OAAO,QAAQ,mBAAmB,IAAI;AAC3D,iBAAK,mBAAmB;UAC3B;AACE,iBAAO,KAAK,SAAS,WAAU;QACjC;QACC,cAAc,WAAY;AACzB,iBAAO,KAAK,SAAS,aAAY;QACnC;QAGC,WAAW,SAAU,MAAM,yBAAyB;AAEnD,eAAK,mBAAmB;AAExB,eAAK,oBAAoB;AACzB,eAAK,kBAAkB,IAAI;AAE3B,cAAI,gBAAgB,EAAE,eAAe;AACpC,gBAAI,CAAC,yBAAyB;AAC7B,mBAAK,eAAe,KAAK,IAAI;AAC7B,mBAAK,WAAW;YACpB;AACG,iBAAK,eAAe,KAAK;UAC5B,OAAS;AACN,gBAAI,CAAC,yBAAyB;AAC7B,mBAAK,SAAS,KAAK,IAAI;YAC3B;AACG,iBAAK;UACR;AAEE,cAAI,KAAK,UAAU;AAClB,iBAAK,SAAS,UAAU,MAAM,IAAI;UACrC;QACA;;;;;;QAOC,mBAAmB,SAAU,OAAO;AACnC,cAAI,CAAC,KAAK,UAAU;AAEnB,iBAAK,WAAW,MAAM,YAAY,MAAM;UAC3C;QACA;;;;;;;QAQC,cAAc,WAAY;AACzB,cAAI,SAAS,KAAK;AAElB,cAAI,OAAO,YAAY;AACtB,mBAAO,WAAW,MAAM;AACxB,mBAAO,WAAW,MAAM;UAC3B;AACE,cAAI,OAAO,YAAY;AACtB,mBAAO,WAAW,MAAM;AACxB,mBAAO,WAAW,MAAM;UAC3B;QACA;QAEC,oBAAoB,WAAY;AAC/B,cAAI,UAAU,KAAK,UACf,gBAAgB,KAAK,gBACrB,SAAS,GACT,SAAS,GACT,aAAa,KAAK,aAClB,GAAG,OAAO,aAAa;AAG3B,cAAI,eAAe,GAAG;AACrB;UACH;AAGE,eAAK,aAAY;AAGjB,eAAK,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACpC,0BAAc,QAAQ,CAAC,EAAE;AAEzB,iBAAK,QAAQ,OAAO,WAAW;AAE/B,sBAAU,YAAY;AACtB,sBAAU,YAAY;UACzB;AAGE,eAAK,IAAI,GAAG,IAAI,cAAc,QAAQ,KAAK;AAC1C,oBAAQ,cAAc,CAAC;AAGvB,gBAAI,MAAM,mBAAmB;AAC5B,oBAAM,mBAAkB;YAC5B;AAEG,iBAAK,QAAQ,OAAO,MAAM,OAAO;AAEjC,0BAAc,MAAM;AACpB,yBAAa,MAAM;AAEnB,sBAAU,YAAY,MAAM;AAC5B,sBAAU,YAAY,MAAM;UAC/B;AAEE,eAAK,UAAU,KAAK,WAAW,IAAI,EAAE,OAAO,SAAS,YAAY,SAAS,UAAU;AAGpF,eAAK,oBAAoB;QAC3B;;QAGC,WAAW,SAAU,UAAU;AAC9B,cAAI,UAAU;AACb,iBAAK,gBAAgB,KAAK;AAC1B,iBAAK,UAAU,QAAQ;UAC1B;AACE,eAAK,OAAO,cAAc,SAAS,IAAI;QACzC;QAEC,+BAA+B,SAAU,QAAQ,QAAQ,SAAS;AACjE,eAAK;YAAa;YAAQ,KAAK,OAAO,KAAK,WAAU;YAAI,UAAU;YAClE,SAAU,GAAG;AACZ,kBAAI,UAAU,EAAE,UACf,GAAG;AACJ,mBAAK,IAAI,QAAQ,SAAS,GAAG,KAAK,GAAG,KAAK;AACzC,oBAAI,QAAQ,CAAC;AAGb,oBAAI,EAAE,OAAO;AACZ,oBAAE,QAAQ,MAAM;AAChB,oBAAE,YAAW;gBACnB;cACA;YACA;YACG,SAAU,GAAG;AACZ,kBAAI,gBAAgB,EAAE,gBACrB,GAAG;AACJ,mBAAK,IAAI,cAAc,SAAS,GAAG,KAAK,GAAG,KAAK;AAC/C,qBAAK,cAAc,CAAC;AACpB,oBAAI,GAAG,OAAO;AACb,qBAAG,QAAQ,MAAM;AACjB,qBAAG,YAAW;gBACpB;cACA;YACA;UACA;QACA;QAEC,8CAA8C,SAAU,QAAQ,YAAY,mBAAmB,cAAc;AAC5G,eAAK;YAAa;YAAQ;YAAc;YACvC,SAAU,GAAG;AACZ,gBAAE,8BAA8B,QAAQ,EAAE,OAAO,KAAK,mBAAmB,EAAE,UAAS,CAAE,EAAE,MAAK,GAAI,iBAAiB;AAIlH,kBAAI,EAAE,gBAAe,KAAM,oBAAoB,MAAM,cAAc;AAClE,kBAAE,YAAW;AACb,kBAAE,kCAAkC,QAAQ,YAAY,iBAAiB;cAC9E,OAAW;AACN,kBAAE,YAAW;cAClB;AAEI,gBAAE,UAAS;YACf;UACA;QACA;QAEC,2BAA2B,SAAU,QAAQ,WAAW;AACvD,eAAK,aAAa,QAAQ,KAAK,OAAO,KAAK,WAAU,GAAI,WAAW,MAAM,SAAU,GAAG;AACtF,cAAE,YAAW;UAChB,CAAG;QACH;QAEC,8BAA8B,SAAU,UAAU,WAAW,QAAQ;AACpE,eAAK;YAAa;YAAQ,KAAK,OAAO,KAAK,WAAU,IAAK;YAAG;YAC5D,SAAU,GAAG;AACZ,kBAAI,cAAc,EAAE,OAAO;AAC1B;cACL;AAGI,uBAAS,IAAI,EAAE,SAAS,SAAS,GAAG,KAAK,GAAG,KAAK;AAChD,oBAAI,KAAK,EAAE,SAAS,CAAC;AAErB,oBAAI,CAAC,OAAO,SAAS,GAAG,OAAO,GAAG;AACjC;gBACN;AAEK,oBAAI,UAAU;AACb,qBAAG,gBAAgB,GAAG,UAAS;AAE/B,qBAAG,UAAU,QAAQ;AACrB,sBAAI,GAAG,aAAa;AACnB,uBAAG,YAAW;kBACrB;gBACA;AAEK,kBAAE,OAAO,cAAc,SAAS,EAAE;cACvC;YACA;YACG,SAAU,GAAG;AACZ,gBAAE,UAAU,QAAQ;YACxB;UACA;QACA;QAEC,mCAAmC,SAAU,WAAW;AAEvD,mBAAS,IAAI,KAAK,SAAS,SAAS,GAAG,KAAK,GAAG,KAAK;AACnD,gBAAI,KAAK,KAAK,SAAS,CAAC;AACxB,gBAAI,GAAG,eAAe;AACrB,iBAAG,UAAU,GAAG,aAAa;AAC7B,qBAAO,GAAG;YACd;UACA;AAEE,cAAI,YAAY,MAAM,KAAK,OAAO;AAEjC,qBAAS,IAAI,KAAK,eAAe,SAAS,GAAG,KAAK,GAAG,KAAK;AACzD,mBAAK,eAAe,CAAC,EAAE,iBAAgB;YAC3C;UACA,OAAS;AACN,qBAAS,IAAI,KAAK,eAAe,SAAS,GAAG,KAAK,GAAG,KAAK;AACzD,mBAAK,eAAe,CAAC,EAAE,kCAAkC,SAAS;YACtE;UACA;QACA;QAEC,kBAAkB,WAAY;AAC7B,cAAI,KAAK,eAAe;AACvB,iBAAK,UAAU,KAAK,aAAa;AACjC,mBAAO,KAAK;UACf;QACA;;QAGC,mCAAmC,SAAU,gBAAgB,YAAY,WAAW,cAAc;AACjG,cAAI,GAAG;AACP,eAAK;YAAa;YAAgB,aAAa;YAAG,YAAY;YAC7D,SAAU,GAAG;AAEZ,mBAAK,IAAI,EAAE,SAAS,SAAS,GAAG,KAAK,GAAG,KAAK;AAC5C,oBAAI,EAAE,SAAS,CAAC;AAChB,oBAAI,CAAC,gBAAgB,CAAC,aAAa,SAAS,EAAE,OAAO,GAAG;AACvD,oBAAE,OAAO,cAAc,YAAY,CAAC;AACpC,sBAAI,EAAE,aAAa;AAClB,sBAAE,YAAW;kBACpB;gBACA;cACA;YACA;YACG,SAAU,GAAG;AAEZ,mBAAK,IAAI,EAAE,eAAe,SAAS,GAAG,KAAK,GAAG,KAAK;AAClD,oBAAI,EAAE,eAAe,CAAC;AACtB,oBAAI,CAAC,gBAAgB,CAAC,aAAa,SAAS,EAAE,OAAO,GAAG;AACvD,oBAAE,OAAO,cAAc,YAAY,CAAC;AACpC,sBAAI,EAAE,aAAa;AAClB,sBAAE,YAAW;kBACpB;gBACA;cACA;YACA;UACA;QACA;;;;;;;QAQC,cAAc,SAAU,iBAAiB,kBAAkB,iBAAiB,iBAAiB,kBAAkB;AAC9G,cAAI,gBAAgB,KAAK,gBACrB,OAAO,KAAK,OACZ,GAAG;AAEP,cAAI,oBAAoB,MAAM;AAC7B,gBAAI,iBAAiB;AACpB,8BAAgB,IAAI;YACxB;AACG,gBAAI,oBAAoB,SAAS,iBAAiB;AACjD,+BAAiB,IAAI;YACzB;UACA;AAEE,cAAI,OAAO,oBAAoB,OAAO,iBAAiB;AACtD,iBAAK,IAAI,cAAc,SAAS,GAAG,KAAK,GAAG,KAAK;AAC/C,kBAAI,cAAc,CAAC;AACnB,kBAAI,EAAE,mBAAmB;AACxB,kBAAE,mBAAkB;cACzB;AACI,kBAAI,gBAAgB,WAAW,EAAE,OAAO,GAAG;AAC1C,kBAAE,aAAa,iBAAiB,kBAAkB,iBAAiB,iBAAiB,gBAAgB;cACzG;YACA;UACA;QACA;;QAGC,iBAAiB,WAAY;AAE5B,iBAAO,KAAK,eAAe,SAAS,KAAK,KAAK,eAAe,CAAC,EAAE,gBAAgB,KAAK;QACvF;MACA,CAAC;AC5YD,QAAE,OAAO,QAAQ;QAChB,aAAa,WAAY;AACxB,cAAI,SAAS,KAAK,QAAQ;AAC1B,eAAK,WAAW,CAAC;AACjB,eAAK,QAAQ,UAAU;AACvB,iBAAO;QACT;QAEC,aAAa,WAAY;AACxB,iBAAO,KAAK,WAAW,KAAK,QAAQ,OAAO;QAC7C;MACA,CAAC;AClBD,QAAE,eAAe,SAAU,UAAU;AACpC,aAAK,YAAY;AACjB,aAAK,cAAc,WAAW;AAC9B,aAAK,QAAQ,CAAA;AACb,aAAK,eAAe,CAAA;MACrB;AAEA,QAAE,aAAa,YAAY;QAE1B,WAAW,SAAU,KAAK,OAAO;AAChC,cAAI,IAAI,KAAK,UAAU,MAAM,CAAC,GAC1B,IAAI,KAAK,UAAU,MAAM,CAAC,GAC1B,OAAO,KAAK,OACZ,MAAM,KAAK,CAAC,IAAI,KAAK,CAAC,KAAK,CAAA,GAC3B,OAAO,IAAI,CAAC,IAAI,IAAI,CAAC,KAAK,CAAA,GAC1B,QAAQ,EAAE,KAAK,MAAM,GAAG;AAE5B,eAAK,aAAa,KAAK,IAAI;AAE3B,eAAK,KAAK,GAAG;QACf;QAEC,cAAc,SAAU,KAAK,OAAO;AACnC,eAAK,aAAa,GAAG;AACrB,eAAK,UAAU,KAAK,KAAK;QAC3B;;QAGC,cAAc,SAAU,KAAK,OAAO;AACnC,cAAI,IAAI,KAAK,UAAU,MAAM,CAAC,GAC1B,IAAI,KAAK,UAAU,MAAM,CAAC,GAC1B,OAAO,KAAK,OACZ,MAAM,KAAK,CAAC,IAAI,KAAK,CAAC,KAAK,CAAA,GAC3B,OAAO,IAAI,CAAC,IAAI,IAAI,CAAC,KAAK,CAAA,GAC1B,GAAG;AAEP,iBAAO,KAAK,aAAa,EAAE,KAAK,MAAM,GAAG,CAAC;AAE1C,eAAK,IAAI,GAAG,MAAM,KAAK,QAAQ,IAAI,KAAK,KAAK;AAC5C,gBAAI,KAAK,CAAC,MAAM,KAAK;AAEpB,mBAAK,OAAO,GAAG,CAAC;AAEhB,kBAAI,QAAQ,GAAG;AACd,uBAAO,IAAI,CAAC;cACjB;AAEI,qBAAO;YACX;UACA;QAEA;QAEC,YAAY,SAAU,IAAI,SAAS;AAClC,cAAI,GAAG,GAAG,GAAG,KAAK,KAAK,MAAM,SACzB,OAAO,KAAK;AAEhB,eAAK,KAAK,MAAM;AACf,kBAAM,KAAK,CAAC;AAEZ,iBAAK,KAAK,KAAK;AACd,qBAAO,IAAI,CAAC;AAEZ,mBAAK,IAAI,GAAG,MAAM,KAAK,QAAQ,IAAI,KAAK,KAAK;AAC5C,0BAAU,GAAG,KAAK,SAAS,KAAK,CAAC,CAAC;AAClC,oBAAI,SAAS;AACZ;AACA;gBACN;cACA;YACA;UACA;QACA;QAEC,eAAe,SAAU,OAAO;AAC/B,cAAI,IAAI,KAAK,UAAU,MAAM,CAAC,GAC1B,IAAI,KAAK,UAAU,MAAM,CAAC,GAC1B,GAAG,GAAG,GAAG,KAAK,MAAM,KAAK,KAAK,MAC9B,cAAc,KAAK,cACnB,gBAAgB,KAAK,aACrB,UAAU;AAEd,eAAK,IAAI,IAAI,GAAG,KAAK,IAAI,GAAG,KAAK;AAChC,kBAAM,KAAK,MAAM,CAAC;AAClB,gBAAI,KAAK;AAER,mBAAK,IAAI,IAAI,GAAG,KAAK,IAAI,GAAG,KAAK;AAChC,uBAAO,IAAI,CAAC;AACZ,oBAAI,MAAM;AAET,uBAAK,IAAI,GAAG,MAAM,KAAK,QAAQ,IAAI,KAAK,KAAK;AAC5C,0BAAM,KAAK,CAAC;AACZ,2BAAO,KAAK,QAAQ,YAAY,EAAE,KAAK,MAAM,GAAG,CAAC,GAAG,KAAK;AACzD,wBAAI,OAAO,iBACV,QAAQ,iBAAiB,YAAY,MAAM;AAC3C,sCAAgB;AAChB,gCAAU;oBAClB;kBACA;gBACA;cACA;YACA;UACA;AACE,iBAAO;QACT;QAEC,WAAW,SAAU,GAAG;AACvB,cAAI,QAAQ,KAAK,MAAM,IAAI,KAAK,SAAS;AACzC,iBAAO,SAAS,KAAK,IAAI,QAAQ;QACnC;QAEC,SAAS,SAAU,GAAG,IAAI;AACzB,cAAI,KAAK,GAAG,IAAI,EAAE,GACd,KAAK,GAAG,IAAI,EAAE;AAClB,iBAAO,KAAK,KAAK,KAAK;QACxB;MACA;AC3FA,OAAC,WAAY;AACZ,UAAE,YAAY;;;;;;;UAQb,YAAY,SAAU,KAAK,IAAI;AAC9B,gBAAI,KAAK,GAAG,CAAC,EAAE,MAAM,GAAG,CAAC,EAAE,KAC1B,KAAK,GAAG,CAAC,EAAE,MAAM,GAAG,CAAC,EAAE;AACxB,mBAAQ,MAAM,IAAI,MAAM,GAAG,CAAC,EAAE,OAAO,MAAM,IAAI,MAAM,GAAG,CAAC,EAAE;UAC9D;;;;;;;;UASE,kCAAkC,SAAU,UAAU,SAAS;AAC9D,gBAAI,OAAO,GACV,QAAQ,MACR,YAAY,CAAA,GACZ,GAAG,IAAI;AAER,iBAAK,IAAI,QAAQ,SAAS,GAAG,KAAK,GAAG,KAAK;AACzC,mBAAK,QAAQ,CAAC;AACd,kBAAI,KAAK,WAAW,IAAI,QAAQ;AAEhC,kBAAI,IAAI,GAAG;AACV,0BAAU,KAAK,EAAE;cACtB,OAAW;AACN;cACL;AAEI,kBAAI,IAAI,MAAM;AACb,uBAAO;AACP,wBAAQ;cACb;YACA;AAEG,mBAAO,EAAE,UAAU,OAAO,UAAoB;UACjD;;;;;;;;UAUE,iBAAiB,SAAU,UAAU,SAAS;AAC7C,gBAAI,sBAAsB,CAAA,GACzB,IAAI,KAAK,iCAAiC,UAAU,OAAO;AAE5D,gBAAI,EAAE,UAAU;AACf,oCACC,oBAAoB;gBACnB,KAAK,gBAAgB,CAAC,SAAS,CAAC,GAAG,EAAE,QAAQ,GAAG,EAAE,SAAS;cACjE;AACI,oCACC,oBAAoB;gBACnB,KAAK,gBAAgB,CAAC,EAAE,UAAU,SAAS,CAAC,CAAC,GAAG,EAAE,SAAS;cACjE;AACI,qBAAO;YACX,OAAU;AACN,qBAAO,CAAC,SAAS,CAAC,CAAC;YACvB;UACA;;;;;;;;UASE,eAAe,SAAU,SAAS;AAEjC,gBAAI,SAAS,OAAO,SAAS,OAC5B,SAAS,OAAO,SAAS,OACzB,WAAW,MAAM,WAAW,MAC5B,WAAW,MAAM,WAAW,MAC5B,QAAQ,MAAM,QAAQ,MACtB;AAED,iBAAK,IAAI,QAAQ,SAAS,GAAG,KAAK,GAAG,KAAK;AACzC,kBAAI,KAAK,QAAQ,CAAC;AAClB,kBAAI,WAAW,SAAS,GAAG,MAAM,QAAQ;AACxC,2BAAW;AACX,yBAAS,GAAG;cACjB;AACI,kBAAI,WAAW,SAAS,GAAG,MAAM,QAAQ;AACxC,2BAAW;AACX,yBAAS,GAAG;cACjB;AACI,kBAAI,WAAW,SAAS,GAAG,MAAM,QAAQ;AACxC,2BAAW;AACX,yBAAS,GAAG;cACjB;AACI,kBAAI,WAAW,SAAS,GAAG,MAAM,QAAQ;AACxC,2BAAW;AACX,yBAAS,GAAG;cACjB;YACA;AAEG,gBAAI,WAAW,QAAQ;AACtB,sBAAQ;AACR,sBAAQ;YACZ,OAAU;AACN,sBAAQ;AACR,sBAAQ;YACZ;AAEG,gBAAI,KAAK,CAAA,EAAG;cAAO,KAAK,gBAAgB,CAAC,OAAO,KAAK,GAAG,OAAO;cAC1D,KAAK,gBAAgB,CAAC,OAAO,KAAK,GAAG,OAAO;YAAC;AAClD,mBAAO;UACV;QACA;MACA,GAAC;AAED,QAAE,cAAc,QAAQ;QACvB,eAAe,WAAY;AAC1B,cAAI,eAAe,KAAK,mBAAkB,GACzC,SAAS,CAAA,GACT,GAAG;AAEJ,eAAK,IAAI,aAAa,SAAS,GAAG,KAAK,GAAG,KAAK;AAC9C,gBAAI,aAAa,CAAC,EAAE,UAAS;AAC7B,mBAAO,KAAK,CAAC;UAChB;AAEE,iBAAO,EAAE,UAAU,cAAc,MAAM;QACzC;MACA,CAAC;ACjKD,QAAE,cAAc,QAAQ;QAEvB,MAAM,KAAK,KAAK;QAChB,uBAAuB;;QACvB,mBAAmB;QAEnB,uBAAwB;;QACxB,oBAAoB;QACpB,qBAAqB;QAErB,yBAAyB;;;QAGzB,UAAU,WAAY;AACrB,cAAI,KAAK,OAAO,gBAAgB,QAAQ,KAAK,OAAO,kBAAkB;AACrE;UACH;AAEE,cAAI,eAAe,KAAK,mBAAmB,MAAM,IAAI,GACpD,QAAQ,KAAK,QACb,MAAM,MAAM,MACZ,SAAS,IAAI,mBAAmB,KAAK,OAAO,GAC5C;AAED,eAAK,OAAO,YAAW;AACvB,eAAK,OAAO,cAAc;AAI1B,cAAI,KAAK,OAAO,QAAQ,wBAAwB;AAC/C,wBAAY,KAAK,OAAO,QAAQ,uBAAuB,aAAa,QAAQ,MAAM;UACrF,WAAa,aAAa,UAAU,KAAK,yBAAyB;AAC/D,wBAAY,KAAK,sBAAsB,aAAa,QAAQ,MAAM;UACrE,OAAS;AACN,mBAAO,KAAK;AACZ,wBAAY,KAAK,sBAAsB,aAAa,QAAQ,MAAM;UACrE;AAEE,eAAK,mBAAmB,cAAc,SAAS;QACjD;QAEC,YAAY,SAAU,aAAa;AAElC,cAAI,KAAK,OAAO,kBAAkB;AACjC;UACH;AACE,eAAK,qBAAqB,WAAW;AAErC,eAAK,OAAO,cAAc;QAC5B;QAEC,uBAAuB,SAAU,OAAO,UAAU;AACjD,cAAI,gBAAgB,KAAK,OAAO,QAAQ,6BAA6B,KAAK,yBAAyB,IAAI,QACtG,YAAY,gBAAgB,KAAK,MACjC,YAAY,KAAK,OAAO,OACxB,MAAM,CAAA,GACN,GAAG;AAEJ,sBAAY,KAAK,IAAI,WAAW,EAAE;AAElC,cAAI,SAAS;AAEb,eAAK,IAAI,GAAG,IAAI,OAAO,KAAK;AAC3B,oBAAQ,KAAK,oBAAoB,IAAI;AACrC,gBAAI,CAAC,IAAI,IAAI,EAAE,MAAM,SAAS,IAAI,YAAY,KAAK,IAAI,KAAK,GAAG,SAAS,IAAI,YAAY,KAAK,IAAI,KAAK,CAAC,EAAE,OAAM;UAClH;AAEE,iBAAO;QACT;QAEC,uBAAuB,SAAU,OAAO,UAAU;AACjD,cAAI,6BAA6B,KAAK,OAAO,QAAQ,4BACpD,YAAY,6BAA6B,KAAK,oBAC9C,aAAa,6BAA6B,KAAK,uBAC/C,eAAe,6BAA6B,KAAK,sBAAsB,KAAK,MAC5E,QAAQ,GACR,MAAM,CAAA,GACN;AAED,cAAI,SAAS;AAGb,eAAK,IAAI,OAAO,KAAK,GAAG,KAAK;AAG5B,gBAAI,IAAI,OAAO;AACd,kBAAI,CAAC,IAAI,IAAI,EAAE,MAAM,SAAS,IAAI,YAAY,KAAK,IAAI,KAAK,GAAG,SAAS,IAAI,YAAY,KAAK,IAAI,KAAK,CAAC,EAAE,OAAM;YACnH;AACG,qBAAS,aAAa,YAAY,IAAI;AACtC,yBAAa,eAAe;UAC/B;AACE,iBAAO;QACT;QAEC,wBAAwB,WAAY;AACnC,cAAI,QAAQ,KAAK,QAChB,MAAM,MAAM,MACZ,KAAK,MAAM,eACX,eAAe,KAAK,mBAAmB,MAAM,IAAI,GACjD,GAAG;AAEJ,gBAAM,cAAc;AAEpB,eAAK,WAAW,CAAC;AACjB,eAAK,IAAI,aAAa,SAAS,GAAG,KAAK,GAAG,KAAK;AAC9C,gBAAI,aAAa,CAAC;AAElB,eAAG,YAAY,CAAC;AAEhB,gBAAI,EAAE,oBAAoB;AACzB,gBAAE,UAAU,EAAE,kBAAkB;AAChC,qBAAO,EAAE;YACb;AACG,gBAAI,EAAE,iBAAiB;AACtB,gBAAE,gBAAgB,CAAC;YACvB;AAEG,gBAAI,EAAE,YAAY;AACjB,kBAAI,YAAY,EAAE,UAAU;AAC5B,qBAAO,EAAE;YACb;UACA;AAEE,gBAAM,KAAK,gBAAgB;YAC1B,SAAS;YACT,SAAS;UACZ,CAAG;AACD,gBAAM,cAAc;AACpB,gBAAM,cAAc;QACtB;MACA,CAAC;AAGD,QAAE,2BAA2B,EAAE,cAAc,OAAO;QACnD,oBAAoB,SAAU,cAAc,WAAW;AACtD,cAAI,QAAQ,KAAK,QAChB,MAAM,MAAM,MACZ,KAAK,MAAM,eACX,aAAa,KAAK,OAAO,QAAQ,0BACjC,GAAG,GAAG,KAAK;AAEZ,gBAAM,cAAc;AAIpB,eAAK,IAAI,GAAG,IAAI,aAAa,QAAQ,KAAK;AACzC,qBAAS,IAAI,mBAAmB,UAAU,CAAC,CAAC;AAC5C,gBAAI,aAAa,CAAC;AAGlB,kBAAM,IAAI,EAAE,SAAS,CAAC,KAAK,SAAS,MAAM,GAAG,UAAU;AACvD,gBAAI,SAAS,GAAG;AAChB,cAAE,aAAa;AAGf,cAAE,qBAAqB,EAAE;AACzB,cAAE,UAAU,MAAM;AAClB,gBAAI,EAAE,iBAAiB;AACtB,gBAAE,gBAAgB,GAAO;YAC7B;AAEG,eAAG,SAAS,CAAC;UAChB;AACE,eAAK,WAAW,GAAG;AAEnB,gBAAM,cAAc;AACpB,gBAAM,KAAK,cAAc;YACxB,SAAS;YACT,SAAS;UACZ,CAAG;QACH;QAEC,sBAAsB,WAAY;AACjC,eAAK,uBAAsB;QAC7B;MACA,CAAC;AAGD,QAAE,cAAc,QAAQ;QAEvB,oBAAoB,SAAU,cAAc,WAAW;AACtD,cAAI,KAAK,MACR,QAAQ,KAAK,QACb,MAAM,MAAM,MACZ,KAAK,MAAM,eACX,kBAAkB,KAAK,SACvB,eAAe,IAAI,mBAAmB,eAAe,GACrD,MAAM,EAAE,KAAK,KACb,aAAa,EAAE,OAAO,CAAA,GAAI,KAAK,OAAO,QAAQ,wBAAwB,GACtE,kBAAkB,WAAW,SAC7B,GAAG,GAAG,KAAK,SAAS,WAAW;AAEhC,cAAI,oBAAoB,QAAW;AAClC,8BAAkB,EAAE,mBAAmB,UAAU,QAAQ,yBAAyB;UACrF;AAEE,cAAI,KAAK;AAER,uBAAW,UAAU;AAGrB,uBAAW,aAAa,WAAW,aAAa,MAAM;UACzD,OAAS;AAEN,uBAAW,UAAU;UACxB;AAEE,gBAAM,cAAc;AAKpB,eAAK,IAAI,GAAG,IAAI,aAAa,QAAQ,KAAK;AACzC,gBAAI,aAAa,CAAC;AAElB,qBAAS,IAAI,mBAAmB,UAAU,CAAC,CAAC;AAG5C,kBAAM,IAAI,EAAE,SAAS,CAAC,iBAAiB,MAAM,GAAG,UAAU;AAC1D,gBAAI,SAAS,GAAG;AAChB,cAAE,aAAa;AAIf,gBAAI,KAAK;AACR,wBAAU,IAAI;AACd,0BAAY,QAAQ,eAAc,IAAK;AACvC,sBAAQ,MAAM,kBAAkB;AAChC,sBAAQ,MAAM,mBAAmB;YACrC;AAGG,gBAAI,EAAE,iBAAiB;AACtB,gBAAE,gBAAgB,GAAO;YAC7B;AACG,gBAAI,EAAE,aAAa;AAClB,gBAAE,YAAW;YACjB;AAGG,eAAG,SAAS,CAAC;AAEb,gBAAI,EAAE,SAAS;AACd,gBAAE,QAAQ,YAAY;YAC1B;UACA;AAEE,gBAAM,aAAY;AAClB,gBAAM,gBAAe;AAGrB,eAAK,IAAI,aAAa,SAAS,GAAG,KAAK,GAAG,KAAK;AAC9C,qBAAS,IAAI,mBAAmB,UAAU,CAAC,CAAC;AAC5C,gBAAI,aAAa,CAAC;AAGlB,cAAE,qBAAqB,EAAE;AACzB,cAAE,UAAU,MAAM;AAElB,gBAAI,EAAE,aAAa;AAClB,gBAAE,YAAW;YACjB;AAGG,gBAAI,KAAK;AACR,oBAAM,EAAE;AACR,wBAAU,IAAI;AACd,sBAAQ,MAAM,mBAAmB;AAEjC,kBAAI,SAAS,EAAC,SAAS,gBAAe,CAAC;YAC3C;UACA;AACE,eAAK,WAAW,GAAG;AAEnB,gBAAM,cAAc;AAEpB,qBAAW,WAAY;AACtB,kBAAM,cAAa;AACnB,kBAAM,KAAK,cAAc;cACxB,SAAS;cACT,SAAS;YACb,CAAI;UACJ,GAAK,GAAG;QACR;QAEC,sBAAsB,SAAU,aAAa;AAC5C,cAAI,KAAK,MACR,QAAQ,KAAK,QACb,MAAM,MAAM,MACZ,KAAK,MAAM,eACX,eAAe,cAAc,IAAI,uBAAuB,KAAK,SAAS,YAAY,MAAM,YAAY,MAAM,IAAI,IAAI,mBAAmB,KAAK,OAAO,GACjJ,eAAe,KAAK,mBAAmB,MAAM,IAAI,GACjD,MAAM,EAAE,KAAK,KACb,GAAG,GAAG,KAAK,SAAS,WAAW;AAEhC,gBAAM,cAAc;AACpB,gBAAM,gBAAe;AAGrB,eAAK,WAAW,CAAC;AACjB,eAAK,IAAI,aAAa,SAAS,GAAG,KAAK,GAAG,KAAK;AAC9C,gBAAI,aAAa,CAAC;AAGlB,gBAAI,CAAC,EAAE,oBAAoB;AAC1B;YACJ;AAGG,cAAE,WAAU;AAGZ,cAAE,UAAU,EAAE,kBAAkB;AAChC,mBAAO,EAAE;AAGT,4BAAgB;AAChB,gBAAI,EAAE,SAAS;AACd,gBAAE,QAAQ,YAAY;AACtB,8BAAgB;YACpB;AACG,gBAAI,EAAE,aAAa;AAClB,gBAAE,YAAW;AACb,8BAAgB;YACpB;AACG,gBAAI,eAAe;AAClB,iBAAG,YAAY,CAAC;YACpB;AAGG,gBAAI,KAAK;AACR,oBAAM,EAAE;AACR,wBAAU,IAAI;AACd,0BAAY,QAAQ,eAAc,IAAK;AACvC,sBAAQ,MAAM,mBAAmB;AACjC,kBAAI,SAAS,EAAC,SAAS,EAAC,CAAC;YAC7B;UACA;AAEE,gBAAM,cAAc;AAEpB,qBAAW,WAAY;AAEtB,gBAAI,uBAAuB;AAC3B,iBAAK,IAAI,aAAa,SAAS,GAAG,KAAK,GAAG,KAAK;AAC9C,kBAAI,aAAa,CAAC;AAClB,kBAAI,EAAE,YAAY;AACjB;cACL;YACA;AAGG,iBAAK,IAAI,aAAa,SAAS,GAAG,KAAK,GAAG,KAAK;AAC9C,kBAAI,aAAa,CAAC;AAElB,kBAAI,CAAC,EAAE,YAAY;AAClB;cACL;AAEI,kBAAI,EAAE,aAAa;AAClB,kBAAE,YAAW;cAClB;AACI,kBAAI,EAAE,iBAAiB;AACtB,kBAAE,gBAAgB,CAAC;cACxB;AAEI,kBAAI,uBAAuB,GAAG;AAC7B,mBAAG,YAAY,CAAC;cACrB;AAEI,kBAAI,YAAY,EAAE,UAAU;AAC5B,qBAAO,EAAE;YACb;AACG,kBAAM,cAAa;AACnB,kBAAM,KAAK,gBAAgB;cAC1B,SAAS;cACT,SAAS;YACb,CAAI;UACJ,GAAK,GAAG;QACR;MACA,CAAC;AAGD,QAAE,mBAAmB,QAAQ;;QAE5B,aAAa;QAEb,YAAY,WAAY;AACvB,eAAK,YAAY,MAAM,MAAM,SAAS;QACxC;QAEC,kBAAkB,WAAY;AAC7B,eAAK,KAAK,GAAG,SAAS,KAAK,oBAAoB,IAAI;AAEnD,cAAI,KAAK,KAAK,QAAQ,eAAe;AACpC,iBAAK,KAAK,GAAG,aAAa,KAAK,sBAAsB,IAAI;UAC5D;AAEE,eAAK,KAAK,GAAG,WAAW,KAAK,wBAAwB,IAAI;AAEzD,cAAI,CAAC,EAAE,QAAQ,OAAO;AACrB,iBAAK,KAAK,YAAY,IAAI;UAI7B;QACA;QAEC,qBAAqB,WAAY;AAChC,eAAK,KAAK,IAAI,SAAS,KAAK,oBAAoB,IAAI;AACpD,eAAK,KAAK,IAAI,aAAa,KAAK,sBAAsB,IAAI;AAC1D,eAAK,KAAK,IAAI,YAAY,KAAK,qBAAqB,IAAI;AACxD,eAAK,KAAK,IAAI,WAAW,KAAK,wBAAwB,IAAI;AAI1D,eAAK,uBAAsB;QAC7B;;;QAIC,sBAAsB,WAAY;AACjC,cAAI,CAAC,KAAK,MAAM;AACf;UACH;AAEE,eAAK,KAAK,GAAG,YAAY,KAAK,qBAAqB,IAAI;QACzD;QAEC,qBAAqB,SAAU,aAAa;AAE3C,cAAI,EAAE,QAAQ,SAAS,KAAK,KAAK,UAAU,kBAAkB,GAAG;AAC/D;UACH;AAEE,eAAK,KAAK,IAAI,YAAY,KAAK,qBAAqB,IAAI;AACxD,eAAK,YAAY,WAAW;QAC9B;QAEC,oBAAoB,WAAY;AAE/B,eAAK,YAAW;QAClB;QAEC,aAAa,SAAU,aAAa;AACnC,cAAI,KAAK,aAAa;AACrB,iBAAK,YAAY,WAAW,WAAW;UAC1C;QACA;QAEC,wBAAwB,WAAY;AACnC,cAAI,KAAK,aAAa;AACrB,iBAAK,YAAY,uBAAsB;UAC1C;QACA;;QAGC,kBAAkB,SAAU,OAAO;AAClC,cAAI,MAAM,YAAY;AACrB,iBAAK,cAAc,YAAY,KAAK;AAEpC,gBAAI,MAAM,aAAa;AACtB,oBAAM,YAAW;YACrB;AAEG,gBAAI,MAAM,iBAAiB;AAC1B,oBAAM,gBAAgB,CAAC;YAC3B;AAEG,iBAAK,KAAK,YAAY,MAAM,UAAU;AACtC,mBAAO,MAAM;UAChB;QACA;MACA,CAAC;ACpdD,QAAE,mBAAmB,QAAQ;;;;;;;;;QAS5B,iBAAiB,SAAU,QAAQ;AAClC,cAAI,CAAC,QAAQ;AACZ,qBAAS,KAAK,iBAAiB,mBAAkB;UACpD,WAAa,kBAAkB,EAAE,oBAAoB;AAClD,qBAAS,OAAO,iBAAiB,mBAAkB;UACtD,WAAa,kBAAkB,EAAE,YAAY;AAC1C,qBAAS,OAAO;UACnB,WAAa,kBAAkB,EAAE,eAAe;AAC7C,qBAAS,OAAO,mBAAkB;UACrC,WAAa,kBAAkB,EAAE,QAAQ;AACtC,qBAAS,CAAC,MAAM;UACnB;AACE,eAAK,4BAA4B,MAAM;AACvC,eAAK,sBAAqB;AAG1B,cAAI,KAAK,QAAQ,kBAAkB;AAClC,iBAAK,gCAAgC,MAAM;UAC9C;AAEE,iBAAO;QACT;;;;;;QAOC,6BAA6B,SAAU,QAAQ;AAC9C,cAAI,IAAI;AAGR,eAAK,MAAM,QAAQ;AAMlB,qBAAS,OAAO,EAAE,EAAE;AACpB,mBAAO,QAAQ;AACd,qBAAO,mBAAmB;AAC1B,uBAAS,OAAO;YACpB;UACA;QACA;;;;;;;QAQC,iCAAiC,SAAU,QAAQ;AAClD,cAAI,IAAI;AAER,eAAK,MAAM,QAAQ;AAClB,oBAAQ,OAAO,EAAE;AAGjB,gBAAI,KAAK,SAAS,KAAK,GAAG;AAEzB,oBAAM,QAAQ,KAAK,oBAAoB,KAAK,CAAC;YACjD;UACA;QACA;MACA,CAAC;AAED,QAAE,OAAO,QAAQ;;;;;;;;QAQhB,oBAAoB,SAAU,SAAS,yBAAyB;AAC/D,cAAI,OAAO,KAAK,QAAQ;AAExB,YAAE,WAAW,MAAM,OAAO;AAE1B,eAAK,QAAQ,IAAI;AAMjB,cAAI,2BAA2B,KAAK,UAAU;AAC7C,iBAAK,SAAS,OAAO,gBAAgB,IAAI;UAC5C;AAEE,iBAAO;QACT;MACA,CAAC;;;;;;;", "names": []}