{"version": 3, "sources": ["../../@intlify/shared/dist/shared.cjs.js", "../../@intlify/shared/index.js", "../../source-map/lib/base64.js", "../../source-map/lib/base64-vlq.js", "../../source-map/lib/util.js", "../../source-map/lib/array-set.js", "../../source-map/lib/mapping-list.js", "../../source-map/lib/source-map-generator.js", "../../source-map/lib/binary-search.js", "../../source-map/lib/quick-sort.js", "../../source-map/lib/source-map-consumer.js", "../../source-map/lib/source-node.js", "../../source-map/source-map.js", "../../@intlify/message-compiler/dist/message-compiler.cjs.js", "../../@intlify/message-compiler/index.js", "../../@intlify/devtools-if/dist/devtools-if.cjs.js", "../../@intlify/devtools-if/index.js", "../../@intlify/core-base/dist/core-base.cjs.js", "../../@intlify/core-base/index.js", "../../vue-i18n/dist/vue-i18n.cjs.js"], "sourcesContent": ["/*!\n  * shared v9.2.2\n  * (c) 2022 ka<PERSON><PERSON> kawa<PERSON>\n  * Released under the MIT License.\n  */\n'use strict';\n\nObject.defineProperty(exports, '__esModule', { value: true });\n\n/**\r\n * Original Utilities\r\n * written by ka<PERSON>ya kawa<PERSON>\r\n */\r\nconst inBrowser = typeof window !== 'undefined';\r\nexports.mark = void 0;\r\nexports.measure = void 0;\r\n{\r\n    const perf = inBrowser && window.performance;\r\n    if (perf &&\r\n        perf.mark &&\r\n        perf.measure &&\r\n        perf.clearMarks &&\r\n        perf.clearMeasures) {\r\n        exports.mark = (tag) => perf.mark(tag);\r\n        exports.measure = (name, startTag, endTag) => {\r\n            perf.measure(name, startTag, endTag);\r\n            perf.clearMarks(startTag);\r\n            perf.clearMarks(endTag);\r\n        };\r\n    }\r\n}\r\nconst RE_ARGS = /\\{([0-9a-zA-Z]+)\\}/g;\r\n/* eslint-disable */\r\nfunction format(message, ...args) {\r\n    if (args.length === 1 && isObject(args[0])) {\r\n        args = args[0];\r\n    }\r\n    if (!args || !args.hasOwnProperty) {\r\n        args = {};\r\n    }\r\n    return message.replace(RE_ARGS, (match, identifier) => {\r\n        return args.hasOwnProperty(identifier) ? args[identifier] : '';\r\n    });\r\n}\r\nconst hasSymbol = typeof Symbol === 'function' && typeof Symbol.toStringTag === 'symbol';\r\nconst makeSymbol = (name) => hasSymbol ? Symbol(name) : name;\r\nconst generateFormatCacheKey = (locale, key, source) => friendlyJSONstringify({ l: locale, k: key, s: source });\r\nconst friendlyJSONstringify = (json) => JSON.stringify(json)\r\n    .replace(/\\u2028/g, '\\\\u2028')\r\n    .replace(/\\u2029/g, '\\\\u2029')\r\n    .replace(/\\u0027/g, '\\\\u0027');\r\nconst isNumber = (val) => typeof val === 'number' && isFinite(val);\r\nconst isDate = (val) => toTypeString(val) === '[object Date]';\r\nconst isRegExp = (val) => toTypeString(val) === '[object RegExp]';\r\nconst isEmptyObject = (val) => isPlainObject(val) && Object.keys(val).length === 0;\r\nfunction warn(msg, err) {\r\n    if (typeof console !== 'undefined') {\r\n        console.warn(`[intlify] ` + msg);\r\n        /* istanbul ignore if */\r\n        if (err) {\r\n            console.warn(err.stack);\r\n        }\r\n    }\r\n}\r\nconst assign = Object.assign;\r\nlet _globalThis;\r\nconst getGlobalThis = () => {\r\n    // prettier-ignore\r\n    return (_globalThis ||\r\n        (_globalThis =\r\n            typeof globalThis !== 'undefined'\r\n                ? globalThis\r\n                : typeof self !== 'undefined'\r\n                    ? self\r\n                    : typeof window !== 'undefined'\r\n                        ? window\r\n                        : typeof global !== 'undefined'\r\n                            ? global\r\n                            : {}));\r\n};\r\nfunction escapeHtml(rawText) {\r\n    return rawText\r\n        .replace(/</g, '&lt;')\r\n        .replace(/>/g, '&gt;')\r\n        .replace(/\"/g, '&quot;')\r\n        .replace(/'/g, '&apos;');\r\n}\r\nconst hasOwnProperty = Object.prototype.hasOwnProperty;\r\nfunction hasOwn(obj, key) {\r\n    return hasOwnProperty.call(obj, key);\r\n}\r\n/* eslint-enable */\r\n/**\r\n * Useful Utilities By Evan you\r\n * Modified by kazuya kawaguchi\r\n * MIT License\r\n * https://github.com/vuejs/vue-next/blob/master/packages/shared/src/index.ts\r\n * https://github.com/vuejs/vue-next/blob/master/packages/shared/src/codeframe.ts\r\n */\r\nconst isArray = Array.isArray;\r\nconst isFunction = (val) => typeof val === 'function';\r\nconst isString = (val) => typeof val === 'string';\r\nconst isBoolean = (val) => typeof val === 'boolean';\r\nconst isSymbol = (val) => typeof val === 'symbol';\r\nconst isObject = (val) => // eslint-disable-line\r\n val !== null && typeof val === 'object';\r\nconst isPromise = (val) => {\r\n    return isObject(val) && isFunction(val.then) && isFunction(val.catch);\r\n};\r\nconst objectToString = Object.prototype.toString;\r\nconst toTypeString = (value) => objectToString.call(value);\r\nconst isPlainObject = (val) => toTypeString(val) === '[object Object]';\r\n// for converting list and named values to displayed strings.\r\nconst toDisplayString = (val) => {\r\n    return val == null\r\n        ? ''\r\n        : isArray(val) || (isPlainObject(val) && val.toString === objectToString)\r\n            ? JSON.stringify(val, null, 2)\r\n            : String(val);\r\n};\r\nconst RANGE = 2;\r\nfunction generateCodeFrame(source, start = 0, end = source.length) {\r\n    const lines = source.split(/\\r?\\n/);\r\n    let count = 0;\r\n    const res = [];\r\n    for (let i = 0; i < lines.length; i++) {\r\n        count += lines[i].length + 1;\r\n        if (count >= start) {\r\n            for (let j = i - RANGE; j <= i + RANGE || end > count; j++) {\r\n                if (j < 0 || j >= lines.length)\r\n                    continue;\r\n                const line = j + 1;\r\n                res.push(`${line}${' '.repeat(3 - String(line).length)}|  ${lines[j]}`);\r\n                const lineLength = lines[j].length;\r\n                if (j === i) {\r\n                    // push underline\r\n                    const pad = start - (count - lineLength) + 1;\r\n                    const length = Math.max(1, end > count ? lineLength - pad : end - start);\r\n                    res.push(`   |  ` + ' '.repeat(pad) + '^'.repeat(length));\r\n                }\r\n                else if (j > i) {\r\n                    if (end > count) {\r\n                        const length = Math.max(Math.min(end - count, lineLength), 1);\r\n                        res.push(`   |  ` + '^'.repeat(length));\r\n                    }\r\n                    count += lineLength + 1;\r\n                }\r\n            }\r\n            break;\r\n        }\r\n    }\r\n    return res.join('\\n');\r\n}\n\n/**\r\n * Event emitter, forked from the below:\r\n * - original repository url: https://github.com/developit/mitt\r\n * - code url: https://github.com/developit/mitt/blob/master/src/index.ts\r\n * - author: Jason Miller (https://github.com/developit)\r\n * - license: MIT\r\n */\r\n/**\r\n * Create a event emitter\r\n *\r\n * @returns An event emitter\r\n */\r\nfunction createEmitter() {\r\n    const events = new Map();\r\n    const emitter = {\r\n        events,\r\n        on(event, handler) {\r\n            const handlers = events.get(event);\r\n            const added = handlers && handlers.push(handler);\r\n            if (!added) {\r\n                events.set(event, [handler]);\r\n            }\r\n        },\r\n        off(event, handler) {\r\n            const handlers = events.get(event);\r\n            if (handlers) {\r\n                handlers.splice(handlers.indexOf(handler) >>> 0, 1);\r\n            }\r\n        },\r\n        emit(event, payload) {\r\n            (events.get(event) || [])\r\n                .slice()\r\n                .map(handler => handler(payload));\r\n            (events.get('*') || [])\r\n                .slice()\r\n                .map(handler => handler(event, payload));\r\n        }\r\n    };\r\n    return emitter;\r\n}\n\nexports.assign = assign;\nexports.createEmitter = createEmitter;\nexports.escapeHtml = escapeHtml;\nexports.format = format;\nexports.friendlyJSONstringify = friendlyJSONstringify;\nexports.generateCodeFrame = generateCodeFrame;\nexports.generateFormatCacheKey = generateFormatCacheKey;\nexports.getGlobalThis = getGlobalThis;\nexports.hasOwn = hasOwn;\nexports.inBrowser = inBrowser;\nexports.isArray = isArray;\nexports.isBoolean = isBoolean;\nexports.isDate = isDate;\nexports.isEmptyObject = isEmptyObject;\nexports.isFunction = isFunction;\nexports.isNumber = isNumber;\nexports.isObject = isObject;\nexports.isPlainObject = isPlainObject;\nexports.isPromise = isPromise;\nexports.isRegExp = isRegExp;\nexports.isString = isString;\nexports.isSymbol = isSymbol;\nexports.makeSymbol = makeSymbol;\nexports.objectToString = objectToString;\nexports.toDisplayString = toDisplayString;\nexports.toTypeString = toTypeString;\nexports.warn = warn;\n", "'use strict'\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./dist/shared.cjs.prod.js')\n} else {\n  module.exports = require('./dist/shared.cjs.js')\n}\n", "/* -*- Mode: js; js-indent-level: 2; -*- */\n/*\n * Copyright 2011 Mozilla Foundation and contributors\n * Licensed under the New BSD license. See LICENSE or:\n * http://opensource.org/licenses/BSD-3-Clause\n */\n\nvar intToCharMap = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz**********+/'.split('');\n\n/**\n * Encode an integer in the range of 0 to 63 to a single base 64 digit.\n */\nexports.encode = function (number) {\n  if (0 <= number && number < intToCharMap.length) {\n    return intToCharMap[number];\n  }\n  throw new TypeError(\"Must be between 0 and 63: \" + number);\n};\n\n/**\n * Decode a single base 64 character code digit to an integer. Returns -1 on\n * failure.\n */\nexports.decode = function (charCode) {\n  var bigA = 65;     // 'A'\n  var bigZ = 90;     // 'Z'\n\n  var littleA = 97;  // 'a'\n  var littleZ = 122; // 'z'\n\n  var zero = 48;     // '0'\n  var nine = 57;     // '9'\n\n  var plus = 43;     // '+'\n  var slash = 47;    // '/'\n\n  var littleOffset = 26;\n  var numberOffset = 52;\n\n  // 0 - 25: ABCDEFGHIJKLMNOPQRSTUVWXYZ\n  if (bigA <= charCode && charCode <= bigZ) {\n    return (charCode - bigA);\n  }\n\n  // 26 - 51: abcdefghijklmnopqrstuvwxyz\n  if (littleA <= charCode && charCode <= littleZ) {\n    return (charCode - littleA + littleOffset);\n  }\n\n  // 52 - 61: **********\n  if (zero <= charCode && charCode <= nine) {\n    return (charCode - zero + numberOffset);\n  }\n\n  // 62: +\n  if (charCode == plus) {\n    return 62;\n  }\n\n  // 63: /\n  if (charCode == slash) {\n    return 63;\n  }\n\n  // Invalid base64 digit.\n  return -1;\n};\n", "/* -*- Mode: js; js-indent-level: 2; -*- */\n/*\n * Copyright 2011 Mozilla Foundation and contributors\n * Licensed under the New BSD license. See LICENSE or:\n * http://opensource.org/licenses/BSD-3-Clause\n *\n * Based on the Base 64 VLQ implementation in Closure Compiler:\n * https://code.google.com/p/closure-compiler/source/browse/trunk/src/com/google/debugging/sourcemap/Base64VLQ.java\n *\n * Copyright 2011 The Closure Compiler Authors. All rights reserved.\n * Redistribution and use in source and binary forms, with or without\n * modification, are permitted provided that the following conditions are\n * met:\n *\n *  * Redistributions of source code must retain the above copyright\n *    notice, this list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above\n *    copyright notice, this list of conditions and the following\n *    disclaimer in the documentation and/or other materials provided\n *    with the distribution.\n *  * Neither the name of Google Inc. nor the names of its\n *    contributors may be used to endorse or promote products derived\n *    from this software without specific prior written permission.\n *\n * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS\n * \"AS IS\" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT\n * LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR\n * A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT\n * OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,\n * SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT\n * LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,\n * DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY\n * THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT\n * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE\n * OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n */\n\nvar base64 = require('./base64');\n\n// A single base 64 digit can contain 6 bits of data. For the base 64 variable\n// length quantities we use in the source map spec, the first bit is the sign,\n// the next four bits are the actual value, and the 6th bit is the\n// continuation bit. The continuation bit tells us whether there are more\n// digits in this value following this digit.\n//\n//   Continuation\n//   |    Sign\n//   |    |\n//   V    V\n//   101011\n\nvar VLQ_BASE_SHIFT = 5;\n\n// binary: 100000\nvar VLQ_BASE = 1 << VLQ_BASE_SHIFT;\n\n// binary: 011111\nvar VLQ_BASE_MASK = VLQ_BASE - 1;\n\n// binary: 100000\nvar VLQ_CONTINUATION_BIT = VLQ_BASE;\n\n/**\n * Converts from a two-complement value to a value where the sign bit is\n * placed in the least significant bit.  For example, as decimals:\n *   1 becomes 2 (10 binary), -1 becomes 3 (11 binary)\n *   2 becomes 4 (100 binary), -2 becomes 5 (101 binary)\n */\nfunction toVLQSigned(aValue) {\n  return aValue < 0\n    ? ((-aValue) << 1) + 1\n    : (aValue << 1) + 0;\n}\n\n/**\n * Converts to a two-complement value from a value where the sign bit is\n * placed in the least significant bit.  For example, as decimals:\n *   2 (10 binary) becomes 1, 3 (11 binary) becomes -1\n *   4 (100 binary) becomes 2, 5 (101 binary) becomes -2\n */\nfunction fromVLQSigned(aValue) {\n  var isNegative = (aValue & 1) === 1;\n  var shifted = aValue >> 1;\n  return isNegative\n    ? -shifted\n    : shifted;\n}\n\n/**\n * Returns the base 64 VLQ encoded value.\n */\nexports.encode = function base64VLQ_encode(aValue) {\n  var encoded = \"\";\n  var digit;\n\n  var vlq = toVLQSigned(aValue);\n\n  do {\n    digit = vlq & VLQ_BASE_MASK;\n    vlq >>>= VLQ_BASE_SHIFT;\n    if (vlq > 0) {\n      // There are still more digits in this value, so we must make sure the\n      // continuation bit is marked.\n      digit |= VLQ_CONTINUATION_BIT;\n    }\n    encoded += base64.encode(digit);\n  } while (vlq > 0);\n\n  return encoded;\n};\n\n/**\n * Decodes the next base 64 VLQ value from the given string and returns the\n * value and the rest of the string via the out parameter.\n */\nexports.decode = function base64VLQ_decode(aStr, aIndex, aOutParam) {\n  var strLen = aStr.length;\n  var result = 0;\n  var shift = 0;\n  var continuation, digit;\n\n  do {\n    if (aIndex >= strLen) {\n      throw new Error(\"Expected more digits in base 64 VLQ value.\");\n    }\n\n    digit = base64.decode(aStr.charCodeAt(aIndex++));\n    if (digit === -1) {\n      throw new Error(\"Invalid base64 digit: \" + aStr.charAt(aIndex - 1));\n    }\n\n    continuation = !!(digit & VLQ_CONTINUATION_BIT);\n    digit &= VLQ_BASE_MASK;\n    result = result + (digit << shift);\n    shift += VLQ_BASE_SHIFT;\n  } while (continuation);\n\n  aOutParam.value = fromVLQSigned(result);\n  aOutParam.rest = aIndex;\n};\n", "/* -*- Mode: js; js-indent-level: 2; -*- */\n/*\n * Copyright 2011 Mozilla Foundation and contributors\n * Licensed under the New BSD license. See LICENSE or:\n * http://opensource.org/licenses/BSD-3-Clause\n */\n\n/**\n * This is a helper function for getting values from parameter/options\n * objects.\n *\n * @param args The object we are extracting values from\n * @param name The name of the property we are getting.\n * @param defaultValue An optional value to return if the property is missing\n * from the object. If this is not specified and the property is missing, an\n * error will be thrown.\n */\nfunction getArg(aArgs, aName, aDefaultValue) {\n  if (aName in aArgs) {\n    return aArgs[aName];\n  } else if (arguments.length === 3) {\n    return aDefaultValue;\n  } else {\n    throw new Error('\"' + aName + '\" is a required argument.');\n  }\n}\nexports.getArg = getArg;\n\nvar urlRegexp = /^(?:([\\w+\\-.]+):)?\\/\\/(?:(\\w+:\\w+)@)?([\\w.-]*)(?::(\\d+))?(.*)$/;\nvar dataUrlRegexp = /^data:.+\\,.+$/;\n\nfunction urlParse(aUrl) {\n  var match = aUrl.match(urlRegexp);\n  if (!match) {\n    return null;\n  }\n  return {\n    scheme: match[1],\n    auth: match[2],\n    host: match[3],\n    port: match[4],\n    path: match[5]\n  };\n}\nexports.urlParse = urlParse;\n\nfunction urlGenerate(aParsedUrl) {\n  var url = '';\n  if (aParsedUrl.scheme) {\n    url += aParsedUrl.scheme + ':';\n  }\n  url += '//';\n  if (aParsedUrl.auth) {\n    url += aParsedUrl.auth + '@';\n  }\n  if (aParsedUrl.host) {\n    url += aParsedUrl.host;\n  }\n  if (aParsedUrl.port) {\n    url += \":\" + aParsedUrl.port\n  }\n  if (aParsedUrl.path) {\n    url += aParsedUrl.path;\n  }\n  return url;\n}\nexports.urlGenerate = urlGenerate;\n\n/**\n * Normalizes a path, or the path portion of a URL:\n *\n * - Replaces consecutive slashes with one slash.\n * - Removes unnecessary '.' parts.\n * - Removes unnecessary '<dir>/..' parts.\n *\n * Based on code in the Node.js 'path' core module.\n *\n * @param aPath The path or url to normalize.\n */\nfunction normalize(aPath) {\n  var path = aPath;\n  var url = urlParse(aPath);\n  if (url) {\n    if (!url.path) {\n      return aPath;\n    }\n    path = url.path;\n  }\n  var isAbsolute = exports.isAbsolute(path);\n\n  var parts = path.split(/\\/+/);\n  for (var part, up = 0, i = parts.length - 1; i >= 0; i--) {\n    part = parts[i];\n    if (part === '.') {\n      parts.splice(i, 1);\n    } else if (part === '..') {\n      up++;\n    } else if (up > 0) {\n      if (part === '') {\n        // The first part is blank if the path is absolute. Trying to go\n        // above the root is a no-op. Therefore we can remove all '..' parts\n        // directly after the root.\n        parts.splice(i + 1, up);\n        up = 0;\n      } else {\n        parts.splice(i, 2);\n        up--;\n      }\n    }\n  }\n  path = parts.join('/');\n\n  if (path === '') {\n    path = isAbsolute ? '/' : '.';\n  }\n\n  if (url) {\n    url.path = path;\n    return urlGenerate(url);\n  }\n  return path;\n}\nexports.normalize = normalize;\n\n/**\n * Joins two paths/URLs.\n *\n * @param aRoot The root path or URL.\n * @param aPath The path or URL to be joined with the root.\n *\n * - If aPath is a URL or a data URI, aPath is returned, unless aPath is a\n *   scheme-relative URL: Then the scheme of aRoot, if any, is prepended\n *   first.\n * - Otherwise aPath is a path. If aRoot is a URL, then its path portion\n *   is updated with the result and aRoot is returned. Otherwise the result\n *   is returned.\n *   - If aPath is absolute, the result is aPath.\n *   - Otherwise the two paths are joined with a slash.\n * - Joining for example 'http://' and 'www.example.com' is also supported.\n */\nfunction join(aRoot, aPath) {\n  if (aRoot === \"\") {\n    aRoot = \".\";\n  }\n  if (aPath === \"\") {\n    aPath = \".\";\n  }\n  var aPathUrl = urlParse(aPath);\n  var aRootUrl = urlParse(aRoot);\n  if (aRootUrl) {\n    aRoot = aRootUrl.path || '/';\n  }\n\n  // `join(foo, '//www.example.org')`\n  if (aPathUrl && !aPathUrl.scheme) {\n    if (aRootUrl) {\n      aPathUrl.scheme = aRootUrl.scheme;\n    }\n    return urlGenerate(aPathUrl);\n  }\n\n  if (aPathUrl || aPath.match(dataUrlRegexp)) {\n    return aPath;\n  }\n\n  // `join('http://', 'www.example.com')`\n  if (aRootUrl && !aRootUrl.host && !aRootUrl.path) {\n    aRootUrl.host = aPath;\n    return urlGenerate(aRootUrl);\n  }\n\n  var joined = aPath.charAt(0) === '/'\n    ? aPath\n    : normalize(aRoot.replace(/\\/+$/, '') + '/' + aPath);\n\n  if (aRootUrl) {\n    aRootUrl.path = joined;\n    return urlGenerate(aRootUrl);\n  }\n  return joined;\n}\nexports.join = join;\n\nexports.isAbsolute = function (aPath) {\n  return aPath.charAt(0) === '/' || urlRegexp.test(aPath);\n};\n\n/**\n * Make a path relative to a URL or another path.\n *\n * @param aRoot The root path or URL.\n * @param aPath The path or URL to be made relative to aRoot.\n */\nfunction relative(aRoot, aPath) {\n  if (aRoot === \"\") {\n    aRoot = \".\";\n  }\n\n  aRoot = aRoot.replace(/\\/$/, '');\n\n  // It is possible for the path to be above the root. In this case, simply\n  // checking whether the root is a prefix of the path won't work. Instead, we\n  // need to remove components from the root one by one, until either we find\n  // a prefix that fits, or we run out of components to remove.\n  var level = 0;\n  while (aPath.indexOf(aRoot + '/') !== 0) {\n    var index = aRoot.lastIndexOf(\"/\");\n    if (index < 0) {\n      return aPath;\n    }\n\n    // If the only part of the root that is left is the scheme (i.e. http://,\n    // file:///, etc.), one or more slashes (/), or simply nothing at all, we\n    // have exhausted all components, so the path is not relative to the root.\n    aRoot = aRoot.slice(0, index);\n    if (aRoot.match(/^([^\\/]+:\\/)?\\/*$/)) {\n      return aPath;\n    }\n\n    ++level;\n  }\n\n  // Make sure we add a \"../\" for each component we removed from the root.\n  return Array(level + 1).join(\"../\") + aPath.substr(aRoot.length + 1);\n}\nexports.relative = relative;\n\nvar supportsNullProto = (function () {\n  var obj = Object.create(null);\n  return !('__proto__' in obj);\n}());\n\nfunction identity (s) {\n  return s;\n}\n\n/**\n * Because behavior goes wacky when you set `__proto__` on objects, we\n * have to prefix all the strings in our set with an arbitrary character.\n *\n * See https://github.com/mozilla/source-map/pull/31 and\n * https://github.com/mozilla/source-map/issues/30\n *\n * @param String aStr\n */\nfunction toSetString(aStr) {\n  if (isProtoString(aStr)) {\n    return '$' + aStr;\n  }\n\n  return aStr;\n}\nexports.toSetString = supportsNullProto ? identity : toSetString;\n\nfunction fromSetString(aStr) {\n  if (isProtoString(aStr)) {\n    return aStr.slice(1);\n  }\n\n  return aStr;\n}\nexports.fromSetString = supportsNullProto ? identity : fromSetString;\n\nfunction isProtoString(s) {\n  if (!s) {\n    return false;\n  }\n\n  var length = s.length;\n\n  if (length < 9 /* \"__proto__\".length */) {\n    return false;\n  }\n\n  if (s.charCodeAt(length - 1) !== 95  /* '_' */ ||\n      s.charCodeAt(length - 2) !== 95  /* '_' */ ||\n      s.charCodeAt(length - 3) !== 111 /* 'o' */ ||\n      s.charCodeAt(length - 4) !== 116 /* 't' */ ||\n      s.charCodeAt(length - 5) !== 111 /* 'o' */ ||\n      s.charCodeAt(length - 6) !== 114 /* 'r' */ ||\n      s.charCodeAt(length - 7) !== 112 /* 'p' */ ||\n      s.charCodeAt(length - 8) !== 95  /* '_' */ ||\n      s.charCodeAt(length - 9) !== 95  /* '_' */) {\n    return false;\n  }\n\n  for (var i = length - 10; i >= 0; i--) {\n    if (s.charCodeAt(i) !== 36 /* '$' */) {\n      return false;\n    }\n  }\n\n  return true;\n}\n\n/**\n * Comparator between two mappings where the original positions are compared.\n *\n * Optionally pass in `true` as `onlyCompareGenerated` to consider two\n * mappings with the same original source/line/column, but different generated\n * line and column the same. Useful when searching for a mapping with a\n * stubbed out mapping.\n */\nfunction compareByOriginalPositions(mappingA, mappingB, onlyCompareOriginal) {\n  var cmp = strcmp(mappingA.source, mappingB.source);\n  if (cmp !== 0) {\n    return cmp;\n  }\n\n  cmp = mappingA.originalLine - mappingB.originalLine;\n  if (cmp !== 0) {\n    return cmp;\n  }\n\n  cmp = mappingA.originalColumn - mappingB.originalColumn;\n  if (cmp !== 0 || onlyCompareOriginal) {\n    return cmp;\n  }\n\n  cmp = mappingA.generatedColumn - mappingB.generatedColumn;\n  if (cmp !== 0) {\n    return cmp;\n  }\n\n  cmp = mappingA.generatedLine - mappingB.generatedLine;\n  if (cmp !== 0) {\n    return cmp;\n  }\n\n  return strcmp(mappingA.name, mappingB.name);\n}\nexports.compareByOriginalPositions = compareByOriginalPositions;\n\n/**\n * Comparator between two mappings with deflated source and name indices where\n * the generated positions are compared.\n *\n * Optionally pass in `true` as `onlyCompareGenerated` to consider two\n * mappings with the same generated line and column, but different\n * source/name/original line and column the same. Useful when searching for a\n * mapping with a stubbed out mapping.\n */\nfunction compareByGeneratedPositionsDeflated(mappingA, mappingB, onlyCompareGenerated) {\n  var cmp = mappingA.generatedLine - mappingB.generatedLine;\n  if (cmp !== 0) {\n    return cmp;\n  }\n\n  cmp = mappingA.generatedColumn - mappingB.generatedColumn;\n  if (cmp !== 0 || onlyCompareGenerated) {\n    return cmp;\n  }\n\n  cmp = strcmp(mappingA.source, mappingB.source);\n  if (cmp !== 0) {\n    return cmp;\n  }\n\n  cmp = mappingA.originalLine - mappingB.originalLine;\n  if (cmp !== 0) {\n    return cmp;\n  }\n\n  cmp = mappingA.originalColumn - mappingB.originalColumn;\n  if (cmp !== 0) {\n    return cmp;\n  }\n\n  return strcmp(mappingA.name, mappingB.name);\n}\nexports.compareByGeneratedPositionsDeflated = compareByGeneratedPositionsDeflated;\n\nfunction strcmp(aStr1, aStr2) {\n  if (aStr1 === aStr2) {\n    return 0;\n  }\n\n  if (aStr1 === null) {\n    return 1; // aStr2 !== null\n  }\n\n  if (aStr2 === null) {\n    return -1; // aStr1 !== null\n  }\n\n  if (aStr1 > aStr2) {\n    return 1;\n  }\n\n  return -1;\n}\n\n/**\n * Comparator between two mappings with inflated source and name strings where\n * the generated positions are compared.\n */\nfunction compareByGeneratedPositionsInflated(mappingA, mappingB) {\n  var cmp = mappingA.generatedLine - mappingB.generatedLine;\n  if (cmp !== 0) {\n    return cmp;\n  }\n\n  cmp = mappingA.generatedColumn - mappingB.generatedColumn;\n  if (cmp !== 0) {\n    return cmp;\n  }\n\n  cmp = strcmp(mappingA.source, mappingB.source);\n  if (cmp !== 0) {\n    return cmp;\n  }\n\n  cmp = mappingA.originalLine - mappingB.originalLine;\n  if (cmp !== 0) {\n    return cmp;\n  }\n\n  cmp = mappingA.originalColumn - mappingB.originalColumn;\n  if (cmp !== 0) {\n    return cmp;\n  }\n\n  return strcmp(mappingA.name, mappingB.name);\n}\nexports.compareByGeneratedPositionsInflated = compareByGeneratedPositionsInflated;\n\n/**\n * Strip any JSON XSSI avoidance prefix from the string (as documented\n * in the source maps specification), and then parse the string as\n * JSON.\n */\nfunction parseSourceMapInput(str) {\n  return JSON.parse(str.replace(/^\\)]}'[^\\n]*\\n/, ''));\n}\nexports.parseSourceMapInput = parseSourceMapInput;\n\n/**\n * Compute the URL of a source given the the source root, the source's\n * URL, and the source map's URL.\n */\nfunction computeSourceURL(sourceRoot, sourceURL, sourceMapURL) {\n  sourceURL = sourceURL || '';\n\n  if (sourceRoot) {\n    // This follows what Chrome does.\n    if (sourceRoot[sourceRoot.length - 1] !== '/' && sourceURL[0] !== '/') {\n      sourceRoot += '/';\n    }\n    // The spec says:\n    //   Line 4: An optional source root, useful for relocating source\n    //   files on a server or removing repeated values in the\n    //   “sources” entry.  This value is prepended to the individual\n    //   entries in the “source” field.\n    sourceURL = sourceRoot + sourceURL;\n  }\n\n  // Historically, SourceMapConsumer did not take the sourceMapURL as\n  // a parameter.  This mode is still somewhat supported, which is why\n  // this code block is conditional.  However, it's preferable to pass\n  // the source map URL to SourceMapConsumer, so that this function\n  // can implement the source URL resolution algorithm as outlined in\n  // the spec.  This block is basically the equivalent of:\n  //    new URL(sourceURL, sourceMapURL).toString()\n  // ... except it avoids using URL, which wasn't available in the\n  // older releases of node still supported by this library.\n  //\n  // The spec says:\n  //   If the sources are not absolute URLs after prepending of the\n  //   “sourceRoot”, the sources are resolved relative to the\n  //   SourceMap (like resolving script src in a html document).\n  if (sourceMapURL) {\n    var parsed = urlParse(sourceMapURL);\n    if (!parsed) {\n      throw new Error(\"sourceMapURL could not be parsed\");\n    }\n    if (parsed.path) {\n      // Strip the last path component, but keep the \"/\".\n      var index = parsed.path.lastIndexOf('/');\n      if (index >= 0) {\n        parsed.path = parsed.path.substring(0, index + 1);\n      }\n    }\n    sourceURL = join(urlGenerate(parsed), sourceURL);\n  }\n\n  return normalize(sourceURL);\n}\nexports.computeSourceURL = computeSourceURL;\n", "/* -*- Mode: js; js-indent-level: 2; -*- */\n/*\n * Copyright 2011 Mozilla Foundation and contributors\n * Licensed under the New BSD license. See LICENSE or:\n * http://opensource.org/licenses/BSD-3-Clause\n */\n\nvar util = require('./util');\nvar has = Object.prototype.hasOwnProperty;\nvar hasNativeMap = typeof Map !== \"undefined\";\n\n/**\n * A data structure which is a combination of an array and a set. Adding a new\n * member is O(1), testing for membership is O(1), and finding the index of an\n * element is O(1). Removing elements from the set is not supported. Only\n * strings are supported for membership.\n */\nfunction ArraySet() {\n  this._array = [];\n  this._set = hasNativeMap ? new Map() : Object.create(null);\n}\n\n/**\n * Static method for creating ArraySet instances from an existing array.\n */\nArraySet.fromArray = function ArraySet_fromArray(aArray, aAllowDuplicates) {\n  var set = new ArraySet();\n  for (var i = 0, len = aArray.length; i < len; i++) {\n    set.add(aArray[i], aAllowDuplicates);\n  }\n  return set;\n};\n\n/**\n * Return how many unique items are in this ArraySet. If duplicates have been\n * added, than those do not count towards the size.\n *\n * @returns Number\n */\nArraySet.prototype.size = function ArraySet_size() {\n  return hasNativeMap ? this._set.size : Object.getOwnPropertyNames(this._set).length;\n};\n\n/**\n * Add the given string to this set.\n *\n * @param String aStr\n */\nArraySet.prototype.add = function ArraySet_add(aStr, aAllowDuplicates) {\n  var sStr = hasNativeMap ? aStr : util.toSetString(aStr);\n  var isDuplicate = hasNativeMap ? this.has(aStr) : has.call(this._set, sStr);\n  var idx = this._array.length;\n  if (!isDuplicate || aAllowDuplicates) {\n    this._array.push(aStr);\n  }\n  if (!isDuplicate) {\n    if (hasNativeMap) {\n      this._set.set(aStr, idx);\n    } else {\n      this._set[sStr] = idx;\n    }\n  }\n};\n\n/**\n * Is the given string a member of this set?\n *\n * @param String aStr\n */\nArraySet.prototype.has = function ArraySet_has(aStr) {\n  if (hasNativeMap) {\n    return this._set.has(aStr);\n  } else {\n    var sStr = util.toSetString(aStr);\n    return has.call(this._set, sStr);\n  }\n};\n\n/**\n * What is the index of the given string in the array?\n *\n * @param String aStr\n */\nArraySet.prototype.indexOf = function ArraySet_indexOf(aStr) {\n  if (hasNativeMap) {\n    var idx = this._set.get(aStr);\n    if (idx >= 0) {\n        return idx;\n    }\n  } else {\n    var sStr = util.toSetString(aStr);\n    if (has.call(this._set, sStr)) {\n      return this._set[sStr];\n    }\n  }\n\n  throw new Error('\"' + aStr + '\" is not in the set.');\n};\n\n/**\n * What is the element at the given index?\n *\n * @param Number aIdx\n */\nArraySet.prototype.at = function ArraySet_at(aIdx) {\n  if (aIdx >= 0 && aIdx < this._array.length) {\n    return this._array[aIdx];\n  }\n  throw new Error('No element indexed by ' + aIdx);\n};\n\n/**\n * Returns the array representation of this set (which has the proper indices\n * indicated by indexOf). Note that this is a copy of the internal array used\n * for storing the members so that no one can mess with internal state.\n */\nArraySet.prototype.toArray = function ArraySet_toArray() {\n  return this._array.slice();\n};\n\nexports.ArraySet = ArraySet;\n", "/* -*- Mode: js; js-indent-level: 2; -*- */\n/*\n * Copyright 2014 Mozilla Foundation and contributors\n * Licensed under the New BSD license. See LICENSE or:\n * http://opensource.org/licenses/BSD-3-Clause\n */\n\nvar util = require('./util');\n\n/**\n * Determine whether mappingB is after mappingA with respect to generated\n * position.\n */\nfunction generatedPositionAfter(mappingA, mappingB) {\n  // Optimized for most common case\n  var lineA = mappingA.generatedLine;\n  var lineB = mappingB.generatedLine;\n  var columnA = mappingA.generatedColumn;\n  var columnB = mappingB.generatedColumn;\n  return lineB > lineA || lineB == lineA && columnB >= columnA ||\n         util.compareByGeneratedPositionsInflated(mappingA, mappingB) <= 0;\n}\n\n/**\n * A data structure to provide a sorted view of accumulated mappings in a\n * performance conscious manner. It trades a neglibable overhead in general\n * case for a large speedup in case of mappings being added in order.\n */\nfunction MappingList() {\n  this._array = [];\n  this._sorted = true;\n  // Serves as infimum\n  this._last = {generatedLine: -1, generatedColumn: 0};\n}\n\n/**\n * Iterate through internal items. This method takes the same arguments that\n * `Array.prototype.forEach` takes.\n *\n * NOTE: The order of the mappings is NOT guaranteed.\n */\nMappingList.prototype.unsortedForEach =\n  function MappingList_forEach(aCallback, aThisArg) {\n    this._array.forEach(aCallback, aThisArg);\n  };\n\n/**\n * Add the given source mapping.\n *\n * @param Object aMapping\n */\nMappingList.prototype.add = function MappingList_add(aMapping) {\n  if (generatedPositionAfter(this._last, aMapping)) {\n    this._last = aMapping;\n    this._array.push(aMapping);\n  } else {\n    this._sorted = false;\n    this._array.push(aMapping);\n  }\n};\n\n/**\n * Returns the flat, sorted array of mappings. The mappings are sorted by\n * generated position.\n *\n * WARNING: This method returns internal data without copying, for\n * performance. The return value must NOT be mutated, and should be treated as\n * an immutable borrow. If you want to take ownership, you must make your own\n * copy.\n */\nMappingList.prototype.toArray = function MappingList_toArray() {\n  if (!this._sorted) {\n    this._array.sort(util.compareByGeneratedPositionsInflated);\n    this._sorted = true;\n  }\n  return this._array;\n};\n\nexports.MappingList = MappingList;\n", "/* -*- Mode: js; js-indent-level: 2; -*- */\n/*\n * Copyright 2011 Mozilla Foundation and contributors\n * Licensed under the New BSD license. See LICENSE or:\n * http://opensource.org/licenses/BSD-3-Clause\n */\n\nvar base64VLQ = require('./base64-vlq');\nvar util = require('./util');\nvar ArraySet = require('./array-set').ArraySet;\nvar MappingList = require('./mapping-list').MappingList;\n\n/**\n * An instance of the SourceMapGenerator represents a source map which is\n * being built incrementally. You may pass an object with the following\n * properties:\n *\n *   - file: The filename of the generated source.\n *   - sourceRoot: A root for all relative URLs in this source map.\n */\nfunction SourceMapGenerator(aArgs) {\n  if (!aArgs) {\n    aArgs = {};\n  }\n  this._file = util.getArg(aArgs, 'file', null);\n  this._sourceRoot = util.getArg(aArgs, 'sourceRoot', null);\n  this._skipValidation = util.getArg(aArgs, 'skipValidation', false);\n  this._sources = new ArraySet();\n  this._names = new ArraySet();\n  this._mappings = new MappingList();\n  this._sourcesContents = null;\n}\n\nSourceMapGenerator.prototype._version = 3;\n\n/**\n * Creates a new SourceMapGenerator based on a SourceMapConsumer\n *\n * @param aSourceMapConsumer The SourceMap.\n */\nSourceMapGenerator.fromSourceMap =\n  function SourceMapGenerator_fromSourceMap(aSourceMapConsumer) {\n    var sourceRoot = aSourceMapConsumer.sourceRoot;\n    var generator = new SourceMapGenerator({\n      file: aSourceMapConsumer.file,\n      sourceRoot: sourceRoot\n    });\n    aSourceMapConsumer.eachMapping(function (mapping) {\n      var newMapping = {\n        generated: {\n          line: mapping.generatedLine,\n          column: mapping.generatedColumn\n        }\n      };\n\n      if (mapping.source != null) {\n        newMapping.source = mapping.source;\n        if (sourceRoot != null) {\n          newMapping.source = util.relative(sourceRoot, newMapping.source);\n        }\n\n        newMapping.original = {\n          line: mapping.originalLine,\n          column: mapping.originalColumn\n        };\n\n        if (mapping.name != null) {\n          newMapping.name = mapping.name;\n        }\n      }\n\n      generator.addMapping(newMapping);\n    });\n    aSourceMapConsumer.sources.forEach(function (sourceFile) {\n      var sourceRelative = sourceFile;\n      if (sourceRoot !== null) {\n        sourceRelative = util.relative(sourceRoot, sourceFile);\n      }\n\n      if (!generator._sources.has(sourceRelative)) {\n        generator._sources.add(sourceRelative);\n      }\n\n      var content = aSourceMapConsumer.sourceContentFor(sourceFile);\n      if (content != null) {\n        generator.setSourceContent(sourceFile, content);\n      }\n    });\n    return generator;\n  };\n\n/**\n * Add a single mapping from original source line and column to the generated\n * source's line and column for this source map being created. The mapping\n * object should have the following properties:\n *\n *   - generated: An object with the generated line and column positions.\n *   - original: An object with the original line and column positions.\n *   - source: The original source file (relative to the sourceRoot).\n *   - name: An optional original token name for this mapping.\n */\nSourceMapGenerator.prototype.addMapping =\n  function SourceMapGenerator_addMapping(aArgs) {\n    var generated = util.getArg(aArgs, 'generated');\n    var original = util.getArg(aArgs, 'original', null);\n    var source = util.getArg(aArgs, 'source', null);\n    var name = util.getArg(aArgs, 'name', null);\n\n    if (!this._skipValidation) {\n      this._validateMapping(generated, original, source, name);\n    }\n\n    if (source != null) {\n      source = String(source);\n      if (!this._sources.has(source)) {\n        this._sources.add(source);\n      }\n    }\n\n    if (name != null) {\n      name = String(name);\n      if (!this._names.has(name)) {\n        this._names.add(name);\n      }\n    }\n\n    this._mappings.add({\n      generatedLine: generated.line,\n      generatedColumn: generated.column,\n      originalLine: original != null && original.line,\n      originalColumn: original != null && original.column,\n      source: source,\n      name: name\n    });\n  };\n\n/**\n * Set the source content for a source file.\n */\nSourceMapGenerator.prototype.setSourceContent =\n  function SourceMapGenerator_setSourceContent(aSourceFile, aSourceContent) {\n    var source = aSourceFile;\n    if (this._sourceRoot != null) {\n      source = util.relative(this._sourceRoot, source);\n    }\n\n    if (aSourceContent != null) {\n      // Add the source content to the _sourcesContents map.\n      // Create a new _sourcesContents map if the property is null.\n      if (!this._sourcesContents) {\n        this._sourcesContents = Object.create(null);\n      }\n      this._sourcesContents[util.toSetString(source)] = aSourceContent;\n    } else if (this._sourcesContents) {\n      // Remove the source file from the _sourcesContents map.\n      // If the _sourcesContents map is empty, set the property to null.\n      delete this._sourcesContents[util.toSetString(source)];\n      if (Object.keys(this._sourcesContents).length === 0) {\n        this._sourcesContents = null;\n      }\n    }\n  };\n\n/**\n * Applies the mappings of a sub-source-map for a specific source file to the\n * source map being generated. Each mapping to the supplied source file is\n * rewritten using the supplied source map. Note: The resolution for the\n * resulting mappings is the minimium of this map and the supplied map.\n *\n * @param aSourceMapConsumer The source map to be applied.\n * @param aSourceFile Optional. The filename of the source file.\n *        If omitted, SourceMapConsumer's file property will be used.\n * @param aSourceMapPath Optional. The dirname of the path to the source map\n *        to be applied. If relative, it is relative to the SourceMapConsumer.\n *        This parameter is needed when the two source maps aren't in the same\n *        directory, and the source map to be applied contains relative source\n *        paths. If so, those relative source paths need to be rewritten\n *        relative to the SourceMapGenerator.\n */\nSourceMapGenerator.prototype.applySourceMap =\n  function SourceMapGenerator_applySourceMap(aSourceMapConsumer, aSourceFile, aSourceMapPath) {\n    var sourceFile = aSourceFile;\n    // If aSourceFile is omitted, we will use the file property of the SourceMap\n    if (aSourceFile == null) {\n      if (aSourceMapConsumer.file == null) {\n        throw new Error(\n          'SourceMapGenerator.prototype.applySourceMap requires either an explicit source file, ' +\n          'or the source map\\'s \"file\" property. Both were omitted.'\n        );\n      }\n      sourceFile = aSourceMapConsumer.file;\n    }\n    var sourceRoot = this._sourceRoot;\n    // Make \"sourceFile\" relative if an absolute Url is passed.\n    if (sourceRoot != null) {\n      sourceFile = util.relative(sourceRoot, sourceFile);\n    }\n    // Applying the SourceMap can add and remove items from the sources and\n    // the names array.\n    var newSources = new ArraySet();\n    var newNames = new ArraySet();\n\n    // Find mappings for the \"sourceFile\"\n    this._mappings.unsortedForEach(function (mapping) {\n      if (mapping.source === sourceFile && mapping.originalLine != null) {\n        // Check if it can be mapped by the source map, then update the mapping.\n        var original = aSourceMapConsumer.originalPositionFor({\n          line: mapping.originalLine,\n          column: mapping.originalColumn\n        });\n        if (original.source != null) {\n          // Copy mapping\n          mapping.source = original.source;\n          if (aSourceMapPath != null) {\n            mapping.source = util.join(aSourceMapPath, mapping.source)\n          }\n          if (sourceRoot != null) {\n            mapping.source = util.relative(sourceRoot, mapping.source);\n          }\n          mapping.originalLine = original.line;\n          mapping.originalColumn = original.column;\n          if (original.name != null) {\n            mapping.name = original.name;\n          }\n        }\n      }\n\n      var source = mapping.source;\n      if (source != null && !newSources.has(source)) {\n        newSources.add(source);\n      }\n\n      var name = mapping.name;\n      if (name != null && !newNames.has(name)) {\n        newNames.add(name);\n      }\n\n    }, this);\n    this._sources = newSources;\n    this._names = newNames;\n\n    // Copy sourcesContents of applied map.\n    aSourceMapConsumer.sources.forEach(function (sourceFile) {\n      var content = aSourceMapConsumer.sourceContentFor(sourceFile);\n      if (content != null) {\n        if (aSourceMapPath != null) {\n          sourceFile = util.join(aSourceMapPath, sourceFile);\n        }\n        if (sourceRoot != null) {\n          sourceFile = util.relative(sourceRoot, sourceFile);\n        }\n        this.setSourceContent(sourceFile, content);\n      }\n    }, this);\n  };\n\n/**\n * A mapping can have one of the three levels of data:\n *\n *   1. Just the generated position.\n *   2. The Generated position, original position, and original source.\n *   3. Generated and original position, original source, as well as a name\n *      token.\n *\n * To maintain consistency, we validate that any new mapping being added falls\n * in to one of these categories.\n */\nSourceMapGenerator.prototype._validateMapping =\n  function SourceMapGenerator_validateMapping(aGenerated, aOriginal, aSource,\n                                              aName) {\n    // When aOriginal is truthy but has empty values for .line and .column,\n    // it is most likely a programmer error. In this case we throw a very\n    // specific error message to try to guide them the right way.\n    // For example: https://github.com/Polymer/polymer-bundler/pull/519\n    if (aOriginal && typeof aOriginal.line !== 'number' && typeof aOriginal.column !== 'number') {\n        throw new Error(\n            'original.line and original.column are not numbers -- you probably meant to omit ' +\n            'the original mapping entirely and only map the generated position. If so, pass ' +\n            'null for the original mapping instead of an object with empty or null values.'\n        );\n    }\n\n    if (aGenerated && 'line' in aGenerated && 'column' in aGenerated\n        && aGenerated.line > 0 && aGenerated.column >= 0\n        && !aOriginal && !aSource && !aName) {\n      // Case 1.\n      return;\n    }\n    else if (aGenerated && 'line' in aGenerated && 'column' in aGenerated\n             && aOriginal && 'line' in aOriginal && 'column' in aOriginal\n             && aGenerated.line > 0 && aGenerated.column >= 0\n             && aOriginal.line > 0 && aOriginal.column >= 0\n             && aSource) {\n      // Cases 2 and 3.\n      return;\n    }\n    else {\n      throw new Error('Invalid mapping: ' + JSON.stringify({\n        generated: aGenerated,\n        source: aSource,\n        original: aOriginal,\n        name: aName\n      }));\n    }\n  };\n\n/**\n * Serialize the accumulated mappings in to the stream of base 64 VLQs\n * specified by the source map format.\n */\nSourceMapGenerator.prototype._serializeMappings =\n  function SourceMapGenerator_serializeMappings() {\n    var previousGeneratedColumn = 0;\n    var previousGeneratedLine = 1;\n    var previousOriginalColumn = 0;\n    var previousOriginalLine = 0;\n    var previousName = 0;\n    var previousSource = 0;\n    var result = '';\n    var next;\n    var mapping;\n    var nameIdx;\n    var sourceIdx;\n\n    var mappings = this._mappings.toArray();\n    for (var i = 0, len = mappings.length; i < len; i++) {\n      mapping = mappings[i];\n      next = ''\n\n      if (mapping.generatedLine !== previousGeneratedLine) {\n        previousGeneratedColumn = 0;\n        while (mapping.generatedLine !== previousGeneratedLine) {\n          next += ';';\n          previousGeneratedLine++;\n        }\n      }\n      else {\n        if (i > 0) {\n          if (!util.compareByGeneratedPositionsInflated(mapping, mappings[i - 1])) {\n            continue;\n          }\n          next += ',';\n        }\n      }\n\n      next += base64VLQ.encode(mapping.generatedColumn\n                                 - previousGeneratedColumn);\n      previousGeneratedColumn = mapping.generatedColumn;\n\n      if (mapping.source != null) {\n        sourceIdx = this._sources.indexOf(mapping.source);\n        next += base64VLQ.encode(sourceIdx - previousSource);\n        previousSource = sourceIdx;\n\n        // lines are stored 0-based in SourceMap spec version 3\n        next += base64VLQ.encode(mapping.originalLine - 1\n                                   - previousOriginalLine);\n        previousOriginalLine = mapping.originalLine - 1;\n\n        next += base64VLQ.encode(mapping.originalColumn\n                                   - previousOriginalColumn);\n        previousOriginalColumn = mapping.originalColumn;\n\n        if (mapping.name != null) {\n          nameIdx = this._names.indexOf(mapping.name);\n          next += base64VLQ.encode(nameIdx - previousName);\n          previousName = nameIdx;\n        }\n      }\n\n      result += next;\n    }\n\n    return result;\n  };\n\nSourceMapGenerator.prototype._generateSourcesContent =\n  function SourceMapGenerator_generateSourcesContent(aSources, aSourceRoot) {\n    return aSources.map(function (source) {\n      if (!this._sourcesContents) {\n        return null;\n      }\n      if (aSourceRoot != null) {\n        source = util.relative(aSourceRoot, source);\n      }\n      var key = util.toSetString(source);\n      return Object.prototype.hasOwnProperty.call(this._sourcesContents, key)\n        ? this._sourcesContents[key]\n        : null;\n    }, this);\n  };\n\n/**\n * Externalize the source map.\n */\nSourceMapGenerator.prototype.toJSON =\n  function SourceMapGenerator_toJSON() {\n    var map = {\n      version: this._version,\n      sources: this._sources.toArray(),\n      names: this._names.toArray(),\n      mappings: this._serializeMappings()\n    };\n    if (this._file != null) {\n      map.file = this._file;\n    }\n    if (this._sourceRoot != null) {\n      map.sourceRoot = this._sourceRoot;\n    }\n    if (this._sourcesContents) {\n      map.sourcesContent = this._generateSourcesContent(map.sources, map.sourceRoot);\n    }\n\n    return map;\n  };\n\n/**\n * Render the source map being generated to a string.\n */\nSourceMapGenerator.prototype.toString =\n  function SourceMapGenerator_toString() {\n    return JSON.stringify(this.toJSON());\n  };\n\nexports.SourceMapGenerator = SourceMapGenerator;\n", "/* -*- Mode: js; js-indent-level: 2; -*- */\n/*\n * Copyright 2011 Mozilla Foundation and contributors\n * Licensed under the New BSD license. See LICENSE or:\n * http://opensource.org/licenses/BSD-3-Clause\n */\n\nexports.GREATEST_LOWER_BOUND = 1;\nexports.LEAST_UPPER_BOUND = 2;\n\n/**\n * Recursive implementation of binary search.\n *\n * @param aLow Indices here and lower do not contain the needle.\n * @param aHigh Indices here and higher do not contain the needle.\n * @param aNeedle The element being searched for.\n * @param aHaystack The non-empty array being searched.\n * @param aCompare Function which takes two elements and returns -1, 0, or 1.\n * @param aBias Either 'binarySearch.GREATEST_LOWER_BOUND' or\n *     'binarySearch.LEAST_UPPER_BOUND'. Specifies whether to return the\n *     closest element that is smaller than or greater than the one we are\n *     searching for, respectively, if the exact element cannot be found.\n */\nfunction recursiveSearch(aLow, aHigh, a<PERSON>eed<PERSON>, aHaystack, aCompare, a<PERSON><PERSON>) {\n  // This function terminates when one of the following is true:\n  //\n  //   1. We find the exact element we are looking for.\n  //\n  //   2. We did not find the exact element, but we can return the index of\n  //      the next-closest element.\n  //\n  //   3. We did not find the exact element, and there is no next-closest\n  //      element than the one we are searching for, so we return -1.\n  var mid = Math.floor((aHigh - aLow) / 2) + aLow;\n  var cmp = aCompare(aNeedle, aHaystack[mid], true);\n  if (cmp === 0) {\n    // Found the element we are looking for.\n    return mid;\n  }\n  else if (cmp > 0) {\n    // Our needle is greater than aHaystack[mid].\n    if (aHigh - mid > 1) {\n      // The element is in the upper half.\n      return recursiveSearch(mid, aHigh, aNeedle, aHaystack, aCompare, aBias);\n    }\n\n    // The exact needle element was not found in this haystack. Determine if\n    // we are in termination case (3) or (2) and return the appropriate thing.\n    if (aBias == exports.LEAST_UPPER_BOUND) {\n      return aHigh < aHaystack.length ? aHigh : -1;\n    } else {\n      return mid;\n    }\n  }\n  else {\n    // Our needle is less than aHaystack[mid].\n    if (mid - aLow > 1) {\n      // The element is in the lower half.\n      return recursiveSearch(aLow, mid, aNeedle, aHaystack, aCompare, aBias);\n    }\n\n    // we are in termination case (3) or (2) and return the appropriate thing.\n    if (aBias == exports.LEAST_UPPER_BOUND) {\n      return mid;\n    } else {\n      return aLow < 0 ? -1 : aLow;\n    }\n  }\n}\n\n/**\n * This is an implementation of binary search which will always try and return\n * the index of the closest element if there is no exact hit. This is because\n * mappings between original and generated line/col pairs are single points,\n * and there is an implicit region between each of them, so a miss just means\n * that you aren't on the very start of a region.\n *\n * @param aNeedle The element you are looking for.\n * @param aHaystack The array that is being searched.\n * @param aCompare A function which takes the needle and an element in the\n *     array and returns -1, 0, or 1 depending on whether the needle is less\n *     than, equal to, or greater than the element, respectively.\n * @param aBias Either 'binarySearch.GREATEST_LOWER_BOUND' or\n *     'binarySearch.LEAST_UPPER_BOUND'. Specifies whether to return the\n *     closest element that is smaller than or greater than the one we are\n *     searching for, respectively, if the exact element cannot be found.\n *     Defaults to 'binarySearch.GREATEST_LOWER_BOUND'.\n */\nexports.search = function search(aNeedle, aHaystack, aCompare, aBias) {\n  if (aHaystack.length === 0) {\n    return -1;\n  }\n\n  var index = recursiveSearch(-1, aHaystack.length, aNeedle, aHaystack,\n                              aCompare, aBias || exports.GREATEST_LOWER_BOUND);\n  if (index < 0) {\n    return -1;\n  }\n\n  // We have found either the exact element, or the next-closest element than\n  // the one we are searching for. However, there may be more than one such\n  // element. Make sure we always return the smallest of these.\n  while (index - 1 >= 0) {\n    if (aCompare(aHaystack[index], aHaystack[index - 1], true) !== 0) {\n      break;\n    }\n    --index;\n  }\n\n  return index;\n};\n", "/* -*- Mode: js; js-indent-level: 2; -*- */\n/*\n * Copyright 2011 Mozilla Foundation and contributors\n * Licensed under the New BSD license. See LICENSE or:\n * http://opensource.org/licenses/BSD-3-Clause\n */\n\n// It turns out that some (most?) JavaScript engines don't self-host\n// `Array.prototype.sort`. This makes sense because C++ will likely remain\n// faster than JS when doing raw CPU-intensive sorting. However, when using a\n// custom comparator function, calling back and forth between the VM's C++ and\n// JIT'd JS is rather slow *and* loses JIT type information, resulting in\n// worse generated code for the comparator function than would be optimal. In\n// fact, when sorting with a comparator, these costs outweigh the benefits of\n// sorting in C++. By using our own JS-implemented Quick Sort (below), we get\n// a ~3500ms mean speed-up in `bench/bench.html`.\n\n/**\n * Swap the elements indexed by `x` and `y` in the array `ary`.\n *\n * @param {Array} ary\n *        The array.\n * @param {Number} x\n *        The index of the first item.\n * @param {Number} y\n *        The index of the second item.\n */\nfunction swap(ary, x, y) {\n  var temp = ary[x];\n  ary[x] = ary[y];\n  ary[y] = temp;\n}\n\n/**\n * Returns a random integer within the range `low .. high` inclusive.\n *\n * @param {Number} low\n *        The lower bound on the range.\n * @param {Number} high\n *        The upper bound on the range.\n */\nfunction randomIntInRange(low, high) {\n  return Math.round(low + (Math.random() * (high - low)));\n}\n\n/**\n * The Quick Sort algorithm.\n *\n * @param {Array} ary\n *        An array to sort.\n * @param {function} comparator\n *        Function to use to compare two items.\n * @param {Number} p\n *        Start index of the array\n * @param {Number} r\n *        End index of the array\n */\nfunction doQuickSort(ary, comparator, p, r) {\n  // If our lower bound is less than our upper bound, we (1) partition the\n  // array into two pieces and (2) recurse on each half. If it is not, this is\n  // the empty array and our base case.\n\n  if (p < r) {\n    // (1) Partitioning.\n    //\n    // The partitioning chooses a pivot between `p` and `r` and moves all\n    // elements that are less than or equal to the pivot to the before it, and\n    // all the elements that are greater than it after it. The effect is that\n    // once partition is done, the pivot is in the exact place it will be when\n    // the array is put in sorted order, and it will not need to be moved\n    // again. This runs in O(n) time.\n\n    // Always choose a random pivot so that an input array which is reverse\n    // sorted does not cause O(n^2) running time.\n    var pivotIndex = randomIntInRange(p, r);\n    var i = p - 1;\n\n    swap(ary, pivotIndex, r);\n    var pivot = ary[r];\n\n    // Immediately after `j` is incremented in this loop, the following hold\n    // true:\n    //\n    //   * Every element in `ary[p .. i]` is less than or equal to the pivot.\n    //\n    //   * Every element in `ary[i+1 .. j-1]` is greater than the pivot.\n    for (var j = p; j < r; j++) {\n      if (comparator(ary[j], pivot) <= 0) {\n        i += 1;\n        swap(ary, i, j);\n      }\n    }\n\n    swap(ary, i + 1, j);\n    var q = i + 1;\n\n    // (2) Recurse on each half.\n\n    doQuickSort(ary, comparator, p, q - 1);\n    doQuickSort(ary, comparator, q + 1, r);\n  }\n}\n\n/**\n * Sort the given array in-place with the given comparator function.\n *\n * @param {Array} ary\n *        An array to sort.\n * @param {function} comparator\n *        Function to use to compare two items.\n */\nexports.quickSort = function (ary, comparator) {\n  doQuickSort(ary, comparator, 0, ary.length - 1);\n};\n", "/* -*- Mode: js; js-indent-level: 2; -*- */\n/*\n * Copyright 2011 Mozilla Foundation and contributors\n * Licensed under the New BSD license. See LICENSE or:\n * http://opensource.org/licenses/BSD-3-Clause\n */\n\nvar util = require('./util');\nvar binarySearch = require('./binary-search');\nvar ArraySet = require('./array-set').ArraySet;\nvar base64VLQ = require('./base64-vlq');\nvar quickSort = require('./quick-sort').quickSort;\n\nfunction SourceMapConsumer(aSourceMap, aSourceMapURL) {\n  var sourceMap = aSourceMap;\n  if (typeof aSourceMap === 'string') {\n    sourceMap = util.parseSourceMapInput(aSourceMap);\n  }\n\n  return sourceMap.sections != null\n    ? new IndexedSourceMapConsumer(sourceMap, aSourceMapURL)\n    : new BasicSourceMapConsumer(sourceMap, aSourceMapURL);\n}\n\nSourceMapConsumer.fromSourceMap = function(aSourceMap, aSourceMapURL) {\n  return BasicSourceMapConsumer.fromSourceMap(aSourceMap, aSourceMapURL);\n}\n\n/**\n * The version of the source mapping spec that we are consuming.\n */\nSourceMapConsumer.prototype._version = 3;\n\n// `__generatedMappings` and `__originalMappings` are arrays that hold the\n// parsed mapping coordinates from the source map's \"mappings\" attribute. They\n// are lazily instantiated, accessed via the `_generatedMappings` and\n// `_originalMappings` getters respectively, and we only parse the mappings\n// and create these arrays once queried for a source location. We jump through\n// these hoops because there can be many thousands of mappings, and parsing\n// them is expensive, so we only want to do it if we must.\n//\n// Each object in the arrays is of the form:\n//\n//     {\n//       generatedLine: The line number in the generated code,\n//       generatedColumn: The column number in the generated code,\n//       source: The path to the original source file that generated this\n//               chunk of code,\n//       originalLine: The line number in the original source that\n//                     corresponds to this chunk of generated code,\n//       originalColumn: The column number in the original source that\n//                       corresponds to this chunk of generated code,\n//       name: The name of the original symbol which generated this chunk of\n//             code.\n//     }\n//\n// All properties except for `generatedLine` and `generatedColumn` can be\n// `null`.\n//\n// `_generatedMappings` is ordered by the generated positions.\n//\n// `_originalMappings` is ordered by the original positions.\n\nSourceMapConsumer.prototype.__generatedMappings = null;\nObject.defineProperty(SourceMapConsumer.prototype, '_generatedMappings', {\n  configurable: true,\n  enumerable: true,\n  get: function () {\n    if (!this.__generatedMappings) {\n      this._parseMappings(this._mappings, this.sourceRoot);\n    }\n\n    return this.__generatedMappings;\n  }\n});\n\nSourceMapConsumer.prototype.__originalMappings = null;\nObject.defineProperty(SourceMapConsumer.prototype, '_originalMappings', {\n  configurable: true,\n  enumerable: true,\n  get: function () {\n    if (!this.__originalMappings) {\n      this._parseMappings(this._mappings, this.sourceRoot);\n    }\n\n    return this.__originalMappings;\n  }\n});\n\nSourceMapConsumer.prototype._charIsMappingSeparator =\n  function SourceMapConsumer_charIsMappingSeparator(aStr, index) {\n    var c = aStr.charAt(index);\n    return c === \";\" || c === \",\";\n  };\n\n/**\n * Parse the mappings in a string in to a data structure which we can easily\n * query (the ordered arrays in the `this.__generatedMappings` and\n * `this.__originalMappings` properties).\n */\nSourceMapConsumer.prototype._parseMappings =\n  function SourceMapConsumer_parseMappings(aStr, aSourceRoot) {\n    throw new Error(\"Subclasses must implement _parseMappings\");\n  };\n\nSourceMapConsumer.GENERATED_ORDER = 1;\nSourceMapConsumer.ORIGINAL_ORDER = 2;\n\nSourceMapConsumer.GREATEST_LOWER_BOUND = 1;\nSourceMapConsumer.LEAST_UPPER_BOUND = 2;\n\n/**\n * Iterate over each mapping between an original source/line/column and a\n * generated line/column in this source map.\n *\n * @param Function aCallback\n *        The function that is called with each mapping.\n * @param Object aContext\n *        Optional. If specified, this object will be the value of `this` every\n *        time that `aCallback` is called.\n * @param aOrder\n *        Either `SourceMapConsumer.GENERATED_ORDER` or\n *        `SourceMapConsumer.ORIGINAL_ORDER`. Specifies whether you want to\n *        iterate over the mappings sorted by the generated file's line/column\n *        order or the original's source/line/column order, respectively. Defaults to\n *        `SourceMapConsumer.GENERATED_ORDER`.\n */\nSourceMapConsumer.prototype.eachMapping =\n  function SourceMapConsumer_eachMapping(aCallback, aContext, aOrder) {\n    var context = aContext || null;\n    var order = aOrder || SourceMapConsumer.GENERATED_ORDER;\n\n    var mappings;\n    switch (order) {\n    case SourceMapConsumer.GENERATED_ORDER:\n      mappings = this._generatedMappings;\n      break;\n    case SourceMapConsumer.ORIGINAL_ORDER:\n      mappings = this._originalMappings;\n      break;\n    default:\n      throw new Error(\"Unknown order of iteration.\");\n    }\n\n    var sourceRoot = this.sourceRoot;\n    mappings.map(function (mapping) {\n      var source = mapping.source === null ? null : this._sources.at(mapping.source);\n      source = util.computeSourceURL(sourceRoot, source, this._sourceMapURL);\n      return {\n        source: source,\n        generatedLine: mapping.generatedLine,\n        generatedColumn: mapping.generatedColumn,\n        originalLine: mapping.originalLine,\n        originalColumn: mapping.originalColumn,\n        name: mapping.name === null ? null : this._names.at(mapping.name)\n      };\n    }, this).forEach(aCallback, context);\n  };\n\n/**\n * Returns all generated line and column information for the original source,\n * line, and column provided. If no column is provided, returns all mappings\n * corresponding to a either the line we are searching for or the next\n * closest line that has any mappings. Otherwise, returns all mappings\n * corresponding to the given line and either the column we are searching for\n * or the next closest column that has any offsets.\n *\n * The only argument is an object with the following properties:\n *\n *   - source: The filename of the original source.\n *   - line: The line number in the original source.  The line number is 1-based.\n *   - column: Optional. the column number in the original source.\n *    The column number is 0-based.\n *\n * and an array of objects is returned, each with the following properties:\n *\n *   - line: The line number in the generated source, or null.  The\n *    line number is 1-based.\n *   - column: The column number in the generated source, or null.\n *    The column number is 0-based.\n */\nSourceMapConsumer.prototype.allGeneratedPositionsFor =\n  function SourceMapConsumer_allGeneratedPositionsFor(aArgs) {\n    var line = util.getArg(aArgs, 'line');\n\n    // When there is no exact match, BasicSourceMapConsumer.prototype._findMapping\n    // returns the index of the closest mapping less than the needle. By\n    // setting needle.originalColumn to 0, we thus find the last mapping for\n    // the given line, provided such a mapping exists.\n    var needle = {\n      source: util.getArg(aArgs, 'source'),\n      originalLine: line,\n      originalColumn: util.getArg(aArgs, 'column', 0)\n    };\n\n    needle.source = this._findSourceIndex(needle.source);\n    if (needle.source < 0) {\n      return [];\n    }\n\n    var mappings = [];\n\n    var index = this._findMapping(needle,\n                                  this._originalMappings,\n                                  \"originalLine\",\n                                  \"originalColumn\",\n                                  util.compareByOriginalPositions,\n                                  binarySearch.LEAST_UPPER_BOUND);\n    if (index >= 0) {\n      var mapping = this._originalMappings[index];\n\n      if (aArgs.column === undefined) {\n        var originalLine = mapping.originalLine;\n\n        // Iterate until either we run out of mappings, or we run into\n        // a mapping for a different line than the one we found. Since\n        // mappings are sorted, this is guaranteed to find all mappings for\n        // the line we found.\n        while (mapping && mapping.originalLine === originalLine) {\n          mappings.push({\n            line: util.getArg(mapping, 'generatedLine', null),\n            column: util.getArg(mapping, 'generatedColumn', null),\n            lastColumn: util.getArg(mapping, 'lastGeneratedColumn', null)\n          });\n\n          mapping = this._originalMappings[++index];\n        }\n      } else {\n        var originalColumn = mapping.originalColumn;\n\n        // Iterate until either we run out of mappings, or we run into\n        // a mapping for a different line than the one we were searching for.\n        // Since mappings are sorted, this is guaranteed to find all mappings for\n        // the line we are searching for.\n        while (mapping &&\n               mapping.originalLine === line &&\n               mapping.originalColumn == originalColumn) {\n          mappings.push({\n            line: util.getArg(mapping, 'generatedLine', null),\n            column: util.getArg(mapping, 'generatedColumn', null),\n            lastColumn: util.getArg(mapping, 'lastGeneratedColumn', null)\n          });\n\n          mapping = this._originalMappings[++index];\n        }\n      }\n    }\n\n    return mappings;\n  };\n\nexports.SourceMapConsumer = SourceMapConsumer;\n\n/**\n * A BasicSourceMapConsumer instance represents a parsed source map which we can\n * query for information about the original file positions by giving it a file\n * position in the generated source.\n *\n * The first parameter is the raw source map (either as a JSON string, or\n * already parsed to an object). According to the spec, source maps have the\n * following attributes:\n *\n *   - version: Which version of the source map spec this map is following.\n *   - sources: An array of URLs to the original source files.\n *   - names: An array of identifiers which can be referrenced by individual mappings.\n *   - sourceRoot: Optional. The URL root from which all sources are relative.\n *   - sourcesContent: Optional. An array of contents of the original source files.\n *   - mappings: A string of base64 VLQs which contain the actual mappings.\n *   - file: Optional. The generated file this source map is associated with.\n *\n * Here is an example source map, taken from the source map spec[0]:\n *\n *     {\n *       version : 3,\n *       file: \"out.js\",\n *       sourceRoot : \"\",\n *       sources: [\"foo.js\", \"bar.js\"],\n *       names: [\"src\", \"maps\", \"are\", \"fun\"],\n *       mappings: \"AA,AB;;ABCDE;\"\n *     }\n *\n * The second parameter, if given, is a string whose value is the URL\n * at which the source map was found.  This URL is used to compute the\n * sources array.\n *\n * [0]: https://docs.google.com/document/d/1U1RGAehQwRypUTovF1KRlpiOFze0b-_2gc6fAH0KY0k/edit?pli=1#\n */\nfunction BasicSourceMapConsumer(aSourceMap, aSourceMapURL) {\n  var sourceMap = aSourceMap;\n  if (typeof aSourceMap === 'string') {\n    sourceMap = util.parseSourceMapInput(aSourceMap);\n  }\n\n  var version = util.getArg(sourceMap, 'version');\n  var sources = util.getArg(sourceMap, 'sources');\n  // Sass 3.3 leaves out the 'names' array, so we deviate from the spec (which\n  // requires the array) to play nice here.\n  var names = util.getArg(sourceMap, 'names', []);\n  var sourceRoot = util.getArg(sourceMap, 'sourceRoot', null);\n  var sourcesContent = util.getArg(sourceMap, 'sourcesContent', null);\n  var mappings = util.getArg(sourceMap, 'mappings');\n  var file = util.getArg(sourceMap, 'file', null);\n\n  // Once again, Sass deviates from the spec and supplies the version as a\n  // string rather than a number, so we use loose equality checking here.\n  if (version != this._version) {\n    throw new Error('Unsupported version: ' + version);\n  }\n\n  if (sourceRoot) {\n    sourceRoot = util.normalize(sourceRoot);\n  }\n\n  sources = sources\n    .map(String)\n    // Some source maps produce relative source paths like \"./foo.js\" instead of\n    // \"foo.js\".  Normalize these first so that future comparisons will succeed.\n    // See bugzil.la/1090768.\n    .map(util.normalize)\n    // Always ensure that absolute sources are internally stored relative to\n    // the source root, if the source root is absolute. Not doing this would\n    // be particularly problematic when the source root is a prefix of the\n    // source (valid, but why??). See github issue #199 and bugzil.la/1188982.\n    .map(function (source) {\n      return sourceRoot && util.isAbsolute(sourceRoot) && util.isAbsolute(source)\n        ? util.relative(sourceRoot, source)\n        : source;\n    });\n\n  // Pass `true` below to allow duplicate names and sources. While source maps\n  // are intended to be compressed and deduplicated, the TypeScript compiler\n  // sometimes generates source maps with duplicates in them. See Github issue\n  // #72 and bugzil.la/889492.\n  this._names = ArraySet.fromArray(names.map(String), true);\n  this._sources = ArraySet.fromArray(sources, true);\n\n  this._absoluteSources = this._sources.toArray().map(function (s) {\n    return util.computeSourceURL(sourceRoot, s, aSourceMapURL);\n  });\n\n  this.sourceRoot = sourceRoot;\n  this.sourcesContent = sourcesContent;\n  this._mappings = mappings;\n  this._sourceMapURL = aSourceMapURL;\n  this.file = file;\n}\n\nBasicSourceMapConsumer.prototype = Object.create(SourceMapConsumer.prototype);\nBasicSourceMapConsumer.prototype.consumer = SourceMapConsumer;\n\n/**\n * Utility function to find the index of a source.  Returns -1 if not\n * found.\n */\nBasicSourceMapConsumer.prototype._findSourceIndex = function(aSource) {\n  var relativeSource = aSource;\n  if (this.sourceRoot != null) {\n    relativeSource = util.relative(this.sourceRoot, relativeSource);\n  }\n\n  if (this._sources.has(relativeSource)) {\n    return this._sources.indexOf(relativeSource);\n  }\n\n  // Maybe aSource is an absolute URL as returned by |sources|.  In\n  // this case we can't simply undo the transform.\n  var i;\n  for (i = 0; i < this._absoluteSources.length; ++i) {\n    if (this._absoluteSources[i] == aSource) {\n      return i;\n    }\n  }\n\n  return -1;\n};\n\n/**\n * Create a BasicSourceMapConsumer from a SourceMapGenerator.\n *\n * @param SourceMapGenerator aSourceMap\n *        The source map that will be consumed.\n * @param String aSourceMapURL\n *        The URL at which the source map can be found (optional)\n * @returns BasicSourceMapConsumer\n */\nBasicSourceMapConsumer.fromSourceMap =\n  function SourceMapConsumer_fromSourceMap(aSourceMap, aSourceMapURL) {\n    var smc = Object.create(BasicSourceMapConsumer.prototype);\n\n    var names = smc._names = ArraySet.fromArray(aSourceMap._names.toArray(), true);\n    var sources = smc._sources = ArraySet.fromArray(aSourceMap._sources.toArray(), true);\n    smc.sourceRoot = aSourceMap._sourceRoot;\n    smc.sourcesContent = aSourceMap._generateSourcesContent(smc._sources.toArray(),\n                                                            smc.sourceRoot);\n    smc.file = aSourceMap._file;\n    smc._sourceMapURL = aSourceMapURL;\n    smc._absoluteSources = smc._sources.toArray().map(function (s) {\n      return util.computeSourceURL(smc.sourceRoot, s, aSourceMapURL);\n    });\n\n    // Because we are modifying the entries (by converting string sources and\n    // names to indices into the sources and names ArraySets), we have to make\n    // a copy of the entry or else bad things happen. Shared mutable state\n    // strikes again! See github issue #191.\n\n    var generatedMappings = aSourceMap._mappings.toArray().slice();\n    var destGeneratedMappings = smc.__generatedMappings = [];\n    var destOriginalMappings = smc.__originalMappings = [];\n\n    for (var i = 0, length = generatedMappings.length; i < length; i++) {\n      var srcMapping = generatedMappings[i];\n      var destMapping = new Mapping;\n      destMapping.generatedLine = srcMapping.generatedLine;\n      destMapping.generatedColumn = srcMapping.generatedColumn;\n\n      if (srcMapping.source) {\n        destMapping.source = sources.indexOf(srcMapping.source);\n        destMapping.originalLine = srcMapping.originalLine;\n        destMapping.originalColumn = srcMapping.originalColumn;\n\n        if (srcMapping.name) {\n          destMapping.name = names.indexOf(srcMapping.name);\n        }\n\n        destOriginalMappings.push(destMapping);\n      }\n\n      destGeneratedMappings.push(destMapping);\n    }\n\n    quickSort(smc.__originalMappings, util.compareByOriginalPositions);\n\n    return smc;\n  };\n\n/**\n * The version of the source mapping spec that we are consuming.\n */\nBasicSourceMapConsumer.prototype._version = 3;\n\n/**\n * The list of original sources.\n */\nObject.defineProperty(BasicSourceMapConsumer.prototype, 'sources', {\n  get: function () {\n    return this._absoluteSources.slice();\n  }\n});\n\n/**\n * Provide the JIT with a nice shape / hidden class.\n */\nfunction Mapping() {\n  this.generatedLine = 0;\n  this.generatedColumn = 0;\n  this.source = null;\n  this.originalLine = null;\n  this.originalColumn = null;\n  this.name = null;\n}\n\n/**\n * Parse the mappings in a string in to a data structure which we can easily\n * query (the ordered arrays in the `this.__generatedMappings` and\n * `this.__originalMappings` properties).\n */\nBasicSourceMapConsumer.prototype._parseMappings =\n  function SourceMapConsumer_parseMappings(aStr, aSourceRoot) {\n    var generatedLine = 1;\n    var previousGeneratedColumn = 0;\n    var previousOriginalLine = 0;\n    var previousOriginalColumn = 0;\n    var previousSource = 0;\n    var previousName = 0;\n    var length = aStr.length;\n    var index = 0;\n    var cachedSegments = {};\n    var temp = {};\n    var originalMappings = [];\n    var generatedMappings = [];\n    var mapping, str, segment, end, value;\n\n    while (index < length) {\n      if (aStr.charAt(index) === ';') {\n        generatedLine++;\n        index++;\n        previousGeneratedColumn = 0;\n      }\n      else if (aStr.charAt(index) === ',') {\n        index++;\n      }\n      else {\n        mapping = new Mapping();\n        mapping.generatedLine = generatedLine;\n\n        // Because each offset is encoded relative to the previous one,\n        // many segments often have the same encoding. We can exploit this\n        // fact by caching the parsed variable length fields of each segment,\n        // allowing us to avoid a second parse if we encounter the same\n        // segment again.\n        for (end = index; end < length; end++) {\n          if (this._charIsMappingSeparator(aStr, end)) {\n            break;\n          }\n        }\n        str = aStr.slice(index, end);\n\n        segment = cachedSegments[str];\n        if (segment) {\n          index += str.length;\n        } else {\n          segment = [];\n          while (index < end) {\n            base64VLQ.decode(aStr, index, temp);\n            value = temp.value;\n            index = temp.rest;\n            segment.push(value);\n          }\n\n          if (segment.length === 2) {\n            throw new Error('Found a source, but no line and column');\n          }\n\n          if (segment.length === 3) {\n            throw new Error('Found a source and line, but no column');\n          }\n\n          cachedSegments[str] = segment;\n        }\n\n        // Generated column.\n        mapping.generatedColumn = previousGeneratedColumn + segment[0];\n        previousGeneratedColumn = mapping.generatedColumn;\n\n        if (segment.length > 1) {\n          // Original source.\n          mapping.source = previousSource + segment[1];\n          previousSource += segment[1];\n\n          // Original line.\n          mapping.originalLine = previousOriginalLine + segment[2];\n          previousOriginalLine = mapping.originalLine;\n          // Lines are stored 0-based\n          mapping.originalLine += 1;\n\n          // Original column.\n          mapping.originalColumn = previousOriginalColumn + segment[3];\n          previousOriginalColumn = mapping.originalColumn;\n\n          if (segment.length > 4) {\n            // Original name.\n            mapping.name = previousName + segment[4];\n            previousName += segment[4];\n          }\n        }\n\n        generatedMappings.push(mapping);\n        if (typeof mapping.originalLine === 'number') {\n          originalMappings.push(mapping);\n        }\n      }\n    }\n\n    quickSort(generatedMappings, util.compareByGeneratedPositionsDeflated);\n    this.__generatedMappings = generatedMappings;\n\n    quickSort(originalMappings, util.compareByOriginalPositions);\n    this.__originalMappings = originalMappings;\n  };\n\n/**\n * Find the mapping that best matches the hypothetical \"needle\" mapping that\n * we are searching for in the given \"haystack\" of mappings.\n */\nBasicSourceMapConsumer.prototype._findMapping =\n  function SourceMapConsumer_findMapping(aNeedle, aMappings, aLineName,\n                                         aColumnName, aComparator, aBias) {\n    // To return the position we are searching for, we must first find the\n    // mapping for the given position and then return the opposite position it\n    // points to. Because the mappings are sorted, we can use binary search to\n    // find the best mapping.\n\n    if (aNeedle[aLineName] <= 0) {\n      throw new TypeError('Line must be greater than or equal to 1, got '\n                          + aNeedle[aLineName]);\n    }\n    if (aNeedle[aColumnName] < 0) {\n      throw new TypeError('Column must be greater than or equal to 0, got '\n                          + aNeedle[aColumnName]);\n    }\n\n    return binarySearch.search(aNeedle, aMappings, aComparator, aBias);\n  };\n\n/**\n * Compute the last column for each generated mapping. The last column is\n * inclusive.\n */\nBasicSourceMapConsumer.prototype.computeColumnSpans =\n  function SourceMapConsumer_computeColumnSpans() {\n    for (var index = 0; index < this._generatedMappings.length; ++index) {\n      var mapping = this._generatedMappings[index];\n\n      // Mappings do not contain a field for the last generated columnt. We\n      // can come up with an optimistic estimate, however, by assuming that\n      // mappings are contiguous (i.e. given two consecutive mappings, the\n      // first mapping ends where the second one starts).\n      if (index + 1 < this._generatedMappings.length) {\n        var nextMapping = this._generatedMappings[index + 1];\n\n        if (mapping.generatedLine === nextMapping.generatedLine) {\n          mapping.lastGeneratedColumn = nextMapping.generatedColumn - 1;\n          continue;\n        }\n      }\n\n      // The last mapping for each line spans the entire line.\n      mapping.lastGeneratedColumn = Infinity;\n    }\n  };\n\n/**\n * Returns the original source, line, and column information for the generated\n * source's line and column positions provided. The only argument is an object\n * with the following properties:\n *\n *   - line: The line number in the generated source.  The line number\n *     is 1-based.\n *   - column: The column number in the generated source.  The column\n *     number is 0-based.\n *   - bias: Either 'SourceMapConsumer.GREATEST_LOWER_BOUND' or\n *     'SourceMapConsumer.LEAST_UPPER_BOUND'. Specifies whether to return the\n *     closest element that is smaller than or greater than the one we are\n *     searching for, respectively, if the exact element cannot be found.\n *     Defaults to 'SourceMapConsumer.GREATEST_LOWER_BOUND'.\n *\n * and an object is returned with the following properties:\n *\n *   - source: The original source file, or null.\n *   - line: The line number in the original source, or null.  The\n *     line number is 1-based.\n *   - column: The column number in the original source, or null.  The\n *     column number is 0-based.\n *   - name: The original identifier, or null.\n */\nBasicSourceMapConsumer.prototype.originalPositionFor =\n  function SourceMapConsumer_originalPositionFor(aArgs) {\n    var needle = {\n      generatedLine: util.getArg(aArgs, 'line'),\n      generatedColumn: util.getArg(aArgs, 'column')\n    };\n\n    var index = this._findMapping(\n      needle,\n      this._generatedMappings,\n      \"generatedLine\",\n      \"generatedColumn\",\n      util.compareByGeneratedPositionsDeflated,\n      util.getArg(aArgs, 'bias', SourceMapConsumer.GREATEST_LOWER_BOUND)\n    );\n\n    if (index >= 0) {\n      var mapping = this._generatedMappings[index];\n\n      if (mapping.generatedLine === needle.generatedLine) {\n        var source = util.getArg(mapping, 'source', null);\n        if (source !== null) {\n          source = this._sources.at(source);\n          source = util.computeSourceURL(this.sourceRoot, source, this._sourceMapURL);\n        }\n        var name = util.getArg(mapping, 'name', null);\n        if (name !== null) {\n          name = this._names.at(name);\n        }\n        return {\n          source: source,\n          line: util.getArg(mapping, 'originalLine', null),\n          column: util.getArg(mapping, 'originalColumn', null),\n          name: name\n        };\n      }\n    }\n\n    return {\n      source: null,\n      line: null,\n      column: null,\n      name: null\n    };\n  };\n\n/**\n * Return true if we have the source content for every source in the source\n * map, false otherwise.\n */\nBasicSourceMapConsumer.prototype.hasContentsOfAllSources =\n  function BasicSourceMapConsumer_hasContentsOfAllSources() {\n    if (!this.sourcesContent) {\n      return false;\n    }\n    return this.sourcesContent.length >= this._sources.size() &&\n      !this.sourcesContent.some(function (sc) { return sc == null; });\n  };\n\n/**\n * Returns the original source content. The only argument is the url of the\n * original source file. Returns null if no original source content is\n * available.\n */\nBasicSourceMapConsumer.prototype.sourceContentFor =\n  function SourceMapConsumer_sourceContentFor(aSource, nullOnMissing) {\n    if (!this.sourcesContent) {\n      return null;\n    }\n\n    var index = this._findSourceIndex(aSource);\n    if (index >= 0) {\n      return this.sourcesContent[index];\n    }\n\n    var relativeSource = aSource;\n    if (this.sourceRoot != null) {\n      relativeSource = util.relative(this.sourceRoot, relativeSource);\n    }\n\n    var url;\n    if (this.sourceRoot != null\n        && (url = util.urlParse(this.sourceRoot))) {\n      // XXX: file:// URIs and absolute paths lead to unexpected behavior for\n      // many users. We can help them out when they expect file:// URIs to\n      // behave like it would if they were running a local HTTP server. See\n      // https://bugzilla.mozilla.org/show_bug.cgi?id=885597.\n      var fileUriAbsPath = relativeSource.replace(/^file:\\/\\//, \"\");\n      if (url.scheme == \"file\"\n          && this._sources.has(fileUriAbsPath)) {\n        return this.sourcesContent[this._sources.indexOf(fileUriAbsPath)]\n      }\n\n      if ((!url.path || url.path == \"/\")\n          && this._sources.has(\"/\" + relativeSource)) {\n        return this.sourcesContent[this._sources.indexOf(\"/\" + relativeSource)];\n      }\n    }\n\n    // This function is used recursively from\n    // IndexedSourceMapConsumer.prototype.sourceContentFor. In that case, we\n    // don't want to throw if we can't find the source - we just want to\n    // return null, so we provide a flag to exit gracefully.\n    if (nullOnMissing) {\n      return null;\n    }\n    else {\n      throw new Error('\"' + relativeSource + '\" is not in the SourceMap.');\n    }\n  };\n\n/**\n * Returns the generated line and column information for the original source,\n * line, and column positions provided. The only argument is an object with\n * the following properties:\n *\n *   - source: The filename of the original source.\n *   - line: The line number in the original source.  The line number\n *     is 1-based.\n *   - column: The column number in the original source.  The column\n *     number is 0-based.\n *   - bias: Either 'SourceMapConsumer.GREATEST_LOWER_BOUND' or\n *     'SourceMapConsumer.LEAST_UPPER_BOUND'. Specifies whether to return the\n *     closest element that is smaller than or greater than the one we are\n *     searching for, respectively, if the exact element cannot be found.\n *     Defaults to 'SourceMapConsumer.GREATEST_LOWER_BOUND'.\n *\n * and an object is returned with the following properties:\n *\n *   - line: The line number in the generated source, or null.  The\n *     line number is 1-based.\n *   - column: The column number in the generated source, or null.\n *     The column number is 0-based.\n */\nBasicSourceMapConsumer.prototype.generatedPositionFor =\n  function SourceMapConsumer_generatedPositionFor(aArgs) {\n    var source = util.getArg(aArgs, 'source');\n    source = this._findSourceIndex(source);\n    if (source < 0) {\n      return {\n        line: null,\n        column: null,\n        lastColumn: null\n      };\n    }\n\n    var needle = {\n      source: source,\n      originalLine: util.getArg(aArgs, 'line'),\n      originalColumn: util.getArg(aArgs, 'column')\n    };\n\n    var index = this._findMapping(\n      needle,\n      this._originalMappings,\n      \"originalLine\",\n      \"originalColumn\",\n      util.compareByOriginalPositions,\n      util.getArg(aArgs, 'bias', SourceMapConsumer.GREATEST_LOWER_BOUND)\n    );\n\n    if (index >= 0) {\n      var mapping = this._originalMappings[index];\n\n      if (mapping.source === needle.source) {\n        return {\n          line: util.getArg(mapping, 'generatedLine', null),\n          column: util.getArg(mapping, 'generatedColumn', null),\n          lastColumn: util.getArg(mapping, 'lastGeneratedColumn', null)\n        };\n      }\n    }\n\n    return {\n      line: null,\n      column: null,\n      lastColumn: null\n    };\n  };\n\nexports.BasicSourceMapConsumer = BasicSourceMapConsumer;\n\n/**\n * An IndexedSourceMapConsumer instance represents a parsed source map which\n * we can query for information. It differs from BasicSourceMapConsumer in\n * that it takes \"indexed\" source maps (i.e. ones with a \"sections\" field) as\n * input.\n *\n * The first parameter is a raw source map (either as a JSON string, or already\n * parsed to an object). According to the spec for indexed source maps, they\n * have the following attributes:\n *\n *   - version: Which version of the source map spec this map is following.\n *   - file: Optional. The generated file this source map is associated with.\n *   - sections: A list of section definitions.\n *\n * Each value under the \"sections\" field has two fields:\n *   - offset: The offset into the original specified at which this section\n *       begins to apply, defined as an object with a \"line\" and \"column\"\n *       field.\n *   - map: A source map definition. This source map could also be indexed,\n *       but doesn't have to be.\n *\n * Instead of the \"map\" field, it's also possible to have a \"url\" field\n * specifying a URL to retrieve a source map from, but that's currently\n * unsupported.\n *\n * Here's an example source map, taken from the source map spec[0], but\n * modified to omit a section which uses the \"url\" field.\n *\n *  {\n *    version : 3,\n *    file: \"app.js\",\n *    sections: [{\n *      offset: {line:100, column:10},\n *      map: {\n *        version : 3,\n *        file: \"section.js\",\n *        sources: [\"foo.js\", \"bar.js\"],\n *        names: [\"src\", \"maps\", \"are\", \"fun\"],\n *        mappings: \"AAAA,E;;ABCDE;\"\n *      }\n *    }],\n *  }\n *\n * The second parameter, if given, is a string whose value is the URL\n * at which the source map was found.  This URL is used to compute the\n * sources array.\n *\n * [0]: https://docs.google.com/document/d/1U1RGAehQwRypUTovF1KRlpiOFze0b-_2gc6fAH0KY0k/edit#heading=h.535es3xeprgt\n */\nfunction IndexedSourceMapConsumer(aSourceMap, aSourceMapURL) {\n  var sourceMap = aSourceMap;\n  if (typeof aSourceMap === 'string') {\n    sourceMap = util.parseSourceMapInput(aSourceMap);\n  }\n\n  var version = util.getArg(sourceMap, 'version');\n  var sections = util.getArg(sourceMap, 'sections');\n\n  if (version != this._version) {\n    throw new Error('Unsupported version: ' + version);\n  }\n\n  this._sources = new ArraySet();\n  this._names = new ArraySet();\n\n  var lastOffset = {\n    line: -1,\n    column: 0\n  };\n  this._sections = sections.map(function (s) {\n    if (s.url) {\n      // The url field will require support for asynchronicity.\n      // See https://github.com/mozilla/source-map/issues/16\n      throw new Error('Support for url field in sections not implemented.');\n    }\n    var offset = util.getArg(s, 'offset');\n    var offsetLine = util.getArg(offset, 'line');\n    var offsetColumn = util.getArg(offset, 'column');\n\n    if (offsetLine < lastOffset.line ||\n        (offsetLine === lastOffset.line && offsetColumn < lastOffset.column)) {\n      throw new Error('Section offsets must be ordered and non-overlapping.');\n    }\n    lastOffset = offset;\n\n    return {\n      generatedOffset: {\n        // The offset fields are 0-based, but we use 1-based indices when\n        // encoding/decoding from VLQ.\n        generatedLine: offsetLine + 1,\n        generatedColumn: offsetColumn + 1\n      },\n      consumer: new SourceMapConsumer(util.getArg(s, 'map'), aSourceMapURL)\n    }\n  });\n}\n\nIndexedSourceMapConsumer.prototype = Object.create(SourceMapConsumer.prototype);\nIndexedSourceMapConsumer.prototype.constructor = SourceMapConsumer;\n\n/**\n * The version of the source mapping spec that we are consuming.\n */\nIndexedSourceMapConsumer.prototype._version = 3;\n\n/**\n * The list of original sources.\n */\nObject.defineProperty(IndexedSourceMapConsumer.prototype, 'sources', {\n  get: function () {\n    var sources = [];\n    for (var i = 0; i < this._sections.length; i++) {\n      for (var j = 0; j < this._sections[i].consumer.sources.length; j++) {\n        sources.push(this._sections[i].consumer.sources[j]);\n      }\n    }\n    return sources;\n  }\n});\n\n/**\n * Returns the original source, line, and column information for the generated\n * source's line and column positions provided. The only argument is an object\n * with the following properties:\n *\n *   - line: The line number in the generated source.  The line number\n *     is 1-based.\n *   - column: The column number in the generated source.  The column\n *     number is 0-based.\n *\n * and an object is returned with the following properties:\n *\n *   - source: The original source file, or null.\n *   - line: The line number in the original source, or null.  The\n *     line number is 1-based.\n *   - column: The column number in the original source, or null.  The\n *     column number is 0-based.\n *   - name: The original identifier, or null.\n */\nIndexedSourceMapConsumer.prototype.originalPositionFor =\n  function IndexedSourceMapConsumer_originalPositionFor(aArgs) {\n    var needle = {\n      generatedLine: util.getArg(aArgs, 'line'),\n      generatedColumn: util.getArg(aArgs, 'column')\n    };\n\n    // Find the section containing the generated position we're trying to map\n    // to an original position.\n    var sectionIndex = binarySearch.search(needle, this._sections,\n      function(needle, section) {\n        var cmp = needle.generatedLine - section.generatedOffset.generatedLine;\n        if (cmp) {\n          return cmp;\n        }\n\n        return (needle.generatedColumn -\n                section.generatedOffset.generatedColumn);\n      });\n    var section = this._sections[sectionIndex];\n\n    if (!section) {\n      return {\n        source: null,\n        line: null,\n        column: null,\n        name: null\n      };\n    }\n\n    return section.consumer.originalPositionFor({\n      line: needle.generatedLine -\n        (section.generatedOffset.generatedLine - 1),\n      column: needle.generatedColumn -\n        (section.generatedOffset.generatedLine === needle.generatedLine\n         ? section.generatedOffset.generatedColumn - 1\n         : 0),\n      bias: aArgs.bias\n    });\n  };\n\n/**\n * Return true if we have the source content for every source in the source\n * map, false otherwise.\n */\nIndexedSourceMapConsumer.prototype.hasContentsOfAllSources =\n  function IndexedSourceMapConsumer_hasContentsOfAllSources() {\n    return this._sections.every(function (s) {\n      return s.consumer.hasContentsOfAllSources();\n    });\n  };\n\n/**\n * Returns the original source content. The only argument is the url of the\n * original source file. Returns null if no original source content is\n * available.\n */\nIndexedSourceMapConsumer.prototype.sourceContentFor =\n  function IndexedSourceMapConsumer_sourceContentFor(aSource, nullOnMissing) {\n    for (var i = 0; i < this._sections.length; i++) {\n      var section = this._sections[i];\n\n      var content = section.consumer.sourceContentFor(aSource, true);\n      if (content) {\n        return content;\n      }\n    }\n    if (nullOnMissing) {\n      return null;\n    }\n    else {\n      throw new Error('\"' + aSource + '\" is not in the SourceMap.');\n    }\n  };\n\n/**\n * Returns the generated line and column information for the original source,\n * line, and column positions provided. The only argument is an object with\n * the following properties:\n *\n *   - source: The filename of the original source.\n *   - line: The line number in the original source.  The line number\n *     is 1-based.\n *   - column: The column number in the original source.  The column\n *     number is 0-based.\n *\n * and an object is returned with the following properties:\n *\n *   - line: The line number in the generated source, or null.  The\n *     line number is 1-based. \n *   - column: The column number in the generated source, or null.\n *     The column number is 0-based.\n */\nIndexedSourceMapConsumer.prototype.generatedPositionFor =\n  function IndexedSourceMapConsumer_generatedPositionFor(aArgs) {\n    for (var i = 0; i < this._sections.length; i++) {\n      var section = this._sections[i];\n\n      // Only consider this section if the requested source is in the list of\n      // sources of the consumer.\n      if (section.consumer._findSourceIndex(util.getArg(aArgs, 'source')) === -1) {\n        continue;\n      }\n      var generatedPosition = section.consumer.generatedPositionFor(aArgs);\n      if (generatedPosition) {\n        var ret = {\n          line: generatedPosition.line +\n            (section.generatedOffset.generatedLine - 1),\n          column: generatedPosition.column +\n            (section.generatedOffset.generatedLine === generatedPosition.line\n             ? section.generatedOffset.generatedColumn - 1\n             : 0)\n        };\n        return ret;\n      }\n    }\n\n    return {\n      line: null,\n      column: null\n    };\n  };\n\n/**\n * Parse the mappings in a string in to a data structure which we can easily\n * query (the ordered arrays in the `this.__generatedMappings` and\n * `this.__originalMappings` properties).\n */\nIndexedSourceMapConsumer.prototype._parseMappings =\n  function IndexedSourceMapConsumer_parseMappings(aStr, aSourceRoot) {\n    this.__generatedMappings = [];\n    this.__originalMappings = [];\n    for (var i = 0; i < this._sections.length; i++) {\n      var section = this._sections[i];\n      var sectionMappings = section.consumer._generatedMappings;\n      for (var j = 0; j < sectionMappings.length; j++) {\n        var mapping = sectionMappings[j];\n\n        var source = section.consumer._sources.at(mapping.source);\n        source = util.computeSourceURL(section.consumer.sourceRoot, source, this._sourceMapURL);\n        this._sources.add(source);\n        source = this._sources.indexOf(source);\n\n        var name = null;\n        if (mapping.name) {\n          name = section.consumer._names.at(mapping.name);\n          this._names.add(name);\n          name = this._names.indexOf(name);\n        }\n\n        // The mappings coming from the consumer for the section have\n        // generated positions relative to the start of the section, so we\n        // need to offset them to be relative to the start of the concatenated\n        // generated file.\n        var adjustedMapping = {\n          source: source,\n          generatedLine: mapping.generatedLine +\n            (section.generatedOffset.generatedLine - 1),\n          generatedColumn: mapping.generatedColumn +\n            (section.generatedOffset.generatedLine === mapping.generatedLine\n            ? section.generatedOffset.generatedColumn - 1\n            : 0),\n          originalLine: mapping.originalLine,\n          originalColumn: mapping.originalColumn,\n          name: name\n        };\n\n        this.__generatedMappings.push(adjustedMapping);\n        if (typeof adjustedMapping.originalLine === 'number') {\n          this.__originalMappings.push(adjustedMapping);\n        }\n      }\n    }\n\n    quickSort(this.__generatedMappings, util.compareByGeneratedPositionsDeflated);\n    quickSort(this.__originalMappings, util.compareByOriginalPositions);\n  };\n\nexports.IndexedSourceMapConsumer = IndexedSourceMapConsumer;\n", "/* -*- Mode: js; js-indent-level: 2; -*- */\n/*\n * Copyright 2011 Mozilla Foundation and contributors\n * Licensed under the New BSD license. See LICENSE or:\n * http://opensource.org/licenses/BSD-3-Clause\n */\n\nvar SourceMapGenerator = require('./source-map-generator').SourceMapGenerator;\nvar util = require('./util');\n\n// Matches a Windows-style `\\r\\n` newline or a `\\n` newline used by all other\n// operating systems these days (capturing the result).\nvar REGEX_NEWLINE = /(\\r?\\n)/;\n\n// Newline character code for charCodeAt() comparisons\nvar NEWLINE_CODE = 10;\n\n// Private symbol for identifying `SourceNode`s when multiple versions of\n// the source-map library are loaded. This MUST NOT CHANGE across\n// versions!\nvar isSourceNode = \"$$$isSourceNode$$$\";\n\n/**\n * SourceNodes provide a way to abstract over interpolating/concatenating\n * snippets of generated JavaScript source code while maintaining the line and\n * column information associated with the original source code.\n *\n * @param aLine The original line number.\n * @param aColumn The original column number.\n * @param aSource The original source's filename.\n * @param aChunks Optional. An array of strings which are snippets of\n *        generated JS, or other SourceNodes.\n * @param aName The original identifier.\n */\nfunction SourceNode(aLine, aColumn, aSource, aChunks, aName) {\n  this.children = [];\n  this.sourceContents = {};\n  this.line = aLine == null ? null : aLine;\n  this.column = aColumn == null ? null : aColumn;\n  this.source = aSource == null ? null : aSource;\n  this.name = aName == null ? null : aName;\n  this[isSourceNode] = true;\n  if (aChunks != null) this.add(aChunks);\n}\n\n/**\n * Creates a SourceNode from generated code and a SourceMapConsumer.\n *\n * @param aGeneratedCode The generated code\n * @param aSourceMapConsumer The SourceMap for the generated code\n * @param aRelativePath Optional. The path that relative sources in the\n *        SourceMapConsumer should be relative to.\n */\nSourceNode.fromStringWithSourceMap =\n  function SourceNode_fromStringWithSourceMap(aGeneratedCode, aSourceMapConsumer, aRelativePath) {\n    // The SourceNode we want to fill with the generated code\n    // and the SourceMap\n    var node = new SourceNode();\n\n    // All even indices of this array are one line of the generated code,\n    // while all odd indices are the newlines between two adjacent lines\n    // (since `REGEX_NEWLINE` captures its match).\n    // Processed fragments are accessed by calling `shiftNextLine`.\n    var remainingLines = aGeneratedCode.split(REGEX_NEWLINE);\n    var remainingLinesIndex = 0;\n    var shiftNextLine = function() {\n      var lineContents = getNextLine();\n      // The last line of a file might not have a newline.\n      var newLine = getNextLine() || \"\";\n      return lineContents + newLine;\n\n      function getNextLine() {\n        return remainingLinesIndex < remainingLines.length ?\n            remainingLines[remainingLinesIndex++] : undefined;\n      }\n    };\n\n    // We need to remember the position of \"remainingLines\"\n    var lastGeneratedLine = 1, lastGeneratedColumn = 0;\n\n    // The generate SourceNodes we need a code range.\n    // To extract it current and last mapping is used.\n    // Here we store the last mapping.\n    var lastMapping = null;\n\n    aSourceMapConsumer.eachMapping(function (mapping) {\n      if (lastMapping !== null) {\n        // We add the code from \"lastMapping\" to \"mapping\":\n        // First check if there is a new line in between.\n        if (lastGeneratedLine < mapping.generatedLine) {\n          // Associate first line with \"lastMapping\"\n          addMappingWithCode(lastMapping, shiftNextLine());\n          lastGeneratedLine++;\n          lastGeneratedColumn = 0;\n          // The remaining code is added without mapping\n        } else {\n          // There is no new line in between.\n          // Associate the code between \"lastGeneratedColumn\" and\n          // \"mapping.generatedColumn\" with \"lastMapping\"\n          var nextLine = remainingLines[remainingLinesIndex] || '';\n          var code = nextLine.substr(0, mapping.generatedColumn -\n                                        lastGeneratedColumn);\n          remainingLines[remainingLinesIndex] = nextLine.substr(mapping.generatedColumn -\n                                              lastGeneratedColumn);\n          lastGeneratedColumn = mapping.generatedColumn;\n          addMappingWithCode(lastMapping, code);\n          // No more remaining code, continue\n          lastMapping = mapping;\n          return;\n        }\n      }\n      // We add the generated code until the first mapping\n      // to the SourceNode without any mapping.\n      // Each line is added as separate string.\n      while (lastGeneratedLine < mapping.generatedLine) {\n        node.add(shiftNextLine());\n        lastGeneratedLine++;\n      }\n      if (lastGeneratedColumn < mapping.generatedColumn) {\n        var nextLine = remainingLines[remainingLinesIndex] || '';\n        node.add(nextLine.substr(0, mapping.generatedColumn));\n        remainingLines[remainingLinesIndex] = nextLine.substr(mapping.generatedColumn);\n        lastGeneratedColumn = mapping.generatedColumn;\n      }\n      lastMapping = mapping;\n    }, this);\n    // We have processed all mappings.\n    if (remainingLinesIndex < remainingLines.length) {\n      if (lastMapping) {\n        // Associate the remaining code in the current line with \"lastMapping\"\n        addMappingWithCode(lastMapping, shiftNextLine());\n      }\n      // and add the remaining lines without any mapping\n      node.add(remainingLines.splice(remainingLinesIndex).join(\"\"));\n    }\n\n    // Copy sourcesContent into SourceNode\n    aSourceMapConsumer.sources.forEach(function (sourceFile) {\n      var content = aSourceMapConsumer.sourceContentFor(sourceFile);\n      if (content != null) {\n        if (aRelativePath != null) {\n          sourceFile = util.join(aRelativePath, sourceFile);\n        }\n        node.setSourceContent(sourceFile, content);\n      }\n    });\n\n    return node;\n\n    function addMappingWithCode(mapping, code) {\n      if (mapping === null || mapping.source === undefined) {\n        node.add(code);\n      } else {\n        var source = aRelativePath\n          ? util.join(aRelativePath, mapping.source)\n          : mapping.source;\n        node.add(new SourceNode(mapping.originalLine,\n                                mapping.originalColumn,\n                                source,\n                                code,\n                                mapping.name));\n      }\n    }\n  };\n\n/**\n * Add a chunk of generated JS to this source node.\n *\n * @param aChunk A string snippet of generated JS code, another instance of\n *        SourceNode, or an array where each member is one of those things.\n */\nSourceNode.prototype.add = function SourceNode_add(aChunk) {\n  if (Array.isArray(aChunk)) {\n    aChunk.forEach(function (chunk) {\n      this.add(chunk);\n    }, this);\n  }\n  else if (aChunk[isSourceNode] || typeof aChunk === \"string\") {\n    if (aChunk) {\n      this.children.push(aChunk);\n    }\n  }\n  else {\n    throw new TypeError(\n      \"Expected a SourceNode, string, or an array of SourceNodes and strings. Got \" + aChunk\n    );\n  }\n  return this;\n};\n\n/**\n * Add a chunk of generated JS to the beginning of this source node.\n *\n * @param aChunk A string snippet of generated JS code, another instance of\n *        SourceNode, or an array where each member is one of those things.\n */\nSourceNode.prototype.prepend = function SourceNode_prepend(aChunk) {\n  if (Array.isArray(aChunk)) {\n    for (var i = aChunk.length-1; i >= 0; i--) {\n      this.prepend(aChunk[i]);\n    }\n  }\n  else if (aChunk[isSourceNode] || typeof aChunk === \"string\") {\n    this.children.unshift(aChunk);\n  }\n  else {\n    throw new TypeError(\n      \"Expected a SourceNode, string, or an array of SourceNodes and strings. Got \" + aChunk\n    );\n  }\n  return this;\n};\n\n/**\n * Walk over the tree of JS snippets in this node and its children. The\n * walking function is called once for each snippet of JS and is passed that\n * snippet and the its original associated source's line/column location.\n *\n * @param aFn The traversal function.\n */\nSourceNode.prototype.walk = function SourceNode_walk(aFn) {\n  var chunk;\n  for (var i = 0, len = this.children.length; i < len; i++) {\n    chunk = this.children[i];\n    if (chunk[isSourceNode]) {\n      chunk.walk(aFn);\n    }\n    else {\n      if (chunk !== '') {\n        aFn(chunk, { source: this.source,\n                     line: this.line,\n                     column: this.column,\n                     name: this.name });\n      }\n    }\n  }\n};\n\n/**\n * Like `String.prototype.join` except for SourceNodes. Inserts `aStr` between\n * each of `this.children`.\n *\n * @param aSep The separator.\n */\nSourceNode.prototype.join = function SourceNode_join(aSep) {\n  var newChildren;\n  var i;\n  var len = this.children.length;\n  if (len > 0) {\n    newChildren = [];\n    for (i = 0; i < len-1; i++) {\n      newChildren.push(this.children[i]);\n      newChildren.push(aSep);\n    }\n    newChildren.push(this.children[i]);\n    this.children = newChildren;\n  }\n  return this;\n};\n\n/**\n * Call String.prototype.replace on the very right-most source snippet. Useful\n * for trimming whitespace from the end of a source node, etc.\n *\n * @param aPattern The pattern to replace.\n * @param aReplacement The thing to replace the pattern with.\n */\nSourceNode.prototype.replaceRight = function SourceNode_replaceRight(aPattern, aReplacement) {\n  var lastChild = this.children[this.children.length - 1];\n  if (lastChild[isSourceNode]) {\n    lastChild.replaceRight(aPattern, aReplacement);\n  }\n  else if (typeof lastChild === 'string') {\n    this.children[this.children.length - 1] = lastChild.replace(aPattern, aReplacement);\n  }\n  else {\n    this.children.push(''.replace(aPattern, aReplacement));\n  }\n  return this;\n};\n\n/**\n * Set the source content for a source file. This will be added to the SourceMapGenerator\n * in the sourcesContent field.\n *\n * @param aSourceFile The filename of the source file\n * @param aSourceContent The content of the source file\n */\nSourceNode.prototype.setSourceContent =\n  function SourceNode_setSourceContent(aSourceFile, aSourceContent) {\n    this.sourceContents[util.toSetString(aSourceFile)] = aSourceContent;\n  };\n\n/**\n * Walk over the tree of SourceNodes. The walking function is called for each\n * source file content and is passed the filename and source content.\n *\n * @param aFn The traversal function.\n */\nSourceNode.prototype.walkSourceContents =\n  function SourceNode_walkSourceContents(aFn) {\n    for (var i = 0, len = this.children.length; i < len; i++) {\n      if (this.children[i][isSourceNode]) {\n        this.children[i].walkSourceContents(aFn);\n      }\n    }\n\n    var sources = Object.keys(this.sourceContents);\n    for (var i = 0, len = sources.length; i < len; i++) {\n      aFn(util.fromSetString(sources[i]), this.sourceContents[sources[i]]);\n    }\n  };\n\n/**\n * Return the string representation of this source node. Walks over the tree\n * and concatenates all the various snippets together to one string.\n */\nSourceNode.prototype.toString = function SourceNode_toString() {\n  var str = \"\";\n  this.walk(function (chunk) {\n    str += chunk;\n  });\n  return str;\n};\n\n/**\n * Returns the string representation of this source node along with a source\n * map.\n */\nSourceNode.prototype.toStringWithSourceMap = function SourceNode_toStringWithSourceMap(aArgs) {\n  var generated = {\n    code: \"\",\n    line: 1,\n    column: 0\n  };\n  var map = new SourceMapGenerator(aArgs);\n  var sourceMappingActive = false;\n  var lastOriginalSource = null;\n  var lastOriginalLine = null;\n  var lastOriginalColumn = null;\n  var lastOriginalName = null;\n  this.walk(function (chunk, original) {\n    generated.code += chunk;\n    if (original.source !== null\n        && original.line !== null\n        && original.column !== null) {\n      if(lastOriginalSource !== original.source\n         || lastOriginalLine !== original.line\n         || lastOriginalColumn !== original.column\n         || lastOriginalName !== original.name) {\n        map.addMapping({\n          source: original.source,\n          original: {\n            line: original.line,\n            column: original.column\n          },\n          generated: {\n            line: generated.line,\n            column: generated.column\n          },\n          name: original.name\n        });\n      }\n      lastOriginalSource = original.source;\n      lastOriginalLine = original.line;\n      lastOriginalColumn = original.column;\n      lastOriginalName = original.name;\n      sourceMappingActive = true;\n    } else if (sourceMappingActive) {\n      map.addMapping({\n        generated: {\n          line: generated.line,\n          column: generated.column\n        }\n      });\n      lastOriginalSource = null;\n      sourceMappingActive = false;\n    }\n    for (var idx = 0, length = chunk.length; idx < length; idx++) {\n      if (chunk.charCodeAt(idx) === NEWLINE_CODE) {\n        generated.line++;\n        generated.column = 0;\n        // Mappings end at eol\n        if (idx + 1 === length) {\n          lastOriginalSource = null;\n          sourceMappingActive = false;\n        } else if (sourceMappingActive) {\n          map.addMapping({\n            source: original.source,\n            original: {\n              line: original.line,\n              column: original.column\n            },\n            generated: {\n              line: generated.line,\n              column: generated.column\n            },\n            name: original.name\n          });\n        }\n      } else {\n        generated.column++;\n      }\n    }\n  });\n  this.walkSourceContents(function (sourceFile, sourceContent) {\n    map.setSourceContent(sourceFile, sourceContent);\n  });\n\n  return { code: generated.code, map: map };\n};\n\nexports.SourceNode = SourceNode;\n", "/*\n * Copyright 2009-2011 Mozilla Foundation and contributors\n * Licensed under the New BSD license. See LICENSE.txt or:\n * http://opensource.org/licenses/BSD-3-Clause\n */\nexports.SourceMapGenerator = require('./lib/source-map-generator').SourceMapGenerator;\nexports.SourceMapConsumer = require('./lib/source-map-consumer').SourceMapConsumer;\nexports.SourceNode = require('./lib/source-node').SourceNode;\n", "/*!\n  * message-compiler v9.2.2\n  * (c) 2022 ka<PERSON><PERSON> ka<PERSON>\n  * Released under the MIT License.\n  */\n'use strict';\n\nObject.defineProperty(exports, '__esModule', { value: true });\n\nvar shared = require('@intlify/shared');\nvar sourceMap = require('source-map');\n\nconst CompileErrorCodes = {\r\n    // tokenizer error codes\r\n    EXPECTED_TOKEN: 1,\r\n    INVALID_TOKEN_IN_PLACEHOLDER: 2,\r\n    UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER: 3,\r\n    UNKNOWN_ESCAPE_SEQUENCE: 4,\r\n    INVALID_UNICODE_ESCAPE_SEQUENCE: 5,\r\n    UNBALANCED_CLOSING_BRACE: 6,\r\n    UNTERMINATED_CLOSING_BRACE: 7,\r\n    EMPTY_PLACEHOLDER: 8,\r\n    NOT_ALLOW_NEST_PLACEHOLDER: 9,\r\n    INVALID_LINKED_FORMAT: 10,\r\n    // parser error codes\r\n    MUST_HAVE_MESSAGES_IN_PLURAL: 11,\r\n    UNEXPECTED_EMPTY_LINKED_MODIFIER: 12,\r\n    UNEXPECTED_EMPTY_LINKED_KEY: 13,\r\n    UNEXPECTED_LEXICAL_ANALYSIS: 14,\r\n    // Special value for higher-order compilers to pick up the last code\r\n    // to avoid collision of error codes. This should always be kept as the last\r\n    // item.\r\n    __EXTEND_POINT__: 15\r\n};\r\n/** @internal */\r\nconst errorMessages = {\r\n    // tokenizer error messages\r\n    [CompileErrorCodes.EXPECTED_TOKEN]: `Expected token: '{0}'`,\r\n    [CompileErrorCodes.INVALID_TOKEN_IN_PLACEHOLDER]: `Invalid token in placeholder: '{0}'`,\r\n    [CompileErrorCodes.UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER]: `Unterminated single quote in placeholder`,\r\n    [CompileErrorCodes.UNKNOWN_ESCAPE_SEQUENCE]: `Unknown escape sequence: \\\\{0}`,\r\n    [CompileErrorCodes.INVALID_UNICODE_ESCAPE_SEQUENCE]: `Invalid unicode escape sequence: {0}`,\r\n    [CompileErrorCodes.UNBALANCED_CLOSING_BRACE]: `Unbalanced closing brace`,\r\n    [CompileErrorCodes.UNTERMINATED_CLOSING_BRACE]: `Unterminated closing brace`,\r\n    [CompileErrorCodes.EMPTY_PLACEHOLDER]: `Empty placeholder`,\r\n    [CompileErrorCodes.NOT_ALLOW_NEST_PLACEHOLDER]: `Not allowed nest placeholder`,\r\n    [CompileErrorCodes.INVALID_LINKED_FORMAT]: `Invalid linked format`,\r\n    // parser error messages\r\n    [CompileErrorCodes.MUST_HAVE_MESSAGES_IN_PLURAL]: `Plural must have messages`,\r\n    [CompileErrorCodes.UNEXPECTED_EMPTY_LINKED_MODIFIER]: `Unexpected empty linked modifier`,\r\n    [CompileErrorCodes.UNEXPECTED_EMPTY_LINKED_KEY]: `Unexpected empty linked key`,\r\n    [CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS]: `Unexpected lexical analysis in token: '{0}'`\r\n};\r\nfunction createCompileError(code, loc, options = {}) {\r\n    const { domain, messages, args } = options;\r\n    const msg = shared.format((messages || errorMessages)[code] || '', ...(args || []))\r\n        ;\r\n    const error = new SyntaxError(String(msg));\r\n    error.code = code;\r\n    if (loc) {\r\n        error.location = loc;\r\n    }\r\n    error.domain = domain;\r\n    return error;\r\n}\r\n/** @internal */\r\nfunction defaultOnError(error) {\r\n    throw error;\r\n}\n\nconst LocationStub = {\r\n    start: { line: 1, column: 1, offset: 0 },\r\n    end: { line: 1, column: 1, offset: 0 }\r\n};\r\nfunction createPosition(line, column, offset) {\r\n    return { line, column, offset };\r\n}\r\nfunction createLocation(start, end, source) {\r\n    const loc = { start, end };\r\n    if (source != null) {\r\n        loc.source = source;\r\n    }\r\n    return loc;\r\n}\n\nconst CHAR_SP = ' ';\r\nconst CHAR_CR = '\\r';\r\nconst CHAR_LF = '\\n';\r\nconst CHAR_LS = String.fromCharCode(0x2028);\r\nconst CHAR_PS = String.fromCharCode(0x2029);\r\nfunction createScanner(str) {\r\n    const _buf = str;\r\n    let _index = 0;\r\n    let _line = 1;\r\n    let _column = 1;\r\n    let _peekOffset = 0;\r\n    const isCRLF = (index) => _buf[index] === CHAR_CR && _buf[index + 1] === CHAR_LF;\r\n    const isLF = (index) => _buf[index] === CHAR_LF;\r\n    const isPS = (index) => _buf[index] === CHAR_PS;\r\n    const isLS = (index) => _buf[index] === CHAR_LS;\r\n    const isLineEnd = (index) => isCRLF(index) || isLF(index) || isPS(index) || isLS(index);\r\n    const index = () => _index;\r\n    const line = () => _line;\r\n    const column = () => _column;\r\n    const peekOffset = () => _peekOffset;\r\n    const charAt = (offset) => isCRLF(offset) || isPS(offset) || isLS(offset) ? CHAR_LF : _buf[offset];\r\n    const currentChar = () => charAt(_index);\r\n    const currentPeek = () => charAt(_index + _peekOffset);\r\n    function next() {\r\n        _peekOffset = 0;\r\n        if (isLineEnd(_index)) {\r\n            _line++;\r\n            _column = 0;\r\n        }\r\n        if (isCRLF(_index)) {\r\n            _index++;\r\n        }\r\n        _index++;\r\n        _column++;\r\n        return _buf[_index];\r\n    }\r\n    function peek() {\r\n        if (isCRLF(_index + _peekOffset)) {\r\n            _peekOffset++;\r\n        }\r\n        _peekOffset++;\r\n        return _buf[_index + _peekOffset];\r\n    }\r\n    function reset() {\r\n        _index = 0;\r\n        _line = 1;\r\n        _column = 1;\r\n        _peekOffset = 0;\r\n    }\r\n    function resetPeek(offset = 0) {\r\n        _peekOffset = offset;\r\n    }\r\n    function skipToPeek() {\r\n        const target = _index + _peekOffset;\r\n        // eslint-disable-next-line no-unmodified-loop-condition\r\n        while (target !== _index) {\r\n            next();\r\n        }\r\n        _peekOffset = 0;\r\n    }\r\n    return {\r\n        index,\r\n        line,\r\n        column,\r\n        peekOffset,\r\n        charAt,\r\n        currentChar,\r\n        currentPeek,\r\n        next,\r\n        peek,\r\n        reset,\r\n        resetPeek,\r\n        skipToPeek\r\n    };\r\n}\n\nconst EOF = undefined;\r\nconst LITERAL_DELIMITER = \"'\";\r\nconst ERROR_DOMAIN$1 = 'tokenizer';\r\nfunction createTokenizer(source, options = {}) {\r\n    const location = options.location !== false;\r\n    const _scnr = createScanner(source);\r\n    const currentOffset = () => _scnr.index();\r\n    const currentPosition = () => createPosition(_scnr.line(), _scnr.column(), _scnr.index());\r\n    const _initLoc = currentPosition();\r\n    const _initOffset = currentOffset();\r\n    const _context = {\r\n        currentType: 14 /* EOF */,\r\n        offset: _initOffset,\r\n        startLoc: _initLoc,\r\n        endLoc: _initLoc,\r\n        lastType: 14 /* EOF */,\r\n        lastOffset: _initOffset,\r\n        lastStartLoc: _initLoc,\r\n        lastEndLoc: _initLoc,\r\n        braceNest: 0,\r\n        inLinked: false,\r\n        text: ''\r\n    };\r\n    const context = () => _context;\r\n    const { onError } = options;\r\n    function emitError(code, pos, offset, ...args) {\r\n        const ctx = context();\r\n        pos.column += offset;\r\n        pos.offset += offset;\r\n        if (onError) {\r\n            const loc = createLocation(ctx.startLoc, pos);\r\n            const err = createCompileError(code, loc, {\r\n                domain: ERROR_DOMAIN$1,\r\n                args\r\n            });\r\n            onError(err);\r\n        }\r\n    }\r\n    function getToken(context, type, value) {\r\n        context.endLoc = currentPosition();\r\n        context.currentType = type;\r\n        const token = { type };\r\n        if (location) {\r\n            token.loc = createLocation(context.startLoc, context.endLoc);\r\n        }\r\n        if (value != null) {\r\n            token.value = value;\r\n        }\r\n        return token;\r\n    }\r\n    const getEndToken = (context) => getToken(context, 14 /* EOF */);\r\n    function eat(scnr, ch) {\r\n        if (scnr.currentChar() === ch) {\r\n            scnr.next();\r\n            return ch;\r\n        }\r\n        else {\r\n            emitError(CompileErrorCodes.EXPECTED_TOKEN, currentPosition(), 0, ch);\r\n            return '';\r\n        }\r\n    }\r\n    function peekSpaces(scnr) {\r\n        let buf = '';\r\n        while (scnr.currentPeek() === CHAR_SP || scnr.currentPeek() === CHAR_LF) {\r\n            buf += scnr.currentPeek();\r\n            scnr.peek();\r\n        }\r\n        return buf;\r\n    }\r\n    function skipSpaces(scnr) {\r\n        const buf = peekSpaces(scnr);\r\n        scnr.skipToPeek();\r\n        return buf;\r\n    }\r\n    function isIdentifierStart(ch) {\r\n        if (ch === EOF) {\r\n            return false;\r\n        }\r\n        const cc = ch.charCodeAt(0);\r\n        return ((cc >= 97 && cc <= 122) || // a-z\r\n            (cc >= 65 && cc <= 90) || // A-Z\r\n            cc === 95 // _\r\n        );\r\n    }\r\n    function isNumberStart(ch) {\r\n        if (ch === EOF) {\r\n            return false;\r\n        }\r\n        const cc = ch.charCodeAt(0);\r\n        return cc >= 48 && cc <= 57; // 0-9\r\n    }\r\n    function isNamedIdentifierStart(scnr, context) {\r\n        const { currentType } = context;\r\n        if (currentType !== 2 /* BraceLeft */) {\r\n            return false;\r\n        }\r\n        peekSpaces(scnr);\r\n        const ret = isIdentifierStart(scnr.currentPeek());\r\n        scnr.resetPeek();\r\n        return ret;\r\n    }\r\n    function isListIdentifierStart(scnr, context) {\r\n        const { currentType } = context;\r\n        if (currentType !== 2 /* BraceLeft */) {\r\n            return false;\r\n        }\r\n        peekSpaces(scnr);\r\n        const ch = scnr.currentPeek() === '-' ? scnr.peek() : scnr.currentPeek();\r\n        const ret = isNumberStart(ch);\r\n        scnr.resetPeek();\r\n        return ret;\r\n    }\r\n    function isLiteralStart(scnr, context) {\r\n        const { currentType } = context;\r\n        if (currentType !== 2 /* BraceLeft */) {\r\n            return false;\r\n        }\r\n        peekSpaces(scnr);\r\n        const ret = scnr.currentPeek() === LITERAL_DELIMITER;\r\n        scnr.resetPeek();\r\n        return ret;\r\n    }\r\n    function isLinkedDotStart(scnr, context) {\r\n        const { currentType } = context;\r\n        if (currentType !== 8 /* LinkedAlias */) {\r\n            return false;\r\n        }\r\n        peekSpaces(scnr);\r\n        const ret = scnr.currentPeek() === \".\" /* LinkedDot */;\r\n        scnr.resetPeek();\r\n        return ret;\r\n    }\r\n    function isLinkedModifierStart(scnr, context) {\r\n        const { currentType } = context;\r\n        if (currentType !== 9 /* LinkedDot */) {\r\n            return false;\r\n        }\r\n        peekSpaces(scnr);\r\n        const ret = isIdentifierStart(scnr.currentPeek());\r\n        scnr.resetPeek();\r\n        return ret;\r\n    }\r\n    function isLinkedDelimiterStart(scnr, context) {\r\n        const { currentType } = context;\r\n        if (!(currentType === 8 /* LinkedAlias */ ||\r\n            currentType === 12 /* LinkedModifier */)) {\r\n            return false;\r\n        }\r\n        peekSpaces(scnr);\r\n        const ret = scnr.currentPeek() === \":\" /* LinkedDelimiter */;\r\n        scnr.resetPeek();\r\n        return ret;\r\n    }\r\n    function isLinkedReferStart(scnr, context) {\r\n        const { currentType } = context;\r\n        if (currentType !== 10 /* LinkedDelimiter */) {\r\n            return false;\r\n        }\r\n        const fn = () => {\r\n            const ch = scnr.currentPeek();\r\n            if (ch === \"{\" /* BraceLeft */) {\r\n                return isIdentifierStart(scnr.peek());\r\n            }\r\n            else if (ch === \"@\" /* LinkedAlias */ ||\r\n                ch === \"%\" /* Modulo */ ||\r\n                ch === \"|\" /* Pipe */ ||\r\n                ch === \":\" /* LinkedDelimiter */ ||\r\n                ch === \".\" /* LinkedDot */ ||\r\n                ch === CHAR_SP ||\r\n                !ch) {\r\n                return false;\r\n            }\r\n            else if (ch === CHAR_LF) {\r\n                scnr.peek();\r\n                return fn();\r\n            }\r\n            else {\r\n                // other characters\r\n                return isIdentifierStart(ch);\r\n            }\r\n        };\r\n        const ret = fn();\r\n        scnr.resetPeek();\r\n        return ret;\r\n    }\r\n    function isPluralStart(scnr) {\r\n        peekSpaces(scnr);\r\n        const ret = scnr.currentPeek() === \"|\" /* Pipe */;\r\n        scnr.resetPeek();\r\n        return ret;\r\n    }\r\n    function detectModuloStart(scnr) {\r\n        const spaces = peekSpaces(scnr);\r\n        const ret = scnr.currentPeek() === \"%\" /* Modulo */ &&\r\n            scnr.peek() === \"{\" /* BraceLeft */;\r\n        scnr.resetPeek();\r\n        return {\r\n            isModulo: ret,\r\n            hasSpace: spaces.length > 0\r\n        };\r\n    }\r\n    function isTextStart(scnr, reset = true) {\r\n        const fn = (hasSpace = false, prev = '', detectModulo = false) => {\r\n            const ch = scnr.currentPeek();\r\n            if (ch === \"{\" /* BraceLeft */) {\r\n                return prev === \"%\" /* Modulo */ ? false : hasSpace;\r\n            }\r\n            else if (ch === \"@\" /* LinkedAlias */ || !ch) {\r\n                return prev === \"%\" /* Modulo */ ? true : hasSpace;\r\n            }\r\n            else if (ch === \"%\" /* Modulo */) {\r\n                scnr.peek();\r\n                return fn(hasSpace, \"%\" /* Modulo */, true);\r\n            }\r\n            else if (ch === \"|\" /* Pipe */) {\r\n                return prev === \"%\" /* Modulo */ || detectModulo\r\n                    ? true\r\n                    : !(prev === CHAR_SP || prev === CHAR_LF);\r\n            }\r\n            else if (ch === CHAR_SP) {\r\n                scnr.peek();\r\n                return fn(true, CHAR_SP, detectModulo);\r\n            }\r\n            else if (ch === CHAR_LF) {\r\n                scnr.peek();\r\n                return fn(true, CHAR_LF, detectModulo);\r\n            }\r\n            else {\r\n                return true;\r\n            }\r\n        };\r\n        const ret = fn();\r\n        reset && scnr.resetPeek();\r\n        return ret;\r\n    }\r\n    function takeChar(scnr, fn) {\r\n        const ch = scnr.currentChar();\r\n        if (ch === EOF) {\r\n            return EOF;\r\n        }\r\n        if (fn(ch)) {\r\n            scnr.next();\r\n            return ch;\r\n        }\r\n        return null;\r\n    }\r\n    function takeIdentifierChar(scnr) {\r\n        const closure = (ch) => {\r\n            const cc = ch.charCodeAt(0);\r\n            return ((cc >= 97 && cc <= 122) || // a-z\r\n                (cc >= 65 && cc <= 90) || // A-Z\r\n                (cc >= 48 && cc <= 57) || // 0-9\r\n                cc === 95 || // _\r\n                cc === 36 // $\r\n            );\r\n        };\r\n        return takeChar(scnr, closure);\r\n    }\r\n    function takeDigit(scnr) {\r\n        const closure = (ch) => {\r\n            const cc = ch.charCodeAt(0);\r\n            return cc >= 48 && cc <= 57; // 0-9\r\n        };\r\n        return takeChar(scnr, closure);\r\n    }\r\n    function takeHexDigit(scnr) {\r\n        const closure = (ch) => {\r\n            const cc = ch.charCodeAt(0);\r\n            return ((cc >= 48 && cc <= 57) || // 0-9\r\n                (cc >= 65 && cc <= 70) || // A-F\r\n                (cc >= 97 && cc <= 102)); // a-f\r\n        };\r\n        return takeChar(scnr, closure);\r\n    }\r\n    function getDigits(scnr) {\r\n        let ch = '';\r\n        let num = '';\r\n        while ((ch = takeDigit(scnr))) {\r\n            num += ch;\r\n        }\r\n        return num;\r\n    }\r\n    function readModulo(scnr) {\r\n        skipSpaces(scnr);\r\n        const ch = scnr.currentChar();\r\n        if (ch !== \"%\" /* Modulo */) {\r\n            emitError(CompileErrorCodes.EXPECTED_TOKEN, currentPosition(), 0, ch);\r\n        }\r\n        scnr.next();\r\n        return \"%\" /* Modulo */;\r\n    }\r\n    function readText(scnr) {\r\n        let buf = '';\r\n        while (true) {\r\n            const ch = scnr.currentChar();\r\n            if (ch === \"{\" /* BraceLeft */ ||\r\n                ch === \"}\" /* BraceRight */ ||\r\n                ch === \"@\" /* LinkedAlias */ ||\r\n                ch === \"|\" /* Pipe */ ||\r\n                !ch) {\r\n                break;\r\n            }\r\n            else if (ch === \"%\" /* Modulo */) {\r\n                if (isTextStart(scnr)) {\r\n                    buf += ch;\r\n                    scnr.next();\r\n                }\r\n                else {\r\n                    break;\r\n                }\r\n            }\r\n            else if (ch === CHAR_SP || ch === CHAR_LF) {\r\n                if (isTextStart(scnr)) {\r\n                    buf += ch;\r\n                    scnr.next();\r\n                }\r\n                else if (isPluralStart(scnr)) {\r\n                    break;\r\n                }\r\n                else {\r\n                    buf += ch;\r\n                    scnr.next();\r\n                }\r\n            }\r\n            else {\r\n                buf += ch;\r\n                scnr.next();\r\n            }\r\n        }\r\n        return buf;\r\n    }\r\n    function readNamedIdentifier(scnr) {\r\n        skipSpaces(scnr);\r\n        let ch = '';\r\n        let name = '';\r\n        while ((ch = takeIdentifierChar(scnr))) {\r\n            name += ch;\r\n        }\r\n        if (scnr.currentChar() === EOF) {\r\n            emitError(CompileErrorCodes.UNTERMINATED_CLOSING_BRACE, currentPosition(), 0);\r\n        }\r\n        return name;\r\n    }\r\n    function readListIdentifier(scnr) {\r\n        skipSpaces(scnr);\r\n        let value = '';\r\n        if (scnr.currentChar() === '-') {\r\n            scnr.next();\r\n            value += `-${getDigits(scnr)}`;\r\n        }\r\n        else {\r\n            value += getDigits(scnr);\r\n        }\r\n        if (scnr.currentChar() === EOF) {\r\n            emitError(CompileErrorCodes.UNTERMINATED_CLOSING_BRACE, currentPosition(), 0);\r\n        }\r\n        return value;\r\n    }\r\n    function readLiteral(scnr) {\r\n        skipSpaces(scnr);\r\n        eat(scnr, `\\'`);\r\n        let ch = '';\r\n        let literal = '';\r\n        const fn = (x) => x !== LITERAL_DELIMITER && x !== CHAR_LF;\r\n        while ((ch = takeChar(scnr, fn))) {\r\n            if (ch === '\\\\') {\r\n                literal += readEscapeSequence(scnr);\r\n            }\r\n            else {\r\n                literal += ch;\r\n            }\r\n        }\r\n        const current = scnr.currentChar();\r\n        if (current === CHAR_LF || current === EOF) {\r\n            emitError(CompileErrorCodes.UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER, currentPosition(), 0);\r\n            // TODO: Is it correct really?\r\n            if (current === CHAR_LF) {\r\n                scnr.next();\r\n                eat(scnr, `\\'`);\r\n            }\r\n            return literal;\r\n        }\r\n        eat(scnr, `\\'`);\r\n        return literal;\r\n    }\r\n    function readEscapeSequence(scnr) {\r\n        const ch = scnr.currentChar();\r\n        switch (ch) {\r\n            case '\\\\':\r\n            case `\\'`:\r\n                scnr.next();\r\n                return `\\\\${ch}`;\r\n            case 'u':\r\n                return readUnicodeEscapeSequence(scnr, ch, 4);\r\n            case 'U':\r\n                return readUnicodeEscapeSequence(scnr, ch, 6);\r\n            default:\r\n                emitError(CompileErrorCodes.UNKNOWN_ESCAPE_SEQUENCE, currentPosition(), 0, ch);\r\n                return '';\r\n        }\r\n    }\r\n    function readUnicodeEscapeSequence(scnr, unicode, digits) {\r\n        eat(scnr, unicode);\r\n        let sequence = '';\r\n        for (let i = 0; i < digits; i++) {\r\n            const ch = takeHexDigit(scnr);\r\n            if (!ch) {\r\n                emitError(CompileErrorCodes.INVALID_UNICODE_ESCAPE_SEQUENCE, currentPosition(), 0, `\\\\${unicode}${sequence}${scnr.currentChar()}`);\r\n                break;\r\n            }\r\n            sequence += ch;\r\n        }\r\n        return `\\\\${unicode}${sequence}`;\r\n    }\r\n    function readInvalidIdentifier(scnr) {\r\n        skipSpaces(scnr);\r\n        let ch = '';\r\n        let identifiers = '';\r\n        const closure = (ch) => ch !== \"{\" /* BraceLeft */ &&\r\n            ch !== \"}\" /* BraceRight */ &&\r\n            ch !== CHAR_SP &&\r\n            ch !== CHAR_LF;\r\n        while ((ch = takeChar(scnr, closure))) {\r\n            identifiers += ch;\r\n        }\r\n        return identifiers;\r\n    }\r\n    function readLinkedModifier(scnr) {\r\n        let ch = '';\r\n        let name = '';\r\n        while ((ch = takeIdentifierChar(scnr))) {\r\n            name += ch;\r\n        }\r\n        return name;\r\n    }\r\n    function readLinkedRefer(scnr) {\r\n        const fn = (detect = false, buf) => {\r\n            const ch = scnr.currentChar();\r\n            if (ch === \"{\" /* BraceLeft */ ||\r\n                ch === \"%\" /* Modulo */ ||\r\n                ch === \"@\" /* LinkedAlias */ ||\r\n                ch === \"|\" /* Pipe */ ||\r\n                !ch) {\r\n                return buf;\r\n            }\r\n            else if (ch === CHAR_SP) {\r\n                return buf;\r\n            }\r\n            else if (ch === CHAR_LF) {\r\n                buf += ch;\r\n                scnr.next();\r\n                return fn(detect, buf);\r\n            }\r\n            else {\r\n                buf += ch;\r\n                scnr.next();\r\n                return fn(true, buf);\r\n            }\r\n        };\r\n        return fn(false, '');\r\n    }\r\n    function readPlural(scnr) {\r\n        skipSpaces(scnr);\r\n        const plural = eat(scnr, \"|\" /* Pipe */);\r\n        skipSpaces(scnr);\r\n        return plural;\r\n    }\r\n    // TODO: We need refactoring of token parsing ...\r\n    function readTokenInPlaceholder(scnr, context) {\r\n        let token = null;\r\n        const ch = scnr.currentChar();\r\n        switch (ch) {\r\n            case \"{\" /* BraceLeft */:\r\n                if (context.braceNest >= 1) {\r\n                    emitError(CompileErrorCodes.NOT_ALLOW_NEST_PLACEHOLDER, currentPosition(), 0);\r\n                }\r\n                scnr.next();\r\n                token = getToken(context, 2 /* BraceLeft */, \"{\" /* BraceLeft */);\r\n                skipSpaces(scnr);\r\n                context.braceNest++;\r\n                return token;\r\n            case \"}\" /* BraceRight */:\r\n                if (context.braceNest > 0 &&\r\n                    context.currentType === 2 /* BraceLeft */) {\r\n                    emitError(CompileErrorCodes.EMPTY_PLACEHOLDER, currentPosition(), 0);\r\n                }\r\n                scnr.next();\r\n                token = getToken(context, 3 /* BraceRight */, \"}\" /* BraceRight */);\r\n                context.braceNest--;\r\n                context.braceNest > 0 && skipSpaces(scnr);\r\n                if (context.inLinked && context.braceNest === 0) {\r\n                    context.inLinked = false;\r\n                }\r\n                return token;\r\n            case \"@\" /* LinkedAlias */:\r\n                if (context.braceNest > 0) {\r\n                    emitError(CompileErrorCodes.UNTERMINATED_CLOSING_BRACE, currentPosition(), 0);\r\n                }\r\n                token = readTokenInLinked(scnr, context) || getEndToken(context);\r\n                context.braceNest = 0;\r\n                return token;\r\n            default:\r\n                let validNamedIdentifier = true;\r\n                let validListIdentifier = true;\r\n                let validLiteral = true;\r\n                if (isPluralStart(scnr)) {\r\n                    if (context.braceNest > 0) {\r\n                        emitError(CompileErrorCodes.UNTERMINATED_CLOSING_BRACE, currentPosition(), 0);\r\n                    }\r\n                    token = getToken(context, 1 /* Pipe */, readPlural(scnr));\r\n                    // reset\r\n                    context.braceNest = 0;\r\n                    context.inLinked = false;\r\n                    return token;\r\n                }\r\n                if (context.braceNest > 0 &&\r\n                    (context.currentType === 5 /* Named */ ||\r\n                        context.currentType === 6 /* List */ ||\r\n                        context.currentType === 7 /* Literal */)) {\r\n                    emitError(CompileErrorCodes.UNTERMINATED_CLOSING_BRACE, currentPosition(), 0);\r\n                    context.braceNest = 0;\r\n                    return readToken(scnr, context);\r\n                }\r\n                if ((validNamedIdentifier = isNamedIdentifierStart(scnr, context))) {\r\n                    token = getToken(context, 5 /* Named */, readNamedIdentifier(scnr));\r\n                    skipSpaces(scnr);\r\n                    return token;\r\n                }\r\n                if ((validListIdentifier = isListIdentifierStart(scnr, context))) {\r\n                    token = getToken(context, 6 /* List */, readListIdentifier(scnr));\r\n                    skipSpaces(scnr);\r\n                    return token;\r\n                }\r\n                if ((validLiteral = isLiteralStart(scnr, context))) {\r\n                    token = getToken(context, 7 /* Literal */, readLiteral(scnr));\r\n                    skipSpaces(scnr);\r\n                    return token;\r\n                }\r\n                if (!validNamedIdentifier && !validListIdentifier && !validLiteral) {\r\n                    // TODO: we should be re-designed invalid cases, when we will extend message syntax near the future ...\r\n                    token = getToken(context, 13 /* InvalidPlace */, readInvalidIdentifier(scnr));\r\n                    emitError(CompileErrorCodes.INVALID_TOKEN_IN_PLACEHOLDER, currentPosition(), 0, token.value);\r\n                    skipSpaces(scnr);\r\n                    return token;\r\n                }\r\n                break;\r\n        }\r\n        return token;\r\n    }\r\n    // TODO: We need refactoring of token parsing ...\r\n    function readTokenInLinked(scnr, context) {\r\n        const { currentType } = context;\r\n        let token = null;\r\n        const ch = scnr.currentChar();\r\n        if ((currentType === 8 /* LinkedAlias */ ||\r\n            currentType === 9 /* LinkedDot */ ||\r\n            currentType === 12 /* LinkedModifier */ ||\r\n            currentType === 10 /* LinkedDelimiter */) &&\r\n            (ch === CHAR_LF || ch === CHAR_SP)) {\r\n            emitError(CompileErrorCodes.INVALID_LINKED_FORMAT, currentPosition(), 0);\r\n        }\r\n        switch (ch) {\r\n            case \"@\" /* LinkedAlias */:\r\n                scnr.next();\r\n                token = getToken(context, 8 /* LinkedAlias */, \"@\" /* LinkedAlias */);\r\n                context.inLinked = true;\r\n                return token;\r\n            case \".\" /* LinkedDot */:\r\n                skipSpaces(scnr);\r\n                scnr.next();\r\n                return getToken(context, 9 /* LinkedDot */, \".\" /* LinkedDot */);\r\n            case \":\" /* LinkedDelimiter */:\r\n                skipSpaces(scnr);\r\n                scnr.next();\r\n                return getToken(context, 10 /* LinkedDelimiter */, \":\" /* LinkedDelimiter */);\r\n            default:\r\n                if (isPluralStart(scnr)) {\r\n                    token = getToken(context, 1 /* Pipe */, readPlural(scnr));\r\n                    // reset\r\n                    context.braceNest = 0;\r\n                    context.inLinked = false;\r\n                    return token;\r\n                }\r\n                if (isLinkedDotStart(scnr, context) ||\r\n                    isLinkedDelimiterStart(scnr, context)) {\r\n                    skipSpaces(scnr);\r\n                    return readTokenInLinked(scnr, context);\r\n                }\r\n                if (isLinkedModifierStart(scnr, context)) {\r\n                    skipSpaces(scnr);\r\n                    return getToken(context, 12 /* LinkedModifier */, readLinkedModifier(scnr));\r\n                }\r\n                if (isLinkedReferStart(scnr, context)) {\r\n                    skipSpaces(scnr);\r\n                    if (ch === \"{\" /* BraceLeft */) {\r\n                        // scan the placeholder\r\n                        return readTokenInPlaceholder(scnr, context) || token;\r\n                    }\r\n                    else {\r\n                        return getToken(context, 11 /* LinkedKey */, readLinkedRefer(scnr));\r\n                    }\r\n                }\r\n                if (currentType === 8 /* LinkedAlias */) {\r\n                    emitError(CompileErrorCodes.INVALID_LINKED_FORMAT, currentPosition(), 0);\r\n                }\r\n                context.braceNest = 0;\r\n                context.inLinked = false;\r\n                return readToken(scnr, context);\r\n        }\r\n    }\r\n    // TODO: We need refactoring of token parsing ...\r\n    function readToken(scnr, context) {\r\n        let token = { type: 14 /* EOF */ };\r\n        if (context.braceNest > 0) {\r\n            return readTokenInPlaceholder(scnr, context) || getEndToken(context);\r\n        }\r\n        if (context.inLinked) {\r\n            return readTokenInLinked(scnr, context) || getEndToken(context);\r\n        }\r\n        const ch = scnr.currentChar();\r\n        switch (ch) {\r\n            case \"{\" /* BraceLeft */:\r\n                return readTokenInPlaceholder(scnr, context) || getEndToken(context);\r\n            case \"}\" /* BraceRight */:\r\n                emitError(CompileErrorCodes.UNBALANCED_CLOSING_BRACE, currentPosition(), 0);\r\n                scnr.next();\r\n                return getToken(context, 3 /* BraceRight */, \"}\" /* BraceRight */);\r\n            case \"@\" /* LinkedAlias */:\r\n                return readTokenInLinked(scnr, context) || getEndToken(context);\r\n            default:\r\n                if (isPluralStart(scnr)) {\r\n                    token = getToken(context, 1 /* Pipe */, readPlural(scnr));\r\n                    // reset\r\n                    context.braceNest = 0;\r\n                    context.inLinked = false;\r\n                    return token;\r\n                }\r\n                const { isModulo, hasSpace } = detectModuloStart(scnr);\r\n                if (isModulo) {\r\n                    return hasSpace\r\n                        ? getToken(context, 0 /* Text */, readText(scnr))\r\n                        : getToken(context, 4 /* Modulo */, readModulo(scnr));\r\n                }\r\n                if (isTextStart(scnr)) {\r\n                    return getToken(context, 0 /* Text */, readText(scnr));\r\n                }\r\n                break;\r\n        }\r\n        return token;\r\n    }\r\n    function nextToken() {\r\n        const { currentType, offset, startLoc, endLoc } = _context;\r\n        _context.lastType = currentType;\r\n        _context.lastOffset = offset;\r\n        _context.lastStartLoc = startLoc;\r\n        _context.lastEndLoc = endLoc;\r\n        _context.offset = currentOffset();\r\n        _context.startLoc = currentPosition();\r\n        if (_scnr.currentChar() === EOF) {\r\n            return getToken(_context, 14 /* EOF */);\r\n        }\r\n        return readToken(_scnr, _context);\r\n    }\r\n    return {\r\n        nextToken,\r\n        currentOffset,\r\n        currentPosition,\r\n        context\r\n    };\r\n}\n\nconst ERROR_DOMAIN = 'parser';\r\n// Backslash backslash, backslash quote, uHHHH, UHHHHHH.\r\nconst KNOWN_ESCAPES = /(?:\\\\\\\\|\\\\'|\\\\u([0-9a-fA-F]{4})|\\\\U([0-9a-fA-F]{6}))/g;\r\nfunction fromEscapeSequence(match, codePoint4, codePoint6) {\r\n    switch (match) {\r\n        case `\\\\\\\\`:\r\n            return `\\\\`;\r\n        case `\\\\\\'`:\r\n            return `\\'`;\r\n        default: {\r\n            const codePoint = parseInt(codePoint4 || codePoint6, 16);\r\n            if (codePoint <= 0xd7ff || codePoint >= 0xe000) {\r\n                return String.fromCodePoint(codePoint);\r\n            }\r\n            // invalid ...\r\n            // Replace them with U+FFFD REPLACEMENT CHARACTER.\r\n            return '�';\r\n        }\r\n    }\r\n}\r\nfunction createParser(options = {}) {\r\n    const location = options.location !== false;\r\n    const { onError } = options;\r\n    function emitError(tokenzer, code, start, offset, ...args) {\r\n        const end = tokenzer.currentPosition();\r\n        end.offset += offset;\r\n        end.column += offset;\r\n        if (onError) {\r\n            const loc = createLocation(start, end);\r\n            const err = createCompileError(code, loc, {\r\n                domain: ERROR_DOMAIN,\r\n                args\r\n            });\r\n            onError(err);\r\n        }\r\n    }\r\n    function startNode(type, offset, loc) {\r\n        const node = {\r\n            type,\r\n            start: offset,\r\n            end: offset\r\n        };\r\n        if (location) {\r\n            node.loc = { start: loc, end: loc };\r\n        }\r\n        return node;\r\n    }\r\n    function endNode(node, offset, pos, type) {\r\n        node.end = offset;\r\n        if (type) {\r\n            node.type = type;\r\n        }\r\n        if (location && node.loc) {\r\n            node.loc.end = pos;\r\n        }\r\n    }\r\n    function parseText(tokenizer, value) {\r\n        const context = tokenizer.context();\r\n        const node = startNode(3 /* Text */, context.offset, context.startLoc);\r\n        node.value = value;\r\n        endNode(node, tokenizer.currentOffset(), tokenizer.currentPosition());\r\n        return node;\r\n    }\r\n    function parseList(tokenizer, index) {\r\n        const context = tokenizer.context();\r\n        const { lastOffset: offset, lastStartLoc: loc } = context; // get brace left loc\r\n        const node = startNode(5 /* List */, offset, loc);\r\n        node.index = parseInt(index, 10);\r\n        tokenizer.nextToken(); // skip brach right\r\n        endNode(node, tokenizer.currentOffset(), tokenizer.currentPosition());\r\n        return node;\r\n    }\r\n    function parseNamed(tokenizer, key) {\r\n        const context = tokenizer.context();\r\n        const { lastOffset: offset, lastStartLoc: loc } = context; // get brace left loc\r\n        const node = startNode(4 /* Named */, offset, loc);\r\n        node.key = key;\r\n        tokenizer.nextToken(); // skip brach right\r\n        endNode(node, tokenizer.currentOffset(), tokenizer.currentPosition());\r\n        return node;\r\n    }\r\n    function parseLiteral(tokenizer, value) {\r\n        const context = tokenizer.context();\r\n        const { lastOffset: offset, lastStartLoc: loc } = context; // get brace left loc\r\n        const node = startNode(9 /* Literal */, offset, loc);\r\n        node.value = value.replace(KNOWN_ESCAPES, fromEscapeSequence);\r\n        tokenizer.nextToken(); // skip brach right\r\n        endNode(node, tokenizer.currentOffset(), tokenizer.currentPosition());\r\n        return node;\r\n    }\r\n    function parseLinkedModifier(tokenizer) {\r\n        const token = tokenizer.nextToken();\r\n        const context = tokenizer.context();\r\n        const { lastOffset: offset, lastStartLoc: loc } = context; // get linked dot loc\r\n        const node = startNode(8 /* LinkedModifier */, offset, loc);\r\n        if (token.type !== 12 /* LinkedModifier */) {\r\n            // empty modifier\r\n            emitError(tokenizer, CompileErrorCodes.UNEXPECTED_EMPTY_LINKED_MODIFIER, context.lastStartLoc, 0);\r\n            node.value = '';\r\n            endNode(node, offset, loc);\r\n            return {\r\n                nextConsumeToken: token,\r\n                node\r\n            };\r\n        }\r\n        // check token\r\n        if (token.value == null) {\r\n            emitError(tokenizer, CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS, context.lastStartLoc, 0, getTokenCaption(token));\r\n        }\r\n        node.value = token.value || '';\r\n        endNode(node, tokenizer.currentOffset(), tokenizer.currentPosition());\r\n        return {\r\n            node\r\n        };\r\n    }\r\n    function parseLinkedKey(tokenizer, value) {\r\n        const context = tokenizer.context();\r\n        const node = startNode(7 /* LinkedKey */, context.offset, context.startLoc);\r\n        node.value = value;\r\n        endNode(node, tokenizer.currentOffset(), tokenizer.currentPosition());\r\n        return node;\r\n    }\r\n    function parseLinked(tokenizer) {\r\n        const context = tokenizer.context();\r\n        const linkedNode = startNode(6 /* Linked */, context.offset, context.startLoc);\r\n        let token = tokenizer.nextToken();\r\n        if (token.type === 9 /* LinkedDot */) {\r\n            const parsed = parseLinkedModifier(tokenizer);\r\n            linkedNode.modifier = parsed.node;\r\n            token = parsed.nextConsumeToken || tokenizer.nextToken();\r\n        }\r\n        // asset check token\r\n        if (token.type !== 10 /* LinkedDelimiter */) {\r\n            emitError(tokenizer, CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS, context.lastStartLoc, 0, getTokenCaption(token));\r\n        }\r\n        token = tokenizer.nextToken();\r\n        // skip brace left\r\n        if (token.type === 2 /* BraceLeft */) {\r\n            token = tokenizer.nextToken();\r\n        }\r\n        switch (token.type) {\r\n            case 11 /* LinkedKey */:\r\n                if (token.value == null) {\r\n                    emitError(tokenizer, CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS, context.lastStartLoc, 0, getTokenCaption(token));\r\n                }\r\n                linkedNode.key = parseLinkedKey(tokenizer, token.value || '');\r\n                break;\r\n            case 5 /* Named */:\r\n                if (token.value == null) {\r\n                    emitError(tokenizer, CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS, context.lastStartLoc, 0, getTokenCaption(token));\r\n                }\r\n                linkedNode.key = parseNamed(tokenizer, token.value || '');\r\n                break;\r\n            case 6 /* List */:\r\n                if (token.value == null) {\r\n                    emitError(tokenizer, CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS, context.lastStartLoc, 0, getTokenCaption(token));\r\n                }\r\n                linkedNode.key = parseList(tokenizer, token.value || '');\r\n                break;\r\n            case 7 /* Literal */:\r\n                if (token.value == null) {\r\n                    emitError(tokenizer, CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS, context.lastStartLoc, 0, getTokenCaption(token));\r\n                }\r\n                linkedNode.key = parseLiteral(tokenizer, token.value || '');\r\n                break;\r\n            default:\r\n                // empty key\r\n                emitError(tokenizer, CompileErrorCodes.UNEXPECTED_EMPTY_LINKED_KEY, context.lastStartLoc, 0);\r\n                const nextContext = tokenizer.context();\r\n                const emptyLinkedKeyNode = startNode(7 /* LinkedKey */, nextContext.offset, nextContext.startLoc);\r\n                emptyLinkedKeyNode.value = '';\r\n                endNode(emptyLinkedKeyNode, nextContext.offset, nextContext.startLoc);\r\n                linkedNode.key = emptyLinkedKeyNode;\r\n                endNode(linkedNode, nextContext.offset, nextContext.startLoc);\r\n                return {\r\n                    nextConsumeToken: token,\r\n                    node: linkedNode\r\n                };\r\n        }\r\n        endNode(linkedNode, tokenizer.currentOffset(), tokenizer.currentPosition());\r\n        return {\r\n            node: linkedNode\r\n        };\r\n    }\r\n    function parseMessage(tokenizer) {\r\n        const context = tokenizer.context();\r\n        const startOffset = context.currentType === 1 /* Pipe */\r\n            ? tokenizer.currentOffset()\r\n            : context.offset;\r\n        const startLoc = context.currentType === 1 /* Pipe */\r\n            ? context.endLoc\r\n            : context.startLoc;\r\n        const node = startNode(2 /* Message */, startOffset, startLoc);\r\n        node.items = [];\r\n        let nextToken = null;\r\n        do {\r\n            const token = nextToken || tokenizer.nextToken();\r\n            nextToken = null;\r\n            switch (token.type) {\r\n                case 0 /* Text */:\r\n                    if (token.value == null) {\r\n                        emitError(tokenizer, CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS, context.lastStartLoc, 0, getTokenCaption(token));\r\n                    }\r\n                    node.items.push(parseText(tokenizer, token.value || ''));\r\n                    break;\r\n                case 6 /* List */:\r\n                    if (token.value == null) {\r\n                        emitError(tokenizer, CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS, context.lastStartLoc, 0, getTokenCaption(token));\r\n                    }\r\n                    node.items.push(parseList(tokenizer, token.value || ''));\r\n                    break;\r\n                case 5 /* Named */:\r\n                    if (token.value == null) {\r\n                        emitError(tokenizer, CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS, context.lastStartLoc, 0, getTokenCaption(token));\r\n                    }\r\n                    node.items.push(parseNamed(tokenizer, token.value || ''));\r\n                    break;\r\n                case 7 /* Literal */:\r\n                    if (token.value == null) {\r\n                        emitError(tokenizer, CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS, context.lastStartLoc, 0, getTokenCaption(token));\r\n                    }\r\n                    node.items.push(parseLiteral(tokenizer, token.value || ''));\r\n                    break;\r\n                case 8 /* LinkedAlias */:\r\n                    const parsed = parseLinked(tokenizer);\r\n                    node.items.push(parsed.node);\r\n                    nextToken = parsed.nextConsumeToken || null;\r\n                    break;\r\n            }\r\n        } while (context.currentType !== 14 /* EOF */ &&\r\n            context.currentType !== 1 /* Pipe */);\r\n        // adjust message node loc\r\n        const endOffset = context.currentType === 1 /* Pipe */\r\n            ? context.lastOffset\r\n            : tokenizer.currentOffset();\r\n        const endLoc = context.currentType === 1 /* Pipe */\r\n            ? context.lastEndLoc\r\n            : tokenizer.currentPosition();\r\n        endNode(node, endOffset, endLoc);\r\n        return node;\r\n    }\r\n    function parsePlural(tokenizer, offset, loc, msgNode) {\r\n        const context = tokenizer.context();\r\n        let hasEmptyMessage = msgNode.items.length === 0;\r\n        const node = startNode(1 /* Plural */, offset, loc);\r\n        node.cases = [];\r\n        node.cases.push(msgNode);\r\n        do {\r\n            const msg = parseMessage(tokenizer);\r\n            if (!hasEmptyMessage) {\r\n                hasEmptyMessage = msg.items.length === 0;\r\n            }\r\n            node.cases.push(msg);\r\n        } while (context.currentType !== 14 /* EOF */);\r\n        if (hasEmptyMessage) {\r\n            emitError(tokenizer, CompileErrorCodes.MUST_HAVE_MESSAGES_IN_PLURAL, loc, 0);\r\n        }\r\n        endNode(node, tokenizer.currentOffset(), tokenizer.currentPosition());\r\n        return node;\r\n    }\r\n    function parseResource(tokenizer) {\r\n        const context = tokenizer.context();\r\n        const { offset, startLoc } = context;\r\n        const msgNode = parseMessage(tokenizer);\r\n        if (context.currentType === 14 /* EOF */) {\r\n            return msgNode;\r\n        }\r\n        else {\r\n            return parsePlural(tokenizer, offset, startLoc, msgNode);\r\n        }\r\n    }\r\n    function parse(source) {\r\n        const tokenizer = createTokenizer(source, shared.assign({}, options));\r\n        const context = tokenizer.context();\r\n        const node = startNode(0 /* Resource */, context.offset, context.startLoc);\r\n        if (location && node.loc) {\r\n            node.loc.source = source;\r\n        }\r\n        node.body = parseResource(tokenizer);\r\n        // assert whether achieved to EOF\r\n        if (context.currentType !== 14 /* EOF */) {\r\n            emitError(tokenizer, CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS, context.lastStartLoc, 0, source[context.offset] || '');\r\n        }\r\n        endNode(node, tokenizer.currentOffset(), tokenizer.currentPosition());\r\n        return node;\r\n    }\r\n    return { parse };\r\n}\r\nfunction getTokenCaption(token) {\r\n    if (token.type === 14 /* EOF */) {\r\n        return 'EOF';\r\n    }\r\n    const name = (token.value || '').replace(/\\r?\\n/gu, '\\\\n');\r\n    return name.length > 10 ? name.slice(0, 9) + '…' : name;\r\n}\n\nfunction createTransformer(ast, options = {} // eslint-disable-line\r\n) {\r\n    const _context = {\r\n        ast,\r\n        helpers: new Set()\r\n    };\r\n    const context = () => _context;\r\n    const helper = (name) => {\r\n        _context.helpers.add(name);\r\n        return name;\r\n    };\r\n    return { context, helper };\r\n}\r\nfunction traverseNodes(nodes, transformer) {\r\n    for (let i = 0; i < nodes.length; i++) {\r\n        traverseNode(nodes[i], transformer);\r\n    }\r\n}\r\nfunction traverseNode(node, transformer) {\r\n    // TODO: if we need pre-hook of transform, should be implemented to here\r\n    switch (node.type) {\r\n        case 1 /* Plural */:\r\n            traverseNodes(node.cases, transformer);\r\n            transformer.helper(\"plural\" /* PLURAL */);\r\n            break;\r\n        case 2 /* Message */:\r\n            traverseNodes(node.items, transformer);\r\n            break;\r\n        case 6 /* Linked */:\r\n            const linked = node;\r\n            traverseNode(linked.key, transformer);\r\n            transformer.helper(\"linked\" /* LINKED */);\r\n            transformer.helper(\"type\" /* TYPE */);\r\n            break;\r\n        case 5 /* List */:\r\n            transformer.helper(\"interpolate\" /* INTERPOLATE */);\r\n            transformer.helper(\"list\" /* LIST */);\r\n            break;\r\n        case 4 /* Named */:\r\n            transformer.helper(\"interpolate\" /* INTERPOLATE */);\r\n            transformer.helper(\"named\" /* NAMED */);\r\n            break;\r\n    }\r\n    // TODO: if we need post-hook of transform, should be implemented to here\r\n}\r\n// transform AST\r\nfunction transform(ast, options = {} // eslint-disable-line\r\n) {\r\n    const transformer = createTransformer(ast);\r\n    transformer.helper(\"normalize\" /* NORMALIZE */);\r\n    // traverse\r\n    ast.body && traverseNode(ast.body, transformer);\r\n    // set meta information\r\n    const context = transformer.context();\r\n    ast.helpers = Array.from(context.helpers);\r\n}\n\nfunction createCodeGenerator(ast, options) {\r\n    const { sourceMap: sourceMap$1, filename, breakLineCode, needIndent: _needIndent } = options;\r\n    const _context = {\r\n        source: ast.loc.source,\r\n        filename,\r\n        code: '',\r\n        column: 1,\r\n        line: 1,\r\n        offset: 0,\r\n        map: undefined,\r\n        breakLineCode,\r\n        needIndent: _needIndent,\r\n        indentLevel: 0\r\n    };\r\n    const context = () => _context;\r\n    function push(code, node) {\r\n        _context.code += code;\r\n        if (_context.map) {\r\n            if (node && node.loc && node.loc !== LocationStub) {\r\n                addMapping(node.loc.start, getMappingName(node));\r\n            }\r\n            advancePositionWithSource(_context, code);\r\n        }\r\n    }\r\n    function _newline(n, withBreakLine = true) {\r\n        const _breakLineCode = withBreakLine ? breakLineCode : '';\r\n        push(_needIndent ? _breakLineCode + `  `.repeat(n) : _breakLineCode);\r\n    }\r\n    function indent(withNewLine = true) {\r\n        const level = ++_context.indentLevel;\r\n        withNewLine && _newline(level);\r\n    }\r\n    function deindent(withNewLine = true) {\r\n        const level = --_context.indentLevel;\r\n        withNewLine && _newline(level);\r\n    }\r\n    function newline() {\r\n        _newline(_context.indentLevel);\r\n    }\r\n    const helper = (key) => `_${key}`;\r\n    const needIndent = () => _context.needIndent;\r\n    function addMapping(loc, name) {\r\n        _context.map.addMapping({\r\n            name,\r\n            source: _context.filename,\r\n            original: {\r\n                line: loc.line,\r\n                column: loc.column - 1\r\n            },\r\n            generated: {\r\n                line: _context.line,\r\n                column: _context.column - 1\r\n            }\r\n        });\r\n    }\r\n    if (sourceMap$1) {\r\n        _context.map = new sourceMap.SourceMapGenerator();\r\n        _context.map.setSourceContent(filename, _context.source);\r\n    }\r\n    return {\r\n        context,\r\n        push,\r\n        indent,\r\n        deindent,\r\n        newline,\r\n        helper,\r\n        needIndent\r\n    };\r\n}\r\nfunction generateLinkedNode(generator, node) {\r\n    const { helper } = generator;\r\n    generator.push(`${helper(\"linked\" /* LINKED */)}(`);\r\n    generateNode(generator, node.key);\r\n    if (node.modifier) {\r\n        generator.push(`, `);\r\n        generateNode(generator, node.modifier);\r\n        generator.push(`, _type`);\r\n    }\r\n    else {\r\n        generator.push(`, undefined, _type`);\r\n    }\r\n    generator.push(`)`);\r\n}\r\nfunction generateMessageNode(generator, node) {\r\n    const { helper, needIndent } = generator;\r\n    generator.push(`${helper(\"normalize\" /* NORMALIZE */)}([`);\r\n    generator.indent(needIndent());\r\n    const length = node.items.length;\r\n    for (let i = 0; i < length; i++) {\r\n        generateNode(generator, node.items[i]);\r\n        if (i === length - 1) {\r\n            break;\r\n        }\r\n        generator.push(', ');\r\n    }\r\n    generator.deindent(needIndent());\r\n    generator.push('])');\r\n}\r\nfunction generatePluralNode(generator, node) {\r\n    const { helper, needIndent } = generator;\r\n    if (node.cases.length > 1) {\r\n        generator.push(`${helper(\"plural\" /* PLURAL */)}([`);\r\n        generator.indent(needIndent());\r\n        const length = node.cases.length;\r\n        for (let i = 0; i < length; i++) {\r\n            generateNode(generator, node.cases[i]);\r\n            if (i === length - 1) {\r\n                break;\r\n            }\r\n            generator.push(', ');\r\n        }\r\n        generator.deindent(needIndent());\r\n        generator.push(`])`);\r\n    }\r\n}\r\nfunction generateResource(generator, node) {\r\n    if (node.body) {\r\n        generateNode(generator, node.body);\r\n    }\r\n    else {\r\n        generator.push('null');\r\n    }\r\n}\r\nfunction generateNode(generator, node) {\r\n    const { helper } = generator;\r\n    switch (node.type) {\r\n        case 0 /* Resource */:\r\n            generateResource(generator, node);\r\n            break;\r\n        case 1 /* Plural */:\r\n            generatePluralNode(generator, node);\r\n            break;\r\n        case 2 /* Message */:\r\n            generateMessageNode(generator, node);\r\n            break;\r\n        case 6 /* Linked */:\r\n            generateLinkedNode(generator, node);\r\n            break;\r\n        case 8 /* LinkedModifier */:\r\n            generator.push(JSON.stringify(node.value), node);\r\n            break;\r\n        case 7 /* LinkedKey */:\r\n            generator.push(JSON.stringify(node.value), node);\r\n            break;\r\n        case 5 /* List */:\r\n            generator.push(`${helper(\"interpolate\" /* INTERPOLATE */)}(${helper(\"list\" /* LIST */)}(${node.index}))`, node);\r\n            break;\r\n        case 4 /* Named */:\r\n            generator.push(`${helper(\"interpolate\" /* INTERPOLATE */)}(${helper(\"named\" /* NAMED */)}(${JSON.stringify(node.key)}))`, node);\r\n            break;\r\n        case 9 /* Literal */:\r\n            generator.push(JSON.stringify(node.value), node);\r\n            break;\r\n        case 3 /* Text */:\r\n            generator.push(JSON.stringify(node.value), node);\r\n            break;\r\n        default:\r\n            {\r\n                throw new Error(`unhandled codegen node type: ${node.type}`);\r\n            }\r\n    }\r\n}\r\n// generate code from AST\r\nconst generate = (ast, options = {} // eslint-disable-line\r\n) => {\r\n    const mode = shared.isString(options.mode) ? options.mode : 'normal';\r\n    const filename = shared.isString(options.filename)\r\n        ? options.filename\r\n        : 'message.intl';\r\n    const sourceMap = !!options.sourceMap;\r\n    // prettier-ignore\r\n    const breakLineCode = options.breakLineCode != null\r\n        ? options.breakLineCode\r\n        : mode === 'arrow'\r\n            ? ';'\r\n            : '\\n';\r\n    const needIndent = options.needIndent ? options.needIndent : mode !== 'arrow';\r\n    const helpers = ast.helpers || [];\r\n    const generator = createCodeGenerator(ast, {\r\n        mode,\r\n        filename,\r\n        sourceMap,\r\n        breakLineCode,\r\n        needIndent\r\n    });\r\n    generator.push(mode === 'normal' ? `function __msg__ (ctx) {` : `(ctx) => {`);\r\n    generator.indent(needIndent);\r\n    if (helpers.length > 0) {\r\n        generator.push(`const { ${helpers.map(s => `${s}: _${s}`).join(', ')} } = ctx`);\r\n        generator.newline();\r\n    }\r\n    generator.push(`return `);\r\n    generateNode(generator, ast);\r\n    generator.deindent(needIndent);\r\n    generator.push(`}`);\r\n    const { code, map } = generator.context();\r\n    return {\r\n        ast,\r\n        code,\r\n        map: map ? map.toJSON() : undefined // eslint-disable-line @typescript-eslint/no-explicit-any\r\n    };\r\n};\r\nfunction getMappingName(node) {\r\n    switch (node.type) {\r\n        case 3 /* Text */:\r\n        case 9 /* Literal */:\r\n        case 8 /* LinkedModifier */:\r\n        case 7 /* LinkedKey */:\r\n            return node.value;\r\n        case 5 /* List */:\r\n            return node.index.toString();\r\n        case 4 /* Named */:\r\n            return node.key;\r\n        default:\r\n            return undefined;\r\n    }\r\n}\r\nfunction advancePositionWithSource(pos, source, numberOfCharacters = source.length) {\r\n    let linesCount = 0;\r\n    let lastNewLinePos = -1;\r\n    for (let i = 0; i < numberOfCharacters; i++) {\r\n        if (source.charCodeAt(i) === 10 /* newline char code */) {\r\n            linesCount++;\r\n            lastNewLinePos = i;\r\n        }\r\n    }\r\n    pos.offset += numberOfCharacters;\r\n    pos.line += linesCount;\r\n    pos.column =\r\n        lastNewLinePos === -1\r\n            ? pos.column + numberOfCharacters\r\n            : numberOfCharacters - lastNewLinePos;\r\n    return pos;\r\n}\n\nfunction baseCompile(source, options = {}) {\r\n    const assignedOptions = shared.assign({}, options);\r\n    // parse source codes\r\n    const parser = createParser(assignedOptions);\r\n    const ast = parser.parse(source);\r\n    // transform ASTs\r\n    transform(ast, assignedOptions);\r\n    // generate javascript codes\r\n    return generate(ast, assignedOptions);\r\n}\n\nexports.CompileErrorCodes = CompileErrorCodes;\nexports.ERROR_DOMAIN = ERROR_DOMAIN;\nexports.LocationStub = LocationStub;\nexports.baseCompile = baseCompile;\nexports.createCompileError = createCompileError;\nexports.createLocation = createLocation;\nexports.createParser = createParser;\nexports.createPosition = createPosition;\nexports.defaultOnError = defaultOnError;\nexports.errorMessages = errorMessages;\n", "'use strict'\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./dist/message-compiler.cjs.prod.js')\n} else {\n  module.exports = require('./dist/message-compiler.cjs.js')\n}\n", "/*!\n  * devtools-if v9.2.2\n  * (c) 2022 ka<PERSON><PERSON> ka<PERSON>\n  * Released under the MIT License.\n  */\n'use strict';\n\nObject.defineProperty(exports, '__esModule', { value: true });\n\nconst IntlifyDevToolsHooks =  {\r\n    I18nInit: 'i18n:init',\r\n    FunctionTranslate: 'function:translate'\r\n};\n\nexports.IntlifyDevToolsHooks = IntlifyDevToolsHooks;\n", "'use strict'\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./dist/devtools-if.cjs.prod.js')\n} else {\n  module.exports = require('./dist/devtools-if.cjs.js')\n}\n", "/*!\n  * core-base v9.2.2\n  * (c) 2022 ka<PERSON>ya kawa<PERSON>\n  * Released under the MIT License.\n  */\n'use strict';\n\nObject.defineProperty(exports, '__esModule', { value: true });\n\nvar messageCompiler = require('@intlify/message-compiler');\nvar shared = require('@intlify/shared');\nvar devtoolsIf = require('@intlify/devtools-if');\n\nconst pathStateMachine =  [];\r\npathStateMachine[0 /* BEFORE_PATH */] = {\r\n    [\"w\" /* WORKSPACE */]: [0 /* BEFORE_PATH */],\r\n    [\"i\" /* IDENT */]: [3 /* IN_IDENT */, 0 /* APPEND */],\r\n    [\"[\" /* LEFT_BRACKET */]: [4 /* IN_SUB_PATH */],\r\n    [\"o\" /* END_OF_FAIL */]: [7 /* AFTER_PATH */]\r\n};\r\npathStateMachine[1 /* IN_PATH */] = {\r\n    [\"w\" /* WORKSPACE */]: [1 /* IN_PATH */],\r\n    [\".\" /* DOT */]: [2 /* BEFORE_IDENT */],\r\n    [\"[\" /* LEFT_BRACKET */]: [4 /* IN_SUB_PATH */],\r\n    [\"o\" /* END_OF_FAIL */]: [7 /* AFTER_PATH */]\r\n};\r\npathStateMachine[2 /* BEFORE_IDENT */] = {\r\n    [\"w\" /* WORKSPACE */]: [2 /* BEFORE_IDENT */],\r\n    [\"i\" /* IDENT */]: [3 /* IN_IDENT */, 0 /* APPEND */],\r\n    [\"0\" /* ZERO */]: [3 /* IN_IDENT */, 0 /* APPEND */]\r\n};\r\npathStateMachine[3 /* IN_IDENT */] = {\r\n    [\"i\" /* IDENT */]: [3 /* IN_IDENT */, 0 /* APPEND */],\r\n    [\"0\" /* ZERO */]: [3 /* IN_IDENT */, 0 /* APPEND */],\r\n    [\"w\" /* WORKSPACE */]: [1 /* IN_PATH */, 1 /* PUSH */],\r\n    [\".\" /* DOT */]: [2 /* BEFORE_IDENT */, 1 /* PUSH */],\r\n    [\"[\" /* LEFT_BRACKET */]: [4 /* IN_SUB_PATH */, 1 /* PUSH */],\r\n    [\"o\" /* END_OF_FAIL */]: [7 /* AFTER_PATH */, 1 /* PUSH */]\r\n};\r\npathStateMachine[4 /* IN_SUB_PATH */] = {\r\n    [\"'\" /* SINGLE_QUOTE */]: [5 /* IN_SINGLE_QUOTE */, 0 /* APPEND */],\r\n    [\"\\\"\" /* DOUBLE_QUOTE */]: [6 /* IN_DOUBLE_QUOTE */, 0 /* APPEND */],\r\n    [\"[\" /* LEFT_BRACKET */]: [\r\n        4 /* IN_SUB_PATH */,\r\n        2 /* INC_SUB_PATH_DEPTH */\r\n    ],\r\n    [\"]\" /* RIGHT_BRACKET */]: [1 /* IN_PATH */, 3 /* PUSH_SUB_PATH */],\r\n    [\"o\" /* END_OF_FAIL */]: 8 /* ERROR */,\r\n    [\"l\" /* ELSE */]: [4 /* IN_SUB_PATH */, 0 /* APPEND */]\r\n};\r\npathStateMachine[5 /* IN_SINGLE_QUOTE */] = {\r\n    [\"'\" /* SINGLE_QUOTE */]: [4 /* IN_SUB_PATH */, 0 /* APPEND */],\r\n    [\"o\" /* END_OF_FAIL */]: 8 /* ERROR */,\r\n    [\"l\" /* ELSE */]: [5 /* IN_SINGLE_QUOTE */, 0 /* APPEND */]\r\n};\r\npathStateMachine[6 /* IN_DOUBLE_QUOTE */] = {\r\n    [\"\\\"\" /* DOUBLE_QUOTE */]: [4 /* IN_SUB_PATH */, 0 /* APPEND */],\r\n    [\"o\" /* END_OF_FAIL */]: 8 /* ERROR */,\r\n    [\"l\" /* ELSE */]: [6 /* IN_DOUBLE_QUOTE */, 0 /* APPEND */]\r\n};\r\n/**\r\n * Check if an expression is a literal value.\r\n */\r\nconst literalValueRE = /^\\s?(?:true|false|-?[\\d.]+|'[^']*'|\"[^\"]*\")\\s?$/;\r\nfunction isLiteral(exp) {\r\n    return literalValueRE.test(exp);\r\n}\r\n/**\r\n * Strip quotes from a string\r\n */\r\nfunction stripQuotes(str) {\r\n    const a = str.charCodeAt(0);\r\n    const b = str.charCodeAt(str.length - 1);\r\n    return a === b && (a === 0x22 || a === 0x27) ? str.slice(1, -1) : str;\r\n}\r\n/**\r\n * Determine the type of a character in a keypath.\r\n */\r\nfunction getPathCharType(ch) {\r\n    if (ch === undefined || ch === null) {\r\n        return \"o\" /* END_OF_FAIL */;\r\n    }\r\n    const code = ch.charCodeAt(0);\r\n    switch (code) {\r\n        case 0x5b: // [\r\n        case 0x5d: // ]\r\n        case 0x2e: // .\r\n        case 0x22: // \"\r\n        case 0x27: // '\r\n            return ch;\r\n        case 0x5f: // _\r\n        case 0x24: // $\r\n        case 0x2d: // -\r\n            return \"i\" /* IDENT */;\r\n        case 0x09: // Tab (HT)\r\n        case 0x0a: // Newline (LF)\r\n        case 0x0d: // Return (CR)\r\n        case 0xa0: // No-break space (NBSP)\r\n        case 0xfeff: // Byte Order Mark (BOM)\r\n        case 0x2028: // Line Separator (LS)\r\n        case 0x2029: // Paragraph Separator (PS)\r\n            return \"w\" /* WORKSPACE */;\r\n    }\r\n    return \"i\" /* IDENT */;\r\n}\r\n/**\r\n * Format a subPath, return its plain form if it is\r\n * a literal string or number. Otherwise prepend the\r\n * dynamic indicator (*).\r\n */\r\nfunction formatSubPath(path) {\r\n    const trimmed = path.trim();\r\n    // invalid leading 0\r\n    if (path.charAt(0) === '0' && isNaN(parseInt(path))) {\r\n        return false;\r\n    }\r\n    return isLiteral(trimmed)\r\n        ? stripQuotes(trimmed)\r\n        : \"*\" /* ASTARISK */ + trimmed;\r\n}\r\n/**\r\n * Parse a string path into an array of segments\r\n */\r\nfunction parse(path) {\r\n    const keys = [];\r\n    let index = -1;\r\n    let mode = 0 /* BEFORE_PATH */;\r\n    let subPathDepth = 0;\r\n    let c;\r\n    let key; // eslint-disable-line\r\n    let newChar;\r\n    let type;\r\n    let transition;\r\n    let action;\r\n    let typeMap;\r\n    const actions = [];\r\n    actions[0 /* APPEND */] = () => {\r\n        if (key === undefined) {\r\n            key = newChar;\r\n        }\r\n        else {\r\n            key += newChar;\r\n        }\r\n    };\r\n    actions[1 /* PUSH */] = () => {\r\n        if (key !== undefined) {\r\n            keys.push(key);\r\n            key = undefined;\r\n        }\r\n    };\r\n    actions[2 /* INC_SUB_PATH_DEPTH */] = () => {\r\n        actions[0 /* APPEND */]();\r\n        subPathDepth++;\r\n    };\r\n    actions[3 /* PUSH_SUB_PATH */] = () => {\r\n        if (subPathDepth > 0) {\r\n            subPathDepth--;\r\n            mode = 4 /* IN_SUB_PATH */;\r\n            actions[0 /* APPEND */]();\r\n        }\r\n        else {\r\n            subPathDepth = 0;\r\n            if (key === undefined) {\r\n                return false;\r\n            }\r\n            key = formatSubPath(key);\r\n            if (key === false) {\r\n                return false;\r\n            }\r\n            else {\r\n                actions[1 /* PUSH */]();\r\n            }\r\n        }\r\n    };\r\n    function maybeUnescapeQuote() {\r\n        const nextChar = path[index + 1];\r\n        if ((mode === 5 /* IN_SINGLE_QUOTE */ &&\r\n            nextChar === \"'\" /* SINGLE_QUOTE */) ||\r\n            (mode === 6 /* IN_DOUBLE_QUOTE */ &&\r\n                nextChar === \"\\\"\" /* DOUBLE_QUOTE */)) {\r\n            index++;\r\n            newChar = '\\\\' + nextChar;\r\n            actions[0 /* APPEND */]();\r\n            return true;\r\n        }\r\n    }\r\n    while (mode !== null) {\r\n        index++;\r\n        c = path[index];\r\n        if (c === '\\\\' && maybeUnescapeQuote()) {\r\n            continue;\r\n        }\r\n        type = getPathCharType(c);\r\n        typeMap = pathStateMachine[mode];\r\n        transition = typeMap[type] || typeMap[\"l\" /* ELSE */] || 8 /* ERROR */;\r\n        // check parse error\r\n        if (transition === 8 /* ERROR */) {\r\n            return;\r\n        }\r\n        mode = transition[0];\r\n        if (transition[1] !== undefined) {\r\n            action = actions[transition[1]];\r\n            if (action) {\r\n                newChar = c;\r\n                if (action() === false) {\r\n                    return;\r\n                }\r\n            }\r\n        }\r\n        // check parse finish\r\n        if (mode === 7 /* AFTER_PATH */) {\r\n            return keys;\r\n        }\r\n    }\r\n}\r\n// path token cache\r\nconst cache = new Map();\r\n/**\r\n * key-value message resolver\r\n *\r\n * @remarks\r\n * Resolves messages with the key-value structure. Note that messages with a hierarchical structure such as objects cannot be resolved\r\n *\r\n * @param obj - A target object to be resolved with path\r\n * @param path - A {@link Path | path} to resolve the value of message\r\n *\r\n * @returns A resolved {@link PathValue | path value}\r\n *\r\n * @VueI18nGeneral\r\n */\r\nfunction resolveWithKeyValue(obj, path) {\r\n    return shared.isObject(obj) ? obj[path] : null;\r\n}\r\n/**\r\n * message resolver\r\n *\r\n * @remarks\r\n * Resolves messages. messages with a hierarchical structure such as objects can be resolved. This resolver is used in VueI18n as default.\r\n *\r\n * @param obj - A target object to be resolved with path\r\n * @param path - A {@link Path | path} to resolve the value of message\r\n *\r\n * @returns A resolved {@link PathValue | path value}\r\n *\r\n * @VueI18nGeneral\r\n */\r\nfunction resolveValue(obj, path) {\r\n    // check object\r\n    if (!shared.isObject(obj)) {\r\n        return null;\r\n    }\r\n    // parse path\r\n    let hit = cache.get(path);\r\n    if (!hit) {\r\n        hit = parse(path);\r\n        if (hit) {\r\n            cache.set(path, hit);\r\n        }\r\n    }\r\n    // check hit\r\n    if (!hit) {\r\n        return null;\r\n    }\r\n    // resolve path value\r\n    const len = hit.length;\r\n    let last = obj;\r\n    let i = 0;\r\n    while (i < len) {\r\n        const val = last[hit[i]];\r\n        if (val === undefined) {\r\n            return null;\r\n        }\r\n        last = val;\r\n        i++;\r\n    }\r\n    return last;\r\n}\n\nconst DEFAULT_MODIFIER = (str) => str;\r\nconst DEFAULT_MESSAGE = (ctx) => ''; // eslint-disable-line\r\nconst DEFAULT_MESSAGE_DATA_TYPE = 'text';\r\nconst DEFAULT_NORMALIZE = (values) => values.length === 0 ? '' : values.join('');\r\nconst DEFAULT_INTERPOLATE = shared.toDisplayString;\r\nfunction pluralDefault(choice, choicesLength) {\r\n    choice = Math.abs(choice);\r\n    if (choicesLength === 2) {\r\n        // prettier-ignore\r\n        return choice\r\n            ? choice > 1\r\n                ? 1\r\n                : 0\r\n            : 1;\r\n    }\r\n    return choice ? Math.min(choice, 2) : 0;\r\n}\r\nfunction getPluralIndex(options) {\r\n    // prettier-ignore\r\n    const index = shared.isNumber(options.pluralIndex)\r\n        ? options.pluralIndex\r\n        : -1;\r\n    // prettier-ignore\r\n    return options.named && (shared.isNumber(options.named.count) || shared.isNumber(options.named.n))\r\n        ? shared.isNumber(options.named.count)\r\n            ? options.named.count\r\n            : shared.isNumber(options.named.n)\r\n                ? options.named.n\r\n                : index\r\n        : index;\r\n}\r\nfunction normalizeNamed(pluralIndex, props) {\r\n    if (!props.count) {\r\n        props.count = pluralIndex;\r\n    }\r\n    if (!props.n) {\r\n        props.n = pluralIndex;\r\n    }\r\n}\r\nfunction createMessageContext(options = {}) {\r\n    const locale = options.locale;\r\n    const pluralIndex = getPluralIndex(options);\r\n    const pluralRule = shared.isObject(options.pluralRules) &&\r\n        shared.isString(locale) &&\r\n        shared.isFunction(options.pluralRules[locale])\r\n        ? options.pluralRules[locale]\r\n        : pluralDefault;\r\n    const orgPluralRule = shared.isObject(options.pluralRules) &&\r\n        shared.isString(locale) &&\r\n        shared.isFunction(options.pluralRules[locale])\r\n        ? pluralDefault\r\n        : undefined;\r\n    const plural = (messages) => {\r\n        return messages[pluralRule(pluralIndex, messages.length, orgPluralRule)];\r\n    };\r\n    const _list = options.list || [];\r\n    const list = (index) => _list[index];\r\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\r\n    const _named = options.named || {};\r\n    shared.isNumber(options.pluralIndex) && normalizeNamed(pluralIndex, _named);\r\n    const named = (key) => _named[key];\r\n    function message(key) {\r\n        // prettier-ignore\r\n        const msg = shared.isFunction(options.messages)\r\n            ? options.messages(key)\r\n            : shared.isObject(options.messages)\r\n                ? options.messages[key]\r\n                : false;\r\n        return !msg\r\n            ? options.parent\r\n                ? options.parent.message(key) // resolve from parent messages\r\n                : DEFAULT_MESSAGE\r\n            : msg;\r\n    }\r\n    const _modifier = (name) => options.modifiers\r\n        ? options.modifiers[name]\r\n        : DEFAULT_MODIFIER;\r\n    const normalize = shared.isPlainObject(options.processor) && shared.isFunction(options.processor.normalize)\r\n        ? options.processor.normalize\r\n        : DEFAULT_NORMALIZE;\r\n    const interpolate = shared.isPlainObject(options.processor) &&\r\n        shared.isFunction(options.processor.interpolate)\r\n        ? options.processor.interpolate\r\n        : DEFAULT_INTERPOLATE;\r\n    const type = shared.isPlainObject(options.processor) && shared.isString(options.processor.type)\r\n        ? options.processor.type\r\n        : DEFAULT_MESSAGE_DATA_TYPE;\r\n    const linked = (key, ...args) => {\r\n        const [arg1, arg2] = args;\r\n        let type = 'text';\r\n        let modifier = '';\r\n        if (args.length === 1) {\r\n            if (shared.isObject(arg1)) {\r\n                modifier = arg1.modifier || modifier;\r\n                type = arg1.type || type;\r\n            }\r\n            else if (shared.isString(arg1)) {\r\n                modifier = arg1 || modifier;\r\n            }\r\n        }\r\n        else if (args.length === 2) {\r\n            if (shared.isString(arg1)) {\r\n                modifier = arg1 || modifier;\r\n            }\r\n            if (shared.isString(arg2)) {\r\n                type = arg2 || type;\r\n            }\r\n        }\r\n        let msg = message(key)(ctx);\r\n        // The message in vnode resolved with linked are returned as an array by processor.nomalize\r\n        if (type === 'vnode' && shared.isArray(msg) && modifier) {\r\n            msg = msg[0];\r\n        }\r\n        return modifier ? _modifier(modifier)(msg, type) : msg;\r\n    };\r\n    const ctx = {\r\n        [\"list\" /* LIST */]: list,\r\n        [\"named\" /* NAMED */]: named,\r\n        [\"plural\" /* PLURAL */]: plural,\r\n        [\"linked\" /* LINKED */]: linked,\r\n        [\"message\" /* MESSAGE */]: message,\r\n        [\"type\" /* TYPE */]: type,\r\n        [\"interpolate\" /* INTERPOLATE */]: interpolate,\r\n        [\"normalize\" /* NORMALIZE */]: normalize\r\n    };\r\n    return ctx;\r\n}\n\nlet devtools = null;\r\nfunction setDevToolsHook(hook) {\r\n    devtools = hook;\r\n}\r\nfunction getDevToolsHook() {\r\n    return devtools;\r\n}\r\nfunction initI18nDevTools(i18n, version, meta) {\r\n    // TODO: queue if devtools is undefined\r\n    devtools &&\r\n        devtools.emit(devtoolsIf.IntlifyDevToolsHooks.I18nInit, {\r\n            timestamp: Date.now(),\r\n            i18n,\r\n            version,\r\n            meta\r\n        });\r\n}\r\nconst translateDevTools = /* #__PURE__*/ createDevToolsHook(devtoolsIf.IntlifyDevToolsHooks.FunctionTranslate);\r\nfunction createDevToolsHook(hook) {\r\n    return (payloads) => devtools && devtools.emit(hook, payloads);\r\n}\n\nconst CoreWarnCodes = {\r\n    NOT_FOUND_KEY: 1,\r\n    FALLBACK_TO_TRANSLATE: 2,\r\n    CANNOT_FORMAT_NUMBER: 3,\r\n    FALLBACK_TO_NUMBER_FORMAT: 4,\r\n    CANNOT_FORMAT_DATE: 5,\r\n    FALLBACK_TO_DATE_FORMAT: 6,\r\n    __EXTEND_POINT__: 7\r\n};\r\n/** @internal */\r\nconst warnMessages = {\r\n    [CoreWarnCodes.NOT_FOUND_KEY]: `Not found '{key}' key in '{locale}' locale messages.`,\r\n    [CoreWarnCodes.FALLBACK_TO_TRANSLATE]: `Fall back to translate '{key}' key with '{target}' locale.`,\r\n    [CoreWarnCodes.CANNOT_FORMAT_NUMBER]: `Cannot format a number value due to not supported Intl.NumberFormat.`,\r\n    [CoreWarnCodes.FALLBACK_TO_NUMBER_FORMAT]: `Fall back to number format '{key}' key with '{target}' locale.`,\r\n    [CoreWarnCodes.CANNOT_FORMAT_DATE]: `Cannot format a date value due to not supported Intl.DateTimeFormat.`,\r\n    [CoreWarnCodes.FALLBACK_TO_DATE_FORMAT]: `Fall back to datetime format '{key}' key with '{target}' locale.`\r\n};\r\nfunction getWarnMessage(code, ...args) {\r\n    return shared.format(warnMessages[code], ...args);\r\n}\n\n/**\r\n * Fallback with simple implemenation\r\n *\r\n * @remarks\r\n * A fallback locale function implemented with a simple fallback algorithm.\r\n *\r\n * Basically, it returns the value as specified in the `fallbackLocale` props, and is processed with the fallback inside intlify.\r\n *\r\n * @param ctx - A {@link CoreContext | context}\r\n * @param fallback - A {@link FallbackLocale | fallback locale}\r\n * @param start - A starting {@link Locale | locale}\r\n *\r\n * @returns Fallback locales\r\n *\r\n * @VueI18nGeneral\r\n */\r\nfunction fallbackWithSimple(ctx, fallback, start // eslint-disable-line @typescript-eslint/no-unused-vars\r\n) {\r\n    // prettier-ignore\r\n    return [...new Set([\r\n            start,\r\n            ...(shared.isArray(fallback)\r\n                ? fallback\r\n                : shared.isObject(fallback)\r\n                    ? Object.keys(fallback)\r\n                    : shared.isString(fallback)\r\n                        ? [fallback]\r\n                        : [start])\r\n        ])];\r\n}\r\n/**\r\n * Fallback with locale chain\r\n *\r\n * @remarks\r\n * A fallback locale function implemented with a fallback chain algorithm. It's used in VueI18n as default.\r\n *\r\n * @param ctx - A {@link CoreContext | context}\r\n * @param fallback - A {@link FallbackLocale | fallback locale}\r\n * @param start - A starting {@link Locale | locale}\r\n *\r\n * @returns Fallback locales\r\n *\r\n * @VueI18nSee [Fallbacking](../guide/essentials/fallback)\r\n *\r\n * @VueI18nGeneral\r\n */\r\nfunction fallbackWithLocaleChain(ctx, fallback, start) {\r\n    const startLocale = shared.isString(start) ? start : DEFAULT_LOCALE;\r\n    const context = ctx;\r\n    if (!context.__localeChainCache) {\r\n        context.__localeChainCache = new Map();\r\n    }\r\n    let chain = context.__localeChainCache.get(startLocale);\r\n    if (!chain) {\r\n        chain = [];\r\n        // first block defined by start\r\n        let block = [start];\r\n        // while any intervening block found\r\n        while (shared.isArray(block)) {\r\n            block = appendBlockToChain(chain, block, fallback);\r\n        }\r\n        // prettier-ignore\r\n        // last block defined by default\r\n        const defaults = shared.isArray(fallback) || !shared.isPlainObject(fallback)\r\n            ? fallback\r\n            : fallback['default']\r\n                ? fallback['default']\r\n                : null;\r\n        // convert defaults to array\r\n        block = shared.isString(defaults) ? [defaults] : defaults;\r\n        if (shared.isArray(block)) {\r\n            appendBlockToChain(chain, block, false);\r\n        }\r\n        context.__localeChainCache.set(startLocale, chain);\r\n    }\r\n    return chain;\r\n}\r\nfunction appendBlockToChain(chain, block, blocks) {\r\n    let follow = true;\r\n    for (let i = 0; i < block.length && shared.isBoolean(follow); i++) {\r\n        const locale = block[i];\r\n        if (shared.isString(locale)) {\r\n            follow = appendLocaleToChain(chain, block[i], blocks);\r\n        }\r\n    }\r\n    return follow;\r\n}\r\nfunction appendLocaleToChain(chain, locale, blocks) {\r\n    let follow;\r\n    const tokens = locale.split('-');\r\n    do {\r\n        const target = tokens.join('-');\r\n        follow = appendItemToChain(chain, target, blocks);\r\n        tokens.splice(-1, 1);\r\n    } while (tokens.length && follow === true);\r\n    return follow;\r\n}\r\nfunction appendItemToChain(chain, target, blocks) {\r\n    let follow = false;\r\n    if (!chain.includes(target)) {\r\n        follow = true;\r\n        if (target) {\r\n            follow = target[target.length - 1] !== '!';\r\n            const locale = target.replace(/!/g, '');\r\n            chain.push(locale);\r\n            if ((shared.isArray(blocks) || shared.isPlainObject(blocks)) &&\r\n                blocks[locale] // eslint-disable-line @typescript-eslint/no-explicit-any\r\n            ) {\r\n                // eslint-disable-next-line @typescript-eslint/no-explicit-any\r\n                follow = blocks[locale];\r\n            }\r\n        }\r\n    }\r\n    return follow;\r\n}\n\n/* eslint-disable @typescript-eslint/no-explicit-any */\r\n/**\r\n * Intlify core-base version\r\n * @internal\r\n */\r\nconst VERSION = '9.2.2';\r\nconst NOT_REOSLVED = -1;\r\nconst DEFAULT_LOCALE = 'en-US';\r\nconst MISSING_RESOLVE_VALUE = '';\r\nconst capitalize = (str) => `${str.charAt(0).toLocaleUpperCase()}${str.substr(1)}`;\r\nfunction getDefaultLinkedModifiers() {\r\n    return {\r\n        upper: (val, type) => {\r\n            // prettier-ignore\r\n            return type === 'text' && shared.isString(val)\r\n                ? val.toUpperCase()\r\n                : type === 'vnode' && shared.isObject(val) && '__v_isVNode' in val\r\n                    ? val.children.toUpperCase()\r\n                    : val;\r\n        },\r\n        lower: (val, type) => {\r\n            // prettier-ignore\r\n            return type === 'text' && shared.isString(val)\r\n                ? val.toLowerCase()\r\n                : type === 'vnode' && shared.isObject(val) && '__v_isVNode' in val\r\n                    ? val.children.toLowerCase()\r\n                    : val;\r\n        },\r\n        capitalize: (val, type) => {\r\n            // prettier-ignore\r\n            return (type === 'text' && shared.isString(val)\r\n                ? capitalize(val)\r\n                : type === 'vnode' && shared.isObject(val) && '__v_isVNode' in val\r\n                    ? capitalize(val.children)\r\n                    : val);\r\n        }\r\n    };\r\n}\r\nlet _compiler;\r\nfunction registerMessageCompiler(compiler) {\r\n    _compiler = compiler;\r\n}\r\nlet _resolver;\r\n/**\r\n * Register the message resolver\r\n *\r\n * @param resolver - A {@link MessageResolver} function\r\n *\r\n * @VueI18nGeneral\r\n */\r\nfunction registerMessageResolver(resolver) {\r\n    _resolver = resolver;\r\n}\r\nlet _fallbacker;\r\n/**\r\n * Register the locale fallbacker\r\n *\r\n * @param fallbacker - A {@link LocaleFallbacker} function\r\n *\r\n * @VueI18nGeneral\r\n */\r\nfunction registerLocaleFallbacker(fallbacker) {\r\n    _fallbacker = fallbacker;\r\n}\r\n// Additional Meta for Intlify DevTools\r\nlet _additionalMeta = null;\r\nconst setAdditionalMeta =  (meta) => {\r\n    _additionalMeta = meta;\r\n};\r\nconst getAdditionalMeta =  () => _additionalMeta;\r\nlet _fallbackContext = null;\r\nconst setFallbackContext = (context) => {\r\n    _fallbackContext = context;\r\n};\r\nconst getFallbackContext = () => _fallbackContext;\r\n// ID for CoreContext\r\nlet _cid = 0;\r\nfunction createCoreContext(options = {}) {\r\n    // setup options\r\n    const version = shared.isString(options.version) ? options.version : VERSION;\r\n    const locale = shared.isString(options.locale) ? options.locale : DEFAULT_LOCALE;\r\n    const fallbackLocale = shared.isArray(options.fallbackLocale) ||\r\n        shared.isPlainObject(options.fallbackLocale) ||\r\n        shared.isString(options.fallbackLocale) ||\r\n        options.fallbackLocale === false\r\n        ? options.fallbackLocale\r\n        : locale;\r\n    const messages = shared.isPlainObject(options.messages)\r\n        ? options.messages\r\n        : { [locale]: {} };\r\n    const datetimeFormats = shared.isPlainObject(options.datetimeFormats)\r\n            ? options.datetimeFormats\r\n            : { [locale]: {} }\r\n        ;\r\n    const numberFormats = shared.isPlainObject(options.numberFormats)\r\n            ? options.numberFormats\r\n            : { [locale]: {} }\r\n        ;\r\n    const modifiers = shared.assign({}, options.modifiers || {}, getDefaultLinkedModifiers());\r\n    const pluralRules = options.pluralRules || {};\r\n    const missing = shared.isFunction(options.missing) ? options.missing : null;\r\n    const missingWarn = shared.isBoolean(options.missingWarn) || shared.isRegExp(options.missingWarn)\r\n        ? options.missingWarn\r\n        : true;\r\n    const fallbackWarn = shared.isBoolean(options.fallbackWarn) || shared.isRegExp(options.fallbackWarn)\r\n        ? options.fallbackWarn\r\n        : true;\r\n    const fallbackFormat = !!options.fallbackFormat;\r\n    const unresolving = !!options.unresolving;\r\n    const postTranslation = shared.isFunction(options.postTranslation)\r\n        ? options.postTranslation\r\n        : null;\r\n    const processor = shared.isPlainObject(options.processor) ? options.processor : null;\r\n    const warnHtmlMessage = shared.isBoolean(options.warnHtmlMessage)\r\n        ? options.warnHtmlMessage\r\n        : true;\r\n    const escapeParameter = !!options.escapeParameter;\r\n    const messageCompiler = shared.isFunction(options.messageCompiler)\r\n        ? options.messageCompiler\r\n        : _compiler;\r\n    const messageResolver = shared.isFunction(options.messageResolver)\r\n        ? options.messageResolver\r\n        : _resolver || resolveWithKeyValue;\r\n    const localeFallbacker = shared.isFunction(options.localeFallbacker)\r\n        ? options.localeFallbacker\r\n        : _fallbacker || fallbackWithSimple;\r\n    const fallbackContext = shared.isObject(options.fallbackContext)\r\n        ? options.fallbackContext\r\n        : undefined;\r\n    const onWarn = shared.isFunction(options.onWarn) ? options.onWarn : shared.warn;\r\n    // setup internal options\r\n    const internalOptions = options;\r\n    const __datetimeFormatters = shared.isObject(internalOptions.__datetimeFormatters)\r\n            ? internalOptions.__datetimeFormatters\r\n            : new Map()\r\n        ;\r\n    const __numberFormatters = shared.isObject(internalOptions.__numberFormatters)\r\n            ? internalOptions.__numberFormatters\r\n            : new Map()\r\n        ;\r\n    const __meta = shared.isObject(internalOptions.__meta) ? internalOptions.__meta : {};\r\n    _cid++;\r\n    const context = {\r\n        version,\r\n        cid: _cid,\r\n        locale,\r\n        fallbackLocale,\r\n        messages,\r\n        modifiers,\r\n        pluralRules,\r\n        missing,\r\n        missingWarn,\r\n        fallbackWarn,\r\n        fallbackFormat,\r\n        unresolving,\r\n        postTranslation,\r\n        processor,\r\n        warnHtmlMessage,\r\n        escapeParameter,\r\n        messageCompiler,\r\n        messageResolver,\r\n        localeFallbacker,\r\n        fallbackContext,\r\n        onWarn,\r\n        __meta\r\n    };\r\n    {\r\n        context.datetimeFormats = datetimeFormats;\r\n        context.numberFormats = numberFormats;\r\n        context.__datetimeFormatters = __datetimeFormatters;\r\n        context.__numberFormatters = __numberFormatters;\r\n    }\r\n    // for vue-devtools timeline event\r\n    {\r\n        context.__v_emitter =\r\n            internalOptions.__v_emitter != null\r\n                ? internalOptions.__v_emitter\r\n                : undefined;\r\n    }\r\n    // NOTE: experimental !!\r\n    {\r\n        initI18nDevTools(context, version, __meta);\r\n    }\r\n    return context;\r\n}\r\n/** @internal */\r\nfunction isTranslateFallbackWarn(fallback, key) {\r\n    return fallback instanceof RegExp ? fallback.test(key) : fallback;\r\n}\r\n/** @internal */\r\nfunction isTranslateMissingWarn(missing, key) {\r\n    return missing instanceof RegExp ? missing.test(key) : missing;\r\n}\r\n/** @internal */\r\nfunction handleMissing(context, key, locale, missingWarn, type) {\r\n    const { missing, onWarn } = context;\r\n    // for vue-devtools timeline event\r\n    {\r\n        const emitter = context.__v_emitter;\r\n        if (emitter) {\r\n            emitter.emit(\"missing\" /* MISSING */, {\r\n                locale,\r\n                key,\r\n                type,\r\n                groupId: `${type}:${key}`\r\n            });\r\n        }\r\n    }\r\n    if (missing !== null) {\r\n        const ret = missing(context, locale, key, type);\r\n        return shared.isString(ret) ? ret : key;\r\n    }\r\n    else {\r\n        if (isTranslateMissingWarn(missingWarn, key)) {\r\n            onWarn(getWarnMessage(CoreWarnCodes.NOT_FOUND_KEY, { key, locale }));\r\n        }\r\n        return key;\r\n    }\r\n}\r\n/** @internal */\r\nfunction updateFallbackLocale(ctx, locale, fallback) {\r\n    const context = ctx;\r\n    context.__localeChainCache = new Map();\r\n    ctx.localeFallbacker(ctx, fallback, locale);\r\n}\r\n/* eslint-enable @typescript-eslint/no-explicit-any */\n\nconst RE_HTML_TAG = /<\\/?[\\w\\s=\"/.':;#-\\/]+>/;\r\nconst WARN_MESSAGE = `Detected HTML in '{source}' message. Recommend not using HTML messages to avoid XSS.`;\r\nfunction checkHtmlMessage(source, options) {\r\n    const warnHtmlMessage = shared.isBoolean(options.warnHtmlMessage)\r\n        ? options.warnHtmlMessage\r\n        : true;\r\n    if (warnHtmlMessage && RE_HTML_TAG.test(source)) {\r\n        shared.warn(shared.format(WARN_MESSAGE, { source }));\r\n    }\r\n}\r\nconst defaultOnCacheKey = (source) => source;\r\nlet compileCache = Object.create(null);\r\nfunction clearCompileCache() {\r\n    compileCache = Object.create(null);\r\n}\r\nfunction compileToFunction(source, options = {}) {\r\n    {\r\n        // check HTML message\r\n        checkHtmlMessage(source, options);\r\n        // check caches\r\n        const onCacheKey = options.onCacheKey || defaultOnCacheKey;\r\n        const key = onCacheKey(source);\r\n        const cached = compileCache[key];\r\n        if (cached) {\r\n            return cached;\r\n        }\r\n        // compile error detecting\r\n        let occurred = false;\r\n        const onError = options.onError || messageCompiler.defaultOnError;\r\n        options.onError = (err) => {\r\n            occurred = true;\r\n            onError(err);\r\n        };\r\n        // compile\r\n        const { code } = messageCompiler.baseCompile(source, options);\r\n        // evaluate function\r\n        const msg = new Function(`return ${code}`)();\r\n        // if occurred compile error, don't cache\r\n        return !occurred ? (compileCache[key] = msg) : msg;\r\n    }\r\n}\n\nlet code = messageCompiler.CompileErrorCodes.__EXTEND_POINT__;\r\nconst inc = () => ++code;\r\nconst CoreErrorCodes = {\r\n    INVALID_ARGUMENT: code,\r\n    INVALID_DATE_ARGUMENT: inc(),\r\n    INVALID_ISO_DATE_ARGUMENT: inc(),\r\n    __EXTEND_POINT__: inc() // 18\r\n};\r\nfunction createCoreError(code) {\r\n    return messageCompiler.createCompileError(code, null, { messages: errorMessages } );\r\n}\r\n/** @internal */\r\nconst errorMessages = {\r\n    [CoreErrorCodes.INVALID_ARGUMENT]: 'Invalid arguments',\r\n    [CoreErrorCodes.INVALID_DATE_ARGUMENT]: 'The date provided is an invalid Date object.' +\r\n        'Make sure your Date represents a valid date.',\r\n    [CoreErrorCodes.INVALID_ISO_DATE_ARGUMENT]: 'The argument provided is not a valid ISO date string'\r\n};\n\nconst NOOP_MESSAGE_FUNCTION = () => '';\r\nconst isMessageFunction = (val) => shared.isFunction(val);\r\n// implementation of `translate` function\r\nfunction translate(context, ...args) {\r\n    const { fallbackFormat, postTranslation, unresolving, messageCompiler, fallbackLocale, messages } = context;\r\n    const [key, options] = parseTranslateArgs(...args);\r\n    const missingWarn = shared.isBoolean(options.missingWarn)\r\n        ? options.missingWarn\r\n        : context.missingWarn;\r\n    const fallbackWarn = shared.isBoolean(options.fallbackWarn)\r\n        ? options.fallbackWarn\r\n        : context.fallbackWarn;\r\n    const escapeParameter = shared.isBoolean(options.escapeParameter)\r\n        ? options.escapeParameter\r\n        : context.escapeParameter;\r\n    const resolvedMessage = !!options.resolvedMessage;\r\n    // prettier-ignore\r\n    const defaultMsgOrKey = shared.isString(options.default) || shared.isBoolean(options.default) // default by function option\r\n        ? !shared.isBoolean(options.default)\r\n            ? options.default\r\n            : (!messageCompiler ? () => key : key)\r\n        : fallbackFormat // default by `fallbackFormat` option\r\n            ? (!messageCompiler ? () => key : key)\r\n            : '';\r\n    const enableDefaultMsg = fallbackFormat || defaultMsgOrKey !== '';\r\n    const locale = shared.isString(options.locale) ? options.locale : context.locale;\r\n    // escape params\r\n    escapeParameter && escapeParams(options);\r\n    // resolve message format\r\n    // eslint-disable-next-line prefer-const\r\n    let [formatScope, targetLocale, message] = !resolvedMessage\r\n        ? resolveMessageFormat(context, key, locale, fallbackLocale, fallbackWarn, missingWarn)\r\n        : [\r\n            key,\r\n            locale,\r\n            messages[locale] || {}\r\n        ];\r\n    // NOTE:\r\n    //  Fix to work around `ssrTransfrom` bug in Vite.\r\n    //  https://github.com/vitejs/vite/issues/4306\r\n    //  To get around this, use temporary variables.\r\n    //  https://github.com/nuxt/framework/issues/1461#issuecomment-954606243\r\n    let format = formatScope;\r\n    // if you use default message, set it as message format!\r\n    let cacheBaseKey = key;\r\n    if (!resolvedMessage &&\r\n        !(shared.isString(format) || isMessageFunction(format))) {\r\n        if (enableDefaultMsg) {\r\n            format = defaultMsgOrKey;\r\n            cacheBaseKey = format;\r\n        }\r\n    }\r\n    // checking message format and target locale\r\n    if (!resolvedMessage &&\r\n        (!(shared.isString(format) || isMessageFunction(format)) ||\r\n            !shared.isString(targetLocale))) {\r\n        return unresolving ? NOT_REOSLVED : key;\r\n    }\r\n    if (shared.isString(format) && context.messageCompiler == null) {\r\n        shared.warn(`The message format compilation is not supported in this build. ` +\r\n            `Because message compiler isn't included. ` +\r\n            `You need to pre-compilation all message format. ` +\r\n            `So translate function return '${key}'.`);\r\n        return key;\r\n    }\r\n    // setup compile error detecting\r\n    let occurred = false;\r\n    const errorDetector = () => {\r\n        occurred = true;\r\n    };\r\n    // compile message format\r\n    const msg = !isMessageFunction(format)\r\n        ? compileMessageFormat(context, key, targetLocale, format, cacheBaseKey, errorDetector)\r\n        : format;\r\n    // if occurred compile error, return the message format\r\n    if (occurred) {\r\n        return format;\r\n    }\r\n    // evaluate message with context\r\n    const ctxOptions = getMessageContextOptions(context, targetLocale, message, options);\r\n    const msgContext = createMessageContext(ctxOptions);\r\n    const messaged = evaluateMessage(context, msg, msgContext);\r\n    // if use post translation option, proceed it with handler\r\n    const ret = postTranslation\r\n        ? postTranslation(messaged, key)\r\n        : messaged;\r\n    // NOTE: experimental !!\r\n    {\r\n        // prettier-ignore\r\n        const payloads = {\r\n            timestamp: Date.now(),\r\n            key: shared.isString(key)\r\n                ? key\r\n                : isMessageFunction(format)\r\n                    ? format.key\r\n                    : '',\r\n            locale: targetLocale || (isMessageFunction(format)\r\n                ? format.locale\r\n                : ''),\r\n            format: shared.isString(format)\r\n                ? format\r\n                : isMessageFunction(format)\r\n                    ? format.source\r\n                    : '',\r\n            message: ret\r\n        };\r\n        payloads.meta = shared.assign({}, context.__meta, getAdditionalMeta() || {});\r\n        translateDevTools(payloads);\r\n    }\r\n    return ret;\r\n}\r\nfunction escapeParams(options) {\r\n    if (shared.isArray(options.list)) {\r\n        options.list = options.list.map(item => shared.isString(item) ? shared.escapeHtml(item) : item);\r\n    }\r\n    else if (shared.isObject(options.named)) {\r\n        Object.keys(options.named).forEach(key => {\r\n            if (shared.isString(options.named[key])) {\r\n                options.named[key] = shared.escapeHtml(options.named[key]);\r\n            }\r\n        });\r\n    }\r\n}\r\nfunction resolveMessageFormat(context, key, locale, fallbackLocale, fallbackWarn, missingWarn) {\r\n    const { messages, onWarn, messageResolver: resolveValue, localeFallbacker } = context;\r\n    const locales = localeFallbacker(context, fallbackLocale, locale); // eslint-disable-line @typescript-eslint/no-explicit-any\r\n    let message = {};\r\n    let targetLocale;\r\n    let format = null;\r\n    let from = locale;\r\n    let to = null;\r\n    const type = 'translate';\r\n    for (let i = 0; i < locales.length; i++) {\r\n        targetLocale = to = locales[i];\r\n        if (locale !== targetLocale &&\r\n            isTranslateFallbackWarn(fallbackWarn, key)) {\r\n            onWarn(getWarnMessage(CoreWarnCodes.FALLBACK_TO_TRANSLATE, {\r\n                key,\r\n                target: targetLocale\r\n            }));\r\n        }\r\n        // for vue-devtools timeline event\r\n        if (locale !== targetLocale) {\r\n            const emitter = context.__v_emitter;\r\n            if (emitter) {\r\n                emitter.emit(\"fallback\" /* FALBACK */, {\r\n                    type,\r\n                    key,\r\n                    from,\r\n                    to,\r\n                    groupId: `${type}:${key}`\r\n                });\r\n            }\r\n        }\r\n        message =\r\n            messages[targetLocale] || {};\r\n        // for vue-devtools timeline event\r\n        let start = null;\r\n        let startTag;\r\n        let endTag;\r\n        if (shared.inBrowser) {\r\n            start = window.performance.now();\r\n            startTag = 'intlify-message-resolve-start';\r\n            endTag = 'intlify-message-resolve-end';\r\n            shared.mark && shared.mark(startTag);\r\n        }\r\n        if ((format = resolveValue(message, key)) === null) {\r\n            // if null, resolve with object key path\r\n            format = message[key]; // eslint-disable-line @typescript-eslint/no-explicit-any\r\n        }\r\n        // for vue-devtools timeline event\r\n        if (shared.inBrowser) {\r\n            const end = window.performance.now();\r\n            const emitter = context.__v_emitter;\r\n            if (emitter && start && format) {\r\n                emitter.emit(\"message-resolve\" /* MESSAGE_RESOLVE */, {\r\n                    type: \"message-resolve\" /* MESSAGE_RESOLVE */,\r\n                    key,\r\n                    message: format,\r\n                    time: end - start,\r\n                    groupId: `${type}:${key}`\r\n                });\r\n            }\r\n            if (startTag && endTag && shared.mark && shared.measure) {\r\n                shared.mark(endTag);\r\n                shared.measure('intlify message resolve', startTag, endTag);\r\n            }\r\n        }\r\n        if (shared.isString(format) || shared.isFunction(format))\r\n            break;\r\n        const missingRet = handleMissing(context, // eslint-disable-line @typescript-eslint/no-explicit-any\r\n        key, targetLocale, missingWarn, type);\r\n        if (missingRet !== key) {\r\n            format = missingRet;\r\n        }\r\n        from = to;\r\n    }\r\n    return [format, targetLocale, message];\r\n}\r\nfunction compileMessageFormat(context, key, targetLocale, format, cacheBaseKey, errorDetector) {\r\n    const { messageCompiler, warnHtmlMessage } = context;\r\n    if (isMessageFunction(format)) {\r\n        const msg = format;\r\n        msg.locale = msg.locale || targetLocale;\r\n        msg.key = msg.key || key;\r\n        return msg;\r\n    }\r\n    if (messageCompiler == null) {\r\n        const msg = (() => format);\r\n        msg.locale = targetLocale;\r\n        msg.key = key;\r\n        return msg;\r\n    }\r\n    // for vue-devtools timeline event\r\n    let start = null;\r\n    let startTag;\r\n    let endTag;\r\n    if (shared.inBrowser) {\r\n        start = window.performance.now();\r\n        startTag = 'intlify-message-compilation-start';\r\n        endTag = 'intlify-message-compilation-end';\r\n        shared.mark && shared.mark(startTag);\r\n    }\r\n    const msg = messageCompiler(format, getCompileOptions(context, targetLocale, cacheBaseKey, format, warnHtmlMessage, errorDetector));\r\n    // for vue-devtools timeline event\r\n    if (shared.inBrowser) {\r\n        const end = window.performance.now();\r\n        const emitter = context.__v_emitter;\r\n        if (emitter && start) {\r\n            emitter.emit(\"message-compilation\" /* MESSAGE_COMPILATION */, {\r\n                type: \"message-compilation\" /* MESSAGE_COMPILATION */,\r\n                message: format,\r\n                time: end - start,\r\n                groupId: `${'translate'}:${key}`\r\n            });\r\n        }\r\n        if (startTag && endTag && shared.mark && shared.measure) {\r\n            shared.mark(endTag);\r\n            shared.measure('intlify message compilation', startTag, endTag);\r\n        }\r\n    }\r\n    msg.locale = targetLocale;\r\n    msg.key = key;\r\n    msg.source = format;\r\n    return msg;\r\n}\r\nfunction evaluateMessage(context, msg, msgCtx) {\r\n    // for vue-devtools timeline event\r\n    let start = null;\r\n    let startTag;\r\n    let endTag;\r\n    if (shared.inBrowser) {\r\n        start = window.performance.now();\r\n        startTag = 'intlify-message-evaluation-start';\r\n        endTag = 'intlify-message-evaluation-end';\r\n        shared.mark && shared.mark(startTag);\r\n    }\r\n    const messaged = msg(msgCtx);\r\n    // for vue-devtools timeline event\r\n    if (shared.inBrowser) {\r\n        const end = window.performance.now();\r\n        const emitter = context.__v_emitter;\r\n        if (emitter && start) {\r\n            emitter.emit(\"message-evaluation\" /* MESSAGE_EVALUATION */, {\r\n                type: \"message-evaluation\" /* MESSAGE_EVALUATION */,\r\n                value: messaged,\r\n                time: end - start,\r\n                groupId: `${'translate'}:${msg.key}`\r\n            });\r\n        }\r\n        if (startTag && endTag && shared.mark && shared.measure) {\r\n            shared.mark(endTag);\r\n            shared.measure('intlify message evaluation', startTag, endTag);\r\n        }\r\n    }\r\n    return messaged;\r\n}\r\n/** @internal */\r\nfunction parseTranslateArgs(...args) {\r\n    const [arg1, arg2, arg3] = args;\r\n    const options = {};\r\n    if (!shared.isString(arg1) && !shared.isNumber(arg1) && !isMessageFunction(arg1)) {\r\n        throw createCoreError(CoreErrorCodes.INVALID_ARGUMENT);\r\n    }\r\n    // prettier-ignore\r\n    const key = shared.isNumber(arg1)\r\n        ? String(arg1)\r\n        : isMessageFunction(arg1)\r\n            ? arg1\r\n            : arg1;\r\n    if (shared.isNumber(arg2)) {\r\n        options.plural = arg2;\r\n    }\r\n    else if (shared.isString(arg2)) {\r\n        options.default = arg2;\r\n    }\r\n    else if (shared.isPlainObject(arg2) && !shared.isEmptyObject(arg2)) {\r\n        options.named = arg2;\r\n    }\r\n    else if (shared.isArray(arg2)) {\r\n        options.list = arg2;\r\n    }\r\n    if (shared.isNumber(arg3)) {\r\n        options.plural = arg3;\r\n    }\r\n    else if (shared.isString(arg3)) {\r\n        options.default = arg3;\r\n    }\r\n    else if (shared.isPlainObject(arg3)) {\r\n        shared.assign(options, arg3);\r\n    }\r\n    return [key, options];\r\n}\r\nfunction getCompileOptions(context, locale, key, source, warnHtmlMessage, errorDetector) {\r\n    return {\r\n        warnHtmlMessage,\r\n        onError: (err) => {\r\n            errorDetector && errorDetector(err);\r\n            {\r\n                const message = `Message compilation error: ${err.message}`;\r\n                const codeFrame = err.location &&\r\n                    shared.generateCodeFrame(source, err.location.start.offset, err.location.end.offset);\r\n                const emitter = context.__v_emitter;\r\n                if (emitter) {\r\n                    emitter.emit(\"compile-error\" /* COMPILE_ERROR */, {\r\n                        message: source,\r\n                        error: err.message,\r\n                        start: err.location && err.location.start.offset,\r\n                        end: err.location && err.location.end.offset,\r\n                        groupId: `${'translate'}:${key}`\r\n                    });\r\n                }\r\n                console.error(codeFrame ? `${message}\\n${codeFrame}` : message);\r\n            }\r\n        },\r\n        onCacheKey: (source) => shared.generateFormatCacheKey(locale, key, source)\r\n    };\r\n}\r\nfunction getMessageContextOptions(context, locale, message, options) {\r\n    const { modifiers, pluralRules, messageResolver: resolveValue, fallbackLocale, fallbackWarn, missingWarn, fallbackContext } = context;\r\n    const resolveMessage = (key) => {\r\n        let val = resolveValue(message, key);\r\n        // fallback to root context\r\n        if (val == null && fallbackContext) {\r\n            const [, , message] = resolveMessageFormat(fallbackContext, key, locale, fallbackLocale, fallbackWarn, missingWarn);\r\n            val = resolveValue(message, key);\r\n        }\r\n        if (shared.isString(val)) {\r\n            let occurred = false;\r\n            const errorDetector = () => {\r\n                occurred = true;\r\n            };\r\n            const msg = compileMessageFormat(context, key, locale, val, key, errorDetector);\r\n            return !occurred\r\n                ? msg\r\n                : NOOP_MESSAGE_FUNCTION;\r\n        }\r\n        else if (isMessageFunction(val)) {\r\n            return val;\r\n        }\r\n        else {\r\n            // TODO: should be implemented warning message\r\n            return NOOP_MESSAGE_FUNCTION;\r\n        }\r\n    };\r\n    const ctxOptions = {\r\n        locale,\r\n        modifiers,\r\n        pluralRules,\r\n        messages: resolveMessage\r\n    };\r\n    if (context.processor) {\r\n        ctxOptions.processor = context.processor;\r\n    }\r\n    if (options.list) {\r\n        ctxOptions.list = options.list;\r\n    }\r\n    if (options.named) {\r\n        ctxOptions.named = options.named;\r\n    }\r\n    if (shared.isNumber(options.plural)) {\r\n        ctxOptions.pluralIndex = options.plural;\r\n    }\r\n    return ctxOptions;\r\n}\n\nconst intlDefined = typeof Intl !== 'undefined';\r\nconst Availabilities = {\r\n    dateTimeFormat: intlDefined && typeof Intl.DateTimeFormat !== 'undefined',\r\n    numberFormat: intlDefined && typeof Intl.NumberFormat !== 'undefined'\r\n};\n\n// implementation of `datetime` function\r\nfunction datetime(context, ...args) {\r\n    const { datetimeFormats, unresolving, fallbackLocale, onWarn, localeFallbacker } = context;\r\n    const { __datetimeFormatters } = context;\r\n    if (!Availabilities.dateTimeFormat) {\r\n        onWarn(getWarnMessage(CoreWarnCodes.CANNOT_FORMAT_DATE));\r\n        return MISSING_RESOLVE_VALUE;\r\n    }\r\n    const [key, value, options, overrides] = parseDateTimeArgs(...args);\r\n    const missingWarn = shared.isBoolean(options.missingWarn)\r\n        ? options.missingWarn\r\n        : context.missingWarn;\r\n    const fallbackWarn = shared.isBoolean(options.fallbackWarn)\r\n        ? options.fallbackWarn\r\n        : context.fallbackWarn;\r\n    const part = !!options.part;\r\n    const locale = shared.isString(options.locale) ? options.locale : context.locale;\r\n    const locales = localeFallbacker(context, // eslint-disable-line @typescript-eslint/no-explicit-any\r\n    fallbackLocale, locale);\r\n    if (!shared.isString(key) || key === '') {\r\n        return new Intl.DateTimeFormat(locale, overrides).format(value);\r\n    }\r\n    // resolve format\r\n    let datetimeFormat = {};\r\n    let targetLocale;\r\n    let format = null;\r\n    let from = locale;\r\n    let to = null;\r\n    const type = 'datetime format';\r\n    for (let i = 0; i < locales.length; i++) {\r\n        targetLocale = to = locales[i];\r\n        if (locale !== targetLocale &&\r\n            isTranslateFallbackWarn(fallbackWarn, key)) {\r\n            onWarn(getWarnMessage(CoreWarnCodes.FALLBACK_TO_DATE_FORMAT, {\r\n                key,\r\n                target: targetLocale\r\n            }));\r\n        }\r\n        // for vue-devtools timeline event\r\n        if (locale !== targetLocale) {\r\n            const emitter = context.__v_emitter;\r\n            if (emitter) {\r\n                emitter.emit(\"fallback\" /* FALBACK */, {\r\n                    type,\r\n                    key,\r\n                    from,\r\n                    to,\r\n                    groupId: `${type}:${key}`\r\n                });\r\n            }\r\n        }\r\n        datetimeFormat =\r\n            datetimeFormats[targetLocale] || {};\r\n        format = datetimeFormat[key];\r\n        if (shared.isPlainObject(format))\r\n            break;\r\n        handleMissing(context, key, targetLocale, missingWarn, type); // eslint-disable-line @typescript-eslint/no-explicit-any\r\n        from = to;\r\n    }\r\n    // checking format and target locale\r\n    if (!shared.isPlainObject(format) || !shared.isString(targetLocale)) {\r\n        return unresolving ? NOT_REOSLVED : key;\r\n    }\r\n    let id = `${targetLocale}__${key}`;\r\n    if (!shared.isEmptyObject(overrides)) {\r\n        id = `${id}__${JSON.stringify(overrides)}`;\r\n    }\r\n    let formatter = __datetimeFormatters.get(id);\r\n    if (!formatter) {\r\n        formatter = new Intl.DateTimeFormat(targetLocale, shared.assign({}, format, overrides));\r\n        __datetimeFormatters.set(id, formatter);\r\n    }\r\n    return !part ? formatter.format(value) : formatter.formatToParts(value);\r\n}\r\n/** @internal */\r\nconst DATETIME_FORMAT_OPTIONS_KEYS = [\r\n    'localeMatcher',\r\n    'weekday',\r\n    'era',\r\n    'year',\r\n    'month',\r\n    'day',\r\n    'hour',\r\n    'minute',\r\n    'second',\r\n    'timeZoneName',\r\n    'formatMatcher',\r\n    'hour12',\r\n    'timeZone',\r\n    'dateStyle',\r\n    'timeStyle',\r\n    'calendar',\r\n    'dayPeriod',\r\n    'numberingSystem',\r\n    'hourCycle',\r\n    'fractionalSecondDigits'\r\n];\r\n/** @internal */\r\nfunction parseDateTimeArgs(...args) {\r\n    const [arg1, arg2, arg3, arg4] = args;\r\n    const options = {};\r\n    let overrides = {};\r\n    let value;\r\n    if (shared.isString(arg1)) {\r\n        // Only allow ISO strings - other date formats are often supported,\r\n        // but may cause different results in different browsers.\r\n        const matches = arg1.match(/(\\d{4}-\\d{2}-\\d{2})(T|\\s)?(.*)/);\r\n        if (!matches) {\r\n            throw createCoreError(CoreErrorCodes.INVALID_ISO_DATE_ARGUMENT);\r\n        }\r\n        // Some browsers can not parse the iso datetime separated by space,\r\n        // this is a compromise solution by replace the 'T'/' ' with 'T'\r\n        const dateTime = matches[3]\r\n            ? matches[3].trim().startsWith('T')\r\n                ? `${matches[1].trim()}${matches[3].trim()}`\r\n                : `${matches[1].trim()}T${matches[3].trim()}`\r\n            : matches[1].trim();\r\n        value = new Date(dateTime);\r\n        try {\r\n            // This will fail if the date is not valid\r\n            value.toISOString();\r\n        }\r\n        catch (e) {\r\n            throw createCoreError(CoreErrorCodes.INVALID_ISO_DATE_ARGUMENT);\r\n        }\r\n    }\r\n    else if (shared.isDate(arg1)) {\r\n        if (isNaN(arg1.getTime())) {\r\n            throw createCoreError(CoreErrorCodes.INVALID_DATE_ARGUMENT);\r\n        }\r\n        value = arg1;\r\n    }\r\n    else if (shared.isNumber(arg1)) {\r\n        value = arg1;\r\n    }\r\n    else {\r\n        throw createCoreError(CoreErrorCodes.INVALID_ARGUMENT);\r\n    }\r\n    if (shared.isString(arg2)) {\r\n        options.key = arg2;\r\n    }\r\n    else if (shared.isPlainObject(arg2)) {\r\n        Object.keys(arg2).forEach(key => {\r\n            if (DATETIME_FORMAT_OPTIONS_KEYS.includes(key)) {\r\n                overrides[key] = arg2[key];\r\n            }\r\n            else {\r\n                options[key] = arg2[key];\r\n            }\r\n        });\r\n    }\r\n    if (shared.isString(arg3)) {\r\n        options.locale = arg3;\r\n    }\r\n    else if (shared.isPlainObject(arg3)) {\r\n        overrides = arg3;\r\n    }\r\n    if (shared.isPlainObject(arg4)) {\r\n        overrides = arg4;\r\n    }\r\n    return [options.key || '', value, options, overrides];\r\n}\r\n/** @internal */\r\nfunction clearDateTimeFormat(ctx, locale, format) {\r\n    const context = ctx;\r\n    for (const key in format) {\r\n        const id = `${locale}__${key}`;\r\n        if (!context.__datetimeFormatters.has(id)) {\r\n            continue;\r\n        }\r\n        context.__datetimeFormatters.delete(id);\r\n    }\r\n}\n\n// implementation of `number` function\r\nfunction number(context, ...args) {\r\n    const { numberFormats, unresolving, fallbackLocale, onWarn, localeFallbacker } = context;\r\n    const { __numberFormatters } = context;\r\n    if (!Availabilities.numberFormat) {\r\n        onWarn(getWarnMessage(CoreWarnCodes.CANNOT_FORMAT_NUMBER));\r\n        return MISSING_RESOLVE_VALUE;\r\n    }\r\n    const [key, value, options, overrides] = parseNumberArgs(...args);\r\n    const missingWarn = shared.isBoolean(options.missingWarn)\r\n        ? options.missingWarn\r\n        : context.missingWarn;\r\n    const fallbackWarn = shared.isBoolean(options.fallbackWarn)\r\n        ? options.fallbackWarn\r\n        : context.fallbackWarn;\r\n    const part = !!options.part;\r\n    const locale = shared.isString(options.locale) ? options.locale : context.locale;\r\n    const locales = localeFallbacker(context, // eslint-disable-line @typescript-eslint/no-explicit-any\r\n    fallbackLocale, locale);\r\n    if (!shared.isString(key) || key === '') {\r\n        return new Intl.NumberFormat(locale, overrides).format(value);\r\n    }\r\n    // resolve format\r\n    let numberFormat = {};\r\n    let targetLocale;\r\n    let format = null;\r\n    let from = locale;\r\n    let to = null;\r\n    const type = 'number format';\r\n    for (let i = 0; i < locales.length; i++) {\r\n        targetLocale = to = locales[i];\r\n        if (locale !== targetLocale &&\r\n            isTranslateFallbackWarn(fallbackWarn, key)) {\r\n            onWarn(getWarnMessage(CoreWarnCodes.FALLBACK_TO_NUMBER_FORMAT, {\r\n                key,\r\n                target: targetLocale\r\n            }));\r\n        }\r\n        // for vue-devtools timeline event\r\n        if (locale !== targetLocale) {\r\n            const emitter = context.__v_emitter;\r\n            if (emitter) {\r\n                emitter.emit(\"fallback\" /* FALBACK */, {\r\n                    type,\r\n                    key,\r\n                    from,\r\n                    to,\r\n                    groupId: `${type}:${key}`\r\n                });\r\n            }\r\n        }\r\n        numberFormat =\r\n            numberFormats[targetLocale] || {};\r\n        format = numberFormat[key];\r\n        if (shared.isPlainObject(format))\r\n            break;\r\n        handleMissing(context, key, targetLocale, missingWarn, type); // eslint-disable-line @typescript-eslint/no-explicit-any\r\n        from = to;\r\n    }\r\n    // checking format and target locale\r\n    if (!shared.isPlainObject(format) || !shared.isString(targetLocale)) {\r\n        return unresolving ? NOT_REOSLVED : key;\r\n    }\r\n    let id = `${targetLocale}__${key}`;\r\n    if (!shared.isEmptyObject(overrides)) {\r\n        id = `${id}__${JSON.stringify(overrides)}`;\r\n    }\r\n    let formatter = __numberFormatters.get(id);\r\n    if (!formatter) {\r\n        formatter = new Intl.NumberFormat(targetLocale, shared.assign({}, format, overrides));\r\n        __numberFormatters.set(id, formatter);\r\n    }\r\n    return !part ? formatter.format(value) : formatter.formatToParts(value);\r\n}\r\n/** @internal */\r\nconst NUMBER_FORMAT_OPTIONS_KEYS = [\r\n    'localeMatcher',\r\n    'style',\r\n    'currency',\r\n    'currencyDisplay',\r\n    'currencySign',\r\n    'useGrouping',\r\n    'minimumIntegerDigits',\r\n    'minimumFractionDigits',\r\n    'maximumFractionDigits',\r\n    'minimumSignificantDigits',\r\n    'maximumSignificantDigits',\r\n    'compactDisplay',\r\n    'notation',\r\n    'signDisplay',\r\n    'unit',\r\n    'unitDisplay',\r\n    'roundingMode',\r\n    'roundingPriority',\r\n    'roundingIncrement',\r\n    'trailingZeroDisplay'\r\n];\r\n/** @internal */\r\nfunction parseNumberArgs(...args) {\r\n    const [arg1, arg2, arg3, arg4] = args;\r\n    const options = {};\r\n    let overrides = {};\r\n    if (!shared.isNumber(arg1)) {\r\n        throw createCoreError(CoreErrorCodes.INVALID_ARGUMENT);\r\n    }\r\n    const value = arg1;\r\n    if (shared.isString(arg2)) {\r\n        options.key = arg2;\r\n    }\r\n    else if (shared.isPlainObject(arg2)) {\r\n        Object.keys(arg2).forEach(key => {\r\n            if (NUMBER_FORMAT_OPTIONS_KEYS.includes(key)) {\r\n                overrides[key] = arg2[key];\r\n            }\r\n            else {\r\n                options[key] = arg2[key];\r\n            }\r\n        });\r\n    }\r\n    if (shared.isString(arg3)) {\r\n        options.locale = arg3;\r\n    }\r\n    else if (shared.isPlainObject(arg3)) {\r\n        overrides = arg3;\r\n    }\r\n    if (shared.isPlainObject(arg4)) {\r\n        overrides = arg4;\r\n    }\r\n    return [options.key || '', value, options, overrides];\r\n}\r\n/** @internal */\r\nfunction clearNumberFormat(ctx, locale, format) {\r\n    const context = ctx;\r\n    for (const key in format) {\r\n        const id = `${locale}__${key}`;\r\n        if (!context.__numberFormatters.has(id)) {\r\n            continue;\r\n        }\r\n        context.__numberFormatters.delete(id);\r\n    }\r\n}\n\nexports.CompileErrorCodes = messageCompiler.CompileErrorCodes;\nexports.createCompileError = messageCompiler.createCompileError;\nexports.CoreErrorCodes = CoreErrorCodes;\nexports.CoreWarnCodes = CoreWarnCodes;\nexports.DATETIME_FORMAT_OPTIONS_KEYS = DATETIME_FORMAT_OPTIONS_KEYS;\nexports.DEFAULT_LOCALE = DEFAULT_LOCALE;\nexports.DEFAULT_MESSAGE_DATA_TYPE = DEFAULT_MESSAGE_DATA_TYPE;\nexports.MISSING_RESOLVE_VALUE = MISSING_RESOLVE_VALUE;\nexports.NOT_REOSLVED = NOT_REOSLVED;\nexports.NUMBER_FORMAT_OPTIONS_KEYS = NUMBER_FORMAT_OPTIONS_KEYS;\nexports.VERSION = VERSION;\nexports.clearCompileCache = clearCompileCache;\nexports.clearDateTimeFormat = clearDateTimeFormat;\nexports.clearNumberFormat = clearNumberFormat;\nexports.compileToFunction = compileToFunction;\nexports.createCoreContext = createCoreContext;\nexports.createCoreError = createCoreError;\nexports.createMessageContext = createMessageContext;\nexports.datetime = datetime;\nexports.fallbackWithLocaleChain = fallbackWithLocaleChain;\nexports.fallbackWithSimple = fallbackWithSimple;\nexports.getAdditionalMeta = getAdditionalMeta;\nexports.getDevToolsHook = getDevToolsHook;\nexports.getFallbackContext = getFallbackContext;\nexports.getWarnMessage = getWarnMessage;\nexports.handleMissing = handleMissing;\nexports.initI18nDevTools = initI18nDevTools;\nexports.isMessageFunction = isMessageFunction;\nexports.isTranslateFallbackWarn = isTranslateFallbackWarn;\nexports.isTranslateMissingWarn = isTranslateMissingWarn;\nexports.number = number;\nexports.parse = parse;\nexports.parseDateTimeArgs = parseDateTimeArgs;\nexports.parseNumberArgs = parseNumberArgs;\nexports.parseTranslateArgs = parseTranslateArgs;\nexports.registerLocaleFallbacker = registerLocaleFallbacker;\nexports.registerMessageCompiler = registerMessageCompiler;\nexports.registerMessageResolver = registerMessageResolver;\nexports.resolveValue = resolveValue;\nexports.resolveWithKeyValue = resolveWithKeyValue;\nexports.setAdditionalMeta = setAdditionalMeta;\nexports.setDevToolsHook = setDevToolsHook;\nexports.setFallbackContext = setFallbackContext;\nexports.translate = translate;\nexports.translateDevTools = translateDevTools;\nexports.updateFallbackLocale = updateFallbackLocale;\n", "'use strict'\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./dist/core-base.cjs.prod.js')\n} else {\n  module.exports = require('./dist/core-base.cjs.js')\n}\n", "/*!\n  * vue-i18n v9.2.2\n  * (c) 2022 kazuya kawa<PERSON>\n  * Released under the MIT License.\n  */\n'use strict';\n\nObject.defineProperty(exports, '__esModule', { value: true });\n\nvar shared = require('@intlify/shared');\nvar coreBase = require('@intlify/core-base');\nvar vue = require('vue');\n\n/**\r\n * Vue I18n Version\r\n *\r\n * @remarks\r\n * Semver format. Same format as the package.json `version` field.\r\n *\r\n * @VueI18nGeneral\r\n */\r\nconst VERSION = '9.2.2';\n\nlet code$1 = coreBase.CoreWarnCodes.__EXTEND_POINT__;\r\nconst inc$1 = () => ++code$1;\r\nconst I18nWarnCodes = {\r\n    FALLBACK_TO_ROOT: code$1,\r\n    NOT_SUPPORTED_PRESERVE: inc$1(),\r\n    NOT_SUPPORTED_FORMATTER: inc$1(),\r\n    NOT_SUPPORTED_PRESERVE_DIRECTIVE: inc$1(),\r\n    NOT_SUPPORTED_GET_CHOICE_INDEX: inc$1(),\r\n    COMPONENT_NAME_LEGACY_COMPATIBLE: inc$1(),\r\n    NOT_FOUND_PARENT_SCOPE: inc$1() // 13\r\n};\r\nconst warnMessages = {\r\n    [I18nWarnCodes.FALLBACK_TO_ROOT]: `Fall back to {type} '{key}' with root locale.`,\r\n    [I18nWarnCodes.NOT_SUPPORTED_PRESERVE]: `Not supported 'preserve'.`,\r\n    [I18nWarnCodes.NOT_SUPPORTED_FORMATTER]: `Not supported 'formatter'.`,\r\n    [I18nWarnCodes.NOT_SUPPORTED_PRESERVE_DIRECTIVE]: `Not supported 'preserveDirectiveContent'.`,\r\n    [I18nWarnCodes.NOT_SUPPORTED_GET_CHOICE_INDEX]: `Not supported 'getChoiceIndex'.`,\r\n    [I18nWarnCodes.COMPONENT_NAME_LEGACY_COMPATIBLE]: `Component name legacy compatible: '{name}' -> 'i18n'`,\r\n    [I18nWarnCodes.NOT_FOUND_PARENT_SCOPE]: `Not found parent scope. use the global scope.`\r\n};\r\nfunction getWarnMessage(code, ...args) {\r\n    return shared.format(warnMessages[code], ...args);\r\n}\n\nlet code = coreBase.CompileErrorCodes.__EXTEND_POINT__;\r\nconst inc = () => ++code;\r\nconst I18nErrorCodes = {\r\n    // composer module errors\r\n    UNEXPECTED_RETURN_TYPE: code,\r\n    // legacy module errors\r\n    INVALID_ARGUMENT: inc(),\r\n    // i18n module errors\r\n    MUST_BE_CALL_SETUP_TOP: inc(),\r\n    NOT_INSLALLED: inc(),\r\n    NOT_AVAILABLE_IN_LEGACY_MODE: inc(),\r\n    // directive module errors\r\n    REQUIRED_VALUE: inc(),\r\n    INVALID_VALUE: inc(),\r\n    // vue-devtools errors\r\n    CANNOT_SETUP_VUE_DEVTOOLS_PLUGIN: inc(),\r\n    NOT_INSLALLED_WITH_PROVIDE: inc(),\r\n    // unexpected error\r\n    UNEXPECTED_ERROR: inc(),\r\n    // not compatible legacy vue-i18n constructor\r\n    NOT_COMPATIBLE_LEGACY_VUE_I18N: inc(),\r\n    // bridge support vue 2.x only\r\n    BRIDGE_SUPPORT_VUE_2_ONLY: inc(),\r\n    // need to define `i18n` option in `allowComposition: true` and `useScope: 'local' at `useI18n``\r\n    MUST_DEFINE_I18N_OPTION_IN_ALLOW_COMPOSITION: inc(),\r\n    // Not available Compostion API in Legacy API mode. Please make sure that the legacy API mode is working properly\r\n    NOT_AVAILABLE_COMPOSITION_IN_LEGACY: inc(),\r\n    // for enhancement\r\n    __EXTEND_POINT__: inc() // 29\r\n};\r\nfunction createI18nError(code, ...args) {\r\n    return coreBase.createCompileError(code, null, { messages: errorMessages, args } );\r\n}\r\nconst errorMessages = {\r\n    [I18nErrorCodes.UNEXPECTED_RETURN_TYPE]: 'Unexpected return type in composer',\r\n    [I18nErrorCodes.INVALID_ARGUMENT]: 'Invalid argument',\r\n    [I18nErrorCodes.MUST_BE_CALL_SETUP_TOP]: 'Must be called at the top of a `setup` function',\r\n    [I18nErrorCodes.NOT_INSLALLED]: 'Need to install with `app.use` function',\r\n    [I18nErrorCodes.UNEXPECTED_ERROR]: 'Unexpected error',\r\n    [I18nErrorCodes.NOT_AVAILABLE_IN_LEGACY_MODE]: 'Not available in legacy mode',\r\n    [I18nErrorCodes.REQUIRED_VALUE]: `Required in value: {0}`,\r\n    [I18nErrorCodes.INVALID_VALUE]: `Invalid value`,\r\n    [I18nErrorCodes.CANNOT_SETUP_VUE_DEVTOOLS_PLUGIN]: `Cannot setup vue-devtools plugin`,\r\n    [I18nErrorCodes.NOT_INSLALLED_WITH_PROVIDE]: 'Need to install with `provide` function',\r\n    [I18nErrorCodes.NOT_COMPATIBLE_LEGACY_VUE_I18N]: 'Not compatible legacy VueI18n.',\r\n    [I18nErrorCodes.BRIDGE_SUPPORT_VUE_2_ONLY]: 'vue-i18n-bridge support Vue 2.x only',\r\n    [I18nErrorCodes.MUST_DEFINE_I18N_OPTION_IN_ALLOW_COMPOSITION]: 'Must define ‘i18n’ option or custom block in Composition API with using local scope in Legacy API mode',\r\n    [I18nErrorCodes.NOT_AVAILABLE_COMPOSITION_IN_LEGACY]: 'Not available Compostion API in Legacy API mode. Please make sure that the legacy API mode is working properly'\r\n};\n\nconst TransrateVNodeSymbol = \r\n/* #__PURE__*/ shared.makeSymbol('__transrateVNode');\r\nconst DatetimePartsSymbol = /* #__PURE__*/ shared.makeSymbol('__datetimeParts');\r\nconst NumberPartsSymbol = /* #__PURE__*/ shared.makeSymbol('__numberParts');\r\nconst EnableEmitter = /* #__PURE__*/ shared.makeSymbol('__enableEmitter');\r\nconst DisableEmitter = /* #__PURE__*/ shared.makeSymbol('__disableEmitter');\r\nconst SetPluralRulesSymbol = shared.makeSymbol('__setPluralRules');\r\nshared.makeSymbol('__intlifyMeta');\r\nconst InejctWithOption = /* #__PURE__*/ shared.makeSymbol('__injectWithOption');\r\nconst __VUE_I18N_BRIDGE__ =  '__VUE_I18N_BRIDGE__';\n\n/* eslint-disable @typescript-eslint/no-explicit-any */\r\n/**\r\n * Transform flat json in obj to normal json in obj\r\n */\r\nfunction handleFlatJson(obj) {\r\n    // check obj\r\n    if (!shared.isObject(obj)) {\r\n        return obj;\r\n    }\r\n    for (const key in obj) {\r\n        // check key\r\n        if (!shared.hasOwn(obj, key)) {\r\n            continue;\r\n        }\r\n        // handle for normal json\r\n        if (!key.includes('.')) {\r\n            // recursive process value if value is also a object\r\n            if (shared.isObject(obj[key])) {\r\n                handleFlatJson(obj[key]);\r\n            }\r\n        }\r\n        // handle for flat json, transform to normal json\r\n        else {\r\n            // go to the last object\r\n            const subKeys = key.split('.');\r\n            const lastIndex = subKeys.length - 1;\r\n            let currentObj = obj;\r\n            for (let i = 0; i < lastIndex; i++) {\r\n                if (!(subKeys[i] in currentObj)) {\r\n                    currentObj[subKeys[i]] = {};\r\n                }\r\n                currentObj = currentObj[subKeys[i]];\r\n            }\r\n            // update last object value, delete old property\r\n            currentObj[subKeys[lastIndex]] = obj[key];\r\n            delete obj[key];\r\n            // recursive process value if value is also a object\r\n            if (shared.isObject(currentObj[subKeys[lastIndex]])) {\r\n                handleFlatJson(currentObj[subKeys[lastIndex]]);\r\n            }\r\n        }\r\n    }\r\n    return obj;\r\n}\r\nfunction getLocaleMessages(locale, options) {\r\n    const { messages, __i18n, messageResolver, flatJson } = options;\r\n    // prettier-ignore\r\n    const ret = shared.isPlainObject(messages)\r\n        ? messages\r\n        : shared.isArray(__i18n)\r\n            ? {}\r\n            : { [locale]: {} };\r\n    // merge locale messages of i18n custom block\r\n    if (shared.isArray(__i18n)) {\r\n        __i18n.forEach(custom => {\r\n            if ('locale' in custom && 'resource' in custom) {\r\n                const { locale, resource } = custom;\r\n                if (locale) {\r\n                    ret[locale] = ret[locale] || {};\r\n                    deepCopy(resource, ret[locale]);\r\n                }\r\n                else {\r\n                    deepCopy(resource, ret);\r\n                }\r\n            }\r\n            else {\r\n                shared.isString(custom) && deepCopy(JSON.parse(custom), ret);\r\n            }\r\n        });\r\n    }\r\n    // handle messages for flat json\r\n    if (messageResolver == null && flatJson) {\r\n        for (const key in ret) {\r\n            if (shared.hasOwn(ret, key)) {\r\n                handleFlatJson(ret[key]);\r\n            }\r\n        }\r\n    }\r\n    return ret;\r\n}\r\nconst isNotObjectOrIsArray = (val) => !shared.isObject(val) || shared.isArray(val);\r\n// eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/explicit-module-boundary-types\r\nfunction deepCopy(src, des) {\r\n    // src and des should both be objects, and non of then can be a array\r\n    if (isNotObjectOrIsArray(src) || isNotObjectOrIsArray(des)) {\r\n        throw createI18nError(I18nErrorCodes.INVALID_VALUE);\r\n    }\r\n    for (const key in src) {\r\n        if (shared.hasOwn(src, key)) {\r\n            if (isNotObjectOrIsArray(src[key]) || isNotObjectOrIsArray(des[key])) {\r\n                // replace with src[key] when:\r\n                // src[key] or des[key] is not a object, or\r\n                // src[key] or des[key] is a array\r\n                des[key] = src[key];\r\n            }\r\n            else {\r\n                // src[key] and des[key] are both object, merge them\r\n                deepCopy(src[key], des[key]);\r\n            }\r\n        }\r\n    }\r\n}\r\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\r\nfunction getComponentOptions(instance) {\r\n    return instance.type ;\r\n}\r\nfunction adjustI18nResources(global, options, componentOptions // eslint-disable-line @typescript-eslint/no-explicit-any\r\n) {\r\n    let messages = shared.isObject(options.messages) ? options.messages : {};\r\n    if ('__i18nGlobal' in componentOptions) {\r\n        messages = getLocaleMessages(global.locale.value, {\r\n            messages,\r\n            __i18n: componentOptions.__i18nGlobal\r\n        });\r\n    }\r\n    // merge locale messages\r\n    const locales = Object.keys(messages);\r\n    if (locales.length) {\r\n        locales.forEach(locale => {\r\n            global.mergeLocaleMessage(locale, messages[locale]);\r\n        });\r\n    }\r\n    {\r\n        // merge datetime formats\r\n        if (shared.isObject(options.datetimeFormats)) {\r\n            const locales = Object.keys(options.datetimeFormats);\r\n            if (locales.length) {\r\n                locales.forEach(locale => {\r\n                    global.mergeDateTimeFormat(locale, options.datetimeFormats[locale]);\r\n                });\r\n            }\r\n        }\r\n        // merge number formats\r\n        if (shared.isObject(options.numberFormats)) {\r\n            const locales = Object.keys(options.numberFormats);\r\n            if (locales.length) {\r\n                locales.forEach(locale => {\r\n                    global.mergeNumberFormat(locale, options.numberFormats[locale]);\r\n                });\r\n            }\r\n        }\r\n    }\r\n}\r\nfunction createTextNode(key) {\r\n    return vue.createVNode(vue.Text, null, key, 0)\r\n        ;\r\n}\r\n/* eslint-enable @typescript-eslint/no-explicit-any */\n\n/* eslint-disable @typescript-eslint/no-explicit-any */\r\n// extend VNode interface\r\nconst DEVTOOLS_META = '__INTLIFY_META__';\r\nlet composerID = 0;\r\nfunction defineCoreMissingHandler(missing) {\r\n    return ((ctx, locale, key, type) => {\r\n        return missing(locale, key, vue.getCurrentInstance() || undefined, type);\r\n    });\r\n}\r\n// for Intlify DevTools\r\nconst getMetaInfo =  () => {\r\n    const instance = vue.getCurrentInstance();\r\n    let meta = null; // eslint-disable-line @typescript-eslint/no-explicit-any\r\n    return instance && (meta = getComponentOptions(instance)[DEVTOOLS_META])\r\n        ? { [DEVTOOLS_META]: meta } // eslint-disable-line @typescript-eslint/no-explicit-any\r\n        : null;\r\n};\r\n/**\r\n * Create composer interface factory\r\n *\r\n * @internal\r\n */\r\n// eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types\r\nfunction createComposer(options = {}, VueI18nLegacy) {\r\n    const { __root } = options;\r\n    const _isGlobal = __root === undefined;\r\n    let _inheritLocale = shared.isBoolean(options.inheritLocale)\r\n        ? options.inheritLocale\r\n        : true;\r\n    const _locale = vue.ref(\r\n    // prettier-ignore\r\n    __root && _inheritLocale\r\n        ? __root.locale.value\r\n        : shared.isString(options.locale)\r\n            ? options.locale\r\n            : coreBase.DEFAULT_LOCALE);\r\n    const _fallbackLocale = vue.ref(\r\n    // prettier-ignore\r\n    __root && _inheritLocale\r\n        ? __root.fallbackLocale.value\r\n        : shared.isString(options.fallbackLocale) ||\r\n            shared.isArray(options.fallbackLocale) ||\r\n            shared.isPlainObject(options.fallbackLocale) ||\r\n            options.fallbackLocale === false\r\n            ? options.fallbackLocale\r\n            : _locale.value);\r\n    const _messages = vue.ref(getLocaleMessages(_locale.value, options));\r\n    // prettier-ignore\r\n    const _datetimeFormats = vue.ref(shared.isPlainObject(options.datetimeFormats)\r\n            ? options.datetimeFormats\r\n            : { [_locale.value]: {} })\r\n        ;\r\n    // prettier-ignore\r\n    const _numberFormats = vue.ref(shared.isPlainObject(options.numberFormats)\r\n            ? options.numberFormats\r\n            : { [_locale.value]: {} })\r\n        ;\r\n    // warning suppress options\r\n    // prettier-ignore\r\n    let _missingWarn = __root\r\n        ? __root.missingWarn\r\n        : shared.isBoolean(options.missingWarn) || shared.isRegExp(options.missingWarn)\r\n            ? options.missingWarn\r\n            : true;\r\n    // prettier-ignore\r\n    let _fallbackWarn = __root\r\n        ? __root.fallbackWarn\r\n        : shared.isBoolean(options.fallbackWarn) || shared.isRegExp(options.fallbackWarn)\r\n            ? options.fallbackWarn\r\n            : true;\r\n    // prettier-ignore\r\n    let _fallbackRoot = __root\r\n        ? __root.fallbackRoot\r\n        : shared.isBoolean(options.fallbackRoot)\r\n            ? options.fallbackRoot\r\n            : true;\r\n    // configure fall back to root\r\n    let _fallbackFormat = !!options.fallbackFormat;\r\n    // runtime missing\r\n    let _missing = shared.isFunction(options.missing) ? options.missing : null;\r\n    let _runtimeMissing = shared.isFunction(options.missing)\r\n        ? defineCoreMissingHandler(options.missing)\r\n        : null;\r\n    // postTranslation handler\r\n    let _postTranslation = shared.isFunction(options.postTranslation)\r\n        ? options.postTranslation\r\n        : null;\r\n    // prettier-ignore\r\n    let _warnHtmlMessage = __root\r\n        ? __root.warnHtmlMessage\r\n        : shared.isBoolean(options.warnHtmlMessage)\r\n            ? options.warnHtmlMessage\r\n            : true;\r\n    let _escapeParameter = !!options.escapeParameter;\r\n    // custom linked modifiers\r\n    // prettier-ignore\r\n    const _modifiers = __root\r\n        ? __root.modifiers\r\n        : shared.isPlainObject(options.modifiers)\r\n            ? options.modifiers\r\n            : {};\r\n    // pluralRules\r\n    let _pluralRules = options.pluralRules || (__root && __root.pluralRules);\r\n    // runtime context\r\n    // eslint-disable-next-line prefer-const\r\n    let _context;\r\n    const getCoreContext = () => {\r\n        _isGlobal && coreBase.setFallbackContext(null);\r\n        const ctxOptions = {\r\n            version: VERSION,\r\n            locale: _locale.value,\r\n            fallbackLocale: _fallbackLocale.value,\r\n            messages: _messages.value,\r\n            modifiers: _modifiers,\r\n            pluralRules: _pluralRules,\r\n            missing: _runtimeMissing === null ? undefined : _runtimeMissing,\r\n            missingWarn: _missingWarn,\r\n            fallbackWarn: _fallbackWarn,\r\n            fallbackFormat: _fallbackFormat,\r\n            unresolving: true,\r\n            postTranslation: _postTranslation === null ? undefined : _postTranslation,\r\n            warnHtmlMessage: _warnHtmlMessage,\r\n            escapeParameter: _escapeParameter,\r\n            messageResolver: options.messageResolver,\r\n            __meta: { framework: 'vue' }\r\n        };\r\n        {\r\n            ctxOptions.datetimeFormats = _datetimeFormats.value;\r\n            ctxOptions.numberFormats = _numberFormats.value;\r\n            ctxOptions.__datetimeFormatters = shared.isPlainObject(_context)\r\n                ? _context.__datetimeFormatters\r\n                : undefined;\r\n            ctxOptions.__numberFormatters = shared.isPlainObject(_context)\r\n                ? _context.__numberFormatters\r\n                : undefined;\r\n        }\r\n        {\r\n            ctxOptions.__v_emitter = shared.isPlainObject(_context)\r\n                ? _context.__v_emitter\r\n                : undefined;\r\n        }\r\n        const ctx = coreBase.createCoreContext(ctxOptions);\r\n        _isGlobal && coreBase.setFallbackContext(ctx);\r\n        return ctx;\r\n    };\r\n    _context = getCoreContext();\r\n    coreBase.updateFallbackLocale(_context, _locale.value, _fallbackLocale.value);\r\n    // track reactivity\r\n    function trackReactivityValues() {\r\n        return [\r\n                _locale.value,\r\n                _fallbackLocale.value,\r\n                _messages.value,\r\n                _datetimeFormats.value,\r\n                _numberFormats.value\r\n            ]\r\n            ;\r\n    }\r\n    // locale\r\n    const locale = vue.computed({\r\n        get: () => _locale.value,\r\n        set: val => {\r\n            _locale.value = val;\r\n            _context.locale = _locale.value;\r\n        }\r\n    });\r\n    // fallbackLocale\r\n    const fallbackLocale = vue.computed({\r\n        get: () => _fallbackLocale.value,\r\n        set: val => {\r\n            _fallbackLocale.value = val;\r\n            _context.fallbackLocale = _fallbackLocale.value;\r\n            coreBase.updateFallbackLocale(_context, _locale.value, val);\r\n        }\r\n    });\r\n    // messages\r\n    const messages = vue.computed(() => _messages.value);\r\n    // datetimeFormats\r\n    const datetimeFormats = /* #__PURE__*/ vue.computed(() => _datetimeFormats.value);\r\n    // numberFormats\r\n    const numberFormats = /* #__PURE__*/ vue.computed(() => _numberFormats.value);\r\n    // getPostTranslationHandler\r\n    function getPostTranslationHandler() {\r\n        return shared.isFunction(_postTranslation) ? _postTranslation : null;\r\n    }\r\n    // setPostTranslationHandler\r\n    function setPostTranslationHandler(handler) {\r\n        _postTranslation = handler;\r\n        _context.postTranslation = handler;\r\n    }\r\n    // getMissingHandler\r\n    function getMissingHandler() {\r\n        return _missing;\r\n    }\r\n    // setMissingHandler\r\n    function setMissingHandler(handler) {\r\n        if (handler !== null) {\r\n            _runtimeMissing = defineCoreMissingHandler(handler);\r\n        }\r\n        _missing = handler;\r\n        _context.missing = _runtimeMissing;\r\n    }\r\n    function isResolvedTranslateMessage(type, arg // eslint-disable-line @typescript-eslint/no-explicit-any\r\n    ) {\r\n        return type !== 'translate' || !arg.resolvedMessage;\r\n    }\r\n    const wrapWithDeps = (fn, argumentParser, warnType, fallbackSuccess, fallbackFail, successCondition) => {\r\n        trackReactivityValues(); // track reactive dependency\r\n        // NOTE: experimental !!\r\n        let ret;\r\n        {\r\n            try {\r\n                coreBase.setAdditionalMeta(getMetaInfo());\r\n                if (!_isGlobal) {\r\n                    _context.fallbackContext = __root\r\n                        ? coreBase.getFallbackContext()\r\n                        : undefined;\r\n                }\r\n                ret = fn(_context);\r\n            }\r\n            finally {\r\n                coreBase.setAdditionalMeta(null);\r\n                if (!_isGlobal) {\r\n                    _context.fallbackContext = undefined;\r\n                }\r\n            }\r\n        }\r\n        if (shared.isNumber(ret) && ret === coreBase.NOT_REOSLVED) {\r\n            const [key, arg2] = argumentParser();\r\n            if (__root &&\r\n                shared.isString(key) &&\r\n                isResolvedTranslateMessage(warnType, arg2)) {\r\n                if (_fallbackRoot &&\r\n                    (coreBase.isTranslateFallbackWarn(_fallbackWarn, key) ||\r\n                        coreBase.isTranslateMissingWarn(_missingWarn, key))) {\r\n                    shared.warn(getWarnMessage(I18nWarnCodes.FALLBACK_TO_ROOT, {\r\n                        key,\r\n                        type: warnType\r\n                    }));\r\n                }\r\n                // for vue-devtools timeline event\r\n                {\r\n                    const { __v_emitter: emitter } = _context;\r\n                    if (emitter && _fallbackRoot) {\r\n                        emitter.emit(\"fallback\" /* FALBACK */, {\r\n                            type: warnType,\r\n                            key,\r\n                            to: 'global',\r\n                            groupId: `${warnType}:${key}`\r\n                        });\r\n                    }\r\n                }\r\n            }\r\n            return __root && _fallbackRoot\r\n                ? fallbackSuccess(__root)\r\n                : fallbackFail(key);\r\n        }\r\n        else if (successCondition(ret)) {\r\n            return ret;\r\n        }\r\n        else {\r\n            /* istanbul ignore next */\r\n            throw createI18nError(I18nErrorCodes.UNEXPECTED_RETURN_TYPE);\r\n        }\r\n    };\r\n    // t\r\n    function t(...args) {\r\n        return wrapWithDeps(context => Reflect.apply(coreBase.translate, null, [context, ...args]), () => coreBase.parseTranslateArgs(...args), 'translate', root => Reflect.apply(root.t, root, [...args]), key => key, val => shared.isString(val));\r\n    }\r\n    // rt\r\n    function rt(...args) {\r\n        const [arg1, arg2, arg3] = args;\r\n        if (arg3 && !shared.isObject(arg3)) {\r\n            throw createI18nError(I18nErrorCodes.INVALID_ARGUMENT);\r\n        }\r\n        return t(...[arg1, arg2, shared.assign({ resolvedMessage: true }, arg3 || {})]);\r\n    }\r\n    // d\r\n    function d(...args) {\r\n        return wrapWithDeps(context => Reflect.apply(coreBase.datetime, null, [context, ...args]), () => coreBase.parseDateTimeArgs(...args), 'datetime format', root => Reflect.apply(root.d, root, [...args]), () => coreBase.MISSING_RESOLVE_VALUE, val => shared.isString(val));\r\n    }\r\n    // n\r\n    function n(...args) {\r\n        return wrapWithDeps(context => Reflect.apply(coreBase.number, null, [context, ...args]), () => coreBase.parseNumberArgs(...args), 'number format', root => Reflect.apply(root.n, root, [...args]), () => coreBase.MISSING_RESOLVE_VALUE, val => shared.isString(val));\r\n    }\r\n    // for custom processor\r\n    function normalize(values) {\r\n        return values.map(val => shared.isString(val) || shared.isNumber(val) || shared.isBoolean(val)\r\n            ? createTextNode(String(val))\r\n            : val);\r\n    }\r\n    const interpolate = (val) => val;\r\n    const processor = {\r\n        normalize,\r\n        interpolate,\r\n        type: 'vnode'\r\n    };\r\n    // transrateVNode, using for `i18n-t` component\r\n    function transrateVNode(...args) {\r\n        return wrapWithDeps(context => {\r\n            let ret;\r\n            const _context = context;\r\n            try {\r\n                _context.processor = processor;\r\n                ret = Reflect.apply(coreBase.translate, null, [_context, ...args]);\r\n            }\r\n            finally {\r\n                _context.processor = null;\r\n            }\r\n            return ret;\r\n        }, () => coreBase.parseTranslateArgs(...args), 'translate', \r\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\r\n        root => root[TransrateVNodeSymbol](...args), key => [createTextNode(key)], val => shared.isArray(val));\r\n    }\r\n    // numberParts, using for `i18n-n` component\r\n    function numberParts(...args) {\r\n        return wrapWithDeps(context => Reflect.apply(coreBase.number, null, [context, ...args]), () => coreBase.parseNumberArgs(...args), 'number format', \r\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\r\n        root => root[NumberPartsSymbol](...args), () => [], val => shared.isString(val) || shared.isArray(val));\r\n    }\r\n    // datetimeParts, using for `i18n-d` component\r\n    function datetimeParts(...args) {\r\n        return wrapWithDeps(context => Reflect.apply(coreBase.datetime, null, [context, ...args]), () => coreBase.parseDateTimeArgs(...args), 'datetime format', \r\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\r\n        root => root[DatetimePartsSymbol](...args), () => [], val => shared.isString(val) || shared.isArray(val));\r\n    }\r\n    function setPluralRules(rules) {\r\n        _pluralRules = rules;\r\n        _context.pluralRules = _pluralRules;\r\n    }\r\n    // te\r\n    function te(key, locale) {\r\n        const targetLocale = shared.isString(locale) ? locale : _locale.value;\r\n        const message = getLocaleMessage(targetLocale);\r\n        return _context.messageResolver(message, key) !== null;\r\n    }\r\n    function resolveMessages(key) {\r\n        let messages = null;\r\n        const locales = coreBase.fallbackWithLocaleChain(_context, _fallbackLocale.value, _locale.value);\r\n        for (let i = 0; i < locales.length; i++) {\r\n            const targetLocaleMessages = _messages.value[locales[i]] || {};\r\n            const messageValue = _context.messageResolver(targetLocaleMessages, key);\r\n            if (messageValue != null) {\r\n                messages = messageValue;\r\n                break;\r\n            }\r\n        }\r\n        return messages;\r\n    }\r\n    // tm\r\n    function tm(key) {\r\n        const messages = resolveMessages(key);\r\n        // prettier-ignore\r\n        return messages != null\r\n            ? messages\r\n            : __root\r\n                ? __root.tm(key) || {}\r\n                : {};\r\n    }\r\n    // getLocaleMessage\r\n    function getLocaleMessage(locale) {\r\n        return (_messages.value[locale] || {});\r\n    }\r\n    // setLocaleMessage\r\n    function setLocaleMessage(locale, message) {\r\n        _messages.value[locale] = message;\r\n        _context.messages = _messages.value;\r\n    }\r\n    // mergeLocaleMessage\r\n    function mergeLocaleMessage(locale, message) {\r\n        _messages.value[locale] = _messages.value[locale] || {};\r\n        deepCopy(message, _messages.value[locale]);\r\n        _context.messages = _messages.value;\r\n    }\r\n    // getDateTimeFormat\r\n    function getDateTimeFormat(locale) {\r\n        return _datetimeFormats.value[locale] || {};\r\n    }\r\n    // setDateTimeFormat\r\n    function setDateTimeFormat(locale, format) {\r\n        _datetimeFormats.value[locale] = format;\r\n        _context.datetimeFormats = _datetimeFormats.value;\r\n        coreBase.clearDateTimeFormat(_context, locale, format);\r\n    }\r\n    // mergeDateTimeFormat\r\n    function mergeDateTimeFormat(locale, format) {\r\n        _datetimeFormats.value[locale] = shared.assign(_datetimeFormats.value[locale] || {}, format);\r\n        _context.datetimeFormats = _datetimeFormats.value;\r\n        coreBase.clearDateTimeFormat(_context, locale, format);\r\n    }\r\n    // getNumberFormat\r\n    function getNumberFormat(locale) {\r\n        return _numberFormats.value[locale] || {};\r\n    }\r\n    // setNumberFormat\r\n    function setNumberFormat(locale, format) {\r\n        _numberFormats.value[locale] = format;\r\n        _context.numberFormats = _numberFormats.value;\r\n        coreBase.clearNumberFormat(_context, locale, format);\r\n    }\r\n    // mergeNumberFormat\r\n    function mergeNumberFormat(locale, format) {\r\n        _numberFormats.value[locale] = shared.assign(_numberFormats.value[locale] || {}, format);\r\n        _context.numberFormats = _numberFormats.value;\r\n        coreBase.clearNumberFormat(_context, locale, format);\r\n    }\r\n    // for debug\r\n    composerID++;\r\n    // watch root locale & fallbackLocale\r\n    if (__root && shared.inBrowser) {\r\n        vue.watch(__root.locale, (val) => {\r\n            if (_inheritLocale) {\r\n                _locale.value = val;\r\n                _context.locale = val;\r\n                coreBase.updateFallbackLocale(_context, _locale.value, _fallbackLocale.value);\r\n            }\r\n        });\r\n        vue.watch(__root.fallbackLocale, (val) => {\r\n            if (_inheritLocale) {\r\n                _fallbackLocale.value = val;\r\n                _context.fallbackLocale = val;\r\n                coreBase.updateFallbackLocale(_context, _locale.value, _fallbackLocale.value);\r\n            }\r\n        });\r\n    }\r\n    // define basic composition API!\r\n    const composer = {\r\n        id: composerID,\r\n        locale,\r\n        fallbackLocale,\r\n        get inheritLocale() {\r\n            return _inheritLocale;\r\n        },\r\n        set inheritLocale(val) {\r\n            _inheritLocale = val;\r\n            if (val && __root) {\r\n                _locale.value = __root.locale.value;\r\n                _fallbackLocale.value = __root.fallbackLocale.value;\r\n                coreBase.updateFallbackLocale(_context, _locale.value, _fallbackLocale.value);\r\n            }\r\n        },\r\n        get availableLocales() {\r\n            return Object.keys(_messages.value).sort();\r\n        },\r\n        messages,\r\n        get modifiers() {\r\n            return _modifiers;\r\n        },\r\n        get pluralRules() {\r\n            return _pluralRules || {};\r\n        },\r\n        get isGlobal() {\r\n            return _isGlobal;\r\n        },\r\n        get missingWarn() {\r\n            return _missingWarn;\r\n        },\r\n        set missingWarn(val) {\r\n            _missingWarn = val;\r\n            _context.missingWarn = _missingWarn;\r\n        },\r\n        get fallbackWarn() {\r\n            return _fallbackWarn;\r\n        },\r\n        set fallbackWarn(val) {\r\n            _fallbackWarn = val;\r\n            _context.fallbackWarn = _fallbackWarn;\r\n        },\r\n        get fallbackRoot() {\r\n            return _fallbackRoot;\r\n        },\r\n        set fallbackRoot(val) {\r\n            _fallbackRoot = val;\r\n        },\r\n        get fallbackFormat() {\r\n            return _fallbackFormat;\r\n        },\r\n        set fallbackFormat(val) {\r\n            _fallbackFormat = val;\r\n            _context.fallbackFormat = _fallbackFormat;\r\n        },\r\n        get warnHtmlMessage() {\r\n            return _warnHtmlMessage;\r\n        },\r\n        set warnHtmlMessage(val) {\r\n            _warnHtmlMessage = val;\r\n            _context.warnHtmlMessage = val;\r\n        },\r\n        get escapeParameter() {\r\n            return _escapeParameter;\r\n        },\r\n        set escapeParameter(val) {\r\n            _escapeParameter = val;\r\n            _context.escapeParameter = val;\r\n        },\r\n        t,\r\n        getLocaleMessage,\r\n        setLocaleMessage,\r\n        mergeLocaleMessage,\r\n        getPostTranslationHandler,\r\n        setPostTranslationHandler,\r\n        getMissingHandler,\r\n        setMissingHandler,\r\n        [SetPluralRulesSymbol]: setPluralRules\r\n    };\r\n    {\r\n        composer.datetimeFormats = datetimeFormats;\r\n        composer.numberFormats = numberFormats;\r\n        composer.rt = rt;\r\n        composer.te = te;\r\n        composer.tm = tm;\r\n        composer.d = d;\r\n        composer.n = n;\r\n        composer.getDateTimeFormat = getDateTimeFormat;\r\n        composer.setDateTimeFormat = setDateTimeFormat;\r\n        composer.mergeDateTimeFormat = mergeDateTimeFormat;\r\n        composer.getNumberFormat = getNumberFormat;\r\n        composer.setNumberFormat = setNumberFormat;\r\n        composer.mergeNumberFormat = mergeNumberFormat;\r\n        composer[InejctWithOption] = options.__injectWithOption;\r\n        composer[TransrateVNodeSymbol] = transrateVNode;\r\n        composer[DatetimePartsSymbol] = datetimeParts;\r\n        composer[NumberPartsSymbol] = numberParts;\r\n    }\r\n    // for vue-devtools timeline event\r\n    {\r\n        composer[EnableEmitter] = (emitter) => {\r\n            _context.__v_emitter = emitter;\r\n        };\r\n        composer[DisableEmitter] = () => {\r\n            _context.__v_emitter = undefined;\r\n        };\r\n    }\r\n    return composer;\r\n}\r\n/* eslint-enable @typescript-eslint/no-explicit-any */\n\n/* eslint-disable @typescript-eslint/no-explicit-any */\r\n/**\r\n * Convert to I18n Composer Options from VueI18n Options\r\n *\r\n * @internal\r\n */\r\nfunction convertComposerOptions(options) {\r\n    const locale = shared.isString(options.locale) ? options.locale : coreBase.DEFAULT_LOCALE;\r\n    const fallbackLocale = shared.isString(options.fallbackLocale) ||\r\n        shared.isArray(options.fallbackLocale) ||\r\n        shared.isPlainObject(options.fallbackLocale) ||\r\n        options.fallbackLocale === false\r\n        ? options.fallbackLocale\r\n        : locale;\r\n    const missing = shared.isFunction(options.missing) ? options.missing : undefined;\r\n    const missingWarn = shared.isBoolean(options.silentTranslationWarn) ||\r\n        shared.isRegExp(options.silentTranslationWarn)\r\n        ? !options.silentTranslationWarn\r\n        : true;\r\n    const fallbackWarn = shared.isBoolean(options.silentFallbackWarn) ||\r\n        shared.isRegExp(options.silentFallbackWarn)\r\n        ? !options.silentFallbackWarn\r\n        : true;\r\n    const fallbackRoot = shared.isBoolean(options.fallbackRoot)\r\n        ? options.fallbackRoot\r\n        : true;\r\n    const fallbackFormat = !!options.formatFallbackMessages;\r\n    const modifiers = shared.isPlainObject(options.modifiers) ? options.modifiers : {};\r\n    const pluralizationRules = options.pluralizationRules;\r\n    const postTranslation = shared.isFunction(options.postTranslation)\r\n        ? options.postTranslation\r\n        : undefined;\r\n    const warnHtmlMessage = shared.isString(options.warnHtmlInMessage)\r\n        ? options.warnHtmlInMessage !== 'off'\r\n        : true;\r\n    const escapeParameter = !!options.escapeParameterHtml;\r\n    const inheritLocale = shared.isBoolean(options.sync) ? options.sync : true;\r\n    if (options.formatter) {\r\n        shared.warn(getWarnMessage(I18nWarnCodes.NOT_SUPPORTED_FORMATTER));\r\n    }\r\n    if (options.preserveDirectiveContent) {\r\n        shared.warn(getWarnMessage(I18nWarnCodes.NOT_SUPPORTED_PRESERVE_DIRECTIVE));\r\n    }\r\n    let messages = options.messages;\r\n    if (shared.isPlainObject(options.sharedMessages)) {\r\n        const sharedMessages = options.sharedMessages;\r\n        const locales = Object.keys(sharedMessages);\r\n        messages = locales.reduce((messages, locale) => {\r\n            const message = messages[locale] || (messages[locale] = {});\r\n            shared.assign(message, sharedMessages[locale]);\r\n            return messages;\r\n        }, (messages || {}));\r\n    }\r\n    const { __i18n, __root, __injectWithOption } = options;\r\n    const datetimeFormats = options.datetimeFormats;\r\n    const numberFormats = options.numberFormats;\r\n    const flatJson = options.flatJson;\r\n    return {\r\n        locale,\r\n        fallbackLocale,\r\n        messages,\r\n        flatJson,\r\n        datetimeFormats,\r\n        numberFormats,\r\n        missing,\r\n        missingWarn,\r\n        fallbackWarn,\r\n        fallbackRoot,\r\n        fallbackFormat,\r\n        modifiers,\r\n        pluralRules: pluralizationRules,\r\n        postTranslation,\r\n        warnHtmlMessage,\r\n        escapeParameter,\r\n        messageResolver: options.messageResolver,\r\n        inheritLocale,\r\n        __i18n,\r\n        __root,\r\n        __injectWithOption\r\n    };\r\n}\r\n/**\r\n * create VueI18n interface factory\r\n *\r\n * @internal\r\n */\r\n// eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types\r\nfunction createVueI18n(options = {}, VueI18nLegacy) {\r\n    {\r\n        const composer = createComposer(convertComposerOptions(options));\r\n        // defines VueI18n\r\n        const vueI18n = {\r\n            // id\r\n            id: composer.id,\r\n            // locale\r\n            get locale() {\r\n                return composer.locale.value;\r\n            },\r\n            set locale(val) {\r\n                composer.locale.value = val;\r\n            },\r\n            // fallbackLocale\r\n            get fallbackLocale() {\r\n                return composer.fallbackLocale.value;\r\n            },\r\n            set fallbackLocale(val) {\r\n                composer.fallbackLocale.value = val;\r\n            },\r\n            // messages\r\n            get messages() {\r\n                return composer.messages.value;\r\n            },\r\n            // datetimeFormats\r\n            get datetimeFormats() {\r\n                return composer.datetimeFormats.value;\r\n            },\r\n            // numberFormats\r\n            get numberFormats() {\r\n                return composer.numberFormats.value;\r\n            },\r\n            // availableLocales\r\n            get availableLocales() {\r\n                return composer.availableLocales;\r\n            },\r\n            // formatter\r\n            get formatter() {\r\n                shared.warn(getWarnMessage(I18nWarnCodes.NOT_SUPPORTED_FORMATTER));\r\n                // dummy\r\n                return {\r\n                    interpolate() {\r\n                        return [];\r\n                    }\r\n                };\r\n            },\r\n            set formatter(val) {\r\n                shared.warn(getWarnMessage(I18nWarnCodes.NOT_SUPPORTED_FORMATTER));\r\n            },\r\n            // missing\r\n            get missing() {\r\n                return composer.getMissingHandler();\r\n            },\r\n            set missing(handler) {\r\n                composer.setMissingHandler(handler);\r\n            },\r\n            // silentTranslationWarn\r\n            get silentTranslationWarn() {\r\n                return shared.isBoolean(composer.missingWarn)\r\n                    ? !composer.missingWarn\r\n                    : composer.missingWarn;\r\n            },\r\n            set silentTranslationWarn(val) {\r\n                composer.missingWarn = shared.isBoolean(val) ? !val : val;\r\n            },\r\n            // silentFallbackWarn\r\n            get silentFallbackWarn() {\r\n                return shared.isBoolean(composer.fallbackWarn)\r\n                    ? !composer.fallbackWarn\r\n                    : composer.fallbackWarn;\r\n            },\r\n            set silentFallbackWarn(val) {\r\n                composer.fallbackWarn = shared.isBoolean(val) ? !val : val;\r\n            },\r\n            // modifiers\r\n            get modifiers() {\r\n                return composer.modifiers;\r\n            },\r\n            // formatFallbackMessages\r\n            get formatFallbackMessages() {\r\n                return composer.fallbackFormat;\r\n            },\r\n            set formatFallbackMessages(val) {\r\n                composer.fallbackFormat = val;\r\n            },\r\n            // postTranslation\r\n            get postTranslation() {\r\n                return composer.getPostTranslationHandler();\r\n            },\r\n            set postTranslation(handler) {\r\n                composer.setPostTranslationHandler(handler);\r\n            },\r\n            // sync\r\n            get sync() {\r\n                return composer.inheritLocale;\r\n            },\r\n            set sync(val) {\r\n                composer.inheritLocale = val;\r\n            },\r\n            // warnInHtmlMessage\r\n            get warnHtmlInMessage() {\r\n                return composer.warnHtmlMessage ? 'warn' : 'off';\r\n            },\r\n            set warnHtmlInMessage(val) {\r\n                composer.warnHtmlMessage = val !== 'off';\r\n            },\r\n            // escapeParameterHtml\r\n            get escapeParameterHtml() {\r\n                return composer.escapeParameter;\r\n            },\r\n            set escapeParameterHtml(val) {\r\n                composer.escapeParameter = val;\r\n            },\r\n            // preserveDirectiveContent\r\n            get preserveDirectiveContent() {\r\n                shared.warn(getWarnMessage(I18nWarnCodes.NOT_SUPPORTED_PRESERVE_DIRECTIVE));\r\n                return true;\r\n            },\r\n            set preserveDirectiveContent(val) {\r\n                shared.warn(getWarnMessage(I18nWarnCodes.NOT_SUPPORTED_PRESERVE_DIRECTIVE));\r\n            },\r\n            // pluralizationRules\r\n            get pluralizationRules() {\r\n                return composer.pluralRules || {};\r\n            },\r\n            // for internal\r\n            __composer: composer,\r\n            // t\r\n            t(...args) {\r\n                const [arg1, arg2, arg3] = args;\r\n                const options = {};\r\n                let list = null;\r\n                let named = null;\r\n                if (!shared.isString(arg1)) {\r\n                    throw createI18nError(I18nErrorCodes.INVALID_ARGUMENT);\r\n                }\r\n                const key = arg1;\r\n                if (shared.isString(arg2)) {\r\n                    options.locale = arg2;\r\n                }\r\n                else if (shared.isArray(arg2)) {\r\n                    list = arg2;\r\n                }\r\n                else if (shared.isPlainObject(arg2)) {\r\n                    named = arg2;\r\n                }\r\n                if (shared.isArray(arg3)) {\r\n                    list = arg3;\r\n                }\r\n                else if (shared.isPlainObject(arg3)) {\r\n                    named = arg3;\r\n                }\r\n                // return composer.t(key, (list || named || {}) as any, options)\r\n                return Reflect.apply(composer.t, composer, [\r\n                    key,\r\n                    (list || named || {}),\r\n                    options\r\n                ]);\r\n            },\r\n            rt(...args) {\r\n                return Reflect.apply(composer.rt, composer, [...args]);\r\n            },\r\n            // tc\r\n            tc(...args) {\r\n                const [arg1, arg2, arg3] = args;\r\n                const options = { plural: 1 };\r\n                let list = null;\r\n                let named = null;\r\n                if (!shared.isString(arg1)) {\r\n                    throw createI18nError(I18nErrorCodes.INVALID_ARGUMENT);\r\n                }\r\n                const key = arg1;\r\n                if (shared.isString(arg2)) {\r\n                    options.locale = arg2;\r\n                }\r\n                else if (shared.isNumber(arg2)) {\r\n                    options.plural = arg2;\r\n                }\r\n                else if (shared.isArray(arg2)) {\r\n                    list = arg2;\r\n                }\r\n                else if (shared.isPlainObject(arg2)) {\r\n                    named = arg2;\r\n                }\r\n                if (shared.isString(arg3)) {\r\n                    options.locale = arg3;\r\n                }\r\n                else if (shared.isArray(arg3)) {\r\n                    list = arg3;\r\n                }\r\n                else if (shared.isPlainObject(arg3)) {\r\n                    named = arg3;\r\n                }\r\n                // return composer.t(key, (list || named || {}) as any, options)\r\n                return Reflect.apply(composer.t, composer, [\r\n                    key,\r\n                    (list || named || {}),\r\n                    options\r\n                ]);\r\n            },\r\n            // te\r\n            te(key, locale) {\r\n                return composer.te(key, locale);\r\n            },\r\n            // tm\r\n            tm(key) {\r\n                return composer.tm(key);\r\n            },\r\n            // getLocaleMessage\r\n            getLocaleMessage(locale) {\r\n                return composer.getLocaleMessage(locale);\r\n            },\r\n            // setLocaleMessage\r\n            setLocaleMessage(locale, message) {\r\n                composer.setLocaleMessage(locale, message);\r\n            },\r\n            // mergeLocaleMessage\r\n            mergeLocaleMessage(locale, message) {\r\n                composer.mergeLocaleMessage(locale, message);\r\n            },\r\n            // d\r\n            d(...args) {\r\n                return Reflect.apply(composer.d, composer, [...args]);\r\n            },\r\n            // getDateTimeFormat\r\n            getDateTimeFormat(locale) {\r\n                return composer.getDateTimeFormat(locale);\r\n            },\r\n            // setDateTimeFormat\r\n            setDateTimeFormat(locale, format) {\r\n                composer.setDateTimeFormat(locale, format);\r\n            },\r\n            // mergeDateTimeFormat\r\n            mergeDateTimeFormat(locale, format) {\r\n                composer.mergeDateTimeFormat(locale, format);\r\n            },\r\n            // n\r\n            n(...args) {\r\n                return Reflect.apply(composer.n, composer, [...args]);\r\n            },\r\n            // getNumberFormat\r\n            getNumberFormat(locale) {\r\n                return composer.getNumberFormat(locale);\r\n            },\r\n            // setNumberFormat\r\n            setNumberFormat(locale, format) {\r\n                composer.setNumberFormat(locale, format);\r\n            },\r\n            // mergeNumberFormat\r\n            mergeNumberFormat(locale, format) {\r\n                composer.mergeNumberFormat(locale, format);\r\n            },\r\n            // getChoiceIndex\r\n            // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n            getChoiceIndex(choice, choicesLength) {\r\n                shared.warn(getWarnMessage(I18nWarnCodes.NOT_SUPPORTED_GET_CHOICE_INDEX));\r\n                return -1;\r\n            },\r\n            // for internal\r\n            __onComponentInstanceCreated(target) {\r\n                const { componentInstanceCreatedListener } = options;\r\n                if (componentInstanceCreatedListener) {\r\n                    componentInstanceCreatedListener(target, vueI18n);\r\n                }\r\n            }\r\n        };\r\n        // for vue-devtools timeline event\r\n        {\r\n            vueI18n.__enableEmitter = (emitter) => {\r\n                const __composer = composer;\r\n                __composer[EnableEmitter] && __composer[EnableEmitter](emitter);\r\n            };\r\n            vueI18n.__disableEmitter = () => {\r\n                const __composer = composer;\r\n                __composer[DisableEmitter] && __composer[DisableEmitter]();\r\n            };\r\n        }\r\n        return vueI18n;\r\n    }\r\n}\r\n/* eslint-enable @typescript-eslint/no-explicit-any */\n\nconst baseFormatProps = {\r\n    tag: {\r\n        type: [String, Object]\r\n    },\r\n    locale: {\r\n        type: String\r\n    },\r\n    scope: {\r\n        type: String,\r\n        // NOTE: avoid https://github.com/microsoft/rushstack/issues/1050\r\n        validator: (val /* ComponetI18nScope */) => val === 'parent' || val === 'global',\r\n        default: 'parent' /* ComponetI18nScope */\r\n    },\r\n    i18n: {\r\n        type: Object\r\n    }\r\n};\n\nfunction getInterpolateArg(\r\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\r\n{ slots }, // SetupContext,\r\nkeys) {\r\n    if (keys.length === 1 && keys[0] === 'default') {\r\n        // default slot with list\r\n        const ret = slots.default ? slots.default() : [];\r\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\r\n        return ret.reduce((slot, current) => {\r\n            return (slot = [\r\n                ...slot,\r\n                ...(shared.isArray(current.children) ? current.children : [current])\r\n            ]);\r\n        }, []);\r\n    }\r\n    else {\r\n        // named slots\r\n        return keys.reduce((arg, key) => {\r\n            const slot = slots[key];\r\n            if (slot) {\r\n                arg[key] = slot();\r\n            }\r\n            return arg;\r\n        }, {});\r\n    }\r\n}\r\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\r\nfunction getFragmentableTag(tag) {\r\n    return vue.Fragment ;\r\n}\n\n/**\r\n * Translation Component\r\n *\r\n * @remarks\r\n * See the following items for property about details\r\n *\r\n * @VueI18nSee [TranslationProps](component#translationprops)\r\n * @VueI18nSee [BaseFormatProps](component#baseformatprops)\r\n * @VueI18nSee [Component Interpolation](../guide/advanced/component)\r\n *\r\n * @example\r\n * ```html\r\n * <div id=\"app\">\r\n *   <!-- ... -->\r\n *   <i18n path=\"term\" tag=\"label\" for=\"tos\">\r\n *     <a :href=\"url\" target=\"_blank\">{{ $t('tos') }}</a>\r\n *   </i18n>\r\n *   <!-- ... -->\r\n * </div>\r\n * ```\r\n * ```js\r\n * import { createApp } from 'vue'\r\n * import { createI18n } from 'vue-i18n'\r\n *\r\n * const messages = {\r\n *   en: {\r\n *     tos: 'Term of Service',\r\n *     term: 'I accept xxx {0}.'\r\n *   },\r\n *   ja: {\r\n *     tos: '利用規約',\r\n *     term: '私は xxx の{0}に同意します。'\r\n *   }\r\n * }\r\n *\r\n * const i18n = createI18n({\r\n *   locale: 'en',\r\n *   messages\r\n * })\r\n *\r\n * const app = createApp({\r\n *   data: {\r\n *     url: '/term'\r\n *   }\r\n * }).use(i18n).mount('#app')\r\n * ```\r\n *\r\n * @VueI18nComponent\r\n */\r\nconst Translation =  /* defineComponent */ {\r\n    /* eslint-disable */\r\n    name: 'i18n-t',\r\n    props: shared.assign({\r\n        keypath: {\r\n            type: String,\r\n            required: true\r\n        },\r\n        plural: {\r\n            type: [Number, String],\r\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\r\n            validator: (val) => shared.isNumber(val) || !isNaN(val)\r\n        }\r\n    }, baseFormatProps),\r\n    /* eslint-enable */\r\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\r\n    setup(props, context) {\r\n        const { slots, attrs } = context;\r\n        // NOTE: avoid https://github.com/microsoft/rushstack/issues/1050\r\n        const i18n = props.i18n ||\r\n            useI18n({\r\n                useScope: props.scope,\r\n                __useComponent: true\r\n            });\r\n        return () => {\r\n            const keys = Object.keys(slots).filter(key => key !== '_');\r\n            const options = {};\r\n            if (props.locale) {\r\n                options.locale = props.locale;\r\n            }\r\n            if (props.plural !== undefined) {\r\n                options.plural = shared.isString(props.plural) ? +props.plural : props.plural;\r\n            }\r\n            const arg = getInterpolateArg(context, keys);\r\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\r\n            const children = i18n[TransrateVNodeSymbol](props.keypath, arg, options);\r\n            const assignedAttrs = shared.assign({}, attrs);\r\n            const tag = shared.isString(props.tag) || shared.isObject(props.tag)\r\n                ? props.tag\r\n                : getFragmentableTag();\r\n            return vue.h(tag, assignedAttrs, children);\r\n        };\r\n    }\r\n};\n\nfunction isVNode(target) {\r\n    return shared.isArray(target) && !shared.isString(target[0]);\r\n}\r\nfunction renderFormatter(props, context, slotKeys, partFormatter) {\r\n    const { slots, attrs } = context;\r\n    return () => {\r\n        const options = { part: true };\r\n        let overrides = {};\r\n        if (props.locale) {\r\n            options.locale = props.locale;\r\n        }\r\n        if (shared.isString(props.format)) {\r\n            options.key = props.format;\r\n        }\r\n        else if (shared.isObject(props.format)) {\r\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\r\n            if (shared.isString(props.format.key)) {\r\n                // eslint-disable-next-line @typescript-eslint/no-explicit-any\r\n                options.key = props.format.key;\r\n            }\r\n            // Filter out number format options only\r\n            overrides = Object.keys(props.format).reduce((options, prop) => {\r\n                return slotKeys.includes(prop)\r\n                    ? shared.assign({}, options, { [prop]: props.format[prop] }) // eslint-disable-line @typescript-eslint/no-explicit-any\r\n                    : options;\r\n            }, {});\r\n        }\r\n        const parts = partFormatter(...[props.value, options, overrides]);\r\n        let children = [options.key];\r\n        if (shared.isArray(parts)) {\r\n            children = parts.map((part, index) => {\r\n                const slot = slots[part.type];\r\n                const node = slot\r\n                    ? slot({ [part.type]: part.value, index, parts })\r\n                    : [part.value];\r\n                if (isVNode(node)) {\r\n                    node[0].key = `${part.type}-${index}`;\r\n                }\r\n                return node;\r\n            });\r\n        }\r\n        else if (shared.isString(parts)) {\r\n            children = [parts];\r\n        }\r\n        const assignedAttrs = shared.assign({}, attrs);\r\n        const tag = shared.isString(props.tag) || shared.isObject(props.tag)\r\n            ? props.tag\r\n            : getFragmentableTag();\r\n        return vue.h(tag, assignedAttrs, children);\r\n    };\r\n}\n\n/**\r\n * Number Format Component\r\n *\r\n * @remarks\r\n * See the following items for property about details\r\n *\r\n * @VueI18nSee [FormattableProps](component#formattableprops)\r\n * @VueI18nSee [BaseFormatProps](component#baseformatprops)\r\n * @VueI18nSee [Custom Formatting](../guide/essentials/number#custom-formatting)\r\n *\r\n * @VueI18nDanger\r\n * Not supported IE, due to no support `Intl.NumberFormat#formatToParts` in [IE](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/NumberFormat/formatToParts)\r\n *\r\n * If you want to use it, you need to use [polyfill](https://github.com/formatjs/formatjs/tree/main/packages/intl-numberformat)\r\n *\r\n * @VueI18nComponent\r\n */\r\nconst NumberFormat =  /* defineComponent */ {\r\n    /* eslint-disable */\r\n    name: 'i18n-n',\r\n    props: shared.assign({\r\n        value: {\r\n            type: Number,\r\n            required: true\r\n        },\r\n        format: {\r\n            type: [String, Object]\r\n        }\r\n    }, baseFormatProps),\r\n    /* eslint-enable */\r\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\r\n    setup(props, context) {\r\n        const i18n = props.i18n ||\r\n            useI18n({ useScope: 'parent', __useComponent: true });\r\n        return renderFormatter(props, context, coreBase.NUMBER_FORMAT_OPTIONS_KEYS, (...args) => \r\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\r\n        i18n[NumberPartsSymbol](...args));\r\n    }\r\n};\n\n/**\r\n * Datetime Format Component\r\n *\r\n * @remarks\r\n * See the following items for property about details\r\n *\r\n * @VueI18nSee [FormattableProps](component#formattableprops)\r\n * @VueI18nSee [BaseFormatProps](component#baseformatprops)\r\n * @VueI18nSee [Custom Formatting](../guide/essentials/datetime#custom-formatting)\r\n *\r\n * @VueI18nDanger\r\n * Not supported IE, due to no support `Intl.DateTimeFormat#formatToParts` in [IE](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/DateTimeFormat/formatToParts)\r\n *\r\n * If you want to use it, you need to use [polyfill](https://github.com/formatjs/formatjs/tree/main/packages/intl-datetimeformat)\r\n *\r\n * @VueI18nComponent\r\n */\r\nconst DatetimeFormat =  /*defineComponent */ {\r\n    /* eslint-disable */\r\n    name: 'i18n-d',\r\n    props: shared.assign({\r\n        value: {\r\n            type: [Number, Date],\r\n            required: true\r\n        },\r\n        format: {\r\n            type: [String, Object]\r\n        }\r\n    }, baseFormatProps),\r\n    /* eslint-enable */\r\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\r\n    setup(props, context) {\r\n        const i18n = props.i18n ||\r\n            useI18n({ useScope: 'parent', __useComponent: true });\r\n        return renderFormatter(props, context, coreBase.DATETIME_FORMAT_OPTIONS_KEYS, (...args) => \r\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\r\n        i18n[DatetimePartsSymbol](...args));\r\n    }\r\n};\n\nfunction getComposer$1(i18n, instance) {\r\n    const i18nInternal = i18n;\r\n    if (i18n.mode === 'composition') {\r\n        return (i18nInternal.__getInstance(instance) || i18n.global);\r\n    }\r\n    else {\r\n        const vueI18n = i18nInternal.__getInstance(instance);\r\n        return vueI18n != null\r\n            ? vueI18n.__composer\r\n            : i18n.global.__composer;\r\n    }\r\n}\r\nfunction vTDirective(i18n) {\r\n    const _process = (binding) => {\r\n        const { instance, modifiers, value } = binding;\r\n        /* istanbul ignore if */\r\n        if (!instance || !instance.$) {\r\n            throw createI18nError(I18nErrorCodes.UNEXPECTED_ERROR);\r\n        }\r\n        const composer = getComposer$1(i18n, instance.$);\r\n        if (modifiers.preserve) {\r\n            shared.warn(getWarnMessage(I18nWarnCodes.NOT_SUPPORTED_PRESERVE));\r\n        }\r\n        const parsedValue = parseValue(value);\r\n        return [\r\n            Reflect.apply(composer.t, composer, [...makeParams(parsedValue)]),\r\n            composer\r\n        ];\r\n    };\r\n    const register = (el, binding) => {\r\n        const [textContent, composer] = _process(binding);\r\n        if (shared.inBrowser && i18n.global === composer) {\r\n            // global scope only\r\n            el.__i18nWatcher = vue.watch(composer.locale, () => {\r\n                binding.instance && binding.instance.$forceUpdate();\r\n            });\r\n        }\r\n        el.__composer = composer;\r\n        el.textContent = textContent;\r\n    };\r\n    const unregister = (el) => {\r\n        if (shared.inBrowser && el.__i18nWatcher) {\r\n            el.__i18nWatcher();\r\n            el.__i18nWatcher = undefined;\r\n            delete el.__i18nWatcher;\r\n        }\r\n        if (el.__composer) {\r\n            el.__composer = undefined;\r\n            delete el.__composer;\r\n        }\r\n    };\r\n    const update = (el, { value }) => {\r\n        if (el.__composer) {\r\n            const composer = el.__composer;\r\n            const parsedValue = parseValue(value);\r\n            el.textContent = Reflect.apply(composer.t, composer, [\r\n                ...makeParams(parsedValue)\r\n            ]);\r\n        }\r\n    };\r\n    const getSSRProps = (binding) => {\r\n        const [textContent] = _process(binding);\r\n        return { textContent };\r\n    };\r\n    return {\r\n        created: register,\r\n        unmounted: unregister,\r\n        beforeUpdate: update,\r\n        getSSRProps\r\n    };\r\n}\r\nfunction parseValue(value) {\r\n    if (shared.isString(value)) {\r\n        return { path: value };\r\n    }\r\n    else if (shared.isPlainObject(value)) {\r\n        if (!('path' in value)) {\r\n            throw createI18nError(I18nErrorCodes.REQUIRED_VALUE, 'path');\r\n        }\r\n        return value;\r\n    }\r\n    else {\r\n        throw createI18nError(I18nErrorCodes.INVALID_VALUE);\r\n    }\r\n}\r\nfunction makeParams(value) {\r\n    const { path, locale, args, choice, plural } = value;\r\n    const options = {};\r\n    const named = args || {};\r\n    if (shared.isString(locale)) {\r\n        options.locale = locale;\r\n    }\r\n    if (shared.isNumber(choice)) {\r\n        options.plural = choice;\r\n    }\r\n    if (shared.isNumber(plural)) {\r\n        options.plural = plural;\r\n    }\r\n    return [path, named, options];\r\n}\n\nfunction apply(app, i18n, ...options) {\r\n    const pluginOptions = shared.isPlainObject(options[0])\r\n        ? options[0]\r\n        : {};\r\n    const useI18nComponentName = !!pluginOptions.useI18nComponentName;\r\n    const globalInstall = shared.isBoolean(pluginOptions.globalInstall)\r\n        ? pluginOptions.globalInstall\r\n        : true;\r\n    if (globalInstall && useI18nComponentName) {\r\n        shared.warn(getWarnMessage(I18nWarnCodes.COMPONENT_NAME_LEGACY_COMPATIBLE, {\r\n            name: Translation.name\r\n        }));\r\n    }\r\n    if (globalInstall) {\r\n        // install components\r\n        app.component(!useI18nComponentName ? Translation.name : 'i18n', Translation);\r\n        app.component(NumberFormat.name, NumberFormat);\r\n        app.component(DatetimeFormat.name, DatetimeFormat);\r\n    }\r\n    // install directive\r\n    {\r\n        app.directive('t', vTDirective(i18n));\r\n    }\r\n}\n\n/**\r\n * Supports compatibility for legacy vue-i18n APIs\r\n * This mixin is used when we use vue-i18n@v9.x or later\r\n */\r\nfunction defineMixin(vuei18n, composer, i18n) {\r\n    return {\r\n        beforeCreate() {\r\n            const instance = vue.getCurrentInstance();\r\n            /* istanbul ignore if */\r\n            if (!instance) {\r\n                throw createI18nError(I18nErrorCodes.UNEXPECTED_ERROR);\r\n            }\r\n            const options = this.$options;\r\n            if (options.i18n) {\r\n                const optionsI18n = options.i18n;\r\n                if (options.__i18n) {\r\n                    optionsI18n.__i18n = options.__i18n;\r\n                }\r\n                optionsI18n.__root = composer;\r\n                if (this === this.$root) {\r\n                    this.$i18n = mergeToRoot(vuei18n, optionsI18n);\r\n                }\r\n                else {\r\n                    optionsI18n.__injectWithOption = true;\r\n                    this.$i18n = createVueI18n(optionsI18n);\r\n                }\r\n            }\r\n            else if (options.__i18n) {\r\n                if (this === this.$root) {\r\n                    this.$i18n = mergeToRoot(vuei18n, options);\r\n                }\r\n                else {\r\n                    this.$i18n = createVueI18n({\r\n                        __i18n: options.__i18n,\r\n                        __injectWithOption: true,\r\n                        __root: composer\r\n                    });\r\n                }\r\n            }\r\n            else {\r\n                // set global\r\n                this.$i18n = vuei18n;\r\n            }\r\n            if (options.__i18nGlobal) {\r\n                adjustI18nResources(composer, options, options);\r\n            }\r\n            vuei18n.__onComponentInstanceCreated(this.$i18n);\r\n            i18n.__setInstance(instance, this.$i18n);\r\n            // defines vue-i18n legacy APIs\r\n            this.$t = (...args) => this.$i18n.t(...args);\r\n            this.$rt = (...args) => this.$i18n.rt(...args);\r\n            this.$tc = (...args) => this.$i18n.tc(...args);\r\n            this.$te = (key, locale) => this.$i18n.te(key, locale);\r\n            this.$d = (...args) => this.$i18n.d(...args);\r\n            this.$n = (...args) => this.$i18n.n(...args);\r\n            this.$tm = (key) => this.$i18n.tm(key);\r\n        },\r\n        mounted() {\r\n        },\r\n        unmounted() {\r\n            const instance = vue.getCurrentInstance();\r\n            /* istanbul ignore if */\r\n            if (!instance) {\r\n                throw createI18nError(I18nErrorCodes.UNEXPECTED_ERROR);\r\n            }\r\n            delete this.$t;\r\n            delete this.$rt;\r\n            delete this.$tc;\r\n            delete this.$te;\r\n            delete this.$d;\r\n            delete this.$n;\r\n            delete this.$tm;\r\n            i18n.__deleteInstance(instance);\r\n            delete this.$i18n;\r\n        }\r\n    };\r\n}\r\nfunction mergeToRoot(root, options) {\r\n    root.locale = options.locale || root.locale;\r\n    root.fallbackLocale = options.fallbackLocale || root.fallbackLocale;\r\n    root.missing = options.missing || root.missing;\r\n    root.silentTranslationWarn =\r\n        options.silentTranslationWarn || root.silentFallbackWarn;\r\n    root.silentFallbackWarn =\r\n        options.silentFallbackWarn || root.silentFallbackWarn;\r\n    root.formatFallbackMessages =\r\n        options.formatFallbackMessages || root.formatFallbackMessages;\r\n    root.postTranslation = options.postTranslation || root.postTranslation;\r\n    root.warnHtmlInMessage = options.warnHtmlInMessage || root.warnHtmlInMessage;\r\n    root.escapeParameterHtml =\r\n        options.escapeParameterHtml || root.escapeParameterHtml;\r\n    root.sync = options.sync || root.sync;\r\n    root.__composer[SetPluralRulesSymbol](options.pluralizationRules || root.pluralizationRules);\r\n    const messages = getLocaleMessages(root.locale, {\r\n        messages: options.messages,\r\n        __i18n: options.__i18n\r\n    });\r\n    Object.keys(messages).forEach(locale => root.mergeLocaleMessage(locale, messages[locale]));\r\n    if (options.datetimeFormats) {\r\n        Object.keys(options.datetimeFormats).forEach(locale => root.mergeDateTimeFormat(locale, options.datetimeFormats[locale]));\r\n    }\r\n    if (options.numberFormats) {\r\n        Object.keys(options.numberFormats).forEach(locale => root.mergeNumberFormat(locale, options.numberFormats[locale]));\r\n    }\r\n    return root;\r\n}\n\n/**\r\n * Injection key for {@link useI18n}\r\n *\r\n * @remarks\r\n * The global injection key for I18n instances with `useI18n`. this injection key is used in Web Components.\r\n * Specify the i18n instance created by {@link createI18n} together with `provide` function.\r\n *\r\n * @VueI18nGeneral\r\n */\r\nconst I18nInjectionKey = \r\n/* #__PURE__*/ shared.makeSymbol('global-vue-i18n');\r\n// eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/explicit-module-boundary-types\r\nfunction createI18n(options = {}, VueI18nLegacy) {\r\n    // prettier-ignore\r\n    const __legacyMode = shared.isBoolean(options.legacy)\r\n            ? options.legacy\r\n            : true;\r\n    // prettier-ignore\r\n    const __globalInjection = shared.isBoolean(options.globalInjection)\r\n        ? options.globalInjection\r\n        : true;\r\n    // prettier-ignore\r\n    const __allowComposition = __legacyMode\r\n            ? !!options.allowComposition\r\n            : true;\r\n    const __instances = new Map();\r\n    const [globalScope, __global] = createGlobal(options, __legacyMode);\r\n    const symbol = shared.makeSymbol('vue-i18n' );\r\n    function __getInstance(component) {\r\n        return __instances.get(component) || null;\r\n    }\r\n    function __setInstance(component, instance) {\r\n        __instances.set(component, instance);\r\n    }\r\n    function __deleteInstance(component) {\r\n        __instances.delete(component);\r\n    }\r\n    {\r\n        const i18n = {\r\n            // mode\r\n            get mode() {\r\n                return __legacyMode\r\n                    ? 'legacy'\r\n                    : 'composition';\r\n            },\r\n            // allowComposition\r\n            get allowComposition() {\r\n                return __allowComposition;\r\n            },\r\n            // install plugin\r\n            async install(app, ...options) {\r\n                // setup global provider\r\n                app.__VUE_I18N_SYMBOL__ = symbol;\r\n                app.provide(app.__VUE_I18N_SYMBOL__, i18n);\r\n                // global method and properties injection for Composition API\r\n                if (!__legacyMode && __globalInjection) {\r\n                    injectGlobalFields(app, i18n.global);\r\n                }\r\n                // install built-in components and directive\r\n                {\r\n                    apply(app, i18n, ...options);\r\n                }\r\n                // setup mixin for Legacy API\r\n                if (__legacyMode) {\r\n                    app.mixin(defineMixin(__global, __global.__composer, i18n));\r\n                }\r\n                // release global scope\r\n                const unmountApp = app.unmount;\r\n                app.unmount = () => {\r\n                    i18n.dispose();\r\n                    unmountApp();\r\n                };\r\n            },\r\n            // global accessor\r\n            get global() {\r\n                return __global;\r\n            },\r\n            dispose() {\r\n                globalScope.stop();\r\n            },\r\n            // @internal\r\n            __instances,\r\n            // @internal\r\n            __getInstance,\r\n            // @internal\r\n            __setInstance,\r\n            // @internal\r\n            __deleteInstance\r\n        };\r\n        return i18n;\r\n    }\r\n}\r\n// eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types\r\nfunction useI18n(options = {}) {\r\n    const instance = vue.getCurrentInstance();\r\n    if (instance == null) {\r\n        throw createI18nError(I18nErrorCodes.MUST_BE_CALL_SETUP_TOP);\r\n    }\r\n    if (!instance.isCE &&\r\n        instance.appContext.app != null &&\r\n        !instance.appContext.app.__VUE_I18N_SYMBOL__) {\r\n        throw createI18nError(I18nErrorCodes.NOT_INSLALLED);\r\n    }\r\n    const i18n = getI18nInstance(instance);\r\n    const global = getGlobalComposer(i18n);\r\n    const componentOptions = getComponentOptions(instance);\r\n    const scope = getScope(options, componentOptions);\r\n    {\r\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\r\n        if (i18n.mode === 'legacy' && !options.__useComponent) {\r\n            if (!i18n.allowComposition) {\r\n                throw createI18nError(I18nErrorCodes.NOT_AVAILABLE_IN_LEGACY_MODE);\r\n            }\r\n            return useI18nForLegacy(instance, scope, global, options);\r\n        }\r\n    }\r\n    if (scope === 'global') {\r\n        adjustI18nResources(global, options, componentOptions);\r\n        return global;\r\n    }\r\n    if (scope === 'parent') {\r\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\r\n        let composer = getComposer(i18n, instance, options.__useComponent);\r\n        if (composer == null) {\r\n            {\r\n                shared.warn(getWarnMessage(I18nWarnCodes.NOT_FOUND_PARENT_SCOPE));\r\n            }\r\n            composer = global;\r\n        }\r\n        return composer;\r\n    }\r\n    const i18nInternal = i18n;\r\n    let composer = i18nInternal.__getInstance(instance);\r\n    if (composer == null) {\r\n        const composerOptions = shared.assign({}, options);\r\n        if ('__i18n' in componentOptions) {\r\n            composerOptions.__i18n = componentOptions.__i18n;\r\n        }\r\n        if (global) {\r\n            composerOptions.__root = global;\r\n        }\r\n        composer = createComposer(composerOptions);\r\n        setupLifeCycle(i18nInternal, instance);\r\n        i18nInternal.__setInstance(instance, composer);\r\n    }\r\n    return composer;\r\n}\r\n/**\r\n * Cast to VueI18n legacy compatible type\r\n *\r\n * @remarks\r\n * This API is provided only with [vue-i18n-bridge](https://vue-i18n.intlify.dev/guide/migration/ways.html#what-is-vue-i18n-bridge).\r\n *\r\n * The purpose of this function is to convert an {@link I18n} instance created with {@link createI18n | createI18n(legacy: true)} into a `vue-i18n@v8.x` compatible instance of `new VueI18n` in a TypeScript environment.\r\n *\r\n * @param i18n - An instance of {@link I18n}\r\n * @returns A i18n instance which is casted to {@link VueI18n} type\r\n *\r\n * @VueI18nTip\r\n * :new: provided by **vue-i18n-bridge only**\r\n *\r\n * @VueI18nGeneral\r\n */\r\nconst castToVueI18n =  (i18n\r\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\r\n) => {\r\n    if (!(__VUE_I18N_BRIDGE__ in i18n)) {\r\n        throw createI18nError(I18nErrorCodes.NOT_COMPATIBLE_LEGACY_VUE_I18N);\r\n    }\r\n    return i18n;\r\n};\r\nfunction createGlobal(options, legacyMode, VueI18nLegacy // eslint-disable-line @typescript-eslint/no-explicit-any\r\n) {\r\n    const scope = vue.effectScope();\r\n    {\r\n        const obj = legacyMode\r\n            ? scope.run(() => createVueI18n(options))\r\n            : scope.run(() => createComposer(options));\r\n        if (obj == null) {\r\n            throw createI18nError(I18nErrorCodes.UNEXPECTED_ERROR);\r\n        }\r\n        return [scope, obj];\r\n    }\r\n}\r\nfunction getI18nInstance(instance) {\r\n    {\r\n        const i18n = vue.inject(!instance.isCE\r\n            ? instance.appContext.app.__VUE_I18N_SYMBOL__\r\n            : I18nInjectionKey);\r\n        /* istanbul ignore if */\r\n        if (!i18n) {\r\n            throw createI18nError(!instance.isCE\r\n                ? I18nErrorCodes.UNEXPECTED_ERROR\r\n                : I18nErrorCodes.NOT_INSLALLED_WITH_PROVIDE);\r\n        }\r\n        return i18n;\r\n    }\r\n}\r\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\r\nfunction getScope(options, componentOptions) {\r\n    // prettier-ignore\r\n    return shared.isEmptyObject(options)\r\n        ? ('__i18n' in componentOptions)\r\n            ? 'local'\r\n            : 'global'\r\n        : !options.useScope\r\n            ? 'local'\r\n            : options.useScope;\r\n}\r\nfunction getGlobalComposer(i18n) {\r\n    // prettier-ignore\r\n    return i18n.mode === 'composition'\r\n            ? i18n.global\r\n            : i18n.global.__composer\r\n        ;\r\n}\r\nfunction getComposer(i18n, target, useComponent = false) {\r\n    let composer = null;\r\n    const root = target.root;\r\n    let current = target.parent;\r\n    while (current != null) {\r\n        const i18nInternal = i18n;\r\n        if (i18n.mode === 'composition') {\r\n            composer = i18nInternal.__getInstance(current);\r\n        }\r\n        else {\r\n            {\r\n                const vueI18n = i18nInternal.__getInstance(current);\r\n                if (vueI18n != null) {\r\n                    composer = vueI18n\r\n                        .__composer;\r\n                    if (useComponent &&\r\n                        composer &&\r\n                        !composer[InejctWithOption] // eslint-disable-line @typescript-eslint/no-explicit-any\r\n                    ) {\r\n                        composer = null;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n        if (composer != null) {\r\n            break;\r\n        }\r\n        if (root === current) {\r\n            break;\r\n        }\r\n        current = current.parent;\r\n    }\r\n    return composer;\r\n}\r\nfunction setupLifeCycle(i18n, target, composer) {\r\n    {\r\n        vue.onMounted(() => {\r\n        }, target);\r\n        vue.onUnmounted(() => {\r\n            i18n.__deleteInstance(target);\r\n        }, target);\r\n    }\r\n}\r\nfunction useI18nForLegacy(instance, scope, root, options = {} // eslint-disable-line @typescript-eslint/no-explicit-any\r\n) {\r\n    const isLocale = scope === 'local';\r\n    const _composer = vue.shallowRef(null);\r\n    if (isLocale &&\r\n        instance.proxy &&\r\n        !(instance.proxy.$options.i18n || instance.proxy.$options.__i18n)) {\r\n        throw createI18nError(I18nErrorCodes.MUST_DEFINE_I18N_OPTION_IN_ALLOW_COMPOSITION);\r\n    }\r\n    const _inheritLocale = shared.isBoolean(options.inheritLocale)\r\n        ? options.inheritLocale\r\n        : true;\r\n    const _locale = vue.ref(\r\n    // prettier-ignore\r\n    isLocale && _inheritLocale\r\n        ? root.locale.value\r\n        : shared.isString(options.locale)\r\n            ? options.locale\r\n            : coreBase.DEFAULT_LOCALE);\r\n    const _fallbackLocale = vue.ref(\r\n    // prettier-ignore\r\n    isLocale && _inheritLocale\r\n        ? root.fallbackLocale.value\r\n        : shared.isString(options.fallbackLocale) ||\r\n            shared.isArray(options.fallbackLocale) ||\r\n            shared.isPlainObject(options.fallbackLocale) ||\r\n            options.fallbackLocale === false\r\n            ? options.fallbackLocale\r\n            : _locale.value);\r\n    const _messages = vue.ref(getLocaleMessages(_locale.value, options));\r\n    // prettier-ignore\r\n    const _datetimeFormats = vue.ref(shared.isPlainObject(options.datetimeFormats)\r\n        ? options.datetimeFormats\r\n        : { [_locale.value]: {} });\r\n    // prettier-ignore\r\n    const _numberFormats = vue.ref(shared.isPlainObject(options.numberFormats)\r\n        ? options.numberFormats\r\n        : { [_locale.value]: {} });\r\n    // prettier-ignore\r\n    const _missingWarn = isLocale\r\n        ? root.missingWarn\r\n        : shared.isBoolean(options.missingWarn) || shared.isRegExp(options.missingWarn)\r\n            ? options.missingWarn\r\n            : true;\r\n    // prettier-ignore\r\n    const _fallbackWarn = isLocale\r\n        ? root.fallbackWarn\r\n        : shared.isBoolean(options.fallbackWarn) || shared.isRegExp(options.fallbackWarn)\r\n            ? options.fallbackWarn\r\n            : true;\r\n    // prettier-ignore\r\n    const _fallbackRoot = isLocale\r\n        ? root.fallbackRoot\r\n        : shared.isBoolean(options.fallbackRoot)\r\n            ? options.fallbackRoot\r\n            : true;\r\n    // configure fall back to root\r\n    const _fallbackFormat = !!options.fallbackFormat;\r\n    // runtime missing\r\n    const _missing = shared.isFunction(options.missing) ? options.missing : null;\r\n    // postTranslation handler\r\n    const _postTranslation = shared.isFunction(options.postTranslation)\r\n        ? options.postTranslation\r\n        : null;\r\n    // prettier-ignore\r\n    const _warnHtmlMessage = isLocale\r\n        ? root.warnHtmlMessage\r\n        : shared.isBoolean(options.warnHtmlMessage)\r\n            ? options.warnHtmlMessage\r\n            : true;\r\n    const _escapeParameter = !!options.escapeParameter;\r\n    // prettier-ignore\r\n    const _modifiers = isLocale\r\n        ? root.modifiers\r\n        : shared.isPlainObject(options.modifiers)\r\n            ? options.modifiers\r\n            : {};\r\n    // pluralRules\r\n    const _pluralRules = options.pluralRules || (isLocale && root.pluralRules);\r\n    // track reactivity\r\n    function trackReactivityValues() {\r\n        return [\r\n            _locale.value,\r\n            _fallbackLocale.value,\r\n            _messages.value,\r\n            _datetimeFormats.value,\r\n            _numberFormats.value\r\n        ];\r\n    }\r\n    // locale\r\n    const locale = vue.computed({\r\n        get: () => {\r\n            return _composer.value ? _composer.value.locale.value : _locale.value;\r\n        },\r\n        set: val => {\r\n            if (_composer.value) {\r\n                _composer.value.locale.value = val;\r\n            }\r\n            _locale.value = val;\r\n        }\r\n    });\r\n    // fallbackLocale\r\n    const fallbackLocale = vue.computed({\r\n        get: () => {\r\n            return _composer.value\r\n                ? _composer.value.fallbackLocale.value\r\n                : _fallbackLocale.value;\r\n        },\r\n        set: val => {\r\n            if (_composer.value) {\r\n                _composer.value.fallbackLocale.value = val;\r\n            }\r\n            _fallbackLocale.value = val;\r\n        }\r\n    });\r\n    // messages\r\n    const messages = vue.computed(() => {\r\n        if (_composer.value) {\r\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\r\n            return _composer.value.messages.value;\r\n        }\r\n        else {\r\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\r\n            return _messages.value;\r\n        }\r\n    });\r\n    const datetimeFormats = vue.computed(() => _datetimeFormats.value);\r\n    const numberFormats = vue.computed(() => _numberFormats.value);\r\n    function getPostTranslationHandler() {\r\n        return _composer.value\r\n            ? _composer.value.getPostTranslationHandler()\r\n            : _postTranslation;\r\n    }\r\n    function setPostTranslationHandler(handler) {\r\n        if (_composer.value) {\r\n            _composer.value.setPostTranslationHandler(handler);\r\n        }\r\n    }\r\n    function getMissingHandler() {\r\n        return _composer.value ? _composer.value.getMissingHandler() : _missing;\r\n    }\r\n    function setMissingHandler(handler) {\r\n        if (_composer.value) {\r\n            _composer.value.setMissingHandler(handler);\r\n        }\r\n    }\r\n    function warpWithDeps(fn) {\r\n        trackReactivityValues();\r\n        return fn();\r\n    }\r\n    function t(...args) {\r\n        return _composer.value\r\n            ? warpWithDeps(() => Reflect.apply(_composer.value.t, null, [...args]))\r\n            : warpWithDeps(() => '');\r\n    }\r\n    function rt(...args) {\r\n        return _composer.value\r\n            ? Reflect.apply(_composer.value.rt, null, [...args])\r\n            : '';\r\n    }\r\n    function d(...args) {\r\n        return _composer.value\r\n            ? warpWithDeps(() => Reflect.apply(_composer.value.d, null, [...args]))\r\n            : warpWithDeps(() => '');\r\n    }\r\n    function n(...args) {\r\n        return _composer.value\r\n            ? warpWithDeps(() => Reflect.apply(_composer.value.n, null, [...args]))\r\n            : warpWithDeps(() => '');\r\n    }\r\n    function tm(key) {\r\n        return _composer.value ? _composer.value.tm(key) : {};\r\n    }\r\n    function te(key, locale) {\r\n        return _composer.value ? _composer.value.te(key, locale) : false;\r\n    }\r\n    function getLocaleMessage(locale) {\r\n        return _composer.value ? _composer.value.getLocaleMessage(locale) : {};\r\n    }\r\n    function setLocaleMessage(locale, message) {\r\n        if (_composer.value) {\r\n            _composer.value.setLocaleMessage(locale, message);\r\n            _messages.value[locale] = message;\r\n        }\r\n    }\r\n    function mergeLocaleMessage(locale, message) {\r\n        if (_composer.value) {\r\n            _composer.value.mergeLocaleMessage(locale, message);\r\n        }\r\n    }\r\n    function getDateTimeFormat(locale) {\r\n        return _composer.value ? _composer.value.getDateTimeFormat(locale) : {};\r\n    }\r\n    function setDateTimeFormat(locale, format) {\r\n        if (_composer.value) {\r\n            _composer.value.setDateTimeFormat(locale, format);\r\n            _datetimeFormats.value[locale] = format;\r\n        }\r\n    }\r\n    function mergeDateTimeFormat(locale, format) {\r\n        if (_composer.value) {\r\n            _composer.value.mergeDateTimeFormat(locale, format);\r\n        }\r\n    }\r\n    function getNumberFormat(locale) {\r\n        return _composer.value ? _composer.value.getNumberFormat(locale) : {};\r\n    }\r\n    function setNumberFormat(locale, format) {\r\n        if (_composer.value) {\r\n            _composer.value.setNumberFormat(locale, format);\r\n            _numberFormats.value[locale] = format;\r\n        }\r\n    }\r\n    function mergeNumberFormat(locale, format) {\r\n        if (_composer.value) {\r\n            _composer.value.mergeNumberFormat(locale, format);\r\n        }\r\n    }\r\n    const wrapper = {\r\n        get id() {\r\n            return _composer.value ? _composer.value.id : -1;\r\n        },\r\n        locale,\r\n        fallbackLocale,\r\n        messages,\r\n        datetimeFormats,\r\n        numberFormats,\r\n        get inheritLocale() {\r\n            return _composer.value ? _composer.value.inheritLocale : _inheritLocale;\r\n        },\r\n        set inheritLocale(val) {\r\n            if (_composer.value) {\r\n                _composer.value.inheritLocale = val;\r\n            }\r\n        },\r\n        get availableLocales() {\r\n            return _composer.value\r\n                ? _composer.value.availableLocales\r\n                : Object.keys(_messages.value);\r\n        },\r\n        get modifiers() {\r\n            return (_composer.value ? _composer.value.modifiers : _modifiers);\r\n        },\r\n        get pluralRules() {\r\n            return (_composer.value ? _composer.value.pluralRules : _pluralRules);\r\n        },\r\n        get isGlobal() {\r\n            return _composer.value ? _composer.value.isGlobal : false;\r\n        },\r\n        get missingWarn() {\r\n            return _composer.value ? _composer.value.missingWarn : _missingWarn;\r\n        },\r\n        set missingWarn(val) {\r\n            if (_composer.value) {\r\n                _composer.value.missingWarn = val;\r\n            }\r\n        },\r\n        get fallbackWarn() {\r\n            return _composer.value ? _composer.value.fallbackWarn : _fallbackWarn;\r\n        },\r\n        set fallbackWarn(val) {\r\n            if (_composer.value) {\r\n                _composer.value.missingWarn = val;\r\n            }\r\n        },\r\n        get fallbackRoot() {\r\n            return _composer.value ? _composer.value.fallbackRoot : _fallbackRoot;\r\n        },\r\n        set fallbackRoot(val) {\r\n            if (_composer.value) {\r\n                _composer.value.fallbackRoot = val;\r\n            }\r\n        },\r\n        get fallbackFormat() {\r\n            return _composer.value ? _composer.value.fallbackFormat : _fallbackFormat;\r\n        },\r\n        set fallbackFormat(val) {\r\n            if (_composer.value) {\r\n                _composer.value.fallbackFormat = val;\r\n            }\r\n        },\r\n        get warnHtmlMessage() {\r\n            return _composer.value\r\n                ? _composer.value.warnHtmlMessage\r\n                : _warnHtmlMessage;\r\n        },\r\n        set warnHtmlMessage(val) {\r\n            if (_composer.value) {\r\n                _composer.value.warnHtmlMessage = val;\r\n            }\r\n        },\r\n        get escapeParameter() {\r\n            return _composer.value\r\n                ? _composer.value.escapeParameter\r\n                : _escapeParameter;\r\n        },\r\n        set escapeParameter(val) {\r\n            if (_composer.value) {\r\n                _composer.value.escapeParameter = val;\r\n            }\r\n        },\r\n        t,\r\n        getPostTranslationHandler,\r\n        setPostTranslationHandler,\r\n        getMissingHandler,\r\n        setMissingHandler,\r\n        rt,\r\n        d,\r\n        n,\r\n        tm,\r\n        te,\r\n        getLocaleMessage,\r\n        setLocaleMessage,\r\n        mergeLocaleMessage,\r\n        getDateTimeFormat,\r\n        setDateTimeFormat,\r\n        mergeDateTimeFormat,\r\n        getNumberFormat,\r\n        setNumberFormat,\r\n        mergeNumberFormat\r\n    };\r\n    function sync(composer) {\r\n        composer.locale.value = _locale.value;\r\n        composer.fallbackLocale.value = _fallbackLocale.value;\r\n        Object.keys(_messages.value).forEach(locale => {\r\n            composer.mergeLocaleMessage(locale, _messages.value[locale]);\r\n        });\r\n        Object.keys(_datetimeFormats.value).forEach(locale => {\r\n            composer.mergeDateTimeFormat(locale, _datetimeFormats.value[locale]);\r\n        });\r\n        Object.keys(_numberFormats.value).forEach(locale => {\r\n            composer.mergeNumberFormat(locale, _numberFormats.value[locale]);\r\n        });\r\n        composer.escapeParameter = _escapeParameter;\r\n        composer.fallbackFormat = _fallbackFormat;\r\n        composer.fallbackRoot = _fallbackRoot;\r\n        composer.fallbackWarn = _fallbackWarn;\r\n        composer.missingWarn = _missingWarn;\r\n        composer.warnHtmlMessage = _warnHtmlMessage;\r\n    }\r\n    vue.onBeforeMount(() => {\r\n        if (instance.proxy == null || instance.proxy.$i18n == null) {\r\n            throw createI18nError(I18nErrorCodes.NOT_AVAILABLE_COMPOSITION_IN_LEGACY);\r\n        }\r\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\r\n        const composer = (_composer.value = instance.proxy.$i18n\r\n            .__composer);\r\n        if (scope === 'global') {\r\n            _locale.value = composer.locale.value;\r\n            _fallbackLocale.value = composer.fallbackLocale.value;\r\n            _messages.value = composer.messages.value;\r\n            _datetimeFormats.value = composer.datetimeFormats.value;\r\n            _numberFormats.value = composer.numberFormats.value;\r\n        }\r\n        else if (isLocale) {\r\n            sync(composer);\r\n        }\r\n    });\r\n    return wrapper;\r\n}\r\nconst globalExportProps = [\r\n    'locale',\r\n    'fallbackLocale',\r\n    'availableLocales'\r\n];\r\nconst globalExportMethods = ['t', 'rt', 'd', 'n', 'tm'] ;\r\nfunction injectGlobalFields(app, composer) {\r\n    const i18n = Object.create(null);\r\n    globalExportProps.forEach(prop => {\r\n        const desc = Object.getOwnPropertyDescriptor(composer, prop);\r\n        if (!desc) {\r\n            throw createI18nError(I18nErrorCodes.UNEXPECTED_ERROR);\r\n        }\r\n        const wrap = vue.isRef(desc.value) // check computed props\r\n            ? {\r\n                get() {\r\n                    return desc.value.value;\r\n                },\r\n                // eslint-disable-next-line @typescript-eslint/no-explicit-any\r\n                set(val) {\r\n                    desc.value.value = val;\r\n                }\r\n            }\r\n            : {\r\n                get() {\r\n                    return desc.get && desc.get();\r\n                }\r\n            };\r\n        Object.defineProperty(i18n, prop, wrap);\r\n    });\r\n    app.config.globalProperties.$i18n = i18n;\r\n    globalExportMethods.forEach(method => {\r\n        const desc = Object.getOwnPropertyDescriptor(composer, method);\r\n        if (!desc || !desc.value) {\r\n            throw createI18nError(I18nErrorCodes.UNEXPECTED_ERROR);\r\n        }\r\n        Object.defineProperty(app.config.globalProperties, `$${method}`, desc);\r\n    });\r\n}\n\n// register message compiler at vue-i18n\r\ncoreBase.registerMessageCompiler(coreBase.compileToFunction);\r\n// register message resolver at vue-i18n\r\ncoreBase.registerMessageResolver(coreBase.resolveValue);\r\n// register fallback locale at vue-i18n\r\ncoreBase.registerLocaleFallbacker(coreBase.fallbackWithLocaleChain);\r\n// NOTE: experimental !!\r\n{\r\n    const target = shared.getGlobalThis();\r\n    target.__INTLIFY__ = true;\r\n    coreBase.setDevToolsHook(target.__INTLIFY_DEVTOOLS_GLOBAL_HOOK__);\r\n}\n\nexports.DatetimeFormat = DatetimeFormat;\nexports.I18nInjectionKey = I18nInjectionKey;\nexports.NumberFormat = NumberFormat;\nexports.Translation = Translation;\nexports.VERSION = VERSION;\nexports.castToVueI18n = castToVueI18n;\nexports.createI18n = createI18n;\nexports.useI18n = useI18n;\nexports.vTDirective = vTDirective;\n"], "mappings": ";;;;;;;;;AAAA;AAAA;AAAA;AAOA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAM5D,QAAM,YAAY,OAAO,WAAW;AACpC,YAAQ,OAAO;AACf,YAAQ,UAAU;AAClB;AACI,YAAM,OAAO,aAAa,OAAO;AACjC,UAAI,QACA,KAAK,QACL,KAAK,WACL,KAAK,cACL,KAAK,eAAe;AACpB,gBAAQ,OAAO,CAAC,QAAQ,KAAK,KAAK,GAAG;AACrC,gBAAQ,UAAU,CAAC,MAAM,UAAU,WAAW;AAC1C,eAAK,QAAQ,MAAM,UAAU,MAAM;AACnC,eAAK,WAAW,QAAQ;AACxB,eAAK,WAAW,MAAM;AAAA,QAC1B;AAAA,MACJ;AAAA,IACJ;AACA,QAAM,UAAU;AAEhB,aAAS,OAAO,YAAY,MAAM;AAC9B,UAAI,KAAK,WAAW,KAAK,SAAS,KAAK,CAAC,CAAC,GAAG;AACxC,eAAO,KAAK,CAAC;AAAA,MACjB;AACA,UAAI,CAAC,QAAQ,CAAC,KAAK,gBAAgB;AAC/B,eAAO,CAAC;AAAA,MACZ;AACA,aAAO,QAAQ,QAAQ,SAAS,CAAC,OAAO,eAAe;AACnD,eAAO,KAAK,eAAe,UAAU,IAAI,KAAK,UAAU,IAAI;AAAA,MAChE,CAAC;AAAA,IACL;AACA,QAAM,YAAY,OAAO,WAAW,cAAc,OAAO,OAAO,gBAAgB;AAChF,QAAM,aAAa,CAAC,SAAS,YAAY,OAAO,IAAI,IAAI;AACxD,QAAM,yBAAyB,CAAC,QAAQ,KAAK,WAAW,sBAAsB,EAAE,GAAG,QAAQ,GAAG,KAAK,GAAG,OAAO,CAAC;AAC9G,QAAM,wBAAwB,CAAC,SAAS,KAAK,UAAU,IAAI,EACtD,QAAQ,WAAW,SAAS,EAC5B,QAAQ,WAAW,SAAS,EAC5B,QAAQ,WAAW,SAAS;AACjC,QAAM,WAAW,CAAC,QAAQ,OAAO,QAAQ,YAAY,SAAS,GAAG;AACjE,QAAM,SAAS,CAAC,QAAQ,aAAa,GAAG,MAAM;AAC9C,QAAM,WAAW,CAAC,QAAQ,aAAa,GAAG,MAAM;AAChD,QAAM,gBAAgB,CAAC,QAAQ,cAAc,GAAG,KAAK,OAAO,KAAK,GAAG,EAAE,WAAW;AACjF,aAAS,KAAK,KAAK,KAAK;AACpB,UAAI,OAAO,YAAY,aAAa;AAChC,gBAAQ,KAAK,eAAe,GAAG;AAE/B,YAAI,KAAK;AACL,kBAAQ,KAAK,IAAI,KAAK;AAAA,QAC1B;AAAA,MACJ;AAAA,IACJ;AACA,QAAM,SAAS,OAAO;AACtB,QAAI;AACJ,QAAM,gBAAgB,MAAM;AAExB,aAAQ,gBACH,cACG,OAAO,eAAe,cAChB,aACA,OAAO,SAAS,cACZ,OACA,OAAO,WAAW,cACd,SACA,OAAO,WAAW,cACd,SACA,CAAC;AAAA,IAC/B;AACA,aAAS,WAAW,SAAS;AACzB,aAAO,QACF,QAAQ,MAAM,MAAM,EACpB,QAAQ,MAAM,MAAM,EACpB,QAAQ,MAAM,QAAQ,EACtB,QAAQ,MAAM,QAAQ;AAAA,IAC/B;AACA,QAAM,iBAAiB,OAAO,UAAU;AACxC,aAAS,OAAO,KAAK,KAAK;AACtB,aAAO,eAAe,KAAK,KAAK,GAAG;AAAA,IACvC;AASA,QAAM,UAAU,MAAM;AACtB,QAAM,aAAa,CAAC,QAAQ,OAAO,QAAQ;AAC3C,QAAM,WAAW,CAAC,QAAQ,OAAO,QAAQ;AACzC,QAAM,YAAY,CAAC,QAAQ,OAAO,QAAQ;AAC1C,QAAM,WAAW,CAAC,QAAQ,OAAO,QAAQ;AACzC,QAAM,WAAW,CAAC;AAAA;AAAA,MACjB,QAAQ,QAAQ,OAAO,QAAQ;AAAA;AAChC,QAAM,YAAY,CAAC,QAAQ;AACvB,aAAO,SAAS,GAAG,KAAK,WAAW,IAAI,IAAI,KAAK,WAAW,IAAI,KAAK;AAAA,IACxE;AACA,QAAM,iBAAiB,OAAO,UAAU;AACxC,QAAM,eAAe,CAAC,UAAU,eAAe,KAAK,KAAK;AACzD,QAAM,gBAAgB,CAAC,QAAQ,aAAa,GAAG,MAAM;AAErD,QAAM,kBAAkB,CAAC,QAAQ;AAC7B,aAAO,OAAO,OACR,KACA,QAAQ,GAAG,KAAM,cAAc,GAAG,KAAK,IAAI,aAAa,iBACpD,KAAK,UAAU,KAAK,MAAM,CAAC,IAC3B,OAAO,GAAG;AAAA,IACxB;AACA,QAAM,QAAQ;AACd,aAAS,kBAAkB,QAAQ,QAAQ,GAAG,MAAM,OAAO,QAAQ;AAC/D,YAAM,QAAQ,OAAO,MAAM,OAAO;AAClC,UAAI,QAAQ;AACZ,YAAM,MAAM,CAAC;AACb,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,iBAAS,MAAM,CAAC,EAAE,SAAS;AAC3B,YAAI,SAAS,OAAO;AAChB,mBAAS,IAAI,IAAI,OAAO,KAAK,IAAI,SAAS,MAAM,OAAO,KAAK;AACxD,gBAAI,IAAI,KAAK,KAAK,MAAM;AACpB;AACJ,kBAAM,OAAO,IAAI;AACjB,gBAAI,KAAK,GAAG,IAAI,GAAG,IAAI,OAAO,IAAI,OAAO,IAAI,EAAE,MAAM,CAAC,MAAM,MAAM,CAAC,CAAC,EAAE;AACtE,kBAAM,aAAa,MAAM,CAAC,EAAE;AAC5B,gBAAI,MAAM,GAAG;AAET,oBAAM,MAAM,SAAS,QAAQ,cAAc;AAC3C,oBAAM,SAAS,KAAK,IAAI,GAAG,MAAM,QAAQ,aAAa,MAAM,MAAM,KAAK;AACvE,kBAAI,KAAK,WAAW,IAAI,OAAO,GAAG,IAAI,IAAI,OAAO,MAAM,CAAC;AAAA,YAC5D,WACS,IAAI,GAAG;AACZ,kBAAI,MAAM,OAAO;AACb,sBAAM,SAAS,KAAK,IAAI,KAAK,IAAI,MAAM,OAAO,UAAU,GAAG,CAAC;AAC5D,oBAAI,KAAK,WAAW,IAAI,OAAO,MAAM,CAAC;AAAA,cAC1C;AACA,uBAAS,aAAa;AAAA,YAC1B;AAAA,UACJ;AACA;AAAA,QACJ;AAAA,MACJ;AACA,aAAO,IAAI,KAAK,IAAI;AAAA,IACxB;AAcA,aAAS,gBAAgB;AACrB,YAAM,SAAS,oBAAI,IAAI;AACvB,YAAM,UAAU;AAAA,QACZ;AAAA,QACA,GAAG,OAAO,SAAS;AACf,gBAAM,WAAW,OAAO,IAAI,KAAK;AACjC,gBAAM,QAAQ,YAAY,SAAS,KAAK,OAAO;AAC/C,cAAI,CAAC,OAAO;AACR,mBAAO,IAAI,OAAO,CAAC,OAAO,CAAC;AAAA,UAC/B;AAAA,QACJ;AAAA,QACA,IAAI,OAAO,SAAS;AAChB,gBAAM,WAAW,OAAO,IAAI,KAAK;AACjC,cAAI,UAAU;AACV,qBAAS,OAAO,SAAS,QAAQ,OAAO,MAAM,GAAG,CAAC;AAAA,UACtD;AAAA,QACJ;AAAA,QACA,KAAK,OAAO,SAAS;AACjB,WAAC,OAAO,IAAI,KAAK,KAAK,CAAC,GAClB,MAAM,EACN,IAAI,aAAW,QAAQ,OAAO,CAAC;AACpC,WAAC,OAAO,IAAI,GAAG,KAAK,CAAC,GAChB,MAAM,EACN,IAAI,aAAW,QAAQ,OAAO,OAAO,CAAC;AAAA,QAC/C;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AAEA,YAAQ,SAAS;AACjB,YAAQ,gBAAgB;AACxB,YAAQ,aAAa;AACrB,YAAQ,SAAS;AACjB,YAAQ,wBAAwB;AAChC,YAAQ,oBAAoB;AAC5B,YAAQ,yBAAyB;AACjC,YAAQ,gBAAgB;AACxB,YAAQ,SAAS;AACjB,YAAQ,YAAY;AACpB,YAAQ,UAAU;AAClB,YAAQ,YAAY;AACpB,YAAQ,SAAS;AACjB,YAAQ,gBAAgB;AACxB,YAAQ,aAAa;AACrB,YAAQ,WAAW;AACnB,YAAQ,WAAW;AACnB,YAAQ,gBAAgB;AACxB,YAAQ,YAAY;AACpB,YAAQ,WAAW;AACnB,YAAQ,WAAW;AACnB,YAAQ,WAAW;AACnB,YAAQ,aAAa;AACrB,YAAQ,iBAAiB;AACzB,YAAQ,kBAAkB;AAC1B,YAAQ,eAAe;AACvB,YAAQ,OAAO;AAAA;AAAA;;;AC7Nf;AAAA;AAAA;AAEA,QAAI,OAAuC;AACzC,aAAO,UAAU;AAAA,IACnB,OAAO;AACL,aAAO,UAAU;AAAA,IACnB;AAAA;AAAA;;;ACNA;AAAA;AAOA,QAAI,eAAe,mEAAmE,MAAM,EAAE;AAK9F,YAAQ,SAAS,SAAU,QAAQ;AACjC,UAAI,KAAK,UAAU,SAAS,aAAa,QAAQ;AAC/C,eAAO,aAAa,MAAM;AAAA,MAC5B;AACA,YAAM,IAAI,UAAU,+BAA+B,MAAM;AAAA,IAC3D;AAMA,YAAQ,SAAS,SAAU,UAAU;AACnC,UAAI,OAAO;AACX,UAAI,OAAO;AAEX,UAAI,UAAU;AACd,UAAI,UAAU;AAEd,UAAI,OAAO;AACX,UAAI,OAAO;AAEX,UAAI,OAAO;AACX,UAAI,QAAQ;AAEZ,UAAI,eAAe;AACnB,UAAI,eAAe;AAGnB,UAAI,QAAQ,YAAY,YAAY,MAAM;AACxC,eAAQ,WAAW;AAAA,MACrB;AAGA,UAAI,WAAW,YAAY,YAAY,SAAS;AAC9C,eAAQ,WAAW,UAAU;AAAA,MAC/B;AAGA,UAAI,QAAQ,YAAY,YAAY,MAAM;AACxC,eAAQ,WAAW,OAAO;AAAA,MAC5B;AAGA,UAAI,YAAY,MAAM;AACpB,eAAO;AAAA,MACT;AAGA,UAAI,YAAY,OAAO;AACrB,eAAO;AAAA,MACT;AAGA,aAAO;AAAA,IACT;AAAA;AAAA;;;AClEA;AAAA;AAqCA,QAAI,SAAS;AAcb,QAAI,iBAAiB;AAGrB,QAAI,WAAW,KAAK;AAGpB,QAAI,gBAAgB,WAAW;AAG/B,QAAI,uBAAuB;AAQ3B,aAAS,YAAY,QAAQ;AAC3B,aAAO,SAAS,KACV,CAAC,UAAW,KAAK,KAClB,UAAU,KAAK;AAAA,IACtB;AAQA,aAAS,cAAc,QAAQ;AAC7B,UAAI,cAAc,SAAS,OAAO;AAClC,UAAI,UAAU,UAAU;AACxB,aAAO,aACH,CAAC,UACD;AAAA,IACN;AAKA,YAAQ,SAAS,SAAS,iBAAiB,QAAQ;AACjD,UAAI,UAAU;AACd,UAAI;AAEJ,UAAI,MAAM,YAAY,MAAM;AAE5B,SAAG;AACD,gBAAQ,MAAM;AACd,iBAAS;AACT,YAAI,MAAM,GAAG;AAGX,mBAAS;AAAA,QACX;AACA,mBAAW,OAAO,OAAO,KAAK;AAAA,MAChC,SAAS,MAAM;AAEf,aAAO;AAAA,IACT;AAMA,YAAQ,SAAS,SAAS,iBAAiB,MAAM,QAAQ,WAAW;AAClE,UAAI,SAAS,KAAK;AAClB,UAAI,SAAS;AACb,UAAI,QAAQ;AACZ,UAAI,cAAc;AAElB,SAAG;AACD,YAAI,UAAU,QAAQ;AACpB,gBAAM,IAAI,MAAM,4CAA4C;AAAA,QAC9D;AAEA,gBAAQ,OAAO,OAAO,KAAK,WAAW,QAAQ,CAAC;AAC/C,YAAI,UAAU,IAAI;AAChB,gBAAM,IAAI,MAAM,2BAA2B,KAAK,OAAO,SAAS,CAAC,CAAC;AAAA,QACpE;AAEA,uBAAe,CAAC,EAAE,QAAQ;AAC1B,iBAAS;AACT,iBAAS,UAAU,SAAS;AAC5B,iBAAS;AAAA,MACX,SAAS;AAET,gBAAU,QAAQ,cAAc,MAAM;AACtC,gBAAU,OAAO;AAAA,IACnB;AAAA;AAAA;;;AC3IA;AAAA;AAiBA,aAAS,OAAO,OAAO,OAAO,eAAe;AAC3C,UAAI,SAAS,OAAO;AAClB,eAAO,MAAM,KAAK;AAAA,MACpB,WAAW,UAAU,WAAW,GAAG;AACjC,eAAO;AAAA,MACT,OAAO;AACL,cAAM,IAAI,MAAM,MAAM,QAAQ,2BAA2B;AAAA,MAC3D;AAAA,IACF;AACA,YAAQ,SAAS;AAEjB,QAAI,YAAY;AAChB,QAAI,gBAAgB;AAEpB,aAAS,SAAS,MAAM;AACtB,UAAI,QAAQ,KAAK,MAAM,SAAS;AAChC,UAAI,CAAC,OAAO;AACV,eAAO;AAAA,MACT;AACA,aAAO;AAAA,QACL,QAAQ,MAAM,CAAC;AAAA,QACf,MAAM,MAAM,CAAC;AAAA,QACb,MAAM,MAAM,CAAC;AAAA,QACb,MAAM,MAAM,CAAC;AAAA,QACb,MAAM,MAAM,CAAC;AAAA,MACf;AAAA,IACF;AACA,YAAQ,WAAW;AAEnB,aAAS,YAAY,YAAY;AAC/B,UAAI,MAAM;AACV,UAAI,WAAW,QAAQ;AACrB,eAAO,WAAW,SAAS;AAAA,MAC7B;AACA,aAAO;AACP,UAAI,WAAW,MAAM;AACnB,eAAO,WAAW,OAAO;AAAA,MAC3B;AACA,UAAI,WAAW,MAAM;AACnB,eAAO,WAAW;AAAA,MACpB;AACA,UAAI,WAAW,MAAM;AACnB,eAAO,MAAM,WAAW;AAAA,MAC1B;AACA,UAAI,WAAW,MAAM;AACnB,eAAO,WAAW;AAAA,MACpB;AACA,aAAO;AAAA,IACT;AACA,YAAQ,cAAc;AAatB,aAAS,UAAU,OAAO;AACxB,UAAI,OAAO;AACX,UAAI,MAAM,SAAS,KAAK;AACxB,UAAI,KAAK;AACP,YAAI,CAAC,IAAI,MAAM;AACb,iBAAO;AAAA,QACT;AACA,eAAO,IAAI;AAAA,MACb;AACA,UAAI,aAAa,QAAQ,WAAW,IAAI;AAExC,UAAI,QAAQ,KAAK,MAAM,KAAK;AAC5B,eAAS,MAAM,KAAK,GAAG,IAAI,MAAM,SAAS,GAAG,KAAK,GAAG,KAAK;AACxD,eAAO,MAAM,CAAC;AACd,YAAI,SAAS,KAAK;AAChB,gBAAM,OAAO,GAAG,CAAC;AAAA,QACnB,WAAW,SAAS,MAAM;AACxB;AAAA,QACF,WAAW,KAAK,GAAG;AACjB,cAAI,SAAS,IAAI;AAIf,kBAAM,OAAO,IAAI,GAAG,EAAE;AACtB,iBAAK;AAAA,UACP,OAAO;AACL,kBAAM,OAAO,GAAG,CAAC;AACjB;AAAA,UACF;AAAA,QACF;AAAA,MACF;AACA,aAAO,MAAM,KAAK,GAAG;AAErB,UAAI,SAAS,IAAI;AACf,eAAO,aAAa,MAAM;AAAA,MAC5B;AAEA,UAAI,KAAK;AACP,YAAI,OAAO;AACX,eAAO,YAAY,GAAG;AAAA,MACxB;AACA,aAAO;AAAA,IACT;AACA,YAAQ,YAAY;AAkBpB,aAAS,KAAK,OAAO,OAAO;AAC1B,UAAI,UAAU,IAAI;AAChB,gBAAQ;AAAA,MACV;AACA,UAAI,UAAU,IAAI;AAChB,gBAAQ;AAAA,MACV;AACA,UAAI,WAAW,SAAS,KAAK;AAC7B,UAAI,WAAW,SAAS,KAAK;AAC7B,UAAI,UAAU;AACZ,gBAAQ,SAAS,QAAQ;AAAA,MAC3B;AAGA,UAAI,YAAY,CAAC,SAAS,QAAQ;AAChC,YAAI,UAAU;AACZ,mBAAS,SAAS,SAAS;AAAA,QAC7B;AACA,eAAO,YAAY,QAAQ;AAAA,MAC7B;AAEA,UAAI,YAAY,MAAM,MAAM,aAAa,GAAG;AAC1C,eAAO;AAAA,MACT;AAGA,UAAI,YAAY,CAAC,SAAS,QAAQ,CAAC,SAAS,MAAM;AAChD,iBAAS,OAAO;AAChB,eAAO,YAAY,QAAQ;AAAA,MAC7B;AAEA,UAAI,SAAS,MAAM,OAAO,CAAC,MAAM,MAC7B,QACA,UAAU,MAAM,QAAQ,QAAQ,EAAE,IAAI,MAAM,KAAK;AAErD,UAAI,UAAU;AACZ,iBAAS,OAAO;AAChB,eAAO,YAAY,QAAQ;AAAA,MAC7B;AACA,aAAO;AAAA,IACT;AACA,YAAQ,OAAO;AAEf,YAAQ,aAAa,SAAU,OAAO;AACpC,aAAO,MAAM,OAAO,CAAC,MAAM,OAAO,UAAU,KAAK,KAAK;AAAA,IACxD;AAQA,aAAS,SAAS,OAAO,OAAO;AAC9B,UAAI,UAAU,IAAI;AAChB,gBAAQ;AAAA,MACV;AAEA,cAAQ,MAAM,QAAQ,OAAO,EAAE;AAM/B,UAAI,QAAQ;AACZ,aAAO,MAAM,QAAQ,QAAQ,GAAG,MAAM,GAAG;AACvC,YAAI,QAAQ,MAAM,YAAY,GAAG;AACjC,YAAI,QAAQ,GAAG;AACb,iBAAO;AAAA,QACT;AAKA,gBAAQ,MAAM,MAAM,GAAG,KAAK;AAC5B,YAAI,MAAM,MAAM,mBAAmB,GAAG;AACpC,iBAAO;AAAA,QACT;AAEA,UAAE;AAAA,MACJ;AAGA,aAAO,MAAM,QAAQ,CAAC,EAAE,KAAK,KAAK,IAAI,MAAM,OAAO,MAAM,SAAS,CAAC;AAAA,IACrE;AACA,YAAQ,WAAW;AAEnB,QAAI,oBAAqB,WAAY;AACnC,UAAI,MAAM,uBAAO,OAAO,IAAI;AAC5B,aAAO,EAAE,eAAe;AAAA,IAC1B,EAAE;AAEF,aAAS,SAAU,GAAG;AACpB,aAAO;AAAA,IACT;AAWA,aAAS,YAAY,MAAM;AACzB,UAAI,cAAc,IAAI,GAAG;AACvB,eAAO,MAAM;AAAA,MACf;AAEA,aAAO;AAAA,IACT;AACA,YAAQ,cAAc,oBAAoB,WAAW;AAErD,aAAS,cAAc,MAAM;AAC3B,UAAI,cAAc,IAAI,GAAG;AACvB,eAAO,KAAK,MAAM,CAAC;AAAA,MACrB;AAEA,aAAO;AAAA,IACT;AACA,YAAQ,gBAAgB,oBAAoB,WAAW;AAEvD,aAAS,cAAc,GAAG;AACxB,UAAI,CAAC,GAAG;AACN,eAAO;AAAA,MACT;AAEA,UAAI,SAAS,EAAE;AAEf,UAAI,SAAS,GAA4B;AACvC,eAAO;AAAA,MACT;AAEA,UAAI,EAAE,WAAW,SAAS,CAAC,MAAM,MAC7B,EAAE,WAAW,SAAS,CAAC,MAAM,MAC7B,EAAE,WAAW,SAAS,CAAC,MAAM,OAC7B,EAAE,WAAW,SAAS,CAAC,MAAM,OAC7B,EAAE,WAAW,SAAS,CAAC,MAAM,OAC7B,EAAE,WAAW,SAAS,CAAC,MAAM,OAC7B,EAAE,WAAW,SAAS,CAAC,MAAM,OAC7B,EAAE,WAAW,SAAS,CAAC,MAAM,MAC7B,EAAE,WAAW,SAAS,CAAC,MAAM,IAAe;AAC9C,eAAO;AAAA,MACT;AAEA,eAAS,IAAI,SAAS,IAAI,KAAK,GAAG,KAAK;AACrC,YAAI,EAAE,WAAW,CAAC,MAAM,IAAc;AACpC,iBAAO;AAAA,QACT;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAUA,aAAS,2BAA2B,UAAU,UAAU,qBAAqB;AAC3E,UAAI,MAAM,OAAO,SAAS,QAAQ,SAAS,MAAM;AACjD,UAAI,QAAQ,GAAG;AACb,eAAO;AAAA,MACT;AAEA,YAAM,SAAS,eAAe,SAAS;AACvC,UAAI,QAAQ,GAAG;AACb,eAAO;AAAA,MACT;AAEA,YAAM,SAAS,iBAAiB,SAAS;AACzC,UAAI,QAAQ,KAAK,qBAAqB;AACpC,eAAO;AAAA,MACT;AAEA,YAAM,SAAS,kBAAkB,SAAS;AAC1C,UAAI,QAAQ,GAAG;AACb,eAAO;AAAA,MACT;AAEA,YAAM,SAAS,gBAAgB,SAAS;AACxC,UAAI,QAAQ,GAAG;AACb,eAAO;AAAA,MACT;AAEA,aAAO,OAAO,SAAS,MAAM,SAAS,IAAI;AAAA,IAC5C;AACA,YAAQ,6BAA6B;AAWrC,aAAS,oCAAoC,UAAU,UAAU,sBAAsB;AACrF,UAAI,MAAM,SAAS,gBAAgB,SAAS;AAC5C,UAAI,QAAQ,GAAG;AACb,eAAO;AAAA,MACT;AAEA,YAAM,SAAS,kBAAkB,SAAS;AAC1C,UAAI,QAAQ,KAAK,sBAAsB;AACrC,eAAO;AAAA,MACT;AAEA,YAAM,OAAO,SAAS,QAAQ,SAAS,MAAM;AAC7C,UAAI,QAAQ,GAAG;AACb,eAAO;AAAA,MACT;AAEA,YAAM,SAAS,eAAe,SAAS;AACvC,UAAI,QAAQ,GAAG;AACb,eAAO;AAAA,MACT;AAEA,YAAM,SAAS,iBAAiB,SAAS;AACzC,UAAI,QAAQ,GAAG;AACb,eAAO;AAAA,MACT;AAEA,aAAO,OAAO,SAAS,MAAM,SAAS,IAAI;AAAA,IAC5C;AACA,YAAQ,sCAAsC;AAE9C,aAAS,OAAO,OAAO,OAAO;AAC5B,UAAI,UAAU,OAAO;AACnB,eAAO;AAAA,MACT;AAEA,UAAI,UAAU,MAAM;AAClB,eAAO;AAAA,MACT;AAEA,UAAI,UAAU,MAAM;AAClB,eAAO;AAAA,MACT;AAEA,UAAI,QAAQ,OAAO;AACjB,eAAO;AAAA,MACT;AAEA,aAAO;AAAA,IACT;AAMA,aAAS,oCAAoC,UAAU,UAAU;AAC/D,UAAI,MAAM,SAAS,gBAAgB,SAAS;AAC5C,UAAI,QAAQ,GAAG;AACb,eAAO;AAAA,MACT;AAEA,YAAM,SAAS,kBAAkB,SAAS;AAC1C,UAAI,QAAQ,GAAG;AACb,eAAO;AAAA,MACT;AAEA,YAAM,OAAO,SAAS,QAAQ,SAAS,MAAM;AAC7C,UAAI,QAAQ,GAAG;AACb,eAAO;AAAA,MACT;AAEA,YAAM,SAAS,eAAe,SAAS;AACvC,UAAI,QAAQ,GAAG;AACb,eAAO;AAAA,MACT;AAEA,YAAM,SAAS,iBAAiB,SAAS;AACzC,UAAI,QAAQ,GAAG;AACb,eAAO;AAAA,MACT;AAEA,aAAO,OAAO,SAAS,MAAM,SAAS,IAAI;AAAA,IAC5C;AACA,YAAQ,sCAAsC;AAO9C,aAAS,oBAAoB,KAAK;AAChC,aAAO,KAAK,MAAM,IAAI,QAAQ,kBAAkB,EAAE,CAAC;AAAA,IACrD;AACA,YAAQ,sBAAsB;AAM9B,aAAS,iBAAiB,YAAY,WAAW,cAAc;AAC7D,kBAAY,aAAa;AAEzB,UAAI,YAAY;AAEd,YAAI,WAAW,WAAW,SAAS,CAAC,MAAM,OAAO,UAAU,CAAC,MAAM,KAAK;AACrE,wBAAc;AAAA,QAChB;AAMA,oBAAY,aAAa;AAAA,MAC3B;AAgBA,UAAI,cAAc;AAChB,YAAI,SAAS,SAAS,YAAY;AAClC,YAAI,CAAC,QAAQ;AACX,gBAAM,IAAI,MAAM,kCAAkC;AAAA,QACpD;AACA,YAAI,OAAO,MAAM;AAEf,cAAI,QAAQ,OAAO,KAAK,YAAY,GAAG;AACvC,cAAI,SAAS,GAAG;AACd,mBAAO,OAAO,OAAO,KAAK,UAAU,GAAG,QAAQ,CAAC;AAAA,UAClD;AAAA,QACF;AACA,oBAAY,KAAK,YAAY,MAAM,GAAG,SAAS;AAAA,MACjD;AAEA,aAAO,UAAU,SAAS;AAAA,IAC5B;AACA,YAAQ,mBAAmB;AAAA;AAAA;;;ACve3B;AAAA;AAOA,QAAI,OAAO;AACX,QAAI,MAAM,OAAO,UAAU;AAC3B,QAAI,eAAe,OAAO,QAAQ;AAQlC,aAAS,WAAW;AAClB,WAAK,SAAS,CAAC;AACf,WAAK,OAAO,eAAe,oBAAI,IAAI,IAAI,uBAAO,OAAO,IAAI;AAAA,IAC3D;AAKA,aAAS,YAAY,SAAS,mBAAmB,QAAQ,kBAAkB;AACzE,UAAI,MAAM,IAAI,SAAS;AACvB,eAAS,IAAI,GAAG,MAAM,OAAO,QAAQ,IAAI,KAAK,KAAK;AACjD,YAAI,IAAI,OAAO,CAAC,GAAG,gBAAgB;AAAA,MACrC;AACA,aAAO;AAAA,IACT;AAQA,aAAS,UAAU,OAAO,SAAS,gBAAgB;AACjD,aAAO,eAAe,KAAK,KAAK,OAAO,OAAO,oBAAoB,KAAK,IAAI,EAAE;AAAA,IAC/E;AAOA,aAAS,UAAU,MAAM,SAAS,aAAa,MAAM,kBAAkB;AACrE,UAAI,OAAO,eAAe,OAAO,KAAK,YAAY,IAAI;AACtD,UAAI,cAAc,eAAe,KAAK,IAAI,IAAI,IAAI,IAAI,KAAK,KAAK,MAAM,IAAI;AAC1E,UAAI,MAAM,KAAK,OAAO;AACtB,UAAI,CAAC,eAAe,kBAAkB;AACpC,aAAK,OAAO,KAAK,IAAI;AAAA,MACvB;AACA,UAAI,CAAC,aAAa;AAChB,YAAI,cAAc;AAChB,eAAK,KAAK,IAAI,MAAM,GAAG;AAAA,QACzB,OAAO;AACL,eAAK,KAAK,IAAI,IAAI;AAAA,QACpB;AAAA,MACF;AAAA,IACF;AAOA,aAAS,UAAU,MAAM,SAAS,aAAa,MAAM;AACnD,UAAI,cAAc;AAChB,eAAO,KAAK,KAAK,IAAI,IAAI;AAAA,MAC3B,OAAO;AACL,YAAI,OAAO,KAAK,YAAY,IAAI;AAChC,eAAO,IAAI,KAAK,KAAK,MAAM,IAAI;AAAA,MACjC;AAAA,IACF;AAOA,aAAS,UAAU,UAAU,SAAS,iBAAiB,MAAM;AAC3D,UAAI,cAAc;AAChB,YAAI,MAAM,KAAK,KAAK,IAAI,IAAI;AAC5B,YAAI,OAAO,GAAG;AACV,iBAAO;AAAA,QACX;AAAA,MACF,OAAO;AACL,YAAI,OAAO,KAAK,YAAY,IAAI;AAChC,YAAI,IAAI,KAAK,KAAK,MAAM,IAAI,GAAG;AAC7B,iBAAO,KAAK,KAAK,IAAI;AAAA,QACvB;AAAA,MACF;AAEA,YAAM,IAAI,MAAM,MAAM,OAAO,sBAAsB;AAAA,IACrD;AAOA,aAAS,UAAU,KAAK,SAAS,YAAY,MAAM;AACjD,UAAI,QAAQ,KAAK,OAAO,KAAK,OAAO,QAAQ;AAC1C,eAAO,KAAK,OAAO,IAAI;AAAA,MACzB;AACA,YAAM,IAAI,MAAM,2BAA2B,IAAI;AAAA,IACjD;AAOA,aAAS,UAAU,UAAU,SAAS,mBAAmB;AACvD,aAAO,KAAK,OAAO,MAAM;AAAA,IAC3B;AAEA,YAAQ,WAAW;AAAA;AAAA;;;ACxHnB;AAAA;AAOA,QAAI,OAAO;AAMX,aAAS,uBAAuB,UAAU,UAAU;AAElD,UAAI,QAAQ,SAAS;AACrB,UAAI,QAAQ,SAAS;AACrB,UAAI,UAAU,SAAS;AACvB,UAAI,UAAU,SAAS;AACvB,aAAO,QAAQ,SAAS,SAAS,SAAS,WAAW,WAC9C,KAAK,oCAAoC,UAAU,QAAQ,KAAK;AAAA,IACzE;AAOA,aAAS,cAAc;AACrB,WAAK,SAAS,CAAC;AACf,WAAK,UAAU;AAEf,WAAK,QAAQ,EAAC,eAAe,IAAI,iBAAiB,EAAC;AAAA,IACrD;AAQA,gBAAY,UAAU,kBACpB,SAAS,oBAAoB,WAAW,UAAU;AAChD,WAAK,OAAO,QAAQ,WAAW,QAAQ;AAAA,IACzC;AAOF,gBAAY,UAAU,MAAM,SAAS,gBAAgB,UAAU;AAC7D,UAAI,uBAAuB,KAAK,OAAO,QAAQ,GAAG;AAChD,aAAK,QAAQ;AACb,aAAK,OAAO,KAAK,QAAQ;AAAA,MAC3B,OAAO;AACL,aAAK,UAAU;AACf,aAAK,OAAO,KAAK,QAAQ;AAAA,MAC3B;AAAA,IACF;AAWA,gBAAY,UAAU,UAAU,SAAS,sBAAsB;AAC7D,UAAI,CAAC,KAAK,SAAS;AACjB,aAAK,OAAO,KAAK,KAAK,mCAAmC;AACzD,aAAK,UAAU;AAAA,MACjB;AACA,aAAO,KAAK;AAAA,IACd;AAEA,YAAQ,cAAc;AAAA;AAAA;;;AC9EtB;AAAA;AAOA,QAAI,YAAY;AAChB,QAAI,OAAO;AACX,QAAI,WAAW,oBAAuB;AACtC,QAAI,cAAc,uBAA0B;AAU5C,aAAS,mBAAmB,OAAO;AACjC,UAAI,CAAC,OAAO;AACV,gBAAQ,CAAC;AAAA,MACX;AACA,WAAK,QAAQ,KAAK,OAAO,OAAO,QAAQ,IAAI;AAC5C,WAAK,cAAc,KAAK,OAAO,OAAO,cAAc,IAAI;AACxD,WAAK,kBAAkB,KAAK,OAAO,OAAO,kBAAkB,KAAK;AACjE,WAAK,WAAW,IAAI,SAAS;AAC7B,WAAK,SAAS,IAAI,SAAS;AAC3B,WAAK,YAAY,IAAI,YAAY;AACjC,WAAK,mBAAmB;AAAA,IAC1B;AAEA,uBAAmB,UAAU,WAAW;AAOxC,uBAAmB,gBACjB,SAAS,iCAAiC,oBAAoB;AAC5D,UAAI,aAAa,mBAAmB;AACpC,UAAI,YAAY,IAAI,mBAAmB;AAAA,QACrC,MAAM,mBAAmB;AAAA,QACzB;AAAA,MACF,CAAC;AACD,yBAAmB,YAAY,SAAU,SAAS;AAChD,YAAI,aAAa;AAAA,UACf,WAAW;AAAA,YACT,MAAM,QAAQ;AAAA,YACd,QAAQ,QAAQ;AAAA,UAClB;AAAA,QACF;AAEA,YAAI,QAAQ,UAAU,MAAM;AAC1B,qBAAW,SAAS,QAAQ;AAC5B,cAAI,cAAc,MAAM;AACtB,uBAAW,SAAS,KAAK,SAAS,YAAY,WAAW,MAAM;AAAA,UACjE;AAEA,qBAAW,WAAW;AAAA,YACpB,MAAM,QAAQ;AAAA,YACd,QAAQ,QAAQ;AAAA,UAClB;AAEA,cAAI,QAAQ,QAAQ,MAAM;AACxB,uBAAW,OAAO,QAAQ;AAAA,UAC5B;AAAA,QACF;AAEA,kBAAU,WAAW,UAAU;AAAA,MACjC,CAAC;AACD,yBAAmB,QAAQ,QAAQ,SAAU,YAAY;AACvD,YAAI,iBAAiB;AACrB,YAAI,eAAe,MAAM;AACvB,2BAAiB,KAAK,SAAS,YAAY,UAAU;AAAA,QACvD;AAEA,YAAI,CAAC,UAAU,SAAS,IAAI,cAAc,GAAG;AAC3C,oBAAU,SAAS,IAAI,cAAc;AAAA,QACvC;AAEA,YAAI,UAAU,mBAAmB,iBAAiB,UAAU;AAC5D,YAAI,WAAW,MAAM;AACnB,oBAAU,iBAAiB,YAAY,OAAO;AAAA,QAChD;AAAA,MACF,CAAC;AACD,aAAO;AAAA,IACT;AAYF,uBAAmB,UAAU,aAC3B,SAAS,8BAA8B,OAAO;AAC5C,UAAI,YAAY,KAAK,OAAO,OAAO,WAAW;AAC9C,UAAI,WAAW,KAAK,OAAO,OAAO,YAAY,IAAI;AAClD,UAAI,SAAS,KAAK,OAAO,OAAO,UAAU,IAAI;AAC9C,UAAI,OAAO,KAAK,OAAO,OAAO,QAAQ,IAAI;AAE1C,UAAI,CAAC,KAAK,iBAAiB;AACzB,aAAK,iBAAiB,WAAW,UAAU,QAAQ,IAAI;AAAA,MACzD;AAEA,UAAI,UAAU,MAAM;AAClB,iBAAS,OAAO,MAAM;AACtB,YAAI,CAAC,KAAK,SAAS,IAAI,MAAM,GAAG;AAC9B,eAAK,SAAS,IAAI,MAAM;AAAA,QAC1B;AAAA,MACF;AAEA,UAAI,QAAQ,MAAM;AAChB,eAAO,OAAO,IAAI;AAClB,YAAI,CAAC,KAAK,OAAO,IAAI,IAAI,GAAG;AAC1B,eAAK,OAAO,IAAI,IAAI;AAAA,QACtB;AAAA,MACF;AAEA,WAAK,UAAU,IAAI;AAAA,QACjB,eAAe,UAAU;AAAA,QACzB,iBAAiB,UAAU;AAAA,QAC3B,cAAc,YAAY,QAAQ,SAAS;AAAA,QAC3C,gBAAgB,YAAY,QAAQ,SAAS;AAAA,QAC7C;AAAA,QACA;AAAA,MACF,CAAC;AAAA,IACH;AAKF,uBAAmB,UAAU,mBAC3B,SAAS,oCAAoC,aAAa,gBAAgB;AACxE,UAAI,SAAS;AACb,UAAI,KAAK,eAAe,MAAM;AAC5B,iBAAS,KAAK,SAAS,KAAK,aAAa,MAAM;AAAA,MACjD;AAEA,UAAI,kBAAkB,MAAM;AAG1B,YAAI,CAAC,KAAK,kBAAkB;AAC1B,eAAK,mBAAmB,uBAAO,OAAO,IAAI;AAAA,QAC5C;AACA,aAAK,iBAAiB,KAAK,YAAY,MAAM,CAAC,IAAI;AAAA,MACpD,WAAW,KAAK,kBAAkB;AAGhC,eAAO,KAAK,iBAAiB,KAAK,YAAY,MAAM,CAAC;AACrD,YAAI,OAAO,KAAK,KAAK,gBAAgB,EAAE,WAAW,GAAG;AACnD,eAAK,mBAAmB;AAAA,QAC1B;AAAA,MACF;AAAA,IACF;AAkBF,uBAAmB,UAAU,iBAC3B,SAAS,kCAAkC,oBAAoB,aAAa,gBAAgB;AAC1F,UAAI,aAAa;AAEjB,UAAI,eAAe,MAAM;AACvB,YAAI,mBAAmB,QAAQ,MAAM;AACnC,gBAAM,IAAI;AAAA,YACR;AAAA,UAEF;AAAA,QACF;AACA,qBAAa,mBAAmB;AAAA,MAClC;AACA,UAAI,aAAa,KAAK;AAEtB,UAAI,cAAc,MAAM;AACtB,qBAAa,KAAK,SAAS,YAAY,UAAU;AAAA,MACnD;AAGA,UAAI,aAAa,IAAI,SAAS;AAC9B,UAAI,WAAW,IAAI,SAAS;AAG5B,WAAK,UAAU,gBAAgB,SAAU,SAAS;AAChD,YAAI,QAAQ,WAAW,cAAc,QAAQ,gBAAgB,MAAM;AAEjE,cAAI,WAAW,mBAAmB,oBAAoB;AAAA,YACpD,MAAM,QAAQ;AAAA,YACd,QAAQ,QAAQ;AAAA,UAClB,CAAC;AACD,cAAI,SAAS,UAAU,MAAM;AAE3B,oBAAQ,SAAS,SAAS;AAC1B,gBAAI,kBAAkB,MAAM;AAC1B,sBAAQ,SAAS,KAAK,KAAK,gBAAgB,QAAQ,MAAM;AAAA,YAC3D;AACA,gBAAI,cAAc,MAAM;AACtB,sBAAQ,SAAS,KAAK,SAAS,YAAY,QAAQ,MAAM;AAAA,YAC3D;AACA,oBAAQ,eAAe,SAAS;AAChC,oBAAQ,iBAAiB,SAAS;AAClC,gBAAI,SAAS,QAAQ,MAAM;AACzB,sBAAQ,OAAO,SAAS;AAAA,YAC1B;AAAA,UACF;AAAA,QACF;AAEA,YAAI,SAAS,QAAQ;AACrB,YAAI,UAAU,QAAQ,CAAC,WAAW,IAAI,MAAM,GAAG;AAC7C,qBAAW,IAAI,MAAM;AAAA,QACvB;AAEA,YAAI,OAAO,QAAQ;AACnB,YAAI,QAAQ,QAAQ,CAAC,SAAS,IAAI,IAAI,GAAG;AACvC,mBAAS,IAAI,IAAI;AAAA,QACnB;AAAA,MAEF,GAAG,IAAI;AACP,WAAK,WAAW;AAChB,WAAK,SAAS;AAGd,yBAAmB,QAAQ,QAAQ,SAAUA,aAAY;AACvD,YAAI,UAAU,mBAAmB,iBAAiBA,WAAU;AAC5D,YAAI,WAAW,MAAM;AACnB,cAAI,kBAAkB,MAAM;AAC1B,YAAAA,cAAa,KAAK,KAAK,gBAAgBA,WAAU;AAAA,UACnD;AACA,cAAI,cAAc,MAAM;AACtB,YAAAA,cAAa,KAAK,SAAS,YAAYA,WAAU;AAAA,UACnD;AACA,eAAK,iBAAiBA,aAAY,OAAO;AAAA,QAC3C;AAAA,MACF,GAAG,IAAI;AAAA,IACT;AAaF,uBAAmB,UAAU,mBAC3B,SAAS,mCAAmC,YAAY,WAAW,SACvB,OAAO;AAKjD,UAAI,aAAa,OAAO,UAAU,SAAS,YAAY,OAAO,UAAU,WAAW,UAAU;AACzF,cAAM,IAAI;AAAA,UACN;AAAA,QAGJ;AAAA,MACJ;AAEA,UAAI,cAAc,UAAU,cAAc,YAAY,cAC/C,WAAW,OAAO,KAAK,WAAW,UAAU,KAC5C,CAAC,aAAa,CAAC,WAAW,CAAC,OAAO;AAEvC;AAAA,MACF,WACS,cAAc,UAAU,cAAc,YAAY,cAC/C,aAAa,UAAU,aAAa,YAAY,aAChD,WAAW,OAAO,KAAK,WAAW,UAAU,KAC5C,UAAU,OAAO,KAAK,UAAU,UAAU,KAC1C,SAAS;AAEnB;AAAA,MACF,OACK;AACH,cAAM,IAAI,MAAM,sBAAsB,KAAK,UAAU;AAAA,UACnD,WAAW;AAAA,UACX,QAAQ;AAAA,UACR,UAAU;AAAA,UACV,MAAM;AAAA,QACR,CAAC,CAAC;AAAA,MACJ;AAAA,IACF;AAMF,uBAAmB,UAAU,qBAC3B,SAAS,uCAAuC;AAC9C,UAAI,0BAA0B;AAC9B,UAAI,wBAAwB;AAC5B,UAAI,yBAAyB;AAC7B,UAAI,uBAAuB;AAC3B,UAAI,eAAe;AACnB,UAAI,iBAAiB;AACrB,UAAI,SAAS;AACb,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI;AAEJ,UAAI,WAAW,KAAK,UAAU,QAAQ;AACtC,eAAS,IAAI,GAAG,MAAM,SAAS,QAAQ,IAAI,KAAK,KAAK;AACnD,kBAAU,SAAS,CAAC;AACpB,eAAO;AAEP,YAAI,QAAQ,kBAAkB,uBAAuB;AACnD,oCAA0B;AAC1B,iBAAO,QAAQ,kBAAkB,uBAAuB;AACtD,oBAAQ;AACR;AAAA,UACF;AAAA,QACF,OACK;AACH,cAAI,IAAI,GAAG;AACT,gBAAI,CAAC,KAAK,oCAAoC,SAAS,SAAS,IAAI,CAAC,CAAC,GAAG;AACvE;AAAA,YACF;AACA,oBAAQ;AAAA,UACV;AAAA,QACF;AAEA,gBAAQ,UAAU,OAAO,QAAQ,kBACJ,uBAAuB;AACpD,kCAA0B,QAAQ;AAElC,YAAI,QAAQ,UAAU,MAAM;AAC1B,sBAAY,KAAK,SAAS,QAAQ,QAAQ,MAAM;AAChD,kBAAQ,UAAU,OAAO,YAAY,cAAc;AACnD,2BAAiB;AAGjB,kBAAQ,UAAU,OAAO,QAAQ,eAAe,IACnB,oBAAoB;AACjD,iCAAuB,QAAQ,eAAe;AAE9C,kBAAQ,UAAU,OAAO,QAAQ,iBACJ,sBAAsB;AACnD,mCAAyB,QAAQ;AAEjC,cAAI,QAAQ,QAAQ,MAAM;AACxB,sBAAU,KAAK,OAAO,QAAQ,QAAQ,IAAI;AAC1C,oBAAQ,UAAU,OAAO,UAAU,YAAY;AAC/C,2BAAe;AAAA,UACjB;AAAA,QACF;AAEA,kBAAU;AAAA,MACZ;AAEA,aAAO;AAAA,IACT;AAEF,uBAAmB,UAAU,0BAC3B,SAAS,0CAA0C,UAAU,aAAa;AACxE,aAAO,SAAS,IAAI,SAAU,QAAQ;AACpC,YAAI,CAAC,KAAK,kBAAkB;AAC1B,iBAAO;AAAA,QACT;AACA,YAAI,eAAe,MAAM;AACvB,mBAAS,KAAK,SAAS,aAAa,MAAM;AAAA,QAC5C;AACA,YAAI,MAAM,KAAK,YAAY,MAAM;AACjC,eAAO,OAAO,UAAU,eAAe,KAAK,KAAK,kBAAkB,GAAG,IAClE,KAAK,iBAAiB,GAAG,IACzB;AAAA,MACN,GAAG,IAAI;AAAA,IACT;AAKF,uBAAmB,UAAU,SAC3B,SAAS,4BAA4B;AACnC,UAAI,MAAM;AAAA,QACR,SAAS,KAAK;AAAA,QACd,SAAS,KAAK,SAAS,QAAQ;AAAA,QAC/B,OAAO,KAAK,OAAO,QAAQ;AAAA,QAC3B,UAAU,KAAK,mBAAmB;AAAA,MACpC;AACA,UAAI,KAAK,SAAS,MAAM;AACtB,YAAI,OAAO,KAAK;AAAA,MAClB;AACA,UAAI,KAAK,eAAe,MAAM;AAC5B,YAAI,aAAa,KAAK;AAAA,MACxB;AACA,UAAI,KAAK,kBAAkB;AACzB,YAAI,iBAAiB,KAAK,wBAAwB,IAAI,SAAS,IAAI,UAAU;AAAA,MAC/E;AAEA,aAAO;AAAA,IACT;AAKF,uBAAmB,UAAU,WAC3B,SAAS,8BAA8B;AACrC,aAAO,KAAK,UAAU,KAAK,OAAO,CAAC;AAAA,IACrC;AAEF,YAAQ,qBAAqB;AAAA;AAAA;;;ACxa7B;AAAA;AAOA,YAAQ,uBAAuB;AAC/B,YAAQ,oBAAoB;AAe5B,aAAS,gBAAgB,MAAM,OAAO,SAAS,WAAW,UAAU,OAAO;AAUzE,UAAI,MAAM,KAAK,OAAO,QAAQ,QAAQ,CAAC,IAAI;AAC3C,UAAI,MAAM,SAAS,SAAS,UAAU,GAAG,GAAG,IAAI;AAChD,UAAI,QAAQ,GAAG;AAEb,eAAO;AAAA,MACT,WACS,MAAM,GAAG;AAEhB,YAAI,QAAQ,MAAM,GAAG;AAEnB,iBAAO,gBAAgB,KAAK,OAAO,SAAS,WAAW,UAAU,KAAK;AAAA,QACxE;AAIA,YAAI,SAAS,QAAQ,mBAAmB;AACtC,iBAAO,QAAQ,UAAU,SAAS,QAAQ;AAAA,QAC5C,OAAO;AACL,iBAAO;AAAA,QACT;AAAA,MACF,OACK;AAEH,YAAI,MAAM,OAAO,GAAG;AAElB,iBAAO,gBAAgB,MAAM,KAAK,SAAS,WAAW,UAAU,KAAK;AAAA,QACvE;AAGA,YAAI,SAAS,QAAQ,mBAAmB;AACtC,iBAAO;AAAA,QACT,OAAO;AACL,iBAAO,OAAO,IAAI,KAAK;AAAA,QACzB;AAAA,MACF;AAAA,IACF;AAoBA,YAAQ,SAAS,SAAS,OAAO,SAAS,WAAW,UAAU,OAAO;AACpE,UAAI,UAAU,WAAW,GAAG;AAC1B,eAAO;AAAA,MACT;AAEA,UAAI,QAAQ;AAAA,QAAgB;AAAA,QAAI,UAAU;AAAA,QAAQ;AAAA,QAAS;AAAA,QAC/B;AAAA,QAAU,SAAS,QAAQ;AAAA,MAAoB;AAC3E,UAAI,QAAQ,GAAG;AACb,eAAO;AAAA,MACT;AAKA,aAAO,QAAQ,KAAK,GAAG;AACrB,YAAI,SAAS,UAAU,KAAK,GAAG,UAAU,QAAQ,CAAC,GAAG,IAAI,MAAM,GAAG;AAChE;AAAA,QACF;AACA,UAAE;AAAA,MACJ;AAEA,aAAO;AAAA,IACT;AAAA;AAAA;;;AC9GA;AAAA;AA2BA,aAAS,KAAK,KAAK,GAAG,GAAG;AACvB,UAAI,OAAO,IAAI,CAAC;AAChB,UAAI,CAAC,IAAI,IAAI,CAAC;AACd,UAAI,CAAC,IAAI;AAAA,IACX;AAUA,aAAS,iBAAiB,KAAK,MAAM;AACnC,aAAO,KAAK,MAAM,MAAO,KAAK,OAAO,KAAK,OAAO,IAAK;AAAA,IACxD;AAcA,aAAS,YAAY,KAAK,YAAY,GAAG,GAAG;AAK1C,UAAI,IAAI,GAAG;AAYT,YAAI,aAAa,iBAAiB,GAAG,CAAC;AACtC,YAAI,IAAI,IAAI;AAEZ,aAAK,KAAK,YAAY,CAAC;AACvB,YAAI,QAAQ,IAAI,CAAC;AAQjB,iBAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,cAAI,WAAW,IAAI,CAAC,GAAG,KAAK,KAAK,GAAG;AAClC,iBAAK;AACL,iBAAK,KAAK,GAAG,CAAC;AAAA,UAChB;AAAA,QACF;AAEA,aAAK,KAAK,IAAI,GAAG,CAAC;AAClB,YAAI,IAAI,IAAI;AAIZ,oBAAY,KAAK,YAAY,GAAG,IAAI,CAAC;AACrC,oBAAY,KAAK,YAAY,IAAI,GAAG,CAAC;AAAA,MACvC;AAAA,IACF;AAUA,YAAQ,YAAY,SAAU,KAAK,YAAY;AAC7C,kBAAY,KAAK,YAAY,GAAG,IAAI,SAAS,CAAC;AAAA,IAChD;AAAA;AAAA;;;ACjHA;AAAA;AAOA,QAAI,OAAO;AACX,QAAI,eAAe;AACnB,QAAI,WAAW,oBAAuB;AACtC,QAAI,YAAY;AAChB,QAAI,YAAY,qBAAwB;AAExC,aAAS,kBAAkB,YAAY,eAAe;AACpD,UAAI,YAAY;AAChB,UAAI,OAAO,eAAe,UAAU;AAClC,oBAAY,KAAK,oBAAoB,UAAU;AAAA,MACjD;AAEA,aAAO,UAAU,YAAY,OACzB,IAAI,yBAAyB,WAAW,aAAa,IACrD,IAAI,uBAAuB,WAAW,aAAa;AAAA,IACzD;AAEA,sBAAkB,gBAAgB,SAAS,YAAY,eAAe;AACpE,aAAO,uBAAuB,cAAc,YAAY,aAAa;AAAA,IACvE;AAKA,sBAAkB,UAAU,WAAW;AAgCvC,sBAAkB,UAAU,sBAAsB;AAClD,WAAO,eAAe,kBAAkB,WAAW,sBAAsB;AAAA,MACvE,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,KAAK,WAAY;AACf,YAAI,CAAC,KAAK,qBAAqB;AAC7B,eAAK,eAAe,KAAK,WAAW,KAAK,UAAU;AAAA,QACrD;AAEA,eAAO,KAAK;AAAA,MACd;AAAA,IACF,CAAC;AAED,sBAAkB,UAAU,qBAAqB;AACjD,WAAO,eAAe,kBAAkB,WAAW,qBAAqB;AAAA,MACtE,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,KAAK,WAAY;AACf,YAAI,CAAC,KAAK,oBAAoB;AAC5B,eAAK,eAAe,KAAK,WAAW,KAAK,UAAU;AAAA,QACrD;AAEA,eAAO,KAAK;AAAA,MACd;AAAA,IACF,CAAC;AAED,sBAAkB,UAAU,0BAC1B,SAAS,yCAAyC,MAAM,OAAO;AAC7D,UAAI,IAAI,KAAK,OAAO,KAAK;AACzB,aAAO,MAAM,OAAO,MAAM;AAAA,IAC5B;AAOF,sBAAkB,UAAU,iBAC1B,SAAS,gCAAgC,MAAM,aAAa;AAC1D,YAAM,IAAI,MAAM,0CAA0C;AAAA,IAC5D;AAEF,sBAAkB,kBAAkB;AACpC,sBAAkB,iBAAiB;AAEnC,sBAAkB,uBAAuB;AACzC,sBAAkB,oBAAoB;AAkBtC,sBAAkB,UAAU,cAC1B,SAAS,8BAA8B,WAAW,UAAU,QAAQ;AAClE,UAAI,UAAU,YAAY;AAC1B,UAAI,QAAQ,UAAU,kBAAkB;AAExC,UAAI;AACJ,cAAQ,OAAO;AAAA,QACf,KAAK,kBAAkB;AACrB,qBAAW,KAAK;AAChB;AAAA,QACF,KAAK,kBAAkB;AACrB,qBAAW,KAAK;AAChB;AAAA,QACF;AACE,gBAAM,IAAI,MAAM,6BAA6B;AAAA,MAC/C;AAEA,UAAI,aAAa,KAAK;AACtB,eAAS,IAAI,SAAU,SAAS;AAC9B,YAAI,SAAS,QAAQ,WAAW,OAAO,OAAO,KAAK,SAAS,GAAG,QAAQ,MAAM;AAC7E,iBAAS,KAAK,iBAAiB,YAAY,QAAQ,KAAK,aAAa;AACrE,eAAO;AAAA,UACL;AAAA,UACA,eAAe,QAAQ;AAAA,UACvB,iBAAiB,QAAQ;AAAA,UACzB,cAAc,QAAQ;AAAA,UACtB,gBAAgB,QAAQ;AAAA,UACxB,MAAM,QAAQ,SAAS,OAAO,OAAO,KAAK,OAAO,GAAG,QAAQ,IAAI;AAAA,QAClE;AAAA,MACF,GAAG,IAAI,EAAE,QAAQ,WAAW,OAAO;AAAA,IACrC;AAwBF,sBAAkB,UAAU,2BAC1B,SAAS,2CAA2C,OAAO;AACzD,UAAI,OAAO,KAAK,OAAO,OAAO,MAAM;AAMpC,UAAI,SAAS;AAAA,QACX,QAAQ,KAAK,OAAO,OAAO,QAAQ;AAAA,QACnC,cAAc;AAAA,QACd,gBAAgB,KAAK,OAAO,OAAO,UAAU,CAAC;AAAA,MAChD;AAEA,aAAO,SAAS,KAAK,iBAAiB,OAAO,MAAM;AACnD,UAAI,OAAO,SAAS,GAAG;AACrB,eAAO,CAAC;AAAA,MACV;AAEA,UAAI,WAAW,CAAC;AAEhB,UAAI,QAAQ,KAAK;AAAA,QAAa;AAAA,QACA,KAAK;AAAA,QACL;AAAA,QACA;AAAA,QACA,KAAK;AAAA,QACL,aAAa;AAAA,MAAiB;AAC5D,UAAI,SAAS,GAAG;AACd,YAAI,UAAU,KAAK,kBAAkB,KAAK;AAE1C,YAAI,MAAM,WAAW,QAAW;AAC9B,cAAI,eAAe,QAAQ;AAM3B,iBAAO,WAAW,QAAQ,iBAAiB,cAAc;AACvD,qBAAS,KAAK;AAAA,cACZ,MAAM,KAAK,OAAO,SAAS,iBAAiB,IAAI;AAAA,cAChD,QAAQ,KAAK,OAAO,SAAS,mBAAmB,IAAI;AAAA,cACpD,YAAY,KAAK,OAAO,SAAS,uBAAuB,IAAI;AAAA,YAC9D,CAAC;AAED,sBAAU,KAAK,kBAAkB,EAAE,KAAK;AAAA,UAC1C;AAAA,QACF,OAAO;AACL,cAAI,iBAAiB,QAAQ;AAM7B,iBAAO,WACA,QAAQ,iBAAiB,QACzB,QAAQ,kBAAkB,gBAAgB;AAC/C,qBAAS,KAAK;AAAA,cACZ,MAAM,KAAK,OAAO,SAAS,iBAAiB,IAAI;AAAA,cAChD,QAAQ,KAAK,OAAO,SAAS,mBAAmB,IAAI;AAAA,cACpD,YAAY,KAAK,OAAO,SAAS,uBAAuB,IAAI;AAAA,YAC9D,CAAC;AAED,sBAAU,KAAK,kBAAkB,EAAE,KAAK;AAAA,UAC1C;AAAA,QACF;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAEF,YAAQ,oBAAoB;AAoC5B,aAAS,uBAAuB,YAAY,eAAe;AACzD,UAAI,YAAY;AAChB,UAAI,OAAO,eAAe,UAAU;AAClC,oBAAY,KAAK,oBAAoB,UAAU;AAAA,MACjD;AAEA,UAAI,UAAU,KAAK,OAAO,WAAW,SAAS;AAC9C,UAAI,UAAU,KAAK,OAAO,WAAW,SAAS;AAG9C,UAAI,QAAQ,KAAK,OAAO,WAAW,SAAS,CAAC,CAAC;AAC9C,UAAI,aAAa,KAAK,OAAO,WAAW,cAAc,IAAI;AAC1D,UAAI,iBAAiB,KAAK,OAAO,WAAW,kBAAkB,IAAI;AAClE,UAAI,WAAW,KAAK,OAAO,WAAW,UAAU;AAChD,UAAI,OAAO,KAAK,OAAO,WAAW,QAAQ,IAAI;AAI9C,UAAI,WAAW,KAAK,UAAU;AAC5B,cAAM,IAAI,MAAM,0BAA0B,OAAO;AAAA,MACnD;AAEA,UAAI,YAAY;AACd,qBAAa,KAAK,UAAU,UAAU;AAAA,MACxC;AAEA,gBAAU,QACP,IAAI,MAAM,EAIV,IAAI,KAAK,SAAS,EAKlB,IAAI,SAAU,QAAQ;AACrB,eAAO,cAAc,KAAK,WAAW,UAAU,KAAK,KAAK,WAAW,MAAM,IACtE,KAAK,SAAS,YAAY,MAAM,IAChC;AAAA,MACN,CAAC;AAMH,WAAK,SAAS,SAAS,UAAU,MAAM,IAAI,MAAM,GAAG,IAAI;AACxD,WAAK,WAAW,SAAS,UAAU,SAAS,IAAI;AAEhD,WAAK,mBAAmB,KAAK,SAAS,QAAQ,EAAE,IAAI,SAAU,GAAG;AAC/D,eAAO,KAAK,iBAAiB,YAAY,GAAG,aAAa;AAAA,MAC3D,CAAC;AAED,WAAK,aAAa;AAClB,WAAK,iBAAiB;AACtB,WAAK,YAAY;AACjB,WAAK,gBAAgB;AACrB,WAAK,OAAO;AAAA,IACd;AAEA,2BAAuB,YAAY,OAAO,OAAO,kBAAkB,SAAS;AAC5E,2BAAuB,UAAU,WAAW;AAM5C,2BAAuB,UAAU,mBAAmB,SAAS,SAAS;AACpE,UAAI,iBAAiB;AACrB,UAAI,KAAK,cAAc,MAAM;AAC3B,yBAAiB,KAAK,SAAS,KAAK,YAAY,cAAc;AAAA,MAChE;AAEA,UAAI,KAAK,SAAS,IAAI,cAAc,GAAG;AACrC,eAAO,KAAK,SAAS,QAAQ,cAAc;AAAA,MAC7C;AAIA,UAAI;AACJ,WAAK,IAAI,GAAG,IAAI,KAAK,iBAAiB,QAAQ,EAAE,GAAG;AACjD,YAAI,KAAK,iBAAiB,CAAC,KAAK,SAAS;AACvC,iBAAO;AAAA,QACT;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAWA,2BAAuB,gBACrB,SAAS,gCAAgC,YAAY,eAAe;AAClE,UAAI,MAAM,OAAO,OAAO,uBAAuB,SAAS;AAExD,UAAI,QAAQ,IAAI,SAAS,SAAS,UAAU,WAAW,OAAO,QAAQ,GAAG,IAAI;AAC7E,UAAI,UAAU,IAAI,WAAW,SAAS,UAAU,WAAW,SAAS,QAAQ,GAAG,IAAI;AACnF,UAAI,aAAa,WAAW;AAC5B,UAAI,iBAAiB,WAAW;AAAA,QAAwB,IAAI,SAAS,QAAQ;AAAA,QACrB,IAAI;AAAA,MAAU;AACtE,UAAI,OAAO,WAAW;AACtB,UAAI,gBAAgB;AACpB,UAAI,mBAAmB,IAAI,SAAS,QAAQ,EAAE,IAAI,SAAU,GAAG;AAC7D,eAAO,KAAK,iBAAiB,IAAI,YAAY,GAAG,aAAa;AAAA,MAC/D,CAAC;AAOD,UAAI,oBAAoB,WAAW,UAAU,QAAQ,EAAE,MAAM;AAC7D,UAAI,wBAAwB,IAAI,sBAAsB,CAAC;AACvD,UAAI,uBAAuB,IAAI,qBAAqB,CAAC;AAErD,eAAS,IAAI,GAAG,SAAS,kBAAkB,QAAQ,IAAI,QAAQ,KAAK;AAClE,YAAI,aAAa,kBAAkB,CAAC;AACpC,YAAI,cAAc,IAAI;AACtB,oBAAY,gBAAgB,WAAW;AACvC,oBAAY,kBAAkB,WAAW;AAEzC,YAAI,WAAW,QAAQ;AACrB,sBAAY,SAAS,QAAQ,QAAQ,WAAW,MAAM;AACtD,sBAAY,eAAe,WAAW;AACtC,sBAAY,iBAAiB,WAAW;AAExC,cAAI,WAAW,MAAM;AACnB,wBAAY,OAAO,MAAM,QAAQ,WAAW,IAAI;AAAA,UAClD;AAEA,+BAAqB,KAAK,WAAW;AAAA,QACvC;AAEA,8BAAsB,KAAK,WAAW;AAAA,MACxC;AAEA,gBAAU,IAAI,oBAAoB,KAAK,0BAA0B;AAEjE,aAAO;AAAA,IACT;AAKF,2BAAuB,UAAU,WAAW;AAK5C,WAAO,eAAe,uBAAuB,WAAW,WAAW;AAAA,MACjE,KAAK,WAAY;AACf,eAAO,KAAK,iBAAiB,MAAM;AAAA,MACrC;AAAA,IACF,CAAC;AAKD,aAAS,UAAU;AACjB,WAAK,gBAAgB;AACrB,WAAK,kBAAkB;AACvB,WAAK,SAAS;AACd,WAAK,eAAe;AACpB,WAAK,iBAAiB;AACtB,WAAK,OAAO;AAAA,IACd;AAOA,2BAAuB,UAAU,iBAC/B,SAAS,gCAAgC,MAAM,aAAa;AAC1D,UAAI,gBAAgB;AACpB,UAAI,0BAA0B;AAC9B,UAAI,uBAAuB;AAC3B,UAAI,yBAAyB;AAC7B,UAAI,iBAAiB;AACrB,UAAI,eAAe;AACnB,UAAI,SAAS,KAAK;AAClB,UAAI,QAAQ;AACZ,UAAI,iBAAiB,CAAC;AACtB,UAAI,OAAO,CAAC;AACZ,UAAI,mBAAmB,CAAC;AACxB,UAAI,oBAAoB,CAAC;AACzB,UAAI,SAAS,KAAK,SAAS,KAAK;AAEhC,aAAO,QAAQ,QAAQ;AACrB,YAAI,KAAK,OAAO,KAAK,MAAM,KAAK;AAC9B;AACA;AACA,oCAA0B;AAAA,QAC5B,WACS,KAAK,OAAO,KAAK,MAAM,KAAK;AACnC;AAAA,QACF,OACK;AACH,oBAAU,IAAI,QAAQ;AACtB,kBAAQ,gBAAgB;AAOxB,eAAK,MAAM,OAAO,MAAM,QAAQ,OAAO;AACrC,gBAAI,KAAK,wBAAwB,MAAM,GAAG,GAAG;AAC3C;AAAA,YACF;AAAA,UACF;AACA,gBAAM,KAAK,MAAM,OAAO,GAAG;AAE3B,oBAAU,eAAe,GAAG;AAC5B,cAAI,SAAS;AACX,qBAAS,IAAI;AAAA,UACf,OAAO;AACL,sBAAU,CAAC;AACX,mBAAO,QAAQ,KAAK;AAClB,wBAAU,OAAO,MAAM,OAAO,IAAI;AAClC,sBAAQ,KAAK;AACb,sBAAQ,KAAK;AACb,sBAAQ,KAAK,KAAK;AAAA,YACpB;AAEA,gBAAI,QAAQ,WAAW,GAAG;AACxB,oBAAM,IAAI,MAAM,wCAAwC;AAAA,YAC1D;AAEA,gBAAI,QAAQ,WAAW,GAAG;AACxB,oBAAM,IAAI,MAAM,wCAAwC;AAAA,YAC1D;AAEA,2BAAe,GAAG,IAAI;AAAA,UACxB;AAGA,kBAAQ,kBAAkB,0BAA0B,QAAQ,CAAC;AAC7D,oCAA0B,QAAQ;AAElC,cAAI,QAAQ,SAAS,GAAG;AAEtB,oBAAQ,SAAS,iBAAiB,QAAQ,CAAC;AAC3C,8BAAkB,QAAQ,CAAC;AAG3B,oBAAQ,eAAe,uBAAuB,QAAQ,CAAC;AACvD,mCAAuB,QAAQ;AAE/B,oBAAQ,gBAAgB;AAGxB,oBAAQ,iBAAiB,yBAAyB,QAAQ,CAAC;AAC3D,qCAAyB,QAAQ;AAEjC,gBAAI,QAAQ,SAAS,GAAG;AAEtB,sBAAQ,OAAO,eAAe,QAAQ,CAAC;AACvC,8BAAgB,QAAQ,CAAC;AAAA,YAC3B;AAAA,UACF;AAEA,4BAAkB,KAAK,OAAO;AAC9B,cAAI,OAAO,QAAQ,iBAAiB,UAAU;AAC5C,6BAAiB,KAAK,OAAO;AAAA,UAC/B;AAAA,QACF;AAAA,MACF;AAEA,gBAAU,mBAAmB,KAAK,mCAAmC;AACrE,WAAK,sBAAsB;AAE3B,gBAAU,kBAAkB,KAAK,0BAA0B;AAC3D,WAAK,qBAAqB;AAAA,IAC5B;AAMF,2BAAuB,UAAU,eAC/B,SAAS,8BAA8B,SAAS,WAAW,WACpB,aAAa,aAAa,OAAO;AAMtE,UAAI,QAAQ,SAAS,KAAK,GAAG;AAC3B,cAAM,IAAI,UAAU,kDACE,QAAQ,SAAS,CAAC;AAAA,MAC1C;AACA,UAAI,QAAQ,WAAW,IAAI,GAAG;AAC5B,cAAM,IAAI,UAAU,oDACE,QAAQ,WAAW,CAAC;AAAA,MAC5C;AAEA,aAAO,aAAa,OAAO,SAAS,WAAW,aAAa,KAAK;AAAA,IACnE;AAMF,2BAAuB,UAAU,qBAC/B,SAAS,uCAAuC;AAC9C,eAAS,QAAQ,GAAG,QAAQ,KAAK,mBAAmB,QAAQ,EAAE,OAAO;AACnE,YAAI,UAAU,KAAK,mBAAmB,KAAK;AAM3C,YAAI,QAAQ,IAAI,KAAK,mBAAmB,QAAQ;AAC9C,cAAI,cAAc,KAAK,mBAAmB,QAAQ,CAAC;AAEnD,cAAI,QAAQ,kBAAkB,YAAY,eAAe;AACvD,oBAAQ,sBAAsB,YAAY,kBAAkB;AAC5D;AAAA,UACF;AAAA,QACF;AAGA,gBAAQ,sBAAsB;AAAA,MAChC;AAAA,IACF;AA0BF,2BAAuB,UAAU,sBAC/B,SAAS,sCAAsC,OAAO;AACpD,UAAI,SAAS;AAAA,QACX,eAAe,KAAK,OAAO,OAAO,MAAM;AAAA,QACxC,iBAAiB,KAAK,OAAO,OAAO,QAAQ;AAAA,MAC9C;AAEA,UAAI,QAAQ,KAAK;AAAA,QACf;AAAA,QACA,KAAK;AAAA,QACL;AAAA,QACA;AAAA,QACA,KAAK;AAAA,QACL,KAAK,OAAO,OAAO,QAAQ,kBAAkB,oBAAoB;AAAA,MACnE;AAEA,UAAI,SAAS,GAAG;AACd,YAAI,UAAU,KAAK,mBAAmB,KAAK;AAE3C,YAAI,QAAQ,kBAAkB,OAAO,eAAe;AAClD,cAAI,SAAS,KAAK,OAAO,SAAS,UAAU,IAAI;AAChD,cAAI,WAAW,MAAM;AACnB,qBAAS,KAAK,SAAS,GAAG,MAAM;AAChC,qBAAS,KAAK,iBAAiB,KAAK,YAAY,QAAQ,KAAK,aAAa;AAAA,UAC5E;AACA,cAAI,OAAO,KAAK,OAAO,SAAS,QAAQ,IAAI;AAC5C,cAAI,SAAS,MAAM;AACjB,mBAAO,KAAK,OAAO,GAAG,IAAI;AAAA,UAC5B;AACA,iBAAO;AAAA,YACL;AAAA,YACA,MAAM,KAAK,OAAO,SAAS,gBAAgB,IAAI;AAAA,YAC/C,QAAQ,KAAK,OAAO,SAAS,kBAAkB,IAAI;AAAA,YACnD;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAEA,aAAO;AAAA,QACL,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,MAAM;AAAA,MACR;AAAA,IACF;AAMF,2BAAuB,UAAU,0BAC/B,SAAS,iDAAiD;AACxD,UAAI,CAAC,KAAK,gBAAgB;AACxB,eAAO;AAAA,MACT;AACA,aAAO,KAAK,eAAe,UAAU,KAAK,SAAS,KAAK,KACtD,CAAC,KAAK,eAAe,KAAK,SAAU,IAAI;AAAE,eAAO,MAAM;AAAA,MAAM,CAAC;AAAA,IAClE;AAOF,2BAAuB,UAAU,mBAC/B,SAAS,mCAAmC,SAAS,eAAe;AAClE,UAAI,CAAC,KAAK,gBAAgB;AACxB,eAAO;AAAA,MACT;AAEA,UAAI,QAAQ,KAAK,iBAAiB,OAAO;AACzC,UAAI,SAAS,GAAG;AACd,eAAO,KAAK,eAAe,KAAK;AAAA,MAClC;AAEA,UAAI,iBAAiB;AACrB,UAAI,KAAK,cAAc,MAAM;AAC3B,yBAAiB,KAAK,SAAS,KAAK,YAAY,cAAc;AAAA,MAChE;AAEA,UAAI;AACJ,UAAI,KAAK,cAAc,SACf,MAAM,KAAK,SAAS,KAAK,UAAU,IAAI;AAK7C,YAAI,iBAAiB,eAAe,QAAQ,cAAc,EAAE;AAC5D,YAAI,IAAI,UAAU,UACX,KAAK,SAAS,IAAI,cAAc,GAAG;AACxC,iBAAO,KAAK,eAAe,KAAK,SAAS,QAAQ,cAAc,CAAC;AAAA,QAClE;AAEA,aAAK,CAAC,IAAI,QAAQ,IAAI,QAAQ,QACvB,KAAK,SAAS,IAAI,MAAM,cAAc,GAAG;AAC9C,iBAAO,KAAK,eAAe,KAAK,SAAS,QAAQ,MAAM,cAAc,CAAC;AAAA,QACxE;AAAA,MACF;AAMA,UAAI,eAAe;AACjB,eAAO;AAAA,MACT,OACK;AACH,cAAM,IAAI,MAAM,MAAM,iBAAiB,4BAA4B;AAAA,MACrE;AAAA,IACF;AAyBF,2BAAuB,UAAU,uBAC/B,SAAS,uCAAuC,OAAO;AACrD,UAAI,SAAS,KAAK,OAAO,OAAO,QAAQ;AACxC,eAAS,KAAK,iBAAiB,MAAM;AACrC,UAAI,SAAS,GAAG;AACd,eAAO;AAAA,UACL,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,YAAY;AAAA,QACd;AAAA,MACF;AAEA,UAAI,SAAS;AAAA,QACX;AAAA,QACA,cAAc,KAAK,OAAO,OAAO,MAAM;AAAA,QACvC,gBAAgB,KAAK,OAAO,OAAO,QAAQ;AAAA,MAC7C;AAEA,UAAI,QAAQ,KAAK;AAAA,QACf;AAAA,QACA,KAAK;AAAA,QACL;AAAA,QACA;AAAA,QACA,KAAK;AAAA,QACL,KAAK,OAAO,OAAO,QAAQ,kBAAkB,oBAAoB;AAAA,MACnE;AAEA,UAAI,SAAS,GAAG;AACd,YAAI,UAAU,KAAK,kBAAkB,KAAK;AAE1C,YAAI,QAAQ,WAAW,OAAO,QAAQ;AACpC,iBAAO;AAAA,YACL,MAAM,KAAK,OAAO,SAAS,iBAAiB,IAAI;AAAA,YAChD,QAAQ,KAAK,OAAO,SAAS,mBAAmB,IAAI;AAAA,YACpD,YAAY,KAAK,OAAO,SAAS,uBAAuB,IAAI;AAAA,UAC9D;AAAA,QACF;AAAA,MACF;AAEA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,YAAY;AAAA,MACd;AAAA,IACF;AAEF,YAAQ,yBAAyB;AAmDjC,aAAS,yBAAyB,YAAY,eAAe;AAC3D,UAAI,YAAY;AAChB,UAAI,OAAO,eAAe,UAAU;AAClC,oBAAY,KAAK,oBAAoB,UAAU;AAAA,MACjD;AAEA,UAAI,UAAU,KAAK,OAAO,WAAW,SAAS;AAC9C,UAAI,WAAW,KAAK,OAAO,WAAW,UAAU;AAEhD,UAAI,WAAW,KAAK,UAAU;AAC5B,cAAM,IAAI,MAAM,0BAA0B,OAAO;AAAA,MACnD;AAEA,WAAK,WAAW,IAAI,SAAS;AAC7B,WAAK,SAAS,IAAI,SAAS;AAE3B,UAAI,aAAa;AAAA,QACf,MAAM;AAAA,QACN,QAAQ;AAAA,MACV;AACA,WAAK,YAAY,SAAS,IAAI,SAAU,GAAG;AACzC,YAAI,EAAE,KAAK;AAGT,gBAAM,IAAI,MAAM,oDAAoD;AAAA,QACtE;AACA,YAAI,SAAS,KAAK,OAAO,GAAG,QAAQ;AACpC,YAAI,aAAa,KAAK,OAAO,QAAQ,MAAM;AAC3C,YAAI,eAAe,KAAK,OAAO,QAAQ,QAAQ;AAE/C,YAAI,aAAa,WAAW,QACvB,eAAe,WAAW,QAAQ,eAAe,WAAW,QAAS;AACxE,gBAAM,IAAI,MAAM,sDAAsD;AAAA,QACxE;AACA,qBAAa;AAEb,eAAO;AAAA,UACL,iBAAiB;AAAA;AAAA;AAAA,YAGf,eAAe,aAAa;AAAA,YAC5B,iBAAiB,eAAe;AAAA,UAClC;AAAA,UACA,UAAU,IAAI,kBAAkB,KAAK,OAAO,GAAG,KAAK,GAAG,aAAa;AAAA,QACtE;AAAA,MACF,CAAC;AAAA,IACH;AAEA,6BAAyB,YAAY,OAAO,OAAO,kBAAkB,SAAS;AAC9E,6BAAyB,UAAU,cAAc;AAKjD,6BAAyB,UAAU,WAAW;AAK9C,WAAO,eAAe,yBAAyB,WAAW,WAAW;AAAA,MACnE,KAAK,WAAY;AACf,YAAI,UAAU,CAAC;AACf,iBAAS,IAAI,GAAG,IAAI,KAAK,UAAU,QAAQ,KAAK;AAC9C,mBAAS,IAAI,GAAG,IAAI,KAAK,UAAU,CAAC,EAAE,SAAS,QAAQ,QAAQ,KAAK;AAClE,oBAAQ,KAAK,KAAK,UAAU,CAAC,EAAE,SAAS,QAAQ,CAAC,CAAC;AAAA,UACpD;AAAA,QACF;AACA,eAAO;AAAA,MACT;AAAA,IACF,CAAC;AAqBD,6BAAyB,UAAU,sBACjC,SAAS,6CAA6C,OAAO;AAC3D,UAAI,SAAS;AAAA,QACX,eAAe,KAAK,OAAO,OAAO,MAAM;AAAA,QACxC,iBAAiB,KAAK,OAAO,OAAO,QAAQ;AAAA,MAC9C;AAIA,UAAI,eAAe,aAAa;AAAA,QAAO;AAAA,QAAQ,KAAK;AAAA,QAClD,SAASC,SAAQC,UAAS;AACxB,cAAI,MAAMD,QAAO,gBAAgBC,SAAQ,gBAAgB;AACzD,cAAI,KAAK;AACP,mBAAO;AAAA,UACT;AAEA,iBAAQD,QAAO,kBACPC,SAAQ,gBAAgB;AAAA,QAClC;AAAA,MAAC;AACH,UAAI,UAAU,KAAK,UAAU,YAAY;AAEzC,UAAI,CAAC,SAAS;AACZ,eAAO;AAAA,UACL,QAAQ;AAAA,UACR,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,MAAM;AAAA,QACR;AAAA,MACF;AAEA,aAAO,QAAQ,SAAS,oBAAoB;AAAA,QAC1C,MAAM,OAAO,iBACV,QAAQ,gBAAgB,gBAAgB;AAAA,QAC3C,QAAQ,OAAO,mBACZ,QAAQ,gBAAgB,kBAAkB,OAAO,gBAC/C,QAAQ,gBAAgB,kBAAkB,IAC1C;AAAA,QACL,MAAM,MAAM;AAAA,MACd,CAAC;AAAA,IACH;AAMF,6BAAyB,UAAU,0BACjC,SAAS,mDAAmD;AAC1D,aAAO,KAAK,UAAU,MAAM,SAAU,GAAG;AACvC,eAAO,EAAE,SAAS,wBAAwB;AAAA,MAC5C,CAAC;AAAA,IACH;AAOF,6BAAyB,UAAU,mBACjC,SAAS,0CAA0C,SAAS,eAAe;AACzE,eAAS,IAAI,GAAG,IAAI,KAAK,UAAU,QAAQ,KAAK;AAC9C,YAAI,UAAU,KAAK,UAAU,CAAC;AAE9B,YAAI,UAAU,QAAQ,SAAS,iBAAiB,SAAS,IAAI;AAC7D,YAAI,SAAS;AACX,iBAAO;AAAA,QACT;AAAA,MACF;AACA,UAAI,eAAe;AACjB,eAAO;AAAA,MACT,OACK;AACH,cAAM,IAAI,MAAM,MAAM,UAAU,4BAA4B;AAAA,MAC9D;AAAA,IACF;AAoBF,6BAAyB,UAAU,uBACjC,SAAS,8CAA8C,OAAO;AAC5D,eAAS,IAAI,GAAG,IAAI,KAAK,UAAU,QAAQ,KAAK;AAC9C,YAAI,UAAU,KAAK,UAAU,CAAC;AAI9B,YAAI,QAAQ,SAAS,iBAAiB,KAAK,OAAO,OAAO,QAAQ,CAAC,MAAM,IAAI;AAC1E;AAAA,QACF;AACA,YAAI,oBAAoB,QAAQ,SAAS,qBAAqB,KAAK;AACnE,YAAI,mBAAmB;AACrB,cAAI,MAAM;AAAA,YACR,MAAM,kBAAkB,QACrB,QAAQ,gBAAgB,gBAAgB;AAAA,YAC3C,QAAQ,kBAAkB,UACvB,QAAQ,gBAAgB,kBAAkB,kBAAkB,OAC1D,QAAQ,gBAAgB,kBAAkB,IAC1C;AAAA,UACP;AACA,iBAAO;AAAA,QACT;AAAA,MACF;AAEA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,QAAQ;AAAA,MACV;AAAA,IACF;AAOF,6BAAyB,UAAU,iBACjC,SAAS,uCAAuC,MAAM,aAAa;AACjE,WAAK,sBAAsB,CAAC;AAC5B,WAAK,qBAAqB,CAAC;AAC3B,eAAS,IAAI,GAAG,IAAI,KAAK,UAAU,QAAQ,KAAK;AAC9C,YAAI,UAAU,KAAK,UAAU,CAAC;AAC9B,YAAI,kBAAkB,QAAQ,SAAS;AACvC,iBAAS,IAAI,GAAG,IAAI,gBAAgB,QAAQ,KAAK;AAC/C,cAAI,UAAU,gBAAgB,CAAC;AAE/B,cAAI,SAAS,QAAQ,SAAS,SAAS,GAAG,QAAQ,MAAM;AACxD,mBAAS,KAAK,iBAAiB,QAAQ,SAAS,YAAY,QAAQ,KAAK,aAAa;AACtF,eAAK,SAAS,IAAI,MAAM;AACxB,mBAAS,KAAK,SAAS,QAAQ,MAAM;AAErC,cAAI,OAAO;AACX,cAAI,QAAQ,MAAM;AAChB,mBAAO,QAAQ,SAAS,OAAO,GAAG,QAAQ,IAAI;AAC9C,iBAAK,OAAO,IAAI,IAAI;AACpB,mBAAO,KAAK,OAAO,QAAQ,IAAI;AAAA,UACjC;AAMA,cAAI,kBAAkB;AAAA,YACpB;AAAA,YACA,eAAe,QAAQ,iBACpB,QAAQ,gBAAgB,gBAAgB;AAAA,YAC3C,iBAAiB,QAAQ,mBACtB,QAAQ,gBAAgB,kBAAkB,QAAQ,gBACjD,QAAQ,gBAAgB,kBAAkB,IAC1C;AAAA,YACJ,cAAc,QAAQ;AAAA,YACtB,gBAAgB,QAAQ;AAAA,YACxB;AAAA,UACF;AAEA,eAAK,oBAAoB,KAAK,eAAe;AAC7C,cAAI,OAAO,gBAAgB,iBAAiB,UAAU;AACpD,iBAAK,mBAAmB,KAAK,eAAe;AAAA,UAC9C;AAAA,QACF;AAAA,MACF;AAEA,gBAAU,KAAK,qBAAqB,KAAK,mCAAmC;AAC5E,gBAAU,KAAK,oBAAoB,KAAK,0BAA0B;AAAA,IACpE;AAEF,YAAQ,2BAA2B;AAAA;AAAA;;;ACxnCnC;AAAA;AAOA,QAAI,qBAAqB,+BAAkC;AAC3D,QAAI,OAAO;AAIX,QAAI,gBAAgB;AAGpB,QAAI,eAAe;AAKnB,QAAI,eAAe;AAcnB,aAAS,WAAW,OAAO,SAAS,SAAS,SAAS,OAAO;AAC3D,WAAK,WAAW,CAAC;AACjB,WAAK,iBAAiB,CAAC;AACvB,WAAK,OAAO,SAAS,OAAO,OAAO;AACnC,WAAK,SAAS,WAAW,OAAO,OAAO;AACvC,WAAK,SAAS,WAAW,OAAO,OAAO;AACvC,WAAK,OAAO,SAAS,OAAO,OAAO;AACnC,WAAK,YAAY,IAAI;AACrB,UAAI,WAAW;AAAM,aAAK,IAAI,OAAO;AAAA,IACvC;AAUA,eAAW,0BACT,SAAS,mCAAmC,gBAAgB,oBAAoB,eAAe;AAG7F,UAAI,OAAO,IAAI,WAAW;AAM1B,UAAI,iBAAiB,eAAe,MAAM,aAAa;AACvD,UAAI,sBAAsB;AAC1B,UAAI,gBAAgB,WAAW;AAC7B,YAAI,eAAe,YAAY;AAE/B,YAAI,UAAU,YAAY,KAAK;AAC/B,eAAO,eAAe;AAEtB,iBAAS,cAAc;AACrB,iBAAO,sBAAsB,eAAe,SACxC,eAAe,qBAAqB,IAAI;AAAA,QAC9C;AAAA,MACF;AAGA,UAAI,oBAAoB,GAAG,sBAAsB;AAKjD,UAAI,cAAc;AAElB,yBAAmB,YAAY,SAAU,SAAS;AAChD,YAAI,gBAAgB,MAAM;AAGxB,cAAI,oBAAoB,QAAQ,eAAe;AAE7C,+BAAmB,aAAa,cAAc,CAAC;AAC/C;AACA,kCAAsB;AAAA,UAExB,OAAO;AAIL,gBAAI,WAAW,eAAe,mBAAmB,KAAK;AACtD,gBAAI,OAAO,SAAS,OAAO,GAAG,QAAQ,kBACR,mBAAmB;AACjD,2BAAe,mBAAmB,IAAI,SAAS,OAAO,QAAQ,kBAC1B,mBAAmB;AACvD,kCAAsB,QAAQ;AAC9B,+BAAmB,aAAa,IAAI;AAEpC,0BAAc;AACd;AAAA,UACF;AAAA,QACF;AAIA,eAAO,oBAAoB,QAAQ,eAAe;AAChD,eAAK,IAAI,cAAc,CAAC;AACxB;AAAA,QACF;AACA,YAAI,sBAAsB,QAAQ,iBAAiB;AACjD,cAAI,WAAW,eAAe,mBAAmB,KAAK;AACtD,eAAK,IAAI,SAAS,OAAO,GAAG,QAAQ,eAAe,CAAC;AACpD,yBAAe,mBAAmB,IAAI,SAAS,OAAO,QAAQ,eAAe;AAC7E,gCAAsB,QAAQ;AAAA,QAChC;AACA,sBAAc;AAAA,MAChB,GAAG,IAAI;AAEP,UAAI,sBAAsB,eAAe,QAAQ;AAC/C,YAAI,aAAa;AAEf,6BAAmB,aAAa,cAAc,CAAC;AAAA,QACjD;AAEA,aAAK,IAAI,eAAe,OAAO,mBAAmB,EAAE,KAAK,EAAE,CAAC;AAAA,MAC9D;AAGA,yBAAmB,QAAQ,QAAQ,SAAU,YAAY;AACvD,YAAI,UAAU,mBAAmB,iBAAiB,UAAU;AAC5D,YAAI,WAAW,MAAM;AACnB,cAAI,iBAAiB,MAAM;AACzB,yBAAa,KAAK,KAAK,eAAe,UAAU;AAAA,UAClD;AACA,eAAK,iBAAiB,YAAY,OAAO;AAAA,QAC3C;AAAA,MACF,CAAC;AAED,aAAO;AAEP,eAAS,mBAAmB,SAAS,MAAM;AACzC,YAAI,YAAY,QAAQ,QAAQ,WAAW,QAAW;AACpD,eAAK,IAAI,IAAI;AAAA,QACf,OAAO;AACL,cAAI,SAAS,gBACT,KAAK,KAAK,eAAe,QAAQ,MAAM,IACvC,QAAQ;AACZ,eAAK,IAAI,IAAI;AAAA,YAAW,QAAQ;AAAA,YACR,QAAQ;AAAA,YACR;AAAA,YACA;AAAA,YACA,QAAQ;AAAA,UAAI,CAAC;AAAA,QACvC;AAAA,MACF;AAAA,IACF;AAQF,eAAW,UAAU,MAAM,SAAS,eAAe,QAAQ;AACzD,UAAI,MAAM,QAAQ,MAAM,GAAG;AACzB,eAAO,QAAQ,SAAU,OAAO;AAC9B,eAAK,IAAI,KAAK;AAAA,QAChB,GAAG,IAAI;AAAA,MACT,WACS,OAAO,YAAY,KAAK,OAAO,WAAW,UAAU;AAC3D,YAAI,QAAQ;AACV,eAAK,SAAS,KAAK,MAAM;AAAA,QAC3B;AAAA,MACF,OACK;AACH,cAAM,IAAI;AAAA,UACR,gFAAgF;AAAA,QAClF;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAQA,eAAW,UAAU,UAAU,SAAS,mBAAmB,QAAQ;AACjE,UAAI,MAAM,QAAQ,MAAM,GAAG;AACzB,iBAAS,IAAI,OAAO,SAAO,GAAG,KAAK,GAAG,KAAK;AACzC,eAAK,QAAQ,OAAO,CAAC,CAAC;AAAA,QACxB;AAAA,MACF,WACS,OAAO,YAAY,KAAK,OAAO,WAAW,UAAU;AAC3D,aAAK,SAAS,QAAQ,MAAM;AAAA,MAC9B,OACK;AACH,cAAM,IAAI;AAAA,UACR,gFAAgF;AAAA,QAClF;AAAA,MACF;AACA,aAAO;AAAA,IACT;AASA,eAAW,UAAU,OAAO,SAAS,gBAAgB,KAAK;AACxD,UAAI;AACJ,eAAS,IAAI,GAAG,MAAM,KAAK,SAAS,QAAQ,IAAI,KAAK,KAAK;AACxD,gBAAQ,KAAK,SAAS,CAAC;AACvB,YAAI,MAAM,YAAY,GAAG;AACvB,gBAAM,KAAK,GAAG;AAAA,QAChB,OACK;AACH,cAAI,UAAU,IAAI;AAChB,gBAAI,OAAO;AAAA,cAAE,QAAQ,KAAK;AAAA,cACb,MAAM,KAAK;AAAA,cACX,QAAQ,KAAK;AAAA,cACb,MAAM,KAAK;AAAA,YAAK,CAAC;AAAA,UAChC;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAQA,eAAW,UAAU,OAAO,SAAS,gBAAgB,MAAM;AACzD,UAAI;AACJ,UAAI;AACJ,UAAI,MAAM,KAAK,SAAS;AACxB,UAAI,MAAM,GAAG;AACX,sBAAc,CAAC;AACf,aAAK,IAAI,GAAG,IAAI,MAAI,GAAG,KAAK;AAC1B,sBAAY,KAAK,KAAK,SAAS,CAAC,CAAC;AACjC,sBAAY,KAAK,IAAI;AAAA,QACvB;AACA,oBAAY,KAAK,KAAK,SAAS,CAAC,CAAC;AACjC,aAAK,WAAW;AAAA,MAClB;AACA,aAAO;AAAA,IACT;AASA,eAAW,UAAU,eAAe,SAAS,wBAAwB,UAAU,cAAc;AAC3F,UAAI,YAAY,KAAK,SAAS,KAAK,SAAS,SAAS,CAAC;AACtD,UAAI,UAAU,YAAY,GAAG;AAC3B,kBAAU,aAAa,UAAU,YAAY;AAAA,MAC/C,WACS,OAAO,cAAc,UAAU;AACtC,aAAK,SAAS,KAAK,SAAS,SAAS,CAAC,IAAI,UAAU,QAAQ,UAAU,YAAY;AAAA,MACpF,OACK;AACH,aAAK,SAAS,KAAK,GAAG,QAAQ,UAAU,YAAY,CAAC;AAAA,MACvD;AACA,aAAO;AAAA,IACT;AASA,eAAW,UAAU,mBACnB,SAAS,4BAA4B,aAAa,gBAAgB;AAChE,WAAK,eAAe,KAAK,YAAY,WAAW,CAAC,IAAI;AAAA,IACvD;AAQF,eAAW,UAAU,qBACnB,SAAS,8BAA8B,KAAK;AAC1C,eAAS,IAAI,GAAG,MAAM,KAAK,SAAS,QAAQ,IAAI,KAAK,KAAK;AACxD,YAAI,KAAK,SAAS,CAAC,EAAE,YAAY,GAAG;AAClC,eAAK,SAAS,CAAC,EAAE,mBAAmB,GAAG;AAAA,QACzC;AAAA,MACF;AAEA,UAAI,UAAU,OAAO,KAAK,KAAK,cAAc;AAC7C,eAAS,IAAI,GAAG,MAAM,QAAQ,QAAQ,IAAI,KAAK,KAAK;AAClD,YAAI,KAAK,cAAc,QAAQ,CAAC,CAAC,GAAG,KAAK,eAAe,QAAQ,CAAC,CAAC,CAAC;AAAA,MACrE;AAAA,IACF;AAMF,eAAW,UAAU,WAAW,SAAS,sBAAsB;AAC7D,UAAI,MAAM;AACV,WAAK,KAAK,SAAU,OAAO;AACzB,eAAO;AAAA,MACT,CAAC;AACD,aAAO;AAAA,IACT;AAMA,eAAW,UAAU,wBAAwB,SAAS,iCAAiC,OAAO;AAC5F,UAAI,YAAY;AAAA,QACd,MAAM;AAAA,QACN,MAAM;AAAA,QACN,QAAQ;AAAA,MACV;AACA,UAAI,MAAM,IAAI,mBAAmB,KAAK;AACtC,UAAI,sBAAsB;AAC1B,UAAI,qBAAqB;AACzB,UAAI,mBAAmB;AACvB,UAAI,qBAAqB;AACzB,UAAI,mBAAmB;AACvB,WAAK,KAAK,SAAU,OAAO,UAAU;AACnC,kBAAU,QAAQ;AAClB,YAAI,SAAS,WAAW,QACjB,SAAS,SAAS,QAClB,SAAS,WAAW,MAAM;AAC/B,cAAG,uBAAuB,SAAS,UAC7B,qBAAqB,SAAS,QAC9B,uBAAuB,SAAS,UAChC,qBAAqB,SAAS,MAAM;AACxC,gBAAI,WAAW;AAAA,cACb,QAAQ,SAAS;AAAA,cACjB,UAAU;AAAA,gBACR,MAAM,SAAS;AAAA,gBACf,QAAQ,SAAS;AAAA,cACnB;AAAA,cACA,WAAW;AAAA,gBACT,MAAM,UAAU;AAAA,gBAChB,QAAQ,UAAU;AAAA,cACpB;AAAA,cACA,MAAM,SAAS;AAAA,YACjB,CAAC;AAAA,UACH;AACA,+BAAqB,SAAS;AAC9B,6BAAmB,SAAS;AAC5B,+BAAqB,SAAS;AAC9B,6BAAmB,SAAS;AAC5B,gCAAsB;AAAA,QACxB,WAAW,qBAAqB;AAC9B,cAAI,WAAW;AAAA,YACb,WAAW;AAAA,cACT,MAAM,UAAU;AAAA,cAChB,QAAQ,UAAU;AAAA,YACpB;AAAA,UACF,CAAC;AACD,+BAAqB;AACrB,gCAAsB;AAAA,QACxB;AACA,iBAAS,MAAM,GAAG,SAAS,MAAM,QAAQ,MAAM,QAAQ,OAAO;AAC5D,cAAI,MAAM,WAAW,GAAG,MAAM,cAAc;AAC1C,sBAAU;AACV,sBAAU,SAAS;AAEnB,gBAAI,MAAM,MAAM,QAAQ;AACtB,mCAAqB;AACrB,oCAAsB;AAAA,YACxB,WAAW,qBAAqB;AAC9B,kBAAI,WAAW;AAAA,gBACb,QAAQ,SAAS;AAAA,gBACjB,UAAU;AAAA,kBACR,MAAM,SAAS;AAAA,kBACf,QAAQ,SAAS;AAAA,gBACnB;AAAA,gBACA,WAAW;AAAA,kBACT,MAAM,UAAU;AAAA,kBAChB,QAAQ,UAAU;AAAA,gBACpB;AAAA,gBACA,MAAM,SAAS;AAAA,cACjB,CAAC;AAAA,YACH;AAAA,UACF,OAAO;AACL,sBAAU;AAAA,UACZ;AAAA,QACF;AAAA,MACF,CAAC;AACD,WAAK,mBAAmB,SAAU,YAAY,eAAe;AAC3D,YAAI,iBAAiB,YAAY,aAAa;AAAA,MAChD,CAAC;AAED,aAAO,EAAE,MAAM,UAAU,MAAM,IAAS;AAAA,IAC1C;AAEA,YAAQ,aAAa;AAAA;AAAA;;;AC5ZrB;AAAA;AAKA,YAAQ,qBAAqB,+BAAsC;AACnE,YAAQ,oBAAoB,8BAAqC;AACjE,YAAQ,aAAa,sBAA6B;AAAA;AAAA;;;ACPlD;AAAA;AAAA;AAOA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAE5D,QAAI,SAAS;AACb,QAAI,YAAY;AAEhB,QAAM,oBAAoB;AAAA;AAAA,MAEtB,gBAAgB;AAAA,MAChB,8BAA8B;AAAA,MAC9B,0CAA0C;AAAA,MAC1C,yBAAyB;AAAA,MACzB,iCAAiC;AAAA,MACjC,0BAA0B;AAAA,MAC1B,4BAA4B;AAAA,MAC5B,mBAAmB;AAAA,MACnB,4BAA4B;AAAA,MAC5B,uBAAuB;AAAA;AAAA,MAEvB,8BAA8B;AAAA,MAC9B,kCAAkC;AAAA,MAClC,6BAA6B;AAAA,MAC7B,6BAA6B;AAAA;AAAA;AAAA;AAAA,MAI7B,kBAAkB;AAAA,IACtB;AAEA,QAAM,gBAAgB;AAAA;AAAA,MAElB,CAAC,kBAAkB,cAAc,GAAG;AAAA,MACpC,CAAC,kBAAkB,4BAA4B,GAAG;AAAA,MAClD,CAAC,kBAAkB,wCAAwC,GAAG;AAAA,MAC9D,CAAC,kBAAkB,uBAAuB,GAAG;AAAA,MAC7C,CAAC,kBAAkB,+BAA+B,GAAG;AAAA,MACrD,CAAC,kBAAkB,wBAAwB,GAAG;AAAA,MAC9C,CAAC,kBAAkB,0BAA0B,GAAG;AAAA,MAChD,CAAC,kBAAkB,iBAAiB,GAAG;AAAA,MACvC,CAAC,kBAAkB,0BAA0B,GAAG;AAAA,MAChD,CAAC,kBAAkB,qBAAqB,GAAG;AAAA;AAAA,MAE3C,CAAC,kBAAkB,4BAA4B,GAAG;AAAA,MAClD,CAAC,kBAAkB,gCAAgC,GAAG;AAAA,MACtD,CAAC,kBAAkB,2BAA2B,GAAG;AAAA,MACjD,CAAC,kBAAkB,2BAA2B,GAAG;AAAA,IACrD;AACA,aAAS,mBAAmB,MAAM,KAAK,UAAU,CAAC,GAAG;AACjD,YAAM,EAAE,QAAQ,UAAU,KAAK,IAAI;AACnC,YAAM,MAAM,OAAO,QAAQ,YAAY,eAAe,IAAI,KAAK,IAAI,GAAI,QAAQ,CAAC,CAAE;AAElF,YAAM,QAAQ,IAAI,YAAY,OAAO,GAAG,CAAC;AACzC,YAAM,OAAO;AACb,UAAI,KAAK;AACL,cAAM,WAAW;AAAA,MACrB;AACA,YAAM,SAAS;AACf,aAAO;AAAA,IACX;AAEA,aAAS,eAAe,OAAO;AAC3B,YAAM;AAAA,IACV;AAEA,QAAM,eAAe;AAAA,MACjB,OAAO,EAAE,MAAM,GAAG,QAAQ,GAAG,QAAQ,EAAE;AAAA,MACvC,KAAK,EAAE,MAAM,GAAG,QAAQ,GAAG,QAAQ,EAAE;AAAA,IACzC;AACA,aAAS,eAAe,MAAM,QAAQ,QAAQ;AAC1C,aAAO,EAAE,MAAM,QAAQ,OAAO;AAAA,IAClC;AACA,aAAS,eAAe,OAAO,KAAK,QAAQ;AACxC,YAAM,MAAM,EAAE,OAAO,IAAI;AACzB,UAAI,UAAU,MAAM;AAChB,YAAI,SAAS;AAAA,MACjB;AACA,aAAO;AAAA,IACX;AAEA,QAAM,UAAU;AAChB,QAAM,UAAU;AAChB,QAAM,UAAU;AAChB,QAAM,UAAU,OAAO,aAAa,IAAM;AAC1C,QAAM,UAAU,OAAO,aAAa,IAAM;AAC1C,aAAS,cAAc,KAAK;AACxB,YAAM,OAAO;AACb,UAAI,SAAS;AACb,UAAI,QAAQ;AACZ,UAAI,UAAU;AACd,UAAI,cAAc;AAClB,YAAM,SAAS,CAACC,WAAU,KAAKA,MAAK,MAAM,WAAW,KAAKA,SAAQ,CAAC,MAAM;AACzE,YAAM,OAAO,CAACA,WAAU,KAAKA,MAAK,MAAM;AACxC,YAAM,OAAO,CAACA,WAAU,KAAKA,MAAK,MAAM;AACxC,YAAM,OAAO,CAACA,WAAU,KAAKA,MAAK,MAAM;AACxC,YAAM,YAAY,CAACA,WAAU,OAAOA,MAAK,KAAK,KAAKA,MAAK,KAAK,KAAKA,MAAK,KAAK,KAAKA,MAAK;AACtF,YAAM,QAAQ,MAAM;AACpB,YAAM,OAAO,MAAM;AACnB,YAAM,SAAS,MAAM;AACrB,YAAM,aAAa,MAAM;AACzB,YAAM,SAAS,CAAC,WAAW,OAAO,MAAM,KAAK,KAAK,MAAM,KAAK,KAAK,MAAM,IAAI,UAAU,KAAK,MAAM;AACjG,YAAM,cAAc,MAAM,OAAO,MAAM;AACvC,YAAM,cAAc,MAAM,OAAO,SAAS,WAAW;AACrD,eAAS,OAAO;AACZ,sBAAc;AACd,YAAI,UAAU,MAAM,GAAG;AACnB;AACA,oBAAU;AAAA,QACd;AACA,YAAI,OAAO,MAAM,GAAG;AAChB;AAAA,QACJ;AACA;AACA;AACA,eAAO,KAAK,MAAM;AAAA,MACtB;AACA,eAAS,OAAO;AACZ,YAAI,OAAO,SAAS,WAAW,GAAG;AAC9B;AAAA,QACJ;AACA;AACA,eAAO,KAAK,SAAS,WAAW;AAAA,MACpC;AACA,eAAS,QAAQ;AACb,iBAAS;AACT,gBAAQ;AACR,kBAAU;AACV,sBAAc;AAAA,MAClB;AACA,eAAS,UAAU,SAAS,GAAG;AAC3B,sBAAc;AAAA,MAClB;AACA,eAAS,aAAa;AAClB,cAAM,SAAS,SAAS;AAExB,eAAO,WAAW,QAAQ;AACtB,eAAK;AAAA,QACT;AACA,sBAAc;AAAA,MAClB;AACA,aAAO;AAAA,QACH;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,IACJ;AAEA,QAAM,MAAM;AACZ,QAAM,oBAAoB;AAC1B,QAAM,iBAAiB;AACvB,aAAS,gBAAgB,QAAQ,UAAU,CAAC,GAAG;AAC3C,YAAM,WAAW,QAAQ,aAAa;AACtC,YAAM,QAAQ,cAAc,MAAM;AAClC,YAAM,gBAAgB,MAAM,MAAM,MAAM;AACxC,YAAM,kBAAkB,MAAM,eAAe,MAAM,KAAK,GAAG,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC;AACxF,YAAM,WAAW,gBAAgB;AACjC,YAAM,cAAc,cAAc;AAClC,YAAM,WAAW;AAAA,QACb,aAAa;AAAA,QACb,QAAQ;AAAA,QACR,UAAU;AAAA,QACV,QAAQ;AAAA,QACR,UAAU;AAAA,QACV,YAAY;AAAA,QACZ,cAAc;AAAA,QACd,YAAY;AAAA,QACZ,WAAW;AAAA,QACX,UAAU;AAAA,QACV,MAAM;AAAA,MACV;AACA,YAAM,UAAU,MAAM;AACtB,YAAM,EAAE,QAAQ,IAAI;AACpB,eAAS,UAAU,MAAM,KAAK,WAAW,MAAM;AAC3C,cAAM,MAAM,QAAQ;AACpB,YAAI,UAAU;AACd,YAAI,UAAU;AACd,YAAI,SAAS;AACT,gBAAM,MAAM,eAAe,IAAI,UAAU,GAAG;AAC5C,gBAAM,MAAM,mBAAmB,MAAM,KAAK;AAAA,YACtC,QAAQ;AAAA,YACR;AAAA,UACJ,CAAC;AACD,kBAAQ,GAAG;AAAA,QACf;AAAA,MACJ;AACA,eAAS,SAASC,UAAS,MAAM,OAAO;AACpC,QAAAA,SAAQ,SAAS,gBAAgB;AACjC,QAAAA,SAAQ,cAAc;AACtB,cAAM,QAAQ,EAAE,KAAK;AACrB,YAAI,UAAU;AACV,gBAAM,MAAM,eAAeA,SAAQ,UAAUA,SAAQ,MAAM;AAAA,QAC/D;AACA,YAAI,SAAS,MAAM;AACf,gBAAM,QAAQ;AAAA,QAClB;AACA,eAAO;AAAA,MACX;AACA,YAAM,cAAc,CAACA,aAAY;AAAA,QAASA;AAAA,QAAS;AAAA;AAAA,MAAY;AAC/D,eAAS,IAAI,MAAM,IAAI;AACnB,YAAI,KAAK,YAAY,MAAM,IAAI;AAC3B,eAAK,KAAK;AACV,iBAAO;AAAA,QACX,OACK;AACD,oBAAU,kBAAkB,gBAAgB,gBAAgB,GAAG,GAAG,EAAE;AACpE,iBAAO;AAAA,QACX;AAAA,MACJ;AACA,eAAS,WAAW,MAAM;AACtB,YAAI,MAAM;AACV,eAAO,KAAK,YAAY,MAAM,WAAW,KAAK,YAAY,MAAM,SAAS;AACrE,iBAAO,KAAK,YAAY;AACxB,eAAK,KAAK;AAAA,QACd;AACA,eAAO;AAAA,MACX;AACA,eAAS,WAAW,MAAM;AACtB,cAAM,MAAM,WAAW,IAAI;AAC3B,aAAK,WAAW;AAChB,eAAO;AAAA,MACX;AACA,eAAS,kBAAkB,IAAI;AAC3B,YAAI,OAAO,KAAK;AACZ,iBAAO;AAAA,QACX;AACA,cAAM,KAAK,GAAG,WAAW,CAAC;AAC1B,eAAS,MAAM,MAAM,MAAM;AAAA,QACtB,MAAM,MAAM,MAAM;AAAA,QACnB,OAAO;AAAA,MAEf;AACA,eAAS,cAAc,IAAI;AACvB,YAAI,OAAO,KAAK;AACZ,iBAAO;AAAA,QACX;AACA,cAAM,KAAK,GAAG,WAAW,CAAC;AAC1B,eAAO,MAAM,MAAM,MAAM;AAAA,MAC7B;AACA,eAAS,uBAAuB,MAAMA,UAAS;AAC3C,cAAM,EAAE,YAAY,IAAIA;AACxB,YAAI,gBAAgB,GAAmB;AACnC,iBAAO;AAAA,QACX;AACA,mBAAW,IAAI;AACf,cAAM,MAAM,kBAAkB,KAAK,YAAY,CAAC;AAChD,aAAK,UAAU;AACf,eAAO;AAAA,MACX;AACA,eAAS,sBAAsB,MAAMA,UAAS;AAC1C,cAAM,EAAE,YAAY,IAAIA;AACxB,YAAI,gBAAgB,GAAmB;AACnC,iBAAO;AAAA,QACX;AACA,mBAAW,IAAI;AACf,cAAM,KAAK,KAAK,YAAY,MAAM,MAAM,KAAK,KAAK,IAAI,KAAK,YAAY;AACvE,cAAM,MAAM,cAAc,EAAE;AAC5B,aAAK,UAAU;AACf,eAAO;AAAA,MACX;AACA,eAAS,eAAe,MAAMA,UAAS;AACnC,cAAM,EAAE,YAAY,IAAIA;AACxB,YAAI,gBAAgB,GAAmB;AACnC,iBAAO;AAAA,QACX;AACA,mBAAW,IAAI;AACf,cAAM,MAAM,KAAK,YAAY,MAAM;AACnC,aAAK,UAAU;AACf,eAAO;AAAA,MACX;AACA,eAAS,iBAAiB,MAAMA,UAAS;AACrC,cAAM,EAAE,YAAY,IAAIA;AACxB,YAAI,gBAAgB,GAAqB;AACrC,iBAAO;AAAA,QACX;AACA,mBAAW,IAAI;AACf,cAAM,MAAM,KAAK,YAAY,MAAM;AACnC,aAAK,UAAU;AACf,eAAO;AAAA,MACX;AACA,eAAS,sBAAsB,MAAMA,UAAS;AAC1C,cAAM,EAAE,YAAY,IAAIA;AACxB,YAAI,gBAAgB,GAAmB;AACnC,iBAAO;AAAA,QACX;AACA,mBAAW,IAAI;AACf,cAAM,MAAM,kBAAkB,KAAK,YAAY,CAAC;AAChD,aAAK,UAAU;AACf,eAAO;AAAA,MACX;AACA,eAAS,uBAAuB,MAAMA,UAAS;AAC3C,cAAM,EAAE,YAAY,IAAIA;AACxB,YAAI,EAAE,gBAAgB,KAClB,gBAAgB,KAA0B;AAC1C,iBAAO;AAAA,QACX;AACA,mBAAW,IAAI;AACf,cAAM,MAAM,KAAK,YAAY,MAAM;AACnC,aAAK,UAAU;AACf,eAAO;AAAA,MACX;AACA,eAAS,mBAAmB,MAAMA,UAAS;AACvC,cAAM,EAAE,YAAY,IAAIA;AACxB,YAAI,gBAAgB,IAA0B;AAC1C,iBAAO;AAAA,QACX;AACA,cAAM,KAAK,MAAM;AACb,gBAAM,KAAK,KAAK,YAAY;AAC5B,cAAI,OAAO,KAAqB;AAC5B,mBAAO,kBAAkB,KAAK,KAAK,CAAC;AAAA,UACxC,WACS,OAAO,OACZ,OAAO,OACP,OAAO,OACP,OAAO,OACP,OAAO,OACP,OAAO,WACP,CAAC,IAAI;AACL,mBAAO;AAAA,UACX,WACS,OAAO,SAAS;AACrB,iBAAK,KAAK;AACV,mBAAO,GAAG;AAAA,UACd,OACK;AAED,mBAAO,kBAAkB,EAAE;AAAA,UAC/B;AAAA,QACJ;AACA,cAAM,MAAM,GAAG;AACf,aAAK,UAAU;AACf,eAAO;AAAA,MACX;AACA,eAAS,cAAc,MAAM;AACzB,mBAAW,IAAI;AACf,cAAM,MAAM,KAAK,YAAY,MAAM;AACnC,aAAK,UAAU;AACf,eAAO;AAAA,MACX;AACA,eAAS,kBAAkB,MAAM;AAC7B,cAAM,SAAS,WAAW,IAAI;AAC9B,cAAM,MAAM,KAAK,YAAY,MAAM,OAC/B,KAAK,KAAK,MAAM;AACpB,aAAK,UAAU;AACf,eAAO;AAAA,UACH,UAAU;AAAA,UACV,UAAU,OAAO,SAAS;AAAA,QAC9B;AAAA,MACJ;AACA,eAAS,YAAY,MAAM,QAAQ,MAAM;AACrC,cAAM,KAAK,CAAC,WAAW,OAAO,OAAO,IAAI,eAAe,UAAU;AAC9D,gBAAM,KAAK,KAAK,YAAY;AAC5B,cAAI,OAAO,KAAqB;AAC5B,mBAAO,SAAS,MAAmB,QAAQ;AAAA,UAC/C,WACS,OAAO,OAAyB,CAAC,IAAI;AAC1C,mBAAO,SAAS,MAAmB,OAAO;AAAA,UAC9C,WACS,OAAO,KAAkB;AAC9B,iBAAK,KAAK;AACV,mBAAO,GAAG,UAAU,KAAkB,IAAI;AAAA,UAC9C,WACS,OAAO,KAAgB;AAC5B,mBAAO,SAAS,OAAoB,eAC9B,OACA,EAAE,SAAS,WAAW,SAAS;AAAA,UACzC,WACS,OAAO,SAAS;AACrB,iBAAK,KAAK;AACV,mBAAO,GAAG,MAAM,SAAS,YAAY;AAAA,UACzC,WACS,OAAO,SAAS;AACrB,iBAAK,KAAK;AACV,mBAAO,GAAG,MAAM,SAAS,YAAY;AAAA,UACzC,OACK;AACD,mBAAO;AAAA,UACX;AAAA,QACJ;AACA,cAAM,MAAM,GAAG;AACf,iBAAS,KAAK,UAAU;AACxB,eAAO;AAAA,MACX;AACA,eAAS,SAAS,MAAM,IAAI;AACxB,cAAM,KAAK,KAAK,YAAY;AAC5B,YAAI,OAAO,KAAK;AACZ,iBAAO;AAAA,QACX;AACA,YAAI,GAAG,EAAE,GAAG;AACR,eAAK,KAAK;AACV,iBAAO;AAAA,QACX;AACA,eAAO;AAAA,MACX;AACA,eAAS,mBAAmB,MAAM;AAC9B,cAAM,UAAU,CAAC,OAAO;AACpB,gBAAM,KAAK,GAAG,WAAW,CAAC;AAC1B,iBAAS,MAAM,MAAM,MAAM;AAAA,UACtB,MAAM,MAAM,MAAM;AAAA,UAClB,MAAM,MAAM,MAAM;AAAA,UACnB,OAAO;AAAA,UACP,OAAO;AAAA,QAEf;AACA,eAAO,SAAS,MAAM,OAAO;AAAA,MACjC;AACA,eAAS,UAAU,MAAM;AACrB,cAAM,UAAU,CAAC,OAAO;AACpB,gBAAM,KAAK,GAAG,WAAW,CAAC;AAC1B,iBAAO,MAAM,MAAM,MAAM;AAAA,QAC7B;AACA,eAAO,SAAS,MAAM,OAAO;AAAA,MACjC;AACA,eAAS,aAAa,MAAM;AACxB,cAAM,UAAU,CAAC,OAAO;AACpB,gBAAM,KAAK,GAAG,WAAW,CAAC;AAC1B,iBAAS,MAAM,MAAM,MAAM;AAAA,UACtB,MAAM,MAAM,MAAM;AAAA,UAClB,MAAM,MAAM,MAAM;AAAA,QAC3B;AACA,eAAO,SAAS,MAAM,OAAO;AAAA,MACjC;AACA,eAAS,UAAU,MAAM;AACrB,YAAI,KAAK;AACT,YAAI,MAAM;AACV,eAAQ,KAAK,UAAU,IAAI,GAAI;AAC3B,iBAAO;AAAA,QACX;AACA,eAAO;AAAA,MACX;AACA,eAAS,WAAW,MAAM;AACtB,mBAAW,IAAI;AACf,cAAM,KAAK,KAAK,YAAY;AAC5B,YAAI,OAAO,KAAkB;AACzB,oBAAU,kBAAkB,gBAAgB,gBAAgB,GAAG,GAAG,EAAE;AAAA,QACxE;AACA,aAAK,KAAK;AACV,eAAO;AAAA,MACX;AACA,eAAS,SAAS,MAAM;AACpB,YAAI,MAAM;AACV,eAAO,MAAM;AACT,gBAAM,KAAK,KAAK,YAAY;AAC5B,cAAI,OAAO,OACP,OAAO,OACP,OAAO,OACP,OAAO,OACP,CAAC,IAAI;AACL;AAAA,UACJ,WACS,OAAO,KAAkB;AAC9B,gBAAI,YAAY,IAAI,GAAG;AACnB,qBAAO;AACP,mBAAK,KAAK;AAAA,YACd,OACK;AACD;AAAA,YACJ;AAAA,UACJ,WACS,OAAO,WAAW,OAAO,SAAS;AACvC,gBAAI,YAAY,IAAI,GAAG;AACnB,qBAAO;AACP,mBAAK,KAAK;AAAA,YACd,WACS,cAAc,IAAI,GAAG;AAC1B;AAAA,YACJ,OACK;AACD,qBAAO;AACP,mBAAK,KAAK;AAAA,YACd;AAAA,UACJ,OACK;AACD,mBAAO;AACP,iBAAK,KAAK;AAAA,UACd;AAAA,QACJ;AACA,eAAO;AAAA,MACX;AACA,eAAS,oBAAoB,MAAM;AAC/B,mBAAW,IAAI;AACf,YAAI,KAAK;AACT,YAAI,OAAO;AACX,eAAQ,KAAK,mBAAmB,IAAI,GAAI;AACpC,kBAAQ;AAAA,QACZ;AACA,YAAI,KAAK,YAAY,MAAM,KAAK;AAC5B,oBAAU,kBAAkB,4BAA4B,gBAAgB,GAAG,CAAC;AAAA,QAChF;AACA,eAAO;AAAA,MACX;AACA,eAAS,mBAAmB,MAAM;AAC9B,mBAAW,IAAI;AACf,YAAI,QAAQ;AACZ,YAAI,KAAK,YAAY,MAAM,KAAK;AAC5B,eAAK,KAAK;AACV,mBAAS,IAAI,UAAU,IAAI,CAAC;AAAA,QAChC,OACK;AACD,mBAAS,UAAU,IAAI;AAAA,QAC3B;AACA,YAAI,KAAK,YAAY,MAAM,KAAK;AAC5B,oBAAU,kBAAkB,4BAA4B,gBAAgB,GAAG,CAAC;AAAA,QAChF;AACA,eAAO;AAAA,MACX;AACA,eAAS,YAAY,MAAM;AACvB,mBAAW,IAAI;AACf,YAAI,MAAM,GAAI;AACd,YAAI,KAAK;AACT,YAAI,UAAU;AACd,cAAM,KAAK,CAAC,MAAM,MAAM,qBAAqB,MAAM;AACnD,eAAQ,KAAK,SAAS,MAAM,EAAE,GAAI;AAC9B,cAAI,OAAO,MAAM;AACb,uBAAW,mBAAmB,IAAI;AAAA,UACtC,OACK;AACD,uBAAW;AAAA,UACf;AAAA,QACJ;AACA,cAAM,UAAU,KAAK,YAAY;AACjC,YAAI,YAAY,WAAW,YAAY,KAAK;AACxC,oBAAU,kBAAkB,0CAA0C,gBAAgB,GAAG,CAAC;AAE1F,cAAI,YAAY,SAAS;AACrB,iBAAK,KAAK;AACV,gBAAI,MAAM,GAAI;AAAA,UAClB;AACA,iBAAO;AAAA,QACX;AACA,YAAI,MAAM,GAAI;AACd,eAAO;AAAA,MACX;AACA,eAAS,mBAAmB,MAAM;AAC9B,cAAM,KAAK,KAAK,YAAY;AAC5B,gBAAQ,IAAI;AAAA,UACR,KAAK;AAAA,UACL,KAAK;AACD,iBAAK,KAAK;AACV,mBAAO,KAAK,EAAE;AAAA,UAClB,KAAK;AACD,mBAAO,0BAA0B,MAAM,IAAI,CAAC;AAAA,UAChD,KAAK;AACD,mBAAO,0BAA0B,MAAM,IAAI,CAAC;AAAA,UAChD;AACI,sBAAU,kBAAkB,yBAAyB,gBAAgB,GAAG,GAAG,EAAE;AAC7E,mBAAO;AAAA,QACf;AAAA,MACJ;AACA,eAAS,0BAA0B,MAAM,SAAS,QAAQ;AACtD,YAAI,MAAM,OAAO;AACjB,YAAI,WAAW;AACf,iBAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC7B,gBAAM,KAAK,aAAa,IAAI;AAC5B,cAAI,CAAC,IAAI;AACL,sBAAU,kBAAkB,iCAAiC,gBAAgB,GAAG,GAAG,KAAK,OAAO,GAAG,QAAQ,GAAG,KAAK,YAAY,CAAC,EAAE;AACjI;AAAA,UACJ;AACA,sBAAY;AAAA,QAChB;AACA,eAAO,KAAK,OAAO,GAAG,QAAQ;AAAA,MAClC;AACA,eAAS,sBAAsB,MAAM;AACjC,mBAAW,IAAI;AACf,YAAI,KAAK;AACT,YAAI,cAAc;AAClB,cAAM,UAAU,CAACC,QAAOA,QAAO,OAC3BA,QAAO,OACPA,QAAO,WACPA,QAAO;AACX,eAAQ,KAAK,SAAS,MAAM,OAAO,GAAI;AACnC,yBAAe;AAAA,QACnB;AACA,eAAO;AAAA,MACX;AACA,eAAS,mBAAmB,MAAM;AAC9B,YAAI,KAAK;AACT,YAAI,OAAO;AACX,eAAQ,KAAK,mBAAmB,IAAI,GAAI;AACpC,kBAAQ;AAAA,QACZ;AACA,eAAO;AAAA,MACX;AACA,eAAS,gBAAgB,MAAM;AAC3B,cAAM,KAAK,CAAC,SAAS,OAAO,QAAQ;AAChC,gBAAM,KAAK,KAAK,YAAY;AAC5B,cAAI,OAAO,OACP,OAAO,OACP,OAAO,OACP,OAAO,OACP,CAAC,IAAI;AACL,mBAAO;AAAA,UACX,WACS,OAAO,SAAS;AACrB,mBAAO;AAAA,UACX,WACS,OAAO,SAAS;AACrB,mBAAO;AACP,iBAAK,KAAK;AACV,mBAAO,GAAG,QAAQ,GAAG;AAAA,UACzB,OACK;AACD,mBAAO;AACP,iBAAK,KAAK;AACV,mBAAO,GAAG,MAAM,GAAG;AAAA,UACvB;AAAA,QACJ;AACA,eAAO,GAAG,OAAO,EAAE;AAAA,MACvB;AACA,eAAS,WAAW,MAAM;AACtB,mBAAW,IAAI;AACf,cAAM,SAAS;AAAA,UAAI;AAAA,UAAM;AAAA;AAAA,QAAc;AACvC,mBAAW,IAAI;AACf,eAAO;AAAA,MACX;AAEA,eAAS,uBAAuB,MAAMD,UAAS;AAC3C,YAAI,QAAQ;AACZ,cAAM,KAAK,KAAK,YAAY;AAC5B,gBAAQ,IAAI;AAAA,UACR,KAAK;AACD,gBAAIA,SAAQ,aAAa,GAAG;AACxB,wBAAU,kBAAkB,4BAA4B,gBAAgB,GAAG,CAAC;AAAA,YAChF;AACA,iBAAK,KAAK;AACV,oBAAQ;AAAA,cAASA;AAAA,cAAS;AAAA,cAAmB;AAAA;AAAA,YAAmB;AAChE,uBAAW,IAAI;AACf,YAAAA,SAAQ;AACR,mBAAO;AAAA,UACX,KAAK;AACD,gBAAIA,SAAQ,YAAY,KACpBA,SAAQ,gBAAgB,GAAmB;AAC3C,wBAAU,kBAAkB,mBAAmB,gBAAgB,GAAG,CAAC;AAAA,YACvE;AACA,iBAAK,KAAK;AACV,oBAAQ;AAAA,cAASA;AAAA,cAAS;AAAA,cAAoB;AAAA;AAAA,YAAoB;AAClE,YAAAA,SAAQ;AACR,YAAAA,SAAQ,YAAY,KAAK,WAAW,IAAI;AACxC,gBAAIA,SAAQ,YAAYA,SAAQ,cAAc,GAAG;AAC7C,cAAAA,SAAQ,WAAW;AAAA,YACvB;AACA,mBAAO;AAAA,UACX,KAAK;AACD,gBAAIA,SAAQ,YAAY,GAAG;AACvB,wBAAU,kBAAkB,4BAA4B,gBAAgB,GAAG,CAAC;AAAA,YAChF;AACA,oBAAQ,kBAAkB,MAAMA,QAAO,KAAK,YAAYA,QAAO;AAC/D,YAAAA,SAAQ,YAAY;AACpB,mBAAO;AAAA,UACX;AACI,gBAAI,uBAAuB;AAC3B,gBAAI,sBAAsB;AAC1B,gBAAI,eAAe;AACnB,gBAAI,cAAc,IAAI,GAAG;AACrB,kBAAIA,SAAQ,YAAY,GAAG;AACvB,0BAAU,kBAAkB,4BAA4B,gBAAgB,GAAG,CAAC;AAAA,cAChF;AACA,sBAAQ,SAASA,UAAS,GAAc,WAAW,IAAI,CAAC;AAExD,cAAAA,SAAQ,YAAY;AACpB,cAAAA,SAAQ,WAAW;AACnB,qBAAO;AAAA,YACX;AACA,gBAAIA,SAAQ,YAAY,MACnBA,SAAQ,gBAAgB,KACrBA,SAAQ,gBAAgB,KACxBA,SAAQ,gBAAgB,IAAkB;AAC9C,wBAAU,kBAAkB,4BAA4B,gBAAgB,GAAG,CAAC;AAC5E,cAAAA,SAAQ,YAAY;AACpB,qBAAO,UAAU,MAAMA,QAAO;AAAA,YAClC;AACA,gBAAK,uBAAuB,uBAAuB,MAAMA,QAAO,GAAI;AAChE,sBAAQ,SAASA,UAAS,GAAe,oBAAoB,IAAI,CAAC;AAClE,yBAAW,IAAI;AACf,qBAAO;AAAA,YACX;AACA,gBAAK,sBAAsB,sBAAsB,MAAMA,QAAO,GAAI;AAC9D,sBAAQ,SAASA,UAAS,GAAc,mBAAmB,IAAI,CAAC;AAChE,yBAAW,IAAI;AACf,qBAAO;AAAA,YACX;AACA,gBAAK,eAAe,eAAe,MAAMA,QAAO,GAAI;AAChD,sBAAQ,SAASA,UAAS,GAAiB,YAAY,IAAI,CAAC;AAC5D,yBAAW,IAAI;AACf,qBAAO;AAAA,YACX;AACA,gBAAI,CAAC,wBAAwB,CAAC,uBAAuB,CAAC,cAAc;AAEhE,sBAAQ,SAASA,UAAS,IAAuB,sBAAsB,IAAI,CAAC;AAC5E,wBAAU,kBAAkB,8BAA8B,gBAAgB,GAAG,GAAG,MAAM,KAAK;AAC3F,yBAAW,IAAI;AACf,qBAAO;AAAA,YACX;AACA;AAAA,QACR;AACA,eAAO;AAAA,MACX;AAEA,eAAS,kBAAkB,MAAMA,UAAS;AACtC,cAAM,EAAE,YAAY,IAAIA;AACxB,YAAI,QAAQ;AACZ,cAAM,KAAK,KAAK,YAAY;AAC5B,aAAK,gBAAgB,KACjB,gBAAgB,KAChB,gBAAgB,MAChB,gBAAgB,QACf,OAAO,WAAW,OAAO,UAAU;AACpC,oBAAU,kBAAkB,uBAAuB,gBAAgB,GAAG,CAAC;AAAA,QAC3E;AACA,gBAAQ,IAAI;AAAA,UACR,KAAK;AACD,iBAAK,KAAK;AACV,oBAAQ;AAAA,cAASA;AAAA,cAAS;AAAA,cAAqB;AAAA;AAAA,YAAqB;AACpE,YAAAA,SAAQ,WAAW;AACnB,mBAAO;AAAA,UACX,KAAK;AACD,uBAAW,IAAI;AACf,iBAAK,KAAK;AACV,mBAAO;AAAA,cAASA;AAAA,cAAS;AAAA,cAAmB;AAAA;AAAA,YAAmB;AAAA,UACnE,KAAK;AACD,uBAAW,IAAI;AACf,iBAAK,KAAK;AACV,mBAAO;AAAA,cAASA;AAAA,cAAS;AAAA,cAA0B;AAAA;AAAA,YAAyB;AAAA,UAChF;AACI,gBAAI,cAAc,IAAI,GAAG;AACrB,sBAAQ,SAASA,UAAS,GAAc,WAAW,IAAI,CAAC;AAExD,cAAAA,SAAQ,YAAY;AACpB,cAAAA,SAAQ,WAAW;AACnB,qBAAO;AAAA,YACX;AACA,gBAAI,iBAAiB,MAAMA,QAAO,KAC9B,uBAAuB,MAAMA,QAAO,GAAG;AACvC,yBAAW,IAAI;AACf,qBAAO,kBAAkB,MAAMA,QAAO;AAAA,YAC1C;AACA,gBAAI,sBAAsB,MAAMA,QAAO,GAAG;AACtC,yBAAW,IAAI;AACf,qBAAO,SAASA,UAAS,IAAyB,mBAAmB,IAAI,CAAC;AAAA,YAC9E;AACA,gBAAI,mBAAmB,MAAMA,QAAO,GAAG;AACnC,yBAAW,IAAI;AACf,kBAAI,OAAO,KAAqB;AAE5B,uBAAO,uBAAuB,MAAMA,QAAO,KAAK;AAAA,cACpD,OACK;AACD,uBAAO,SAASA,UAAS,IAAoB,gBAAgB,IAAI,CAAC;AAAA,cACtE;AAAA,YACJ;AACA,gBAAI,gBAAgB,GAAqB;AACrC,wBAAU,kBAAkB,uBAAuB,gBAAgB,GAAG,CAAC;AAAA,YAC3E;AACA,YAAAA,SAAQ,YAAY;AACpB,YAAAA,SAAQ,WAAW;AACnB,mBAAO,UAAU,MAAMA,QAAO;AAAA,QACtC;AAAA,MACJ;AAEA,eAAS,UAAU,MAAMA,UAAS;AAC9B,YAAI,QAAQ;AAAA,UAAE,MAAM;AAAA;AAAA,QAAa;AACjC,YAAIA,SAAQ,YAAY,GAAG;AACvB,iBAAO,uBAAuB,MAAMA,QAAO,KAAK,YAAYA,QAAO;AAAA,QACvE;AACA,YAAIA,SAAQ,UAAU;AAClB,iBAAO,kBAAkB,MAAMA,QAAO,KAAK,YAAYA,QAAO;AAAA,QAClE;AACA,cAAM,KAAK,KAAK,YAAY;AAC5B,gBAAQ,IAAI;AAAA,UACR,KAAK;AACD,mBAAO,uBAAuB,MAAMA,QAAO,KAAK,YAAYA,QAAO;AAAA,UACvE,KAAK;AACD,sBAAU,kBAAkB,0BAA0B,gBAAgB,GAAG,CAAC;AAC1E,iBAAK,KAAK;AACV,mBAAO;AAAA,cAASA;AAAA,cAAS;AAAA,cAAoB;AAAA;AAAA,YAAoB;AAAA,UACrE,KAAK;AACD,mBAAO,kBAAkB,MAAMA,QAAO,KAAK,YAAYA,QAAO;AAAA,UAClE;AACI,gBAAI,cAAc,IAAI,GAAG;AACrB,sBAAQ,SAASA,UAAS,GAAc,WAAW,IAAI,CAAC;AAExD,cAAAA,SAAQ,YAAY;AACpB,cAAAA,SAAQ,WAAW;AACnB,qBAAO;AAAA,YACX;AACA,kBAAM,EAAE,UAAU,SAAS,IAAI,kBAAkB,IAAI;AACrD,gBAAI,UAAU;AACV,qBAAO,WACD,SAASA,UAAS,GAAc,SAAS,IAAI,CAAC,IAC9C,SAASA,UAAS,GAAgB,WAAW,IAAI,CAAC;AAAA,YAC5D;AACA,gBAAI,YAAY,IAAI,GAAG;AACnB,qBAAO,SAASA,UAAS,GAAc,SAAS,IAAI,CAAC;AAAA,YACzD;AACA;AAAA,QACR;AACA,eAAO;AAAA,MACX;AACA,eAAS,YAAY;AACjB,cAAM,EAAE,aAAa,QAAQ,UAAU,OAAO,IAAI;AAClD,iBAAS,WAAW;AACpB,iBAAS,aAAa;AACtB,iBAAS,eAAe;AACxB,iBAAS,aAAa;AACtB,iBAAS,SAAS,cAAc;AAChC,iBAAS,WAAW,gBAAgB;AACpC,YAAI,MAAM,YAAY,MAAM,KAAK;AAC7B,iBAAO;AAAA,YAAS;AAAA,YAAU;AAAA;AAAA,UAAY;AAAA,QAC1C;AACA,eAAO,UAAU,OAAO,QAAQ;AAAA,MACpC;AACA,aAAO;AAAA,QACH;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,IACJ;AAEA,QAAM,eAAe;AAErB,QAAM,gBAAgB;AACtB,aAAS,mBAAmB,OAAO,YAAY,YAAY;AACvD,cAAQ,OAAO;AAAA,QACX,KAAK;AACD,iBAAO;AAAA,QACX,KAAK;AACD,iBAAO;AAAA,QACX,SAAS;AACL,gBAAM,YAAY,SAAS,cAAc,YAAY,EAAE;AACvD,cAAI,aAAa,SAAU,aAAa,OAAQ;AAC5C,mBAAO,OAAO,cAAc,SAAS;AAAA,UACzC;AAGA,iBAAO;AAAA,QACX;AAAA,MACJ;AAAA,IACJ;AACA,aAAS,aAAa,UAAU,CAAC,GAAG;AAChC,YAAM,WAAW,QAAQ,aAAa;AACtC,YAAM,EAAE,QAAQ,IAAI;AACpB,eAAS,UAAU,UAAU,MAAM,OAAO,WAAW,MAAM;AACvD,cAAM,MAAM,SAAS,gBAAgB;AACrC,YAAI,UAAU;AACd,YAAI,UAAU;AACd,YAAI,SAAS;AACT,gBAAM,MAAM,eAAe,OAAO,GAAG;AACrC,gBAAM,MAAM,mBAAmB,MAAM,KAAK;AAAA,YACtC,QAAQ;AAAA,YACR;AAAA,UACJ,CAAC;AACD,kBAAQ,GAAG;AAAA,QACf;AAAA,MACJ;AACA,eAAS,UAAU,MAAM,QAAQ,KAAK;AAClC,cAAM,OAAO;AAAA,UACT;AAAA,UACA,OAAO;AAAA,UACP,KAAK;AAAA,QACT;AACA,YAAI,UAAU;AACV,eAAK,MAAM,EAAE,OAAO,KAAK,KAAK,IAAI;AAAA,QACtC;AACA,eAAO;AAAA,MACX;AACA,eAAS,QAAQ,MAAM,QAAQ,KAAK,MAAM;AACtC,aAAK,MAAM;AACX,YAAI,MAAM;AACN,eAAK,OAAO;AAAA,QAChB;AACA,YAAI,YAAY,KAAK,KAAK;AACtB,eAAK,IAAI,MAAM;AAAA,QACnB;AAAA,MACJ;AACA,eAAS,UAAU,WAAW,OAAO;AACjC,cAAM,UAAU,UAAU,QAAQ;AAClC,cAAM,OAAO,UAAU,GAAc,QAAQ,QAAQ,QAAQ,QAAQ;AACrE,aAAK,QAAQ;AACb,gBAAQ,MAAM,UAAU,cAAc,GAAG,UAAU,gBAAgB,CAAC;AACpE,eAAO;AAAA,MACX;AACA,eAAS,UAAU,WAAW,OAAO;AACjC,cAAM,UAAU,UAAU,QAAQ;AAClC,cAAM,EAAE,YAAY,QAAQ,cAAc,IAAI,IAAI;AAClD,cAAM,OAAO,UAAU,GAAc,QAAQ,GAAG;AAChD,aAAK,QAAQ,SAAS,OAAO,EAAE;AAC/B,kBAAU,UAAU;AACpB,gBAAQ,MAAM,UAAU,cAAc,GAAG,UAAU,gBAAgB,CAAC;AACpE,eAAO;AAAA,MACX;AACA,eAAS,WAAW,WAAW,KAAK;AAChC,cAAM,UAAU,UAAU,QAAQ;AAClC,cAAM,EAAE,YAAY,QAAQ,cAAc,IAAI,IAAI;AAClD,cAAM,OAAO,UAAU,GAAe,QAAQ,GAAG;AACjD,aAAK,MAAM;AACX,kBAAU,UAAU;AACpB,gBAAQ,MAAM,UAAU,cAAc,GAAG,UAAU,gBAAgB,CAAC;AACpE,eAAO;AAAA,MACX;AACA,eAAS,aAAa,WAAW,OAAO;AACpC,cAAM,UAAU,UAAU,QAAQ;AAClC,cAAM,EAAE,YAAY,QAAQ,cAAc,IAAI,IAAI;AAClD,cAAM,OAAO,UAAU,GAAiB,QAAQ,GAAG;AACnD,aAAK,QAAQ,MAAM,QAAQ,eAAe,kBAAkB;AAC5D,kBAAU,UAAU;AACpB,gBAAQ,MAAM,UAAU,cAAc,GAAG,UAAU,gBAAgB,CAAC;AACpE,eAAO;AAAA,MACX;AACA,eAAS,oBAAoB,WAAW;AACpC,cAAM,QAAQ,UAAU,UAAU;AAClC,cAAM,UAAU,UAAU,QAAQ;AAClC,cAAM,EAAE,YAAY,QAAQ,cAAc,IAAI,IAAI;AAClD,cAAM,OAAO,UAAU,GAAwB,QAAQ,GAAG;AAC1D,YAAI,MAAM,SAAS,IAAyB;AAExC,oBAAU,WAAW,kBAAkB,kCAAkC,QAAQ,cAAc,CAAC;AAChG,eAAK,QAAQ;AACb,kBAAQ,MAAM,QAAQ,GAAG;AACzB,iBAAO;AAAA,YACH,kBAAkB;AAAA,YAClB;AAAA,UACJ;AAAA,QACJ;AAEA,YAAI,MAAM,SAAS,MAAM;AACrB,oBAAU,WAAW,kBAAkB,6BAA6B,QAAQ,cAAc,GAAG,gBAAgB,KAAK,CAAC;AAAA,QACvH;AACA,aAAK,QAAQ,MAAM,SAAS;AAC5B,gBAAQ,MAAM,UAAU,cAAc,GAAG,UAAU,gBAAgB,CAAC;AACpE,eAAO;AAAA,UACH;AAAA,QACJ;AAAA,MACJ;AACA,eAAS,eAAe,WAAW,OAAO;AACtC,cAAM,UAAU,UAAU,QAAQ;AAClC,cAAM,OAAO,UAAU,GAAmB,QAAQ,QAAQ,QAAQ,QAAQ;AAC1E,aAAK,QAAQ;AACb,gBAAQ,MAAM,UAAU,cAAc,GAAG,UAAU,gBAAgB,CAAC;AACpE,eAAO;AAAA,MACX;AACA,eAAS,YAAY,WAAW;AAC5B,cAAM,UAAU,UAAU,QAAQ;AAClC,cAAM,aAAa,UAAU,GAAgB,QAAQ,QAAQ,QAAQ,QAAQ;AAC7E,YAAI,QAAQ,UAAU,UAAU;AAChC,YAAI,MAAM,SAAS,GAAmB;AAClC,gBAAM,SAAS,oBAAoB,SAAS;AAC5C,qBAAW,WAAW,OAAO;AAC7B,kBAAQ,OAAO,oBAAoB,UAAU,UAAU;AAAA,QAC3D;AAEA,YAAI,MAAM,SAAS,IAA0B;AACzC,oBAAU,WAAW,kBAAkB,6BAA6B,QAAQ,cAAc,GAAG,gBAAgB,KAAK,CAAC;AAAA,QACvH;AACA,gBAAQ,UAAU,UAAU;AAE5B,YAAI,MAAM,SAAS,GAAmB;AAClC,kBAAQ,UAAU,UAAU;AAAA,QAChC;AACA,gBAAQ,MAAM,MAAM;AAAA,UAChB,KAAK;AACD,gBAAI,MAAM,SAAS,MAAM;AACrB,wBAAU,WAAW,kBAAkB,6BAA6B,QAAQ,cAAc,GAAG,gBAAgB,KAAK,CAAC;AAAA,YACvH;AACA,uBAAW,MAAM,eAAe,WAAW,MAAM,SAAS,EAAE;AAC5D;AAAA,UACJ,KAAK;AACD,gBAAI,MAAM,SAAS,MAAM;AACrB,wBAAU,WAAW,kBAAkB,6BAA6B,QAAQ,cAAc,GAAG,gBAAgB,KAAK,CAAC;AAAA,YACvH;AACA,uBAAW,MAAM,WAAW,WAAW,MAAM,SAAS,EAAE;AACxD;AAAA,UACJ,KAAK;AACD,gBAAI,MAAM,SAAS,MAAM;AACrB,wBAAU,WAAW,kBAAkB,6BAA6B,QAAQ,cAAc,GAAG,gBAAgB,KAAK,CAAC;AAAA,YACvH;AACA,uBAAW,MAAM,UAAU,WAAW,MAAM,SAAS,EAAE;AACvD;AAAA,UACJ,KAAK;AACD,gBAAI,MAAM,SAAS,MAAM;AACrB,wBAAU,WAAW,kBAAkB,6BAA6B,QAAQ,cAAc,GAAG,gBAAgB,KAAK,CAAC;AAAA,YACvH;AACA,uBAAW,MAAM,aAAa,WAAW,MAAM,SAAS,EAAE;AAC1D;AAAA,UACJ;AAEI,sBAAU,WAAW,kBAAkB,6BAA6B,QAAQ,cAAc,CAAC;AAC3F,kBAAM,cAAc,UAAU,QAAQ;AACtC,kBAAM,qBAAqB,UAAU,GAAmB,YAAY,QAAQ,YAAY,QAAQ;AAChG,+BAAmB,QAAQ;AAC3B,oBAAQ,oBAAoB,YAAY,QAAQ,YAAY,QAAQ;AACpE,uBAAW,MAAM;AACjB,oBAAQ,YAAY,YAAY,QAAQ,YAAY,QAAQ;AAC5D,mBAAO;AAAA,cACH,kBAAkB;AAAA,cAClB,MAAM;AAAA,YACV;AAAA,QACR;AACA,gBAAQ,YAAY,UAAU,cAAc,GAAG,UAAU,gBAAgB,CAAC;AAC1E,eAAO;AAAA,UACH,MAAM;AAAA,QACV;AAAA,MACJ;AACA,eAAS,aAAa,WAAW;AAC7B,cAAM,UAAU,UAAU,QAAQ;AAClC,cAAM,cAAc,QAAQ,gBAAgB,IACtC,UAAU,cAAc,IACxB,QAAQ;AACd,cAAM,WAAW,QAAQ,gBAAgB,IACnC,QAAQ,SACR,QAAQ;AACd,cAAM,OAAO,UAAU,GAAiB,aAAa,QAAQ;AAC7D,aAAK,QAAQ,CAAC;AACd,YAAI,YAAY;AAChB,WAAG;AACC,gBAAM,QAAQ,aAAa,UAAU,UAAU;AAC/C,sBAAY;AACZ,kBAAQ,MAAM,MAAM;AAAA,YAChB,KAAK;AACD,kBAAI,MAAM,SAAS,MAAM;AACrB,0BAAU,WAAW,kBAAkB,6BAA6B,QAAQ,cAAc,GAAG,gBAAgB,KAAK,CAAC;AAAA,cACvH;AACA,mBAAK,MAAM,KAAK,UAAU,WAAW,MAAM,SAAS,EAAE,CAAC;AACvD;AAAA,YACJ,KAAK;AACD,kBAAI,MAAM,SAAS,MAAM;AACrB,0BAAU,WAAW,kBAAkB,6BAA6B,QAAQ,cAAc,GAAG,gBAAgB,KAAK,CAAC;AAAA,cACvH;AACA,mBAAK,MAAM,KAAK,UAAU,WAAW,MAAM,SAAS,EAAE,CAAC;AACvD;AAAA,YACJ,KAAK;AACD,kBAAI,MAAM,SAAS,MAAM;AACrB,0BAAU,WAAW,kBAAkB,6BAA6B,QAAQ,cAAc,GAAG,gBAAgB,KAAK,CAAC;AAAA,cACvH;AACA,mBAAK,MAAM,KAAK,WAAW,WAAW,MAAM,SAAS,EAAE,CAAC;AACxD;AAAA,YACJ,KAAK;AACD,kBAAI,MAAM,SAAS,MAAM;AACrB,0BAAU,WAAW,kBAAkB,6BAA6B,QAAQ,cAAc,GAAG,gBAAgB,KAAK,CAAC;AAAA,cACvH;AACA,mBAAK,MAAM,KAAK,aAAa,WAAW,MAAM,SAAS,EAAE,CAAC;AAC1D;AAAA,YACJ,KAAK;AACD,oBAAM,SAAS,YAAY,SAAS;AACpC,mBAAK,MAAM,KAAK,OAAO,IAAI;AAC3B,0BAAY,OAAO,oBAAoB;AACvC;AAAA,UACR;AAAA,QACJ,SAAS,QAAQ,gBAAgB,MAC7B,QAAQ,gBAAgB;AAE5B,cAAM,YAAY,QAAQ,gBAAgB,IACpC,QAAQ,aACR,UAAU,cAAc;AAC9B,cAAM,SAAS,QAAQ,gBAAgB,IACjC,QAAQ,aACR,UAAU,gBAAgB;AAChC,gBAAQ,MAAM,WAAW,MAAM;AAC/B,eAAO;AAAA,MACX;AACA,eAAS,YAAY,WAAW,QAAQ,KAAK,SAAS;AAClD,cAAM,UAAU,UAAU,QAAQ;AAClC,YAAI,kBAAkB,QAAQ,MAAM,WAAW;AAC/C,cAAM,OAAO,UAAU,GAAgB,QAAQ,GAAG;AAClD,aAAK,QAAQ,CAAC;AACd,aAAK,MAAM,KAAK,OAAO;AACvB,WAAG;AACC,gBAAM,MAAM,aAAa,SAAS;AAClC,cAAI,CAAC,iBAAiB;AAClB,8BAAkB,IAAI,MAAM,WAAW;AAAA,UAC3C;AACA,eAAK,MAAM,KAAK,GAAG;AAAA,QACvB,SAAS,QAAQ,gBAAgB;AACjC,YAAI,iBAAiB;AACjB,oBAAU,WAAW,kBAAkB,8BAA8B,KAAK,CAAC;AAAA,QAC/E;AACA,gBAAQ,MAAM,UAAU,cAAc,GAAG,UAAU,gBAAgB,CAAC;AACpE,eAAO;AAAA,MACX;AACA,eAAS,cAAc,WAAW;AAC9B,cAAM,UAAU,UAAU,QAAQ;AAClC,cAAM,EAAE,QAAQ,SAAS,IAAI;AAC7B,cAAM,UAAU,aAAa,SAAS;AACtC,YAAI,QAAQ,gBAAgB,IAAc;AACtC,iBAAO;AAAA,QACX,OACK;AACD,iBAAO,YAAY,WAAW,QAAQ,UAAU,OAAO;AAAA,QAC3D;AAAA,MACJ;AACA,eAAS,MAAM,QAAQ;AACnB,cAAM,YAAY,gBAAgB,QAAQ,OAAO,OAAO,CAAC,GAAG,OAAO,CAAC;AACpE,cAAM,UAAU,UAAU,QAAQ;AAClC,cAAM,OAAO,UAAU,GAAkB,QAAQ,QAAQ,QAAQ,QAAQ;AACzE,YAAI,YAAY,KAAK,KAAK;AACtB,eAAK,IAAI,SAAS;AAAA,QACtB;AACA,aAAK,OAAO,cAAc,SAAS;AAEnC,YAAI,QAAQ,gBAAgB,IAAc;AACtC,oBAAU,WAAW,kBAAkB,6BAA6B,QAAQ,cAAc,GAAG,OAAO,QAAQ,MAAM,KAAK,EAAE;AAAA,QAC7H;AACA,gBAAQ,MAAM,UAAU,cAAc,GAAG,UAAU,gBAAgB,CAAC;AACpE,eAAO;AAAA,MACX;AACA,aAAO,EAAE,MAAM;AAAA,IACnB;AACA,aAAS,gBAAgB,OAAO;AAC5B,UAAI,MAAM,SAAS,IAAc;AAC7B,eAAO;AAAA,MACX;AACA,YAAM,QAAQ,MAAM,SAAS,IAAI,QAAQ,WAAW,KAAK;AACzD,aAAO,KAAK,SAAS,KAAK,KAAK,MAAM,GAAG,CAAC,IAAI,MAAM;AAAA,IACvD;AAEA,aAAS,kBAAkB,KAAK,UAAU,CAAC,GACzC;AACE,YAAM,WAAW;AAAA,QACb;AAAA,QACA,SAAS,oBAAI,IAAI;AAAA,MACrB;AACA,YAAM,UAAU,MAAM;AACtB,YAAM,SAAS,CAAC,SAAS;AACrB,iBAAS,QAAQ,IAAI,IAAI;AACzB,eAAO;AAAA,MACX;AACA,aAAO,EAAE,SAAS,OAAO;AAAA,IAC7B;AACA,aAAS,cAAc,OAAO,aAAa;AACvC,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,qBAAa,MAAM,CAAC,GAAG,WAAW;AAAA,MACtC;AAAA,IACJ;AACA,aAAS,aAAa,MAAM,aAAa;AAErC,cAAQ,KAAK,MAAM;AAAA,QACf,KAAK;AACD,wBAAc,KAAK,OAAO,WAAW;AACrC,sBAAY;AAAA,YAAO;AAAA;AAAA,UAAqB;AACxC;AAAA,QACJ,KAAK;AACD,wBAAc,KAAK,OAAO,WAAW;AACrC;AAAA,QACJ,KAAK;AACD,gBAAM,SAAS;AACf,uBAAa,OAAO,KAAK,WAAW;AACpC,sBAAY;AAAA,YAAO;AAAA;AAAA,UAAqB;AACxC,sBAAY;AAAA,YAAO;AAAA;AAAA,UAAiB;AACpC;AAAA,QACJ,KAAK;AACD,sBAAY;AAAA,YAAO;AAAA;AAAA,UAA+B;AAClD,sBAAY;AAAA,YAAO;AAAA;AAAA,UAAiB;AACpC;AAAA,QACJ,KAAK;AACD,sBAAY;AAAA,YAAO;AAAA;AAAA,UAA+B;AAClD,sBAAY;AAAA,YAAO;AAAA;AAAA,UAAmB;AACtC;AAAA,MACR;AAAA,IAEJ;AAEA,aAAS,UAAU,KAAK,UAAU,CAAC,GACjC;AACE,YAAM,cAAc,kBAAkB,GAAG;AACzC,kBAAY;AAAA,QAAO;AAAA;AAAA,MAA2B;AAE9C,UAAI,QAAQ,aAAa,IAAI,MAAM,WAAW;AAE9C,YAAM,UAAU,YAAY,QAAQ;AACpC,UAAI,UAAU,MAAM,KAAK,QAAQ,OAAO;AAAA,IAC5C;AAEA,aAAS,oBAAoB,KAAK,SAAS;AACvC,YAAM,EAAE,WAAW,aAAa,UAAU,eAAe,YAAY,YAAY,IAAI;AACrF,YAAM,WAAW;AAAA,QACb,QAAQ,IAAI,IAAI;AAAA,QAChB;AAAA,QACA,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,KAAK;AAAA,QACL;AAAA,QACA,YAAY;AAAA,QACZ,aAAa;AAAA,MACjB;AACA,YAAM,UAAU,MAAM;AACtB,eAAS,KAAK,MAAM,MAAM;AACtB,iBAAS,QAAQ;AACjB,YAAI,SAAS,KAAK;AACd,cAAI,QAAQ,KAAK,OAAO,KAAK,QAAQ,cAAc;AAC/C,uBAAW,KAAK,IAAI,OAAO,eAAe,IAAI,CAAC;AAAA,UACnD;AACA,oCAA0B,UAAU,IAAI;AAAA,QAC5C;AAAA,MACJ;AACA,eAAS,SAAS,GAAG,gBAAgB,MAAM;AACvC,cAAM,iBAAiB,gBAAgB,gBAAgB;AACvD,aAAK,cAAc,iBAAiB,KAAK,OAAO,CAAC,IAAI,cAAc;AAAA,MACvE;AACA,eAAS,OAAO,cAAc,MAAM;AAChC,cAAM,QAAQ,EAAE,SAAS;AACzB,uBAAe,SAAS,KAAK;AAAA,MACjC;AACA,eAAS,SAAS,cAAc,MAAM;AAClC,cAAM,QAAQ,EAAE,SAAS;AACzB,uBAAe,SAAS,KAAK;AAAA,MACjC;AACA,eAAS,UAAU;AACf,iBAAS,SAAS,WAAW;AAAA,MACjC;AACA,YAAM,SAAS,CAAC,QAAQ,IAAI,GAAG;AAC/B,YAAM,aAAa,MAAM,SAAS;AAClC,eAAS,WAAW,KAAK,MAAM;AAC3B,iBAAS,IAAI,WAAW;AAAA,UACpB;AAAA,UACA,QAAQ,SAAS;AAAA,UACjB,UAAU;AAAA,YACN,MAAM,IAAI;AAAA,YACV,QAAQ,IAAI,SAAS;AAAA,UACzB;AAAA,UACA,WAAW;AAAA,YACP,MAAM,SAAS;AAAA,YACf,QAAQ,SAAS,SAAS;AAAA,UAC9B;AAAA,QACJ,CAAC;AAAA,MACL;AACA,UAAI,aAAa;AACb,iBAAS,MAAM,IAAI,UAAU,mBAAmB;AAChD,iBAAS,IAAI,iBAAiB,UAAU,SAAS,MAAM;AAAA,MAC3D;AACA,aAAO;AAAA,QACH;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,IACJ;AACA,aAAS,mBAAmB,WAAW,MAAM;AACzC,YAAM,EAAE,OAAO,IAAI;AACnB,gBAAU,KAAK,GAAG;AAAA,QAAO;AAAA;AAAA,MAAqB,CAAC,GAAG;AAClD,mBAAa,WAAW,KAAK,GAAG;AAChC,UAAI,KAAK,UAAU;AACf,kBAAU,KAAK,IAAI;AACnB,qBAAa,WAAW,KAAK,QAAQ;AACrC,kBAAU,KAAK,SAAS;AAAA,MAC5B,OACK;AACD,kBAAU,KAAK,oBAAoB;AAAA,MACvC;AACA,gBAAU,KAAK,GAAG;AAAA,IACtB;AACA,aAAS,oBAAoB,WAAW,MAAM;AAC1C,YAAM,EAAE,QAAQ,WAAW,IAAI;AAC/B,gBAAU,KAAK,GAAG;AAAA,QAAO;AAAA;AAAA,MAA2B,CAAC,IAAI;AACzD,gBAAU,OAAO,WAAW,CAAC;AAC7B,YAAM,SAAS,KAAK,MAAM;AAC1B,eAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC7B,qBAAa,WAAW,KAAK,MAAM,CAAC,CAAC;AACrC,YAAI,MAAM,SAAS,GAAG;AAClB;AAAA,QACJ;AACA,kBAAU,KAAK,IAAI;AAAA,MACvB;AACA,gBAAU,SAAS,WAAW,CAAC;AAC/B,gBAAU,KAAK,IAAI;AAAA,IACvB;AACA,aAAS,mBAAmB,WAAW,MAAM;AACzC,YAAM,EAAE,QAAQ,WAAW,IAAI;AAC/B,UAAI,KAAK,MAAM,SAAS,GAAG;AACvB,kBAAU,KAAK,GAAG;AAAA,UAAO;AAAA;AAAA,QAAqB,CAAC,IAAI;AACnD,kBAAU,OAAO,WAAW,CAAC;AAC7B,cAAM,SAAS,KAAK,MAAM;AAC1B,iBAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC7B,uBAAa,WAAW,KAAK,MAAM,CAAC,CAAC;AACrC,cAAI,MAAM,SAAS,GAAG;AAClB;AAAA,UACJ;AACA,oBAAU,KAAK,IAAI;AAAA,QACvB;AACA,kBAAU,SAAS,WAAW,CAAC;AAC/B,kBAAU,KAAK,IAAI;AAAA,MACvB;AAAA,IACJ;AACA,aAAS,iBAAiB,WAAW,MAAM;AACvC,UAAI,KAAK,MAAM;AACX,qBAAa,WAAW,KAAK,IAAI;AAAA,MACrC,OACK;AACD,kBAAU,KAAK,MAAM;AAAA,MACzB;AAAA,IACJ;AACA,aAAS,aAAa,WAAW,MAAM;AACnC,YAAM,EAAE,OAAO,IAAI;AACnB,cAAQ,KAAK,MAAM;AAAA,QACf,KAAK;AACD,2BAAiB,WAAW,IAAI;AAChC;AAAA,QACJ,KAAK;AACD,6BAAmB,WAAW,IAAI;AAClC;AAAA,QACJ,KAAK;AACD,8BAAoB,WAAW,IAAI;AACnC;AAAA,QACJ,KAAK;AACD,6BAAmB,WAAW,IAAI;AAClC;AAAA,QACJ,KAAK;AACD,oBAAU,KAAK,KAAK,UAAU,KAAK,KAAK,GAAG,IAAI;AAC/C;AAAA,QACJ,KAAK;AACD,oBAAU,KAAK,KAAK,UAAU,KAAK,KAAK,GAAG,IAAI;AAC/C;AAAA,QACJ,KAAK;AACD,oBAAU,KAAK,GAAG;AAAA,YAAO;AAAA;AAAA,UAA+B,CAAC,IAAI;AAAA,YAAO;AAAA;AAAA,UAAiB,CAAC,IAAI,KAAK,KAAK,MAAM,IAAI;AAC9G;AAAA,QACJ,KAAK;AACD,oBAAU,KAAK,GAAG;AAAA,YAAO;AAAA;AAAA,UAA+B,CAAC,IAAI;AAAA,YAAO;AAAA;AAAA,UAAmB,CAAC,IAAI,KAAK,UAAU,KAAK,GAAG,CAAC,MAAM,IAAI;AAC9H;AAAA,QACJ,KAAK;AACD,oBAAU,KAAK,KAAK,UAAU,KAAK,KAAK,GAAG,IAAI;AAC/C;AAAA,QACJ,KAAK;AACD,oBAAU,KAAK,KAAK,UAAU,KAAK,KAAK,GAAG,IAAI;AAC/C;AAAA,QACJ,SACI;AACI,gBAAM,IAAI,MAAM,gCAAgC,KAAK,IAAI,EAAE;AAAA,QAC/D;AAAA,MACR;AAAA,IACJ;AAEA,QAAM,WAAW,CAAC,KAAK,UAAU,CAAC,MAC7B;AACD,YAAM,OAAO,OAAO,SAAS,QAAQ,IAAI,IAAI,QAAQ,OAAO;AAC5D,YAAM,WAAW,OAAO,SAAS,QAAQ,QAAQ,IAC3C,QAAQ,WACR;AACN,YAAME,aAAY,CAAC,CAAC,QAAQ;AAE5B,YAAM,gBAAgB,QAAQ,iBAAiB,OACzC,QAAQ,gBACR,SAAS,UACL,MACA;AACV,YAAM,aAAa,QAAQ,aAAa,QAAQ,aAAa,SAAS;AACtE,YAAM,UAAU,IAAI,WAAW,CAAC;AAChC,YAAM,YAAY,oBAAoB,KAAK;AAAA,QACvC;AAAA,QACA;AAAA,QACA,WAAAA;AAAA,QACA;AAAA,QACA;AAAA,MACJ,CAAC;AACD,gBAAU,KAAK,SAAS,WAAW,6BAA6B,YAAY;AAC5E,gBAAU,OAAO,UAAU;AAC3B,UAAI,QAAQ,SAAS,GAAG;AACpB,kBAAU,KAAK,WAAW,QAAQ,IAAI,OAAK,GAAG,CAAC,MAAM,CAAC,EAAE,EAAE,KAAK,IAAI,CAAC,UAAU;AAC9E,kBAAU,QAAQ;AAAA,MACtB;AACA,gBAAU,KAAK,SAAS;AACxB,mBAAa,WAAW,GAAG;AAC3B,gBAAU,SAAS,UAAU;AAC7B,gBAAU,KAAK,GAAG;AAClB,YAAM,EAAE,MAAM,IAAI,IAAI,UAAU,QAAQ;AACxC,aAAO;AAAA,QACH;AAAA,QACA;AAAA,QACA,KAAK,MAAM,IAAI,OAAO,IAAI;AAAA;AAAA,MAC9B;AAAA,IACJ;AACA,aAAS,eAAe,MAAM;AAC1B,cAAQ,KAAK,MAAM;AAAA,QACf,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACD,iBAAO,KAAK;AAAA,QAChB,KAAK;AACD,iBAAO,KAAK,MAAM,SAAS;AAAA,QAC/B,KAAK;AACD,iBAAO,KAAK;AAAA,QAChB;AACI,iBAAO;AAAA,MACf;AAAA,IACJ;AACA,aAAS,0BAA0B,KAAK,QAAQ,qBAAqB,OAAO,QAAQ;AAChF,UAAI,aAAa;AACjB,UAAI,iBAAiB;AACrB,eAAS,IAAI,GAAG,IAAI,oBAAoB,KAAK;AACzC,YAAI,OAAO,WAAW,CAAC,MAAM,IAA4B;AACrD;AACA,2BAAiB;AAAA,QACrB;AAAA,MACJ;AACA,UAAI,UAAU;AACd,UAAI,QAAQ;AACZ,UAAI,SACA,mBAAmB,KACb,IAAI,SAAS,qBACb,qBAAqB;AAC/B,aAAO;AAAA,IACX;AAEA,aAAS,YAAY,QAAQ,UAAU,CAAC,GAAG;AACvC,YAAM,kBAAkB,OAAO,OAAO,CAAC,GAAG,OAAO;AAEjD,YAAM,SAAS,aAAa,eAAe;AAC3C,YAAM,MAAM,OAAO,MAAM,MAAM;AAE/B,gBAAU,KAAK,eAAe;AAE9B,aAAO,SAAS,KAAK,eAAe;AAAA,IACxC;AAEA,YAAQ,oBAAoB;AAC5B,YAAQ,eAAe;AACvB,YAAQ,eAAe;AACvB,YAAQ,cAAc;AACtB,YAAQ,qBAAqB;AAC7B,YAAQ,iBAAiB;AACzB,YAAQ,eAAe;AACvB,YAAQ,iBAAiB;AACzB,YAAQ,iBAAiB;AACzB,YAAQ,gBAAgB;AAAA;AAAA;;;ACh6CxB;AAAA;AAAA;AAEA,QAAI,OAAuC;AACzC,aAAO,UAAU;AAAA,IACnB,OAAO;AACL,aAAO,UAAU;AAAA,IACnB;AAAA;AAAA;;;ACNA;AAAA;AAAA;AAOA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAE5D,QAAM,uBAAwB;AAAA,MAC1B,UAAU;AAAA,MACV,mBAAmB;AAAA,IACvB;AAEA,YAAQ,uBAAuB;AAAA;AAAA;;;ACd/B;AAAA;AAAA;AAEA,QAAI,OAAuC;AACzC,aAAO,UAAU;AAAA,IACnB,OAAO;AACL,aAAO,UAAU;AAAA,IACnB;AAAA;AAAA;;;ACNA;AAAA;AAAA;AAOA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAE5D,QAAI,kBAAkB;AACtB,QAAI,SAAS;AACb,QAAI,aAAa;AAEjB,QAAM,mBAAoB,CAAC;AAC3B;AAAA,MAAiB;AAAA;AAAA,IAAmB,IAAI;AAAA,MACpC;AAAA,QAAC;AAAA;AAAA,MAAmB,GAAG;AAAA,QAAC;AAAA;AAAA,MAAmB;AAAA,MAC3C;AAAA,QAAC;AAAA;AAAA,MAAe,GAAG;AAAA,QAAC;AAAA,QAAkB;AAAA;AAAA,MAAc;AAAA,MACpD;AAAA,QAAC;AAAA;AAAA,MAAsB,GAAG;AAAA,QAAC;AAAA;AAAA,MAAmB;AAAA,MAC9C;AAAA,QAAC;AAAA;AAAA,MAAqB,GAAG;AAAA,QAAC;AAAA;AAAA,MAAkB;AAAA,IAChD;AACA;AAAA,MAAiB;AAAA;AAAA,IAAe,IAAI;AAAA,MAChC;AAAA,QAAC;AAAA;AAAA,MAAmB,GAAG;AAAA,QAAC;AAAA;AAAA,MAAe;AAAA,MACvC;AAAA,QAAC;AAAA;AAAA,MAAa,GAAG;AAAA,QAAC;AAAA;AAAA,MAAoB;AAAA,MACtC;AAAA,QAAC;AAAA;AAAA,MAAsB,GAAG;AAAA,QAAC;AAAA;AAAA,MAAmB;AAAA,MAC9C;AAAA,QAAC;AAAA;AAAA,MAAqB,GAAG;AAAA,QAAC;AAAA;AAAA,MAAkB;AAAA,IAChD;AACA;AAAA,MAAiB;AAAA;AAAA,IAAoB,IAAI;AAAA,MACrC;AAAA,QAAC;AAAA;AAAA,MAAmB,GAAG;AAAA,QAAC;AAAA;AAAA,MAAoB;AAAA,MAC5C;AAAA,QAAC;AAAA;AAAA,MAAe,GAAG;AAAA,QAAC;AAAA,QAAkB;AAAA;AAAA,MAAc;AAAA,MACpD;AAAA,QAAC;AAAA;AAAA,MAAc,GAAG;AAAA,QAAC;AAAA,QAAkB;AAAA;AAAA,MAAc;AAAA,IACvD;AACA;AAAA,MAAiB;AAAA;AAAA,IAAgB,IAAI;AAAA,MACjC;AAAA,QAAC;AAAA;AAAA,MAAe,GAAG;AAAA,QAAC;AAAA,QAAkB;AAAA;AAAA,MAAc;AAAA,MACpD;AAAA,QAAC;AAAA;AAAA,MAAc,GAAG;AAAA,QAAC;AAAA,QAAkB;AAAA;AAAA,MAAc;AAAA,MACnD;AAAA,QAAC;AAAA;AAAA,MAAmB,GAAG;AAAA,QAAC;AAAA,QAAiB;AAAA;AAAA,MAAY;AAAA,MACrD;AAAA,QAAC;AAAA;AAAA,MAAa,GAAG;AAAA,QAAC;AAAA,QAAsB;AAAA;AAAA,MAAY;AAAA,MACpD;AAAA,QAAC;AAAA;AAAA,MAAsB,GAAG;AAAA,QAAC;AAAA,QAAqB;AAAA;AAAA,MAAY;AAAA,MAC5D;AAAA,QAAC;AAAA;AAAA,MAAqB,GAAG;AAAA,QAAC;AAAA,QAAoB;AAAA;AAAA,MAAY;AAAA,IAC9D;AACA;AAAA,MAAiB;AAAA;AAAA,IAAmB,IAAI;AAAA,MACpC;AAAA,QAAC;AAAA;AAAA,MAAsB,GAAG;AAAA,QAAC;AAAA,QAAyB;AAAA;AAAA,MAAc;AAAA,MAClE;AAAA,QAAC;AAAA;AAAA,MAAuB,GAAG;AAAA,QAAC;AAAA,QAAyB;AAAA;AAAA,MAAc;AAAA,MACnE;AAAA,QAAC;AAAA;AAAA,MAAsB,GAAG;AAAA,QACtB;AAAA,QACA;AAAA;AAAA,MACJ;AAAA,MACA;AAAA,QAAC;AAAA;AAAA,MAAuB,GAAG;AAAA,QAAC;AAAA,QAAiB;AAAA;AAAA,MAAqB;AAAA,MAClE;AAAA,QAAC;AAAA;AAAA,MAAqB,GAAG;AAAA,MACzB;AAAA,QAAC;AAAA;AAAA,MAAc,GAAG;AAAA,QAAC;AAAA,QAAqB;AAAA;AAAA,MAAc;AAAA,IAC1D;AACA;AAAA,MAAiB;AAAA;AAAA,IAAuB,IAAI;AAAA,MACxC;AAAA,QAAC;AAAA;AAAA,MAAsB,GAAG;AAAA,QAAC;AAAA,QAAqB;AAAA;AAAA,MAAc;AAAA,MAC9D;AAAA,QAAC;AAAA;AAAA,MAAqB,GAAG;AAAA,MACzB;AAAA,QAAC;AAAA;AAAA,MAAc,GAAG;AAAA,QAAC;AAAA,QAAyB;AAAA;AAAA,MAAc;AAAA,IAC9D;AACA;AAAA,MAAiB;AAAA;AAAA,IAAuB,IAAI;AAAA,MACxC;AAAA,QAAC;AAAA;AAAA,MAAuB,GAAG;AAAA,QAAC;AAAA,QAAqB;AAAA;AAAA,MAAc;AAAA,MAC/D;AAAA,QAAC;AAAA;AAAA,MAAqB,GAAG;AAAA,MACzB;AAAA,QAAC;AAAA;AAAA,MAAc,GAAG;AAAA,QAAC;AAAA,QAAyB;AAAA;AAAA,MAAc;AAAA,IAC9D;AAIA,QAAM,iBAAiB;AACvB,aAAS,UAAU,KAAK;AACpB,aAAO,eAAe,KAAK,GAAG;AAAA,IAClC;AAIA,aAAS,YAAY,KAAK;AACtB,YAAM,IAAI,IAAI,WAAW,CAAC;AAC1B,YAAM,IAAI,IAAI,WAAW,IAAI,SAAS,CAAC;AACvC,aAAO,MAAM,MAAM,MAAM,MAAQ,MAAM,MAAQ,IAAI,MAAM,GAAG,EAAE,IAAI;AAAA,IACtE;AAIA,aAAS,gBAAgB,IAAI;AACzB,UAAI,OAAO,UAAa,OAAO,MAAM;AACjC,eAAO;AAAA,MACX;AACA,YAAMC,QAAO,GAAG,WAAW,CAAC;AAC5B,cAAQA,OAAM;AAAA,QACV,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACD,iBAAO;AAAA,QACX,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACD,iBAAO;AAAA,QACX,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACD,iBAAO;AAAA,MACf;AACA,aAAO;AAAA,IACX;AAMA,aAAS,cAAc,MAAM;AACzB,YAAM,UAAU,KAAK,KAAK;AAE1B,UAAI,KAAK,OAAO,CAAC,MAAM,OAAO,MAAM,SAAS,IAAI,CAAC,GAAG;AACjD,eAAO;AAAA,MACX;AACA,aAAO,UAAU,OAAO,IAClB,YAAY,OAAO,IACnB,MAAqB;AAAA,IAC/B;AAIA,aAAS,MAAM,MAAM;AACjB,YAAM,OAAO,CAAC;AACd,UAAI,QAAQ;AACZ,UAAI,OAAO;AACX,UAAI,eAAe;AACnB,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,YAAM,UAAU,CAAC;AACjB;AAAA,QAAQ;AAAA;AAAA,MAAc,IAAI,MAAM;AAC5B,YAAI,QAAQ,QAAW;AACnB,gBAAM;AAAA,QACV,OACK;AACD,iBAAO;AAAA,QACX;AAAA,MACJ;AACA;AAAA,QAAQ;AAAA;AAAA,MAAY,IAAI,MAAM;AAC1B,YAAI,QAAQ,QAAW;AACnB,eAAK,KAAK,GAAG;AACb,gBAAM;AAAA,QACV;AAAA,MACJ;AACA;AAAA,QAAQ;AAAA;AAAA,MAA0B,IAAI,MAAM;AACxC;AAAA,UAAQ;AAAA;AAAA,QAAc,EAAE;AACxB;AAAA,MACJ;AACA;AAAA,QAAQ;AAAA;AAAA,MAAqB,IAAI,MAAM;AACnC,YAAI,eAAe,GAAG;AAClB;AACA,iBAAO;AACP;AAAA,YAAQ;AAAA;AAAA,UAAc,EAAE;AAAA,QAC5B,OACK;AACD,yBAAe;AACf,cAAI,QAAQ,QAAW;AACnB,mBAAO;AAAA,UACX;AACA,gBAAM,cAAc,GAAG;AACvB,cAAI,QAAQ,OAAO;AACf,mBAAO;AAAA,UACX,OACK;AACD;AAAA,cAAQ;AAAA;AAAA,YAAY,EAAE;AAAA,UAC1B;AAAA,QACJ;AAAA,MACJ;AACA,eAAS,qBAAqB;AAC1B,cAAM,WAAW,KAAK,QAAQ,CAAC;AAC/B,YAAK,SAAS,KACV,aAAa,OACZ,SAAS,KACN,aAAa,KAA0B;AAC3C;AACA,oBAAU,OAAO;AACjB;AAAA,YAAQ;AAAA;AAAA,UAAc,EAAE;AACxB,iBAAO;AAAA,QACX;AAAA,MACJ;AACA,aAAO,SAAS,MAAM;AAClB;AACA,YAAI,KAAK,KAAK;AACd,YAAI,MAAM,QAAQ,mBAAmB,GAAG;AACpC;AAAA,QACJ;AACA,eAAO,gBAAgB,CAAC;AACxB,kBAAU,iBAAiB,IAAI;AAC/B,qBAAa,QAAQ,IAAI,KAAK;AAAA,UAAQ;AAAA;AAAA,QAAc,KAAK;AAEzD,YAAI,eAAe,GAAe;AAC9B;AAAA,QACJ;AACA,eAAO,WAAW,CAAC;AACnB,YAAI,WAAW,CAAC,MAAM,QAAW;AAC7B,mBAAS,QAAQ,WAAW,CAAC,CAAC;AAC9B,cAAI,QAAQ;AACR,sBAAU;AACV,gBAAI,OAAO,MAAM,OAAO;AACpB;AAAA,YACJ;AAAA,UACJ;AAAA,QACJ;AAEA,YAAI,SAAS,GAAoB;AAC7B,iBAAO;AAAA,QACX;AAAA,MACJ;AAAA,IACJ;AAEA,QAAM,QAAQ,oBAAI,IAAI;AActB,aAAS,oBAAoB,KAAK,MAAM;AACpC,aAAO,OAAO,SAAS,GAAG,IAAI,IAAI,IAAI,IAAI;AAAA,IAC9C;AAcA,aAAS,aAAa,KAAK,MAAM;AAE7B,UAAI,CAAC,OAAO,SAAS,GAAG,GAAG;AACvB,eAAO;AAAA,MACX;AAEA,UAAI,MAAM,MAAM,IAAI,IAAI;AACxB,UAAI,CAAC,KAAK;AACN,cAAM,MAAM,IAAI;AAChB,YAAI,KAAK;AACL,gBAAM,IAAI,MAAM,GAAG;AAAA,QACvB;AAAA,MACJ;AAEA,UAAI,CAAC,KAAK;AACN,eAAO;AAAA,MACX;AAEA,YAAM,MAAM,IAAI;AAChB,UAAI,OAAO;AACX,UAAI,IAAI;AACR,aAAO,IAAI,KAAK;AACZ,cAAM,MAAM,KAAK,IAAI,CAAC,CAAC;AACvB,YAAI,QAAQ,QAAW;AACnB,iBAAO;AAAA,QACX;AACA,eAAO;AACP;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AAEA,QAAM,mBAAmB,CAAC,QAAQ;AAClC,QAAM,kBAAkB,CAAC,QAAQ;AACjC,QAAM,4BAA4B;AAClC,QAAM,oBAAoB,CAAC,WAAW,OAAO,WAAW,IAAI,KAAK,OAAO,KAAK,EAAE;AAC/E,QAAM,sBAAsB,OAAO;AACnC,aAAS,cAAc,QAAQ,eAAe;AAC1C,eAAS,KAAK,IAAI,MAAM;AACxB,UAAI,kBAAkB,GAAG;AAErB,eAAO,SACD,SAAS,IACL,IACA,IACJ;AAAA,MACV;AACA,aAAO,SAAS,KAAK,IAAI,QAAQ,CAAC,IAAI;AAAA,IAC1C;AACA,aAAS,eAAe,SAAS;AAE7B,YAAM,QAAQ,OAAO,SAAS,QAAQ,WAAW,IAC3C,QAAQ,cACR;AAEN,aAAO,QAAQ,UAAU,OAAO,SAAS,QAAQ,MAAM,KAAK,KAAK,OAAO,SAAS,QAAQ,MAAM,CAAC,KAC1F,OAAO,SAAS,QAAQ,MAAM,KAAK,IAC/B,QAAQ,MAAM,QACd,OAAO,SAAS,QAAQ,MAAM,CAAC,IAC3B,QAAQ,MAAM,IACd,QACR;AAAA,IACV;AACA,aAAS,eAAe,aAAa,OAAO;AACxC,UAAI,CAAC,MAAM,OAAO;AACd,cAAM,QAAQ;AAAA,MAClB;AACA,UAAI,CAAC,MAAM,GAAG;AACV,cAAM,IAAI;AAAA,MACd;AAAA,IACJ;AACA,aAAS,qBAAqB,UAAU,CAAC,GAAG;AACxC,YAAM,SAAS,QAAQ;AACvB,YAAM,cAAc,eAAe,OAAO;AAC1C,YAAM,aAAa,OAAO,SAAS,QAAQ,WAAW,KAClD,OAAO,SAAS,MAAM,KACtB,OAAO,WAAW,QAAQ,YAAY,MAAM,CAAC,IAC3C,QAAQ,YAAY,MAAM,IAC1B;AACN,YAAM,gBAAgB,OAAO,SAAS,QAAQ,WAAW,KACrD,OAAO,SAAS,MAAM,KACtB,OAAO,WAAW,QAAQ,YAAY,MAAM,CAAC,IAC3C,gBACA;AACN,YAAM,SAAS,CAAC,aAAa;AACzB,eAAO,SAAS,WAAW,aAAa,SAAS,QAAQ,aAAa,CAAC;AAAA,MAC3E;AACA,YAAM,QAAQ,QAAQ,QAAQ,CAAC;AAC/B,YAAM,OAAO,CAAC,UAAU,MAAM,KAAK;AAEnC,YAAM,SAAS,QAAQ,SAAS,CAAC;AACjC,aAAO,SAAS,QAAQ,WAAW,KAAK,eAAe,aAAa,MAAM;AAC1E,YAAM,QAAQ,CAAC,QAAQ,OAAO,GAAG;AACjC,eAAS,QAAQ,KAAK;AAElB,cAAM,MAAM,OAAO,WAAW,QAAQ,QAAQ,IACxC,QAAQ,SAAS,GAAG,IACpB,OAAO,SAAS,QAAQ,QAAQ,IAC5B,QAAQ,SAAS,GAAG,IACpB;AACV,eAAO,CAAC,MACF,QAAQ,SACJ,QAAQ,OAAO,QAAQ,GAAG,IAC1B,kBACJ;AAAA,MACV;AACA,YAAM,YAAY,CAAC,SAAS,QAAQ,YAC9B,QAAQ,UAAU,IAAI,IACtB;AACN,YAAM,YAAY,OAAO,cAAc,QAAQ,SAAS,KAAK,OAAO,WAAW,QAAQ,UAAU,SAAS,IACpG,QAAQ,UAAU,YAClB;AACN,YAAM,cAAc,OAAO,cAAc,QAAQ,SAAS,KACtD,OAAO,WAAW,QAAQ,UAAU,WAAW,IAC7C,QAAQ,UAAU,cAClB;AACN,YAAM,OAAO,OAAO,cAAc,QAAQ,SAAS,KAAK,OAAO,SAAS,QAAQ,UAAU,IAAI,IACxF,QAAQ,UAAU,OAClB;AACN,YAAM,SAAS,CAAC,QAAQ,SAAS;AAC7B,cAAM,CAAC,MAAM,IAAI,IAAI;AACrB,YAAIC,QAAO;AACX,YAAI,WAAW;AACf,YAAI,KAAK,WAAW,GAAG;AACnB,cAAI,OAAO,SAAS,IAAI,GAAG;AACvB,uBAAW,KAAK,YAAY;AAC5B,YAAAA,QAAO,KAAK,QAAQA;AAAA,UACxB,WACS,OAAO,SAAS,IAAI,GAAG;AAC5B,uBAAW,QAAQ;AAAA,UACvB;AAAA,QACJ,WACS,KAAK,WAAW,GAAG;AACxB,cAAI,OAAO,SAAS,IAAI,GAAG;AACvB,uBAAW,QAAQ;AAAA,UACvB;AACA,cAAI,OAAO,SAAS,IAAI,GAAG;AACvB,YAAAA,QAAO,QAAQA;AAAA,UACnB;AAAA,QACJ;AACA,YAAI,MAAM,QAAQ,GAAG,EAAE,GAAG;AAE1B,YAAIA,UAAS,WAAW,OAAO,QAAQ,GAAG,KAAK,UAAU;AACrD,gBAAM,IAAI,CAAC;AAAA,QACf;AACA,eAAO,WAAW,UAAU,QAAQ,EAAE,KAAKA,KAAI,IAAI;AAAA,MACvD;AACA,YAAM,MAAM;AAAA,QACR;AAAA,UAAC;AAAA;AAAA,QAAiB,GAAG;AAAA,QACrB;AAAA,UAAC;AAAA;AAAA,QAAmB,GAAG;AAAA,QACvB;AAAA,UAAC;AAAA;AAAA,QAAqB,GAAG;AAAA,QACzB;AAAA,UAAC;AAAA;AAAA,QAAqB,GAAG;AAAA,QACzB;AAAA,UAAC;AAAA;AAAA,QAAuB,GAAG;AAAA,QAC3B;AAAA,UAAC;AAAA;AAAA,QAAiB,GAAG;AAAA,QACrB;AAAA,UAAC;AAAA;AAAA,QAA+B,GAAG;AAAA,QACnC;AAAA,UAAC;AAAA;AAAA,QAA2B,GAAG;AAAA,MACnC;AACA,aAAO;AAAA,IACX;AAEA,QAAI,WAAW;AACf,aAAS,gBAAgB,MAAM;AAC3B,iBAAW;AAAA,IACf;AACA,aAAS,kBAAkB;AACvB,aAAO;AAAA,IACX;AACA,aAAS,iBAAiB,MAAM,SAAS,MAAM;AAE3C,kBACI,SAAS,KAAK,WAAW,qBAAqB,UAAU;AAAA,QACpD,WAAW,KAAK,IAAI;AAAA,QACpB;AAAA,QACA;AAAA,QACA;AAAA,MACJ,CAAC;AAAA,IACT;AACA,QAAM,oBAAmC,mBAAmB,WAAW,qBAAqB,iBAAiB;AAC7G,aAAS,mBAAmB,MAAM;AAC9B,aAAO,CAAC,aAAa,YAAY,SAAS,KAAK,MAAM,QAAQ;AAAA,IACjE;AAEA,QAAM,gBAAgB;AAAA,MAClB,eAAe;AAAA,MACf,uBAAuB;AAAA,MACvB,sBAAsB;AAAA,MACtB,2BAA2B;AAAA,MAC3B,oBAAoB;AAAA,MACpB,yBAAyB;AAAA,MACzB,kBAAkB;AAAA,IACtB;AAEA,QAAM,eAAe;AAAA,MACjB,CAAC,cAAc,aAAa,GAAG;AAAA,MAC/B,CAAC,cAAc,qBAAqB,GAAG;AAAA,MACvC,CAAC,cAAc,oBAAoB,GAAG;AAAA,MACtC,CAAC,cAAc,yBAAyB,GAAG;AAAA,MAC3C,CAAC,cAAc,kBAAkB,GAAG;AAAA,MACpC,CAAC,cAAc,uBAAuB,GAAG;AAAA,IAC7C;AACA,aAAS,eAAeD,UAAS,MAAM;AACnC,aAAO,OAAO,OAAO,aAAaA,KAAI,GAAG,GAAG,IAAI;AAAA,IACpD;AAkBA,aAAS,mBAAmB,KAAK,UAAU,OACzC;AAEE,aAAO,CAAC,GAAG,oBAAI,IAAI;AAAA,QACX;AAAA,QACA,GAAI,OAAO,QAAQ,QAAQ,IACrB,WACA,OAAO,SAAS,QAAQ,IACpB,OAAO,KAAK,QAAQ,IACpB,OAAO,SAAS,QAAQ,IACpB,CAAC,QAAQ,IACT,CAAC,KAAK;AAAA,MACxB,CAAC,CAAC;AAAA,IACV;AAiBA,aAAS,wBAAwB,KAAK,UAAU,OAAO;AACnD,YAAM,cAAc,OAAO,SAAS,KAAK,IAAI,QAAQ;AACrD,YAAM,UAAU;AAChB,UAAI,CAAC,QAAQ,oBAAoB;AAC7B,gBAAQ,qBAAqB,oBAAI,IAAI;AAAA,MACzC;AACA,UAAI,QAAQ,QAAQ,mBAAmB,IAAI,WAAW;AACtD,UAAI,CAAC,OAAO;AACR,gBAAQ,CAAC;AAET,YAAI,QAAQ,CAAC,KAAK;AAElB,eAAO,OAAO,QAAQ,KAAK,GAAG;AAC1B,kBAAQ,mBAAmB,OAAO,OAAO,QAAQ;AAAA,QACrD;AAGA,cAAM,WAAW,OAAO,QAAQ,QAAQ,KAAK,CAAC,OAAO,cAAc,QAAQ,IACrE,WACA,SAAS,SAAS,IACd,SAAS,SAAS,IAClB;AAEV,gBAAQ,OAAO,SAAS,QAAQ,IAAI,CAAC,QAAQ,IAAI;AACjD,YAAI,OAAO,QAAQ,KAAK,GAAG;AACvB,6BAAmB,OAAO,OAAO,KAAK;AAAA,QAC1C;AACA,gBAAQ,mBAAmB,IAAI,aAAa,KAAK;AAAA,MACrD;AACA,aAAO;AAAA,IACX;AACA,aAAS,mBAAmB,OAAO,OAAO,QAAQ;AAC9C,UAAI,SAAS;AACb,eAAS,IAAI,GAAG,IAAI,MAAM,UAAU,OAAO,UAAU,MAAM,GAAG,KAAK;AAC/D,cAAM,SAAS,MAAM,CAAC;AACtB,YAAI,OAAO,SAAS,MAAM,GAAG;AACzB,mBAAS,oBAAoB,OAAO,MAAM,CAAC,GAAG,MAAM;AAAA,QACxD;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AACA,aAAS,oBAAoB,OAAO,QAAQ,QAAQ;AAChD,UAAI;AACJ,YAAM,SAAS,OAAO,MAAM,GAAG;AAC/B,SAAG;AACC,cAAM,SAAS,OAAO,KAAK,GAAG;AAC9B,iBAAS,kBAAkB,OAAO,QAAQ,MAAM;AAChD,eAAO,OAAO,IAAI,CAAC;AAAA,MACvB,SAAS,OAAO,UAAU,WAAW;AACrC,aAAO;AAAA,IACX;AACA,aAAS,kBAAkB,OAAO,QAAQ,QAAQ;AAC9C,UAAI,SAAS;AACb,UAAI,CAAC,MAAM,SAAS,MAAM,GAAG;AACzB,iBAAS;AACT,YAAI,QAAQ;AACR,mBAAS,OAAO,OAAO,SAAS,CAAC,MAAM;AACvC,gBAAM,SAAS,OAAO,QAAQ,MAAM,EAAE;AACtC,gBAAM,KAAK,MAAM;AACjB,eAAK,OAAO,QAAQ,MAAM,KAAK,OAAO,cAAc,MAAM,MACtD,OAAO,MAAM,GACf;AAEE,qBAAS,OAAO,MAAM;AAAA,UAC1B;AAAA,QACJ;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AAOA,QAAM,UAAU;AAChB,QAAM,eAAe;AACrB,QAAM,iBAAiB;AACvB,QAAM,wBAAwB;AAC9B,QAAM,aAAa,CAAC,QAAQ,GAAG,IAAI,OAAO,CAAC,EAAE,kBAAkB,CAAC,GAAG,IAAI,OAAO,CAAC,CAAC;AAChF,aAAS,4BAA4B;AACjC,aAAO;AAAA,QACH,OAAO,CAAC,KAAK,SAAS;AAElB,iBAAO,SAAS,UAAU,OAAO,SAAS,GAAG,IACvC,IAAI,YAAY,IAChB,SAAS,WAAW,OAAO,SAAS,GAAG,KAAK,iBAAiB,MACzD,IAAI,SAAS,YAAY,IACzB;AAAA,QACd;AAAA,QACA,OAAO,CAAC,KAAK,SAAS;AAElB,iBAAO,SAAS,UAAU,OAAO,SAAS,GAAG,IACvC,IAAI,YAAY,IAChB,SAAS,WAAW,OAAO,SAAS,GAAG,KAAK,iBAAiB,MACzD,IAAI,SAAS,YAAY,IACzB;AAAA,QACd;AAAA,QACA,YAAY,CAAC,KAAK,SAAS;AAEvB,iBAAQ,SAAS,UAAU,OAAO,SAAS,GAAG,IACxC,WAAW,GAAG,IACd,SAAS,WAAW,OAAO,SAAS,GAAG,KAAK,iBAAiB,MACzD,WAAW,IAAI,QAAQ,IACvB;AAAA,QACd;AAAA,MACJ;AAAA,IACJ;AACA,QAAI;AACJ,aAAS,wBAAwB,UAAU;AACvC,kBAAY;AAAA,IAChB;AACA,QAAI;AAQJ,aAAS,wBAAwB,UAAU;AACvC,kBAAY;AAAA,IAChB;AACA,QAAI;AAQJ,aAAS,yBAAyB,YAAY;AAC1C,oBAAc;AAAA,IAClB;AAEA,QAAI,kBAAkB;AACtB,QAAM,oBAAqB,CAAC,SAAS;AACjC,wBAAkB;AAAA,IACtB;AACA,QAAM,oBAAqB,MAAM;AACjC,QAAI,mBAAmB;AACvB,QAAM,qBAAqB,CAAC,YAAY;AACpC,yBAAmB;AAAA,IACvB;AACA,QAAM,qBAAqB,MAAM;AAEjC,QAAI,OAAO;AACX,aAAS,kBAAkB,UAAU,CAAC,GAAG;AAErC,YAAM,UAAU,OAAO,SAAS,QAAQ,OAAO,IAAI,QAAQ,UAAU;AACrE,YAAM,SAAS,OAAO,SAAS,QAAQ,MAAM,IAAI,QAAQ,SAAS;AAClE,YAAM,iBAAiB,OAAO,QAAQ,QAAQ,cAAc,KACxD,OAAO,cAAc,QAAQ,cAAc,KAC3C,OAAO,SAAS,QAAQ,cAAc,KACtC,QAAQ,mBAAmB,QACzB,QAAQ,iBACR;AACN,YAAM,WAAW,OAAO,cAAc,QAAQ,QAAQ,IAChD,QAAQ,WACR,EAAE,CAAC,MAAM,GAAG,CAAC,EAAE;AACrB,YAAM,kBAAkB,OAAO,cAAc,QAAQ,eAAe,IAC1D,QAAQ,kBACR,EAAE,CAAC,MAAM,GAAG,CAAC,EAAE;AAEzB,YAAM,gBAAgB,OAAO,cAAc,QAAQ,aAAa,IACtD,QAAQ,gBACR,EAAE,CAAC,MAAM,GAAG,CAAC,EAAE;AAEzB,YAAM,YAAY,OAAO,OAAO,CAAC,GAAG,QAAQ,aAAa,CAAC,GAAG,0BAA0B,CAAC;AACxF,YAAM,cAAc,QAAQ,eAAe,CAAC;AAC5C,YAAM,UAAU,OAAO,WAAW,QAAQ,OAAO,IAAI,QAAQ,UAAU;AACvE,YAAM,cAAc,OAAO,UAAU,QAAQ,WAAW,KAAK,OAAO,SAAS,QAAQ,WAAW,IAC1F,QAAQ,cACR;AACN,YAAM,eAAe,OAAO,UAAU,QAAQ,YAAY,KAAK,OAAO,SAAS,QAAQ,YAAY,IAC7F,QAAQ,eACR;AACN,YAAM,iBAAiB,CAAC,CAAC,QAAQ;AACjC,YAAM,cAAc,CAAC,CAAC,QAAQ;AAC9B,YAAM,kBAAkB,OAAO,WAAW,QAAQ,eAAe,IAC3D,QAAQ,kBACR;AACN,YAAM,YAAY,OAAO,cAAc,QAAQ,SAAS,IAAI,QAAQ,YAAY;AAChF,YAAM,kBAAkB,OAAO,UAAU,QAAQ,eAAe,IAC1D,QAAQ,kBACR;AACN,YAAM,kBAAkB,CAAC,CAAC,QAAQ;AAClC,YAAME,mBAAkB,OAAO,WAAW,QAAQ,eAAe,IAC3D,QAAQ,kBACR;AACN,YAAM,kBAAkB,OAAO,WAAW,QAAQ,eAAe,IAC3D,QAAQ,kBACR,aAAa;AACnB,YAAM,mBAAmB,OAAO,WAAW,QAAQ,gBAAgB,IAC7D,QAAQ,mBACR,eAAe;AACrB,YAAM,kBAAkB,OAAO,SAAS,QAAQ,eAAe,IACzD,QAAQ,kBACR;AACN,YAAM,SAAS,OAAO,WAAW,QAAQ,MAAM,IAAI,QAAQ,SAAS,OAAO;AAE3E,YAAM,kBAAkB;AACxB,YAAM,uBAAuB,OAAO,SAAS,gBAAgB,oBAAoB,IACvE,gBAAgB,uBAChB,oBAAI,IAAI;AAElB,YAAM,qBAAqB,OAAO,SAAS,gBAAgB,kBAAkB,IACnE,gBAAgB,qBAChB,oBAAI,IAAI;AAElB,YAAM,SAAS,OAAO,SAAS,gBAAgB,MAAM,IAAI,gBAAgB,SAAS,CAAC;AACnF;AACA,YAAM,UAAU;AAAA,QACZ;AAAA,QACA,KAAK;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,iBAAAA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AACA;AACI,gBAAQ,kBAAkB;AAC1B,gBAAQ,gBAAgB;AACxB,gBAAQ,uBAAuB;AAC/B,gBAAQ,qBAAqB;AAAA,MACjC;AAEA;AACI,gBAAQ,cACJ,gBAAgB,eAAe,OACzB,gBAAgB,cAChB;AAAA,MACd;AAEA;AACI,yBAAiB,SAAS,SAAS,MAAM;AAAA,MAC7C;AACA,aAAO;AAAA,IACX;AAEA,aAAS,wBAAwB,UAAU,KAAK;AAC5C,aAAO,oBAAoB,SAAS,SAAS,KAAK,GAAG,IAAI;AAAA,IAC7D;AAEA,aAAS,uBAAuB,SAAS,KAAK;AAC1C,aAAO,mBAAmB,SAAS,QAAQ,KAAK,GAAG,IAAI;AAAA,IAC3D;AAEA,aAAS,cAAc,SAAS,KAAK,QAAQ,aAAa,MAAM;AAC5D,YAAM,EAAE,SAAS,OAAO,IAAI;AAE5B;AACI,cAAM,UAAU,QAAQ;AACxB,YAAI,SAAS;AACT,kBAAQ,KAAK,WAAyB;AAAA,YAClC;AAAA,YACA;AAAA,YACA;AAAA,YACA,SAAS,GAAG,IAAI,IAAI,GAAG;AAAA,UAC3B,CAAC;AAAA,QACL;AAAA,MACJ;AACA,UAAI,YAAY,MAAM;AAClB,cAAM,MAAM,QAAQ,SAAS,QAAQ,KAAK,IAAI;AAC9C,eAAO,OAAO,SAAS,GAAG,IAAI,MAAM;AAAA,MACxC,OACK;AACD,YAAI,uBAAuB,aAAa,GAAG,GAAG;AAC1C,iBAAO,eAAe,cAAc,eAAe,EAAE,KAAK,OAAO,CAAC,CAAC;AAAA,QACvE;AACA,eAAO;AAAA,MACX;AAAA,IACJ;AAEA,aAAS,qBAAqB,KAAK,QAAQ,UAAU;AACjD,YAAM,UAAU;AAChB,cAAQ,qBAAqB,oBAAI,IAAI;AACrC,UAAI,iBAAiB,KAAK,UAAU,MAAM;AAAA,IAC9C;AAGA,QAAM,cAAc;AACpB,QAAM,eAAe;AACrB,aAAS,iBAAiB,QAAQ,SAAS;AACvC,YAAM,kBAAkB,OAAO,UAAU,QAAQ,eAAe,IAC1D,QAAQ,kBACR;AACN,UAAI,mBAAmB,YAAY,KAAK,MAAM,GAAG;AAC7C,eAAO,KAAK,OAAO,OAAO,cAAc,EAAE,OAAO,CAAC,CAAC;AAAA,MACvD;AAAA,IACJ;AACA,QAAM,oBAAoB,CAAC,WAAW;AACtC,QAAI,eAAe,uBAAO,OAAO,IAAI;AACrC,aAAS,oBAAoB;AACzB,qBAAe,uBAAO,OAAO,IAAI;AAAA,IACrC;AACA,aAAS,kBAAkB,QAAQ,UAAU,CAAC,GAAG;AAC7C;AAEI,yBAAiB,QAAQ,OAAO;AAEhC,cAAM,aAAa,QAAQ,cAAc;AACzC,cAAM,MAAM,WAAW,MAAM;AAC7B,cAAM,SAAS,aAAa,GAAG;AAC/B,YAAI,QAAQ;AACR,iBAAO;AAAA,QACX;AAEA,YAAI,WAAW;AACf,cAAM,UAAU,QAAQ,WAAW,gBAAgB;AACnD,gBAAQ,UAAU,CAAC,QAAQ;AACvB,qBAAW;AACX,kBAAQ,GAAG;AAAA,QACf;AAEA,cAAM,EAAE,MAAAF,MAAK,IAAI,gBAAgB,YAAY,QAAQ,OAAO;AAE5D,cAAM,MAAM,IAAI,SAAS,UAAUA,KAAI,EAAE,EAAE;AAE3C,eAAO,CAAC,WAAY,aAAa,GAAG,IAAI,MAAO;AAAA,MACnD;AAAA,IACJ;AAEA,QAAI,OAAO,gBAAgB,kBAAkB;AAC7C,QAAM,MAAM,MAAM,EAAE;AACpB,QAAM,iBAAiB;AAAA,MACnB,kBAAkB;AAAA,MAClB,uBAAuB,IAAI;AAAA,MAC3B,2BAA2B,IAAI;AAAA,MAC/B,kBAAkB,IAAI;AAAA;AAAA,IAC1B;AACA,aAAS,gBAAgBA,OAAM;AAC3B,aAAO,gBAAgB,mBAAmBA,OAAM,MAAM,EAAE,UAAU,cAAc,CAAE;AAAA,IACtF;AAEA,QAAM,gBAAgB;AAAA,MAClB,CAAC,eAAe,gBAAgB,GAAG;AAAA,MACnC,CAAC,eAAe,qBAAqB,GAAG;AAAA,MAExC,CAAC,eAAe,yBAAyB,GAAG;AAAA,IAChD;AAEA,QAAM,wBAAwB,MAAM;AACpC,QAAM,oBAAoB,CAAC,QAAQ,OAAO,WAAW,GAAG;AAExD,aAAS,UAAU,YAAY,MAAM;AACjC,YAAM,EAAE,gBAAgB,iBAAiB,aAAa,iBAAAE,kBAAiB,gBAAgB,SAAS,IAAI;AACpG,YAAM,CAAC,KAAK,OAAO,IAAI,mBAAmB,GAAG,IAAI;AACjD,YAAM,cAAc,OAAO,UAAU,QAAQ,WAAW,IAClD,QAAQ,cACR,QAAQ;AACd,YAAM,eAAe,OAAO,UAAU,QAAQ,YAAY,IACpD,QAAQ,eACR,QAAQ;AACd,YAAM,kBAAkB,OAAO,UAAU,QAAQ,eAAe,IAC1D,QAAQ,kBACR,QAAQ;AACd,YAAM,kBAAkB,CAAC,CAAC,QAAQ;AAElC,YAAM,kBAAkB,OAAO,SAAS,QAAQ,OAAO,KAAK,OAAO,UAAU,QAAQ,OAAO,IACtF,CAAC,OAAO,UAAU,QAAQ,OAAO,IAC7B,QAAQ,UACP,CAACA,mBAAkB,MAAM,MAAM,MACpC,iBACK,CAACA,mBAAkB,MAAM,MAAM,MAChC;AACV,YAAM,mBAAmB,kBAAkB,oBAAoB;AAC/D,YAAM,SAAS,OAAO,SAAS,QAAQ,MAAM,IAAI,QAAQ,SAAS,QAAQ;AAE1E,yBAAmB,aAAa,OAAO;AAGvC,UAAI,CAAC,aAAa,cAAc,OAAO,IAAI,CAAC,kBACtC,qBAAqB,SAAS,KAAK,QAAQ,gBAAgB,cAAc,WAAW,IACpF;AAAA,QACE;AAAA,QACA;AAAA,QACA,SAAS,MAAM,KAAK,CAAC;AAAA,MACzB;AAMJ,UAAI,SAAS;AAEb,UAAI,eAAe;AACnB,UAAI,CAAC,mBACD,EAAE,OAAO,SAAS,MAAM,KAAK,kBAAkB,MAAM,IAAI;AACzD,YAAI,kBAAkB;AAClB,mBAAS;AACT,yBAAe;AAAA,QACnB;AAAA,MACJ;AAEA,UAAI,CAAC,oBACA,EAAE,OAAO,SAAS,MAAM,KAAK,kBAAkB,MAAM,MAClD,CAAC,OAAO,SAAS,YAAY,IAAI;AACrC,eAAO,cAAc,eAAe;AAAA,MACxC;AACA,UAAI,OAAO,SAAS,MAAM,KAAK,QAAQ,mBAAmB,MAAM;AAC5D,eAAO,KAAK,yLAGyB,GAAG,IAAI;AAC5C,eAAO;AAAA,MACX;AAEA,UAAI,WAAW;AACf,YAAM,gBAAgB,MAAM;AACxB,mBAAW;AAAA,MACf;AAEA,YAAM,MAAM,CAAC,kBAAkB,MAAM,IAC/B,qBAAqB,SAAS,KAAK,cAAc,QAAQ,cAAc,aAAa,IACpF;AAEN,UAAI,UAAU;AACV,eAAO;AAAA,MACX;AAEA,YAAM,aAAa,yBAAyB,SAAS,cAAc,SAAS,OAAO;AACnF,YAAM,aAAa,qBAAqB,UAAU;AAClD,YAAM,WAAW,gBAAgB,SAAS,KAAK,UAAU;AAEzD,YAAM,MAAM,kBACN,gBAAgB,UAAU,GAAG,IAC7B;AAEN;AAEI,cAAM,WAAW;AAAA,UACb,WAAW,KAAK,IAAI;AAAA,UACpB,KAAK,OAAO,SAAS,GAAG,IAClB,MACA,kBAAkB,MAAM,IACpB,OAAO,MACP;AAAA,UACV,QAAQ,iBAAiB,kBAAkB,MAAM,IAC3C,OAAO,SACP;AAAA,UACN,QAAQ,OAAO,SAAS,MAAM,IACxB,SACA,kBAAkB,MAAM,IACpB,OAAO,SACP;AAAA,UACV,SAAS;AAAA,QACb;AACA,iBAAS,OAAO,OAAO,OAAO,CAAC,GAAG,QAAQ,QAAQ,kBAAkB,KAAK,CAAC,CAAC;AAC3E,0BAAkB,QAAQ;AAAA,MAC9B;AACA,aAAO;AAAA,IACX;AACA,aAAS,aAAa,SAAS;AAC3B,UAAI,OAAO,QAAQ,QAAQ,IAAI,GAAG;AAC9B,gBAAQ,OAAO,QAAQ,KAAK,IAAI,UAAQ,OAAO,SAAS,IAAI,IAAI,OAAO,WAAW,IAAI,IAAI,IAAI;AAAA,MAClG,WACS,OAAO,SAAS,QAAQ,KAAK,GAAG;AACrC,eAAO,KAAK,QAAQ,KAAK,EAAE,QAAQ,SAAO;AACtC,cAAI,OAAO,SAAS,QAAQ,MAAM,GAAG,CAAC,GAAG;AACrC,oBAAQ,MAAM,GAAG,IAAI,OAAO,WAAW,QAAQ,MAAM,GAAG,CAAC;AAAA,UAC7D;AAAA,QACJ,CAAC;AAAA,MACL;AAAA,IACJ;AACA,aAAS,qBAAqB,SAAS,KAAK,QAAQ,gBAAgB,cAAc,aAAa;AAC3F,YAAM,EAAE,UAAU,QAAQ,iBAAiBC,eAAc,iBAAiB,IAAI;AAC9E,YAAM,UAAU,iBAAiB,SAAS,gBAAgB,MAAM;AAChE,UAAI,UAAU,CAAC;AACf,UAAI;AACJ,UAAI,SAAS;AACb,UAAI,OAAO;AACX,UAAI,KAAK;AACT,YAAM,OAAO;AACb,eAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACrC,uBAAe,KAAK,QAAQ,CAAC;AAC7B,YAAI,WAAW,gBACX,wBAAwB,cAAc,GAAG,GAAG;AAC5C,iBAAO,eAAe,cAAc,uBAAuB;AAAA,YACvD;AAAA,YACA,QAAQ;AAAA,UACZ,CAAC,CAAC;AAAA,QACN;AAEA,YAAI,WAAW,cAAc;AACzB,gBAAM,UAAU,QAAQ;AACxB,cAAI,SAAS;AACT,oBAAQ,KAAK,YAA0B;AAAA,cACnC;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA,SAAS,GAAG,IAAI,IAAI,GAAG;AAAA,YAC3B,CAAC;AAAA,UACL;AAAA,QACJ;AACA,kBACI,SAAS,YAAY,KAAK,CAAC;AAE/B,YAAI,QAAQ;AACZ,YAAI;AACJ,YAAI;AACJ,YAAI,OAAO,WAAW;AAClB,kBAAQ,OAAO,YAAY,IAAI;AAC/B,qBAAW;AACX,mBAAS;AACT,iBAAO,QAAQ,OAAO,KAAK,QAAQ;AAAA,QACvC;AACA,aAAK,SAASA,cAAa,SAAS,GAAG,OAAO,MAAM;AAEhD,mBAAS,QAAQ,GAAG;AAAA,QACxB;AAEA,YAAI,OAAO,WAAW;AAClB,gBAAM,MAAM,OAAO,YAAY,IAAI;AACnC,gBAAM,UAAU,QAAQ;AACxB,cAAI,WAAW,SAAS,QAAQ;AAC5B,oBAAQ,KAAK,mBAAyC;AAAA,cAClD,MAAM;AAAA,cACN;AAAA,cACA,SAAS;AAAA,cACT,MAAM,MAAM;AAAA,cACZ,SAAS,GAAG,IAAI,IAAI,GAAG;AAAA,YAC3B,CAAC;AAAA,UACL;AACA,cAAI,YAAY,UAAU,OAAO,QAAQ,OAAO,SAAS;AACrD,mBAAO,KAAK,MAAM;AAClB,mBAAO,QAAQ,2BAA2B,UAAU,MAAM;AAAA,UAC9D;AAAA,QACJ;AACA,YAAI,OAAO,SAAS,MAAM,KAAK,OAAO,WAAW,MAAM;AACnD;AACJ,cAAM,aAAa;AAAA,UAAc;AAAA;AAAA,UACjC;AAAA,UAAK;AAAA,UAAc;AAAA,UAAa;AAAA,QAAI;AACpC,YAAI,eAAe,KAAK;AACpB,mBAAS;AAAA,QACb;AACA,eAAO;AAAA,MACX;AACA,aAAO,CAAC,QAAQ,cAAc,OAAO;AAAA,IACzC;AACA,aAAS,qBAAqB,SAAS,KAAK,cAAc,QAAQ,cAAc,eAAe;AAC3F,YAAM,EAAE,iBAAAD,kBAAiB,gBAAgB,IAAI;AAC7C,UAAI,kBAAkB,MAAM,GAAG;AAC3B,cAAME,OAAM;AACZ,QAAAA,KAAI,SAASA,KAAI,UAAU;AAC3B,QAAAA,KAAI,MAAMA,KAAI,OAAO;AACrB,eAAOA;AAAA,MACX;AACA,UAAIF,oBAAmB,MAAM;AACzB,cAAME,OAAO,MAAM;AACnB,QAAAA,KAAI,SAAS;AACb,QAAAA,KAAI,MAAM;AACV,eAAOA;AAAA,MACX;AAEA,UAAI,QAAQ;AACZ,UAAI;AACJ,UAAI;AACJ,UAAI,OAAO,WAAW;AAClB,gBAAQ,OAAO,YAAY,IAAI;AAC/B,mBAAW;AACX,iBAAS;AACT,eAAO,QAAQ,OAAO,KAAK,QAAQ;AAAA,MACvC;AACA,YAAM,MAAMF,iBAAgB,QAAQ,kBAAkB,SAAS,cAAc,cAAc,QAAQ,iBAAiB,aAAa,CAAC;AAElI,UAAI,OAAO,WAAW;AAClB,cAAM,MAAM,OAAO,YAAY,IAAI;AACnC,cAAM,UAAU,QAAQ;AACxB,YAAI,WAAW,OAAO;AAClB,kBAAQ,KAAK,uBAAiD;AAAA,YAC1D,MAAM;AAAA,YACN,SAAS;AAAA,YACT,MAAM,MAAM;AAAA,YACZ,SAAS,GAAG,WAAW,IAAI,GAAG;AAAA,UAClC,CAAC;AAAA,QACL;AACA,YAAI,YAAY,UAAU,OAAO,QAAQ,OAAO,SAAS;AACrD,iBAAO,KAAK,MAAM;AAClB,iBAAO,QAAQ,+BAA+B,UAAU,MAAM;AAAA,QAClE;AAAA,MACJ;AACA,UAAI,SAAS;AACb,UAAI,MAAM;AACV,UAAI,SAAS;AACb,aAAO;AAAA,IACX;AACA,aAAS,gBAAgB,SAAS,KAAK,QAAQ;AAE3C,UAAI,QAAQ;AACZ,UAAI;AACJ,UAAI;AACJ,UAAI,OAAO,WAAW;AAClB,gBAAQ,OAAO,YAAY,IAAI;AAC/B,mBAAW;AACX,iBAAS;AACT,eAAO,QAAQ,OAAO,KAAK,QAAQ;AAAA,MACvC;AACA,YAAM,WAAW,IAAI,MAAM;AAE3B,UAAI,OAAO,WAAW;AAClB,cAAM,MAAM,OAAO,YAAY,IAAI;AACnC,cAAM,UAAU,QAAQ;AACxB,YAAI,WAAW,OAAO;AAClB,kBAAQ,KAAK,sBAA+C;AAAA,YACxD,MAAM;AAAA,YACN,OAAO;AAAA,YACP,MAAM,MAAM;AAAA,YACZ,SAAS,GAAG,WAAW,IAAI,IAAI,GAAG;AAAA,UACtC,CAAC;AAAA,QACL;AACA,YAAI,YAAY,UAAU,OAAO,QAAQ,OAAO,SAAS;AACrD,iBAAO,KAAK,MAAM;AAClB,iBAAO,QAAQ,8BAA8B,UAAU,MAAM;AAAA,QACjE;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AAEA,aAAS,sBAAsB,MAAM;AACjC,YAAM,CAAC,MAAM,MAAM,IAAI,IAAI;AAC3B,YAAM,UAAU,CAAC;AACjB,UAAI,CAAC,OAAO,SAAS,IAAI,KAAK,CAAC,OAAO,SAAS,IAAI,KAAK,CAAC,kBAAkB,IAAI,GAAG;AAC9E,cAAM,gBAAgB,eAAe,gBAAgB;AAAA,MACzD;AAEA,YAAM,MAAM,OAAO,SAAS,IAAI,IAC1B,OAAO,IAAI,IACX,kBAAkB,IAAI,IAClB,OACA;AACV,UAAI,OAAO,SAAS,IAAI,GAAG;AACvB,gBAAQ,SAAS;AAAA,MACrB,WACS,OAAO,SAAS,IAAI,GAAG;AAC5B,gBAAQ,UAAU;AAAA,MACtB,WACS,OAAO,cAAc,IAAI,KAAK,CAAC,OAAO,cAAc,IAAI,GAAG;AAChE,gBAAQ,QAAQ;AAAA,MACpB,WACS,OAAO,QAAQ,IAAI,GAAG;AAC3B,gBAAQ,OAAO;AAAA,MACnB;AACA,UAAI,OAAO,SAAS,IAAI,GAAG;AACvB,gBAAQ,SAAS;AAAA,MACrB,WACS,OAAO,SAAS,IAAI,GAAG;AAC5B,gBAAQ,UAAU;AAAA,MACtB,WACS,OAAO,cAAc,IAAI,GAAG;AACjC,eAAO,OAAO,SAAS,IAAI;AAAA,MAC/B;AACA,aAAO,CAAC,KAAK,OAAO;AAAA,IACxB;AACA,aAAS,kBAAkB,SAAS,QAAQ,KAAK,QAAQ,iBAAiB,eAAe;AACrF,aAAO;AAAA,QACH;AAAA,QACA,SAAS,CAAC,QAAQ;AACd,2BAAiB,cAAc,GAAG;AAClC;AACI,kBAAM,UAAU,8BAA8B,IAAI,OAAO;AACzD,kBAAM,YAAY,IAAI,YAClB,OAAO,kBAAkB,QAAQ,IAAI,SAAS,MAAM,QAAQ,IAAI,SAAS,IAAI,MAAM;AACvF,kBAAM,UAAU,QAAQ;AACxB,gBAAI,SAAS;AACT,sBAAQ,KAAK,iBAAqC;AAAA,gBAC9C,SAAS;AAAA,gBACT,OAAO,IAAI;AAAA,gBACX,OAAO,IAAI,YAAY,IAAI,SAAS,MAAM;AAAA,gBAC1C,KAAK,IAAI,YAAY,IAAI,SAAS,IAAI;AAAA,gBACtC,SAAS,GAAG,WAAW,IAAI,GAAG;AAAA,cAClC,CAAC;AAAA,YACL;AACA,oBAAQ,MAAM,YAAY,GAAG,OAAO;AAAA,EAAK,SAAS,KAAK,OAAO;AAAA,UAClE;AAAA,QACJ;AAAA,QACA,YAAY,CAACG,YAAW,OAAO,uBAAuB,QAAQ,KAAKA,OAAM;AAAA,MAC7E;AAAA,IACJ;AACA,aAAS,yBAAyB,SAAS,QAAQ,SAAS,SAAS;AACjE,YAAM,EAAE,WAAW,aAAa,iBAAiBF,eAAc,gBAAgB,cAAc,aAAa,gBAAgB,IAAI;AAC9H,YAAM,iBAAiB,CAAC,QAAQ;AAC5B,YAAI,MAAMA,cAAa,SAAS,GAAG;AAEnC,YAAI,OAAO,QAAQ,iBAAiB;AAChC,gBAAM,CAAC,EAAE,EAAEG,QAAO,IAAI,qBAAqB,iBAAiB,KAAK,QAAQ,gBAAgB,cAAc,WAAW;AAClH,gBAAMH,cAAaG,UAAS,GAAG;AAAA,QACnC;AACA,YAAI,OAAO,SAAS,GAAG,GAAG;AACtB,cAAI,WAAW;AACf,gBAAM,gBAAgB,MAAM;AACxB,uBAAW;AAAA,UACf;AACA,gBAAM,MAAM,qBAAqB,SAAS,KAAK,QAAQ,KAAK,KAAK,aAAa;AAC9E,iBAAO,CAAC,WACF,MACA;AAAA,QACV,WACS,kBAAkB,GAAG,GAAG;AAC7B,iBAAO;AAAA,QACX,OACK;AAED,iBAAO;AAAA,QACX;AAAA,MACJ;AACA,YAAM,aAAa;AAAA,QACf;AAAA,QACA;AAAA,QACA;AAAA,QACA,UAAU;AAAA,MACd;AACA,UAAI,QAAQ,WAAW;AACnB,mBAAW,YAAY,QAAQ;AAAA,MACnC;AACA,UAAI,QAAQ,MAAM;AACd,mBAAW,OAAO,QAAQ;AAAA,MAC9B;AACA,UAAI,QAAQ,OAAO;AACf,mBAAW,QAAQ,QAAQ;AAAA,MAC/B;AACA,UAAI,OAAO,SAAS,QAAQ,MAAM,GAAG;AACjC,mBAAW,cAAc,QAAQ;AAAA,MACrC;AACA,aAAO;AAAA,IACX;AAEA,QAAM,cAAc,OAAO,SAAS;AACpC,QAAM,iBAAiB;AAAA,MACnB,gBAAgB,eAAe,OAAO,KAAK,mBAAmB;AAAA,MAC9D,cAAc,eAAe,OAAO,KAAK,iBAAiB;AAAA,IAC9D;AAGA,aAAS,SAAS,YAAY,MAAM;AAChC,YAAM,EAAE,iBAAiB,aAAa,gBAAgB,QAAQ,iBAAiB,IAAI;AACnF,YAAM,EAAE,qBAAqB,IAAI;AACjC,UAAI,CAAC,eAAe,gBAAgB;AAChC,eAAO,eAAe,cAAc,kBAAkB,CAAC;AACvD,eAAO;AAAA,MACX;AACA,YAAM,CAAC,KAAK,OAAO,SAAS,SAAS,IAAI,kBAAkB,GAAG,IAAI;AAClE,YAAM,cAAc,OAAO,UAAU,QAAQ,WAAW,IAClD,QAAQ,cACR,QAAQ;AACd,YAAM,eAAe,OAAO,UAAU,QAAQ,YAAY,IACpD,QAAQ,eACR,QAAQ;AACd,YAAM,OAAO,CAAC,CAAC,QAAQ;AACvB,YAAM,SAAS,OAAO,SAAS,QAAQ,MAAM,IAAI,QAAQ,SAAS,QAAQ;AAC1E,YAAM,UAAU;AAAA,QAAiB;AAAA;AAAA,QACjC;AAAA,QAAgB;AAAA,MAAM;AACtB,UAAI,CAAC,OAAO,SAAS,GAAG,KAAK,QAAQ,IAAI;AACrC,eAAO,IAAI,KAAK,eAAe,QAAQ,SAAS,EAAE,OAAO,KAAK;AAAA,MAClE;AAEA,UAAI,iBAAiB,CAAC;AACtB,UAAI;AACJ,UAAI,SAAS;AACb,UAAI,OAAO;AACX,UAAI,KAAK;AACT,YAAM,OAAO;AACb,eAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACrC,uBAAe,KAAK,QAAQ,CAAC;AAC7B,YAAI,WAAW,gBACX,wBAAwB,cAAc,GAAG,GAAG;AAC5C,iBAAO,eAAe,cAAc,yBAAyB;AAAA,YACzD;AAAA,YACA,QAAQ;AAAA,UACZ,CAAC,CAAC;AAAA,QACN;AAEA,YAAI,WAAW,cAAc;AACzB,gBAAM,UAAU,QAAQ;AACxB,cAAI,SAAS;AACT,oBAAQ,KAAK,YAA0B;AAAA,cACnC;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA,SAAS,GAAG,IAAI,IAAI,GAAG;AAAA,YAC3B,CAAC;AAAA,UACL;AAAA,QACJ;AACA,yBACI,gBAAgB,YAAY,KAAK,CAAC;AACtC,iBAAS,eAAe,GAAG;AAC3B,YAAI,OAAO,cAAc,MAAM;AAC3B;AACJ,sBAAc,SAAS,KAAK,cAAc,aAAa,IAAI;AAC3D,eAAO;AAAA,MACX;AAEA,UAAI,CAAC,OAAO,cAAc,MAAM,KAAK,CAAC,OAAO,SAAS,YAAY,GAAG;AACjE,eAAO,cAAc,eAAe;AAAA,MACxC;AACA,UAAI,KAAK,GAAG,YAAY,KAAK,GAAG;AAChC,UAAI,CAAC,OAAO,cAAc,SAAS,GAAG;AAClC,aAAK,GAAG,EAAE,KAAK,KAAK,UAAU,SAAS,CAAC;AAAA,MAC5C;AACA,UAAI,YAAY,qBAAqB,IAAI,EAAE;AAC3C,UAAI,CAAC,WAAW;AACZ,oBAAY,IAAI,KAAK,eAAe,cAAc,OAAO,OAAO,CAAC,GAAG,QAAQ,SAAS,CAAC;AACtF,6BAAqB,IAAI,IAAI,SAAS;AAAA,MAC1C;AACA,aAAO,CAAC,OAAO,UAAU,OAAO,KAAK,IAAI,UAAU,cAAc,KAAK;AAAA,IAC1E;AAEA,QAAM,+BAA+B;AAAA,MACjC;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAEA,aAAS,qBAAqB,MAAM;AAChC,YAAM,CAAC,MAAM,MAAM,MAAM,IAAI,IAAI;AACjC,YAAM,UAAU,CAAC;AACjB,UAAI,YAAY,CAAC;AACjB,UAAI;AACJ,UAAI,OAAO,SAAS,IAAI,GAAG;AAGvB,cAAM,UAAU,KAAK,MAAM,gCAAgC;AAC3D,YAAI,CAAC,SAAS;AACV,gBAAM,gBAAgB,eAAe,yBAAyB;AAAA,QAClE;AAGA,cAAM,WAAW,QAAQ,CAAC,IACpB,QAAQ,CAAC,EAAE,KAAK,EAAE,WAAW,GAAG,IAC5B,GAAG,QAAQ,CAAC,EAAE,KAAK,CAAC,GAAG,QAAQ,CAAC,EAAE,KAAK,CAAC,KACxC,GAAG,QAAQ,CAAC,EAAE,KAAK,CAAC,IAAI,QAAQ,CAAC,EAAE,KAAK,CAAC,KAC7C,QAAQ,CAAC,EAAE,KAAK;AACtB,gBAAQ,IAAI,KAAK,QAAQ;AACzB,YAAI;AAEA,gBAAM,YAAY;AAAA,QACtB,SACO,GAAG;AACN,gBAAM,gBAAgB,eAAe,yBAAyB;AAAA,QAClE;AAAA,MACJ,WACS,OAAO,OAAO,IAAI,GAAG;AAC1B,YAAI,MAAM,KAAK,QAAQ,CAAC,GAAG;AACvB,gBAAM,gBAAgB,eAAe,qBAAqB;AAAA,QAC9D;AACA,gBAAQ;AAAA,MACZ,WACS,OAAO,SAAS,IAAI,GAAG;AAC5B,gBAAQ;AAAA,MACZ,OACK;AACD,cAAM,gBAAgB,eAAe,gBAAgB;AAAA,MACzD;AACA,UAAI,OAAO,SAAS,IAAI,GAAG;AACvB,gBAAQ,MAAM;AAAA,MAClB,WACS,OAAO,cAAc,IAAI,GAAG;AACjC,eAAO,KAAK,IAAI,EAAE,QAAQ,SAAO;AAC7B,cAAI,6BAA6B,SAAS,GAAG,GAAG;AAC5C,sBAAU,GAAG,IAAI,KAAK,GAAG;AAAA,UAC7B,OACK;AACD,oBAAQ,GAAG,IAAI,KAAK,GAAG;AAAA,UAC3B;AAAA,QACJ,CAAC;AAAA,MACL;AACA,UAAI,OAAO,SAAS,IAAI,GAAG;AACvB,gBAAQ,SAAS;AAAA,MACrB,WACS,OAAO,cAAc,IAAI,GAAG;AACjC,oBAAY;AAAA,MAChB;AACA,UAAI,OAAO,cAAc,IAAI,GAAG;AAC5B,oBAAY;AAAA,MAChB;AACA,aAAO,CAAC,QAAQ,OAAO,IAAI,OAAO,SAAS,SAAS;AAAA,IACxD;AAEA,aAAS,oBAAoB,KAAK,QAAQ,QAAQ;AAC9C,YAAM,UAAU;AAChB,iBAAW,OAAO,QAAQ;AACtB,cAAM,KAAK,GAAG,MAAM,KAAK,GAAG;AAC5B,YAAI,CAAC,QAAQ,qBAAqB,IAAI,EAAE,GAAG;AACvC;AAAA,QACJ;AACA,gBAAQ,qBAAqB,OAAO,EAAE;AAAA,MAC1C;AAAA,IACJ;AAGA,aAAS,OAAO,YAAY,MAAM;AAC9B,YAAM,EAAE,eAAe,aAAa,gBAAgB,QAAQ,iBAAiB,IAAI;AACjF,YAAM,EAAE,mBAAmB,IAAI;AAC/B,UAAI,CAAC,eAAe,cAAc;AAC9B,eAAO,eAAe,cAAc,oBAAoB,CAAC;AACzD,eAAO;AAAA,MACX;AACA,YAAM,CAAC,KAAK,OAAO,SAAS,SAAS,IAAI,gBAAgB,GAAG,IAAI;AAChE,YAAM,cAAc,OAAO,UAAU,QAAQ,WAAW,IAClD,QAAQ,cACR,QAAQ;AACd,YAAM,eAAe,OAAO,UAAU,QAAQ,YAAY,IACpD,QAAQ,eACR,QAAQ;AACd,YAAM,OAAO,CAAC,CAAC,QAAQ;AACvB,YAAM,SAAS,OAAO,SAAS,QAAQ,MAAM,IAAI,QAAQ,SAAS,QAAQ;AAC1E,YAAM,UAAU;AAAA,QAAiB;AAAA;AAAA,QACjC;AAAA,QAAgB;AAAA,MAAM;AACtB,UAAI,CAAC,OAAO,SAAS,GAAG,KAAK,QAAQ,IAAI;AACrC,eAAO,IAAI,KAAK,aAAa,QAAQ,SAAS,EAAE,OAAO,KAAK;AAAA,MAChE;AAEA,UAAI,eAAe,CAAC;AACpB,UAAI;AACJ,UAAI,SAAS;AACb,UAAI,OAAO;AACX,UAAI,KAAK;AACT,YAAM,OAAO;AACb,eAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACrC,uBAAe,KAAK,QAAQ,CAAC;AAC7B,YAAI,WAAW,gBACX,wBAAwB,cAAc,GAAG,GAAG;AAC5C,iBAAO,eAAe,cAAc,2BAA2B;AAAA,YAC3D;AAAA,YACA,QAAQ;AAAA,UACZ,CAAC,CAAC;AAAA,QACN;AAEA,YAAI,WAAW,cAAc;AACzB,gBAAM,UAAU,QAAQ;AACxB,cAAI,SAAS;AACT,oBAAQ,KAAK,YAA0B;AAAA,cACnC;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA,SAAS,GAAG,IAAI,IAAI,GAAG;AAAA,YAC3B,CAAC;AAAA,UACL;AAAA,QACJ;AACA,uBACI,cAAc,YAAY,KAAK,CAAC;AACpC,iBAAS,aAAa,GAAG;AACzB,YAAI,OAAO,cAAc,MAAM;AAC3B;AACJ,sBAAc,SAAS,KAAK,cAAc,aAAa,IAAI;AAC3D,eAAO;AAAA,MACX;AAEA,UAAI,CAAC,OAAO,cAAc,MAAM,KAAK,CAAC,OAAO,SAAS,YAAY,GAAG;AACjE,eAAO,cAAc,eAAe;AAAA,MACxC;AACA,UAAI,KAAK,GAAG,YAAY,KAAK,GAAG;AAChC,UAAI,CAAC,OAAO,cAAc,SAAS,GAAG;AAClC,aAAK,GAAG,EAAE,KAAK,KAAK,UAAU,SAAS,CAAC;AAAA,MAC5C;AACA,UAAI,YAAY,mBAAmB,IAAI,EAAE;AACzC,UAAI,CAAC,WAAW;AACZ,oBAAY,IAAI,KAAK,aAAa,cAAc,OAAO,OAAO,CAAC,GAAG,QAAQ,SAAS,CAAC;AACpF,2BAAmB,IAAI,IAAI,SAAS;AAAA,MACxC;AACA,aAAO,CAAC,OAAO,UAAU,OAAO,KAAK,IAAI,UAAU,cAAc,KAAK;AAAA,IAC1E;AAEA,QAAM,6BAA6B;AAAA,MAC/B;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAEA,aAAS,mBAAmB,MAAM;AAC9B,YAAM,CAAC,MAAM,MAAM,MAAM,IAAI,IAAI;AACjC,YAAM,UAAU,CAAC;AACjB,UAAI,YAAY,CAAC;AACjB,UAAI,CAAC,OAAO,SAAS,IAAI,GAAG;AACxB,cAAM,gBAAgB,eAAe,gBAAgB;AAAA,MACzD;AACA,YAAM,QAAQ;AACd,UAAI,OAAO,SAAS,IAAI,GAAG;AACvB,gBAAQ,MAAM;AAAA,MAClB,WACS,OAAO,cAAc,IAAI,GAAG;AACjC,eAAO,KAAK,IAAI,EAAE,QAAQ,SAAO;AAC7B,cAAI,2BAA2B,SAAS,GAAG,GAAG;AAC1C,sBAAU,GAAG,IAAI,KAAK,GAAG;AAAA,UAC7B,OACK;AACD,oBAAQ,GAAG,IAAI,KAAK,GAAG;AAAA,UAC3B;AAAA,QACJ,CAAC;AAAA,MACL;AACA,UAAI,OAAO,SAAS,IAAI,GAAG;AACvB,gBAAQ,SAAS;AAAA,MACrB,WACS,OAAO,cAAc,IAAI,GAAG;AACjC,oBAAY;AAAA,MAChB;AACA,UAAI,OAAO,cAAc,IAAI,GAAG;AAC5B,oBAAY;AAAA,MAChB;AACA,aAAO,CAAC,QAAQ,OAAO,IAAI,OAAO,SAAS,SAAS;AAAA,IACxD;AAEA,aAAS,kBAAkB,KAAK,QAAQ,QAAQ;AAC5C,YAAM,UAAU;AAChB,iBAAW,OAAO,QAAQ;AACtB,cAAM,KAAK,GAAG,MAAM,KAAK,GAAG;AAC5B,YAAI,CAAC,QAAQ,mBAAmB,IAAI,EAAE,GAAG;AACrC;AAAA,QACJ;AACA,gBAAQ,mBAAmB,OAAO,EAAE;AAAA,MACxC;AAAA,IACJ;AAEA,YAAQ,oBAAoB,gBAAgB;AAC5C,YAAQ,qBAAqB,gBAAgB;AAC7C,YAAQ,iBAAiB;AACzB,YAAQ,gBAAgB;AACxB,YAAQ,+BAA+B;AACvC,YAAQ,iBAAiB;AACzB,YAAQ,4BAA4B;AACpC,YAAQ,wBAAwB;AAChC,YAAQ,eAAe;AACvB,YAAQ,6BAA6B;AACrC,YAAQ,UAAU;AAClB,YAAQ,oBAAoB;AAC5B,YAAQ,sBAAsB;AAC9B,YAAQ,oBAAoB;AAC5B,YAAQ,oBAAoB;AAC5B,YAAQ,oBAAoB;AAC5B,YAAQ,kBAAkB;AAC1B,YAAQ,uBAAuB;AAC/B,YAAQ,WAAW;AACnB,YAAQ,0BAA0B;AAClC,YAAQ,qBAAqB;AAC7B,YAAQ,oBAAoB;AAC5B,YAAQ,kBAAkB;AAC1B,YAAQ,qBAAqB;AAC7B,YAAQ,iBAAiB;AACzB,YAAQ,gBAAgB;AACxB,YAAQ,mBAAmB;AAC3B,YAAQ,oBAAoB;AAC5B,YAAQ,0BAA0B;AAClC,YAAQ,yBAAyB;AACjC,YAAQ,SAAS;AACjB,YAAQ,QAAQ;AAChB,YAAQ,oBAAoB;AAC5B,YAAQ,kBAAkB;AAC1B,YAAQ,qBAAqB;AAC7B,YAAQ,2BAA2B;AACnC,YAAQ,0BAA0B;AAClC,YAAQ,0BAA0B;AAClC,YAAQ,eAAe;AACvB,YAAQ,sBAAsB;AAC9B,YAAQ,oBAAoB;AAC5B,YAAQ,kBAAkB;AAC1B,YAAQ,qBAAqB;AAC7B,YAAQ,YAAY;AACpB,YAAQ,oBAAoB;AAC5B,YAAQ,uBAAuB;AAAA;AAAA;;;ACvkD/B;AAAA;AAAA;AAEA,QAAI,OAAuC;AACzC,aAAO,UAAU;AAAA,IACnB,OAAO;AACL,aAAO,UAAU;AAAA,IACnB;AAAA;AAAA;;;ACNA;AAAA;AAOA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAE5D,QAAI,SAAS;AACb,QAAI,WAAW;AACf,QAAI,MAAM;AAUV,QAAM,UAAU;AAEhB,QAAI,SAAS,SAAS,cAAc;AACpC,QAAM,QAAQ,MAAM,EAAE;AACtB,QAAM,gBAAgB;AAAA,MAClB,kBAAkB;AAAA,MAClB,wBAAwB,MAAM;AAAA,MAC9B,yBAAyB,MAAM;AAAA,MAC/B,kCAAkC,MAAM;AAAA,MACxC,gCAAgC,MAAM;AAAA,MACtC,kCAAkC,MAAM;AAAA,MACxC,wBAAwB,MAAM;AAAA;AAAA,IAClC;AACA,QAAM,eAAe;AAAA,MACjB,CAAC,cAAc,gBAAgB,GAAG;AAAA,MAClC,CAAC,cAAc,sBAAsB,GAAG;AAAA,MACxC,CAAC,cAAc,uBAAuB,GAAG;AAAA,MACzC,CAAC,cAAc,gCAAgC,GAAG;AAAA,MAClD,CAAC,cAAc,8BAA8B,GAAG;AAAA,MAChD,CAAC,cAAc,gCAAgC,GAAG;AAAA,MAClD,CAAC,cAAc,sBAAsB,GAAG;AAAA,IAC5C;AACA,aAAS,eAAeC,UAAS,MAAM;AACnC,aAAO,OAAO,OAAO,aAAaA,KAAI,GAAG,GAAG,IAAI;AAAA,IACpD;AAEA,QAAI,OAAO,SAAS,kBAAkB;AACtC,QAAM,MAAM,MAAM,EAAE;AACpB,QAAM,iBAAiB;AAAA;AAAA,MAEnB,wBAAwB;AAAA;AAAA,MAExB,kBAAkB,IAAI;AAAA;AAAA,MAEtB,wBAAwB,IAAI;AAAA,MAC5B,eAAe,IAAI;AAAA,MACnB,8BAA8B,IAAI;AAAA;AAAA,MAElC,gBAAgB,IAAI;AAAA,MACpB,eAAe,IAAI;AAAA;AAAA,MAEnB,kCAAkC,IAAI;AAAA,MACtC,4BAA4B,IAAI;AAAA;AAAA,MAEhC,kBAAkB,IAAI;AAAA;AAAA,MAEtB,gCAAgC,IAAI;AAAA;AAAA,MAEpC,2BAA2B,IAAI;AAAA;AAAA,MAE/B,8CAA8C,IAAI;AAAA;AAAA,MAElD,qCAAqC,IAAI;AAAA;AAAA,MAEzC,kBAAkB,IAAI;AAAA;AAAA,IAC1B;AACA,aAAS,gBAAgBA,UAAS,MAAM;AACpC,aAAO,SAAS,mBAAmBA,OAAM,MAAM,EAAE,UAAU,eAAe,KAAK,CAAE;AAAA,IACrF;AACA,QAAM,gBAAgB;AAAA,MAClB,CAAC,eAAe,sBAAsB,GAAG;AAAA,MACzC,CAAC,eAAe,gBAAgB,GAAG;AAAA,MACnC,CAAC,eAAe,sBAAsB,GAAG;AAAA,MACzC,CAAC,eAAe,aAAa,GAAG;AAAA,MAChC,CAAC,eAAe,gBAAgB,GAAG;AAAA,MACnC,CAAC,eAAe,4BAA4B,GAAG;AAAA,MAC/C,CAAC,eAAe,cAAc,GAAG;AAAA,MACjC,CAAC,eAAe,aAAa,GAAG;AAAA,MAChC,CAAC,eAAe,gCAAgC,GAAG;AAAA,MACnD,CAAC,eAAe,0BAA0B,GAAG;AAAA,MAC7C,CAAC,eAAe,8BAA8B,GAAG;AAAA,MACjD,CAAC,eAAe,yBAAyB,GAAG;AAAA,MAC5C,CAAC,eAAe,4CAA4C,GAAG;AAAA,MAC/D,CAAC,eAAe,mCAAmC,GAAG;AAAA,IAC1D;AAEA,QAAM,uBACS,OAAO,WAAW,kBAAkB;AACnD,QAAM,sBAAqC,OAAO,WAAW,iBAAiB;AAC9E,QAAM,oBAAmC,OAAO,WAAW,eAAe;AAC1E,QAAM,gBAA+B,OAAO,WAAW,iBAAiB;AACxE,QAAM,iBAAgC,OAAO,WAAW,kBAAkB;AAC1E,QAAM,uBAAuB,OAAO,WAAW,kBAAkB;AACjE,WAAO,WAAW,eAAe;AACjC,QAAM,mBAAkC,OAAO,WAAW,oBAAoB;AAC9E,QAAM,sBAAuB;AAM7B,aAAS,eAAe,KAAK;AAEzB,UAAI,CAAC,OAAO,SAAS,GAAG,GAAG;AACvB,eAAO;AAAA,MACX;AACA,iBAAW,OAAO,KAAK;AAEnB,YAAI,CAAC,OAAO,OAAO,KAAK,GAAG,GAAG;AAC1B;AAAA,QACJ;AAEA,YAAI,CAAC,IAAI,SAAS,GAAG,GAAG;AAEpB,cAAI,OAAO,SAAS,IAAI,GAAG,CAAC,GAAG;AAC3B,2BAAe,IAAI,GAAG,CAAC;AAAA,UAC3B;AAAA,QACJ,OAEK;AAED,gBAAM,UAAU,IAAI,MAAM,GAAG;AAC7B,gBAAM,YAAY,QAAQ,SAAS;AACnC,cAAI,aAAa;AACjB,mBAAS,IAAI,GAAG,IAAI,WAAW,KAAK;AAChC,gBAAI,EAAE,QAAQ,CAAC,KAAK,aAAa;AAC7B,yBAAW,QAAQ,CAAC,CAAC,IAAI,CAAC;AAAA,YAC9B;AACA,yBAAa,WAAW,QAAQ,CAAC,CAAC;AAAA,UACtC;AAEA,qBAAW,QAAQ,SAAS,CAAC,IAAI,IAAI,GAAG;AACxC,iBAAO,IAAI,GAAG;AAEd,cAAI,OAAO,SAAS,WAAW,QAAQ,SAAS,CAAC,CAAC,GAAG;AACjD,2BAAe,WAAW,QAAQ,SAAS,CAAC,CAAC;AAAA,UACjD;AAAA,QACJ;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AACA,aAAS,kBAAkB,QAAQ,SAAS;AACxC,YAAM,EAAE,UAAU,QAAQ,iBAAiB,SAAS,IAAI;AAExD,YAAM,MAAM,OAAO,cAAc,QAAQ,IACnC,WACA,OAAO,QAAQ,MAAM,IACjB,CAAC,IACD,EAAE,CAAC,MAAM,GAAG,CAAC,EAAE;AAEzB,UAAI,OAAO,QAAQ,MAAM,GAAG;AACxB,eAAO,QAAQ,YAAU;AACrB,cAAI,YAAY,UAAU,cAAc,QAAQ;AAC5C,kBAAM,EAAE,QAAAC,SAAQ,SAAS,IAAI;AAC7B,gBAAIA,SAAQ;AACR,kBAAIA,OAAM,IAAI,IAAIA,OAAM,KAAK,CAAC;AAC9B,uBAAS,UAAU,IAAIA,OAAM,CAAC;AAAA,YAClC,OACK;AACD,uBAAS,UAAU,GAAG;AAAA,YAC1B;AAAA,UACJ,OACK;AACD,mBAAO,SAAS,MAAM,KAAK,SAAS,KAAK,MAAM,MAAM,GAAG,GAAG;AAAA,UAC/D;AAAA,QACJ,CAAC;AAAA,MACL;AAEA,UAAI,mBAAmB,QAAQ,UAAU;AACrC,mBAAW,OAAO,KAAK;AACnB,cAAI,OAAO,OAAO,KAAK,GAAG,GAAG;AACzB,2BAAe,IAAI,GAAG,CAAC;AAAA,UAC3B;AAAA,QACJ;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AACA,QAAM,uBAAuB,CAAC,QAAQ,CAAC,OAAO,SAAS,GAAG,KAAK,OAAO,QAAQ,GAAG;AAEjF,aAAS,SAAS,KAAK,KAAK;AAExB,UAAI,qBAAqB,GAAG,KAAK,qBAAqB,GAAG,GAAG;AACxD,cAAM,gBAAgB,eAAe,aAAa;AAAA,MACtD;AACA,iBAAW,OAAO,KAAK;AACnB,YAAI,OAAO,OAAO,KAAK,GAAG,GAAG;AACzB,cAAI,qBAAqB,IAAI,GAAG,CAAC,KAAK,qBAAqB,IAAI,GAAG,CAAC,GAAG;AAIlE,gBAAI,GAAG,IAAI,IAAI,GAAG;AAAA,UACtB,OACK;AAED,qBAAS,IAAI,GAAG,GAAG,IAAI,GAAG,CAAC;AAAA,UAC/B;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AAEA,aAAS,oBAAoB,UAAU;AACnC,aAAO,SAAS;AAAA,IACpB;AACA,aAAS,oBAAoBC,SAAQ,SAAS,kBAC5C;AACE,UAAI,WAAW,OAAO,SAAS,QAAQ,QAAQ,IAAI,QAAQ,WAAW,CAAC;AACvE,UAAI,kBAAkB,kBAAkB;AACpC,mBAAW,kBAAkBA,QAAO,OAAO,OAAO;AAAA,UAC9C;AAAA,UACA,QAAQ,iBAAiB;AAAA,QAC7B,CAAC;AAAA,MACL;AAEA,YAAM,UAAU,OAAO,KAAK,QAAQ;AACpC,UAAI,QAAQ,QAAQ;AAChB,gBAAQ,QAAQ,YAAU;AACtB,UAAAA,QAAO,mBAAmB,QAAQ,SAAS,MAAM,CAAC;AAAA,QACtD,CAAC;AAAA,MACL;AACA;AAEI,YAAI,OAAO,SAAS,QAAQ,eAAe,GAAG;AAC1C,gBAAMC,WAAU,OAAO,KAAK,QAAQ,eAAe;AACnD,cAAIA,SAAQ,QAAQ;AAChB,YAAAA,SAAQ,QAAQ,YAAU;AACtB,cAAAD,QAAO,oBAAoB,QAAQ,QAAQ,gBAAgB,MAAM,CAAC;AAAA,YACtE,CAAC;AAAA,UACL;AAAA,QACJ;AAEA,YAAI,OAAO,SAAS,QAAQ,aAAa,GAAG;AACxC,gBAAMC,WAAU,OAAO,KAAK,QAAQ,aAAa;AACjD,cAAIA,SAAQ,QAAQ;AAChB,YAAAA,SAAQ,QAAQ,YAAU;AACtB,cAAAD,QAAO,kBAAkB,QAAQ,QAAQ,cAAc,MAAM,CAAC;AAAA,YAClE,CAAC;AAAA,UACL;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AACA,aAAS,eAAe,KAAK;AACzB,aAAO,IAAI,YAAY,IAAI,MAAM,MAAM,KAAK,CAAC;AAAA,IAEjD;AAKA,QAAM,gBAAgB;AACtB,QAAI,aAAa;AACjB,aAAS,yBAAyB,SAAS;AACvC,aAAQ,CAAC,KAAK,QAAQ,KAAK,SAAS;AAChC,eAAO,QAAQ,QAAQ,KAAK,IAAI,mBAAmB,KAAK,QAAW,IAAI;AAAA,MAC3E;AAAA,IACJ;AAEA,QAAM,cAAe,MAAM;AACvB,YAAM,WAAW,IAAI,mBAAmB;AACxC,UAAI,OAAO;AACX,aAAO,aAAa,OAAO,oBAAoB,QAAQ,EAAE,aAAa,KAChE,EAAE,CAAC,aAAa,GAAG,KAAK,IACxB;AAAA,IACV;AAOA,aAAS,eAAe,UAAU,CAAC,GAAG,eAAe;AACjD,YAAM,EAAE,OAAO,IAAI;AACnB,YAAM,YAAY,WAAW;AAC7B,UAAI,iBAAiB,OAAO,UAAU,QAAQ,aAAa,IACrD,QAAQ,gBACR;AACN,YAAM,UAAU,IAAI;AAAA;AAAA,QAEpB,UAAU,iBACJ,OAAO,OAAO,QACd,OAAO,SAAS,QAAQ,MAAM,IAC1B,QAAQ,SACR,SAAS;AAAA,MAAc;AACjC,YAAM,kBAAkB,IAAI;AAAA;AAAA,QAE5B,UAAU,iBACJ,OAAO,eAAe,QACtB,OAAO,SAAS,QAAQ,cAAc,KACpC,OAAO,QAAQ,QAAQ,cAAc,KACrC,OAAO,cAAc,QAAQ,cAAc,KAC3C,QAAQ,mBAAmB,QACzB,QAAQ,iBACR,QAAQ;AAAA,MAAK;AACvB,YAAM,YAAY,IAAI,IAAI,kBAAkB,QAAQ,OAAO,OAAO,CAAC;AAEnE,YAAM,mBAAmB,IAAI,IAAI,OAAO,cAAc,QAAQ,eAAe,IACnE,QAAQ,kBACR,EAAE,CAAC,QAAQ,KAAK,GAAG,CAAC,EAAE,CAAC;AAGjC,YAAM,iBAAiB,IAAI,IAAI,OAAO,cAAc,QAAQ,aAAa,IAC/D,QAAQ,gBACR,EAAE,CAAC,QAAQ,KAAK,GAAG,CAAC,EAAE,CAAC;AAIjC,UAAI,eAAe,SACb,OAAO,cACP,OAAO,UAAU,QAAQ,WAAW,KAAK,OAAO,SAAS,QAAQ,WAAW,IACxE,QAAQ,cACR;AAEV,UAAI,gBAAgB,SACd,OAAO,eACP,OAAO,UAAU,QAAQ,YAAY,KAAK,OAAO,SAAS,QAAQ,YAAY,IAC1E,QAAQ,eACR;AAEV,UAAI,gBAAgB,SACd,OAAO,eACP,OAAO,UAAU,QAAQ,YAAY,IACjC,QAAQ,eACR;AAEV,UAAI,kBAAkB,CAAC,CAAC,QAAQ;AAEhC,UAAI,WAAW,OAAO,WAAW,QAAQ,OAAO,IAAI,QAAQ,UAAU;AACtE,UAAI,kBAAkB,OAAO,WAAW,QAAQ,OAAO,IACjD,yBAAyB,QAAQ,OAAO,IACxC;AAEN,UAAI,mBAAmB,OAAO,WAAW,QAAQ,eAAe,IAC1D,QAAQ,kBACR;AAEN,UAAI,mBAAmB,SACjB,OAAO,kBACP,OAAO,UAAU,QAAQ,eAAe,IACpC,QAAQ,kBACR;AACV,UAAI,mBAAmB,CAAC,CAAC,QAAQ;AAGjC,YAAM,aAAa,SACb,OAAO,YACP,OAAO,cAAc,QAAQ,SAAS,IAClC,QAAQ,YACR,CAAC;AAEX,UAAI,eAAe,QAAQ,eAAgB,UAAU,OAAO;AAG5D,UAAI;AACJ,YAAM,iBAAiB,MAAM;AACzB,qBAAa,SAAS,mBAAmB,IAAI;AAC7C,cAAM,aAAa;AAAA,UACf,SAAS;AAAA,UACT,QAAQ,QAAQ;AAAA,UAChB,gBAAgB,gBAAgB;AAAA,UAChC,UAAU,UAAU;AAAA,UACpB,WAAW;AAAA,UACX,aAAa;AAAA,UACb,SAAS,oBAAoB,OAAO,SAAY;AAAA,UAChD,aAAa;AAAA,UACb,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,aAAa;AAAA,UACb,iBAAiB,qBAAqB,OAAO,SAAY;AAAA,UACzD,iBAAiB;AAAA,UACjB,iBAAiB;AAAA,UACjB,iBAAiB,QAAQ;AAAA,UACzB,QAAQ,EAAE,WAAW,MAAM;AAAA,QAC/B;AACA;AACI,qBAAW,kBAAkB,iBAAiB;AAC9C,qBAAW,gBAAgB,eAAe;AAC1C,qBAAW,uBAAuB,OAAO,cAAc,QAAQ,IACzD,SAAS,uBACT;AACN,qBAAW,qBAAqB,OAAO,cAAc,QAAQ,IACvD,SAAS,qBACT;AAAA,QACV;AACA;AACI,qBAAW,cAAc,OAAO,cAAc,QAAQ,IAChD,SAAS,cACT;AAAA,QACV;AACA,cAAM,MAAM,SAAS,kBAAkB,UAAU;AACjD,qBAAa,SAAS,mBAAmB,GAAG;AAC5C,eAAO;AAAA,MACX;AACA,iBAAW,eAAe;AAC1B,eAAS,qBAAqB,UAAU,QAAQ,OAAO,gBAAgB,KAAK;AAE5E,eAAS,wBAAwB;AAC7B,eAAO;AAAA,UACC,QAAQ;AAAA,UACR,gBAAgB;AAAA,UAChB,UAAU;AAAA,UACV,iBAAiB;AAAA,UACjB,eAAe;AAAA,QACnB;AAAA,MAER;AAEA,YAAM,SAAS,IAAI,SAAS;AAAA,QACxB,KAAK,MAAM,QAAQ;AAAA,QACnB,KAAK,SAAO;AACR,kBAAQ,QAAQ;AAChB,mBAAS,SAAS,QAAQ;AAAA,QAC9B;AAAA,MACJ,CAAC;AAED,YAAM,iBAAiB,IAAI,SAAS;AAAA,QAChC,KAAK,MAAM,gBAAgB;AAAA,QAC3B,KAAK,SAAO;AACR,0BAAgB,QAAQ;AACxB,mBAAS,iBAAiB,gBAAgB;AAC1C,mBAAS,qBAAqB,UAAU,QAAQ,OAAO,GAAG;AAAA,QAC9D;AAAA,MACJ,CAAC;AAED,YAAM,WAAW,IAAI,SAAS,MAAM,UAAU,KAAK;AAEnD,YAAM,kBAAiC,IAAI,SAAS,MAAM,iBAAiB,KAAK;AAEhF,YAAM,gBAA+B,IAAI,SAAS,MAAM,eAAe,KAAK;AAE5E,eAAS,4BAA4B;AACjC,eAAO,OAAO,WAAW,gBAAgB,IAAI,mBAAmB;AAAA,MACpE;AAEA,eAAS,0BAA0B,SAAS;AACxC,2BAAmB;AACnB,iBAAS,kBAAkB;AAAA,MAC/B;AAEA,eAAS,oBAAoB;AACzB,eAAO;AAAA,MACX;AAEA,eAAS,kBAAkB,SAAS;AAChC,YAAI,YAAY,MAAM;AAClB,4BAAkB,yBAAyB,OAAO;AAAA,QACtD;AACA,mBAAW;AACX,iBAAS,UAAU;AAAA,MACvB;AACA,eAAS,2BAA2B,MAAM,KACxC;AACE,eAAO,SAAS,eAAe,CAAC,IAAI;AAAA,MACxC;AACA,YAAM,eAAe,CAAC,IAAI,gBAAgB,UAAU,iBAAiB,cAAc,qBAAqB;AACpG,8BAAsB;AAEtB,YAAI;AACJ;AACI,cAAI;AACA,qBAAS,kBAAkB,YAAY,CAAC;AACxC,gBAAI,CAAC,WAAW;AACZ,uBAAS,kBAAkB,SACrB,SAAS,mBAAmB,IAC5B;AAAA,YACV;AACA,kBAAM,GAAG,QAAQ;AAAA,UACrB,UACA;AACI,qBAAS,kBAAkB,IAAI;AAC/B,gBAAI,CAAC,WAAW;AACZ,uBAAS,kBAAkB;AAAA,YAC/B;AAAA,UACJ;AAAA,QACJ;AACA,YAAI,OAAO,SAAS,GAAG,KAAK,QAAQ,SAAS,cAAc;AACvD,gBAAM,CAAC,KAAK,IAAI,IAAI,eAAe;AACnC,cAAI,UACA,OAAO,SAAS,GAAG,KACnB,2BAA2B,UAAU,IAAI,GAAG;AAC5C,gBAAI,kBACC,SAAS,wBAAwB,eAAe,GAAG,KAChD,SAAS,uBAAuB,cAAc,GAAG,IAAI;AACzD,qBAAO,KAAK,eAAe,cAAc,kBAAkB;AAAA,gBACvD;AAAA,gBACA,MAAM;AAAA,cACV,CAAC,CAAC;AAAA,YACN;AAEA;AACI,oBAAM,EAAE,aAAa,QAAQ,IAAI;AACjC,kBAAI,WAAW,eAAe;AAC1B,wBAAQ,KAAK,YAA0B;AAAA,kBACnC,MAAM;AAAA,kBACN;AAAA,kBACA,IAAI;AAAA,kBACJ,SAAS,GAAG,QAAQ,IAAI,GAAG;AAAA,gBAC/B,CAAC;AAAA,cACL;AAAA,YACJ;AAAA,UACJ;AACA,iBAAO,UAAU,gBACX,gBAAgB,MAAM,IACtB,aAAa,GAAG;AAAA,QAC1B,WACS,iBAAiB,GAAG,GAAG;AAC5B,iBAAO;AAAA,QACX,OACK;AAED,gBAAM,gBAAgB,eAAe,sBAAsB;AAAA,QAC/D;AAAA,MACJ;AAEA,eAAS,KAAK,MAAM;AAChB,eAAO,aAAa,aAAW,QAAQ,MAAM,SAAS,WAAW,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,MAAM,SAAS,mBAAmB,GAAG,IAAI,GAAG,aAAa,UAAQ,QAAQ,MAAM,KAAK,GAAG,MAAM,CAAC,GAAG,IAAI,CAAC,GAAG,SAAO,KAAK,SAAO,OAAO,SAAS,GAAG,CAAC;AAAA,MAChP;AAEA,eAAS,MAAM,MAAM;AACjB,cAAM,CAAC,MAAM,MAAM,IAAI,IAAI;AAC3B,YAAI,QAAQ,CAAC,OAAO,SAAS,IAAI,GAAG;AAChC,gBAAM,gBAAgB,eAAe,gBAAgB;AAAA,QACzD;AACA,eAAO,EAAE,GAAG,CAAC,MAAM,MAAM,OAAO,OAAO,EAAE,iBAAiB,KAAK,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;AAAA,MAClF;AAEA,eAAS,KAAK,MAAM;AAChB,eAAO,aAAa,aAAW,QAAQ,MAAM,SAAS,UAAU,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,MAAM,SAAS,kBAAkB,GAAG,IAAI,GAAG,mBAAmB,UAAQ,QAAQ,MAAM,KAAK,GAAG,MAAM,CAAC,GAAG,IAAI,CAAC,GAAG,MAAM,SAAS,uBAAuB,SAAO,OAAO,SAAS,GAAG,CAAC;AAAA,MAC9Q;AAEA,eAAS,KAAK,MAAM;AAChB,eAAO,aAAa,aAAW,QAAQ,MAAM,SAAS,QAAQ,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,MAAM,SAAS,gBAAgB,GAAG,IAAI,GAAG,iBAAiB,UAAQ,QAAQ,MAAM,KAAK,GAAG,MAAM,CAAC,GAAG,IAAI,CAAC,GAAG,MAAM,SAAS,uBAAuB,SAAO,OAAO,SAAS,GAAG,CAAC;AAAA,MACxQ;AAEA,eAAS,UAAU,QAAQ;AACvB,eAAO,OAAO,IAAI,SAAO,OAAO,SAAS,GAAG,KAAK,OAAO,SAAS,GAAG,KAAK,OAAO,UAAU,GAAG,IACvF,eAAe,OAAO,GAAG,CAAC,IAC1B,GAAG;AAAA,MACb;AACA,YAAM,cAAc,CAAC,QAAQ;AAC7B,YAAM,YAAY;AAAA,QACd;AAAA,QACA;AAAA,QACA,MAAM;AAAA,MACV;AAEA,eAAS,kBAAkB,MAAM;AAC7B,eAAO;AAAA,UAAa,aAAW;AAC3B,gBAAI;AACJ,kBAAME,YAAW;AACjB,gBAAI;AACA,cAAAA,UAAS,YAAY;AACrB,oBAAM,QAAQ,MAAM,SAAS,WAAW,MAAM,CAACA,WAAU,GAAG,IAAI,CAAC;AAAA,YACrE,UACA;AACI,cAAAA,UAAS,YAAY;AAAA,YACzB;AACA,mBAAO;AAAA,UACX;AAAA,UAAG,MAAM,SAAS,mBAAmB,GAAG,IAAI;AAAA,UAAG;AAAA;AAAA,UAE/C,UAAQ,KAAK,oBAAoB,EAAE,GAAG,IAAI;AAAA,UAAG,SAAO,CAAC,eAAe,GAAG,CAAC;AAAA,UAAG,SAAO,OAAO,QAAQ,GAAG;AAAA,QAAC;AAAA,MACzG;AAEA,eAAS,eAAe,MAAM;AAC1B,eAAO;AAAA,UAAa,aAAW,QAAQ,MAAM,SAAS,QAAQ,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC;AAAA,UAAG,MAAM,SAAS,gBAAgB,GAAG,IAAI;AAAA,UAAG;AAAA;AAAA,UAElI,UAAQ,KAAK,iBAAiB,EAAE,GAAG,IAAI;AAAA,UAAG,MAAM,CAAC;AAAA,UAAG,SAAO,OAAO,SAAS,GAAG,KAAK,OAAO,QAAQ,GAAG;AAAA,QAAC;AAAA,MAC1G;AAEA,eAAS,iBAAiB,MAAM;AAC5B,eAAO;AAAA,UAAa,aAAW,QAAQ,MAAM,SAAS,UAAU,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC;AAAA,UAAG,MAAM,SAAS,kBAAkB,GAAG,IAAI;AAAA,UAAG;AAAA;AAAA,UAEtI,UAAQ,KAAK,mBAAmB,EAAE,GAAG,IAAI;AAAA,UAAG,MAAM,CAAC;AAAA,UAAG,SAAO,OAAO,SAAS,GAAG,KAAK,OAAO,QAAQ,GAAG;AAAA,QAAC;AAAA,MAC5G;AACA,eAAS,eAAe,OAAO;AAC3B,uBAAe;AACf,iBAAS,cAAc;AAAA,MAC3B;AAEA,eAAS,GAAG,KAAKH,SAAQ;AACrB,cAAM,eAAe,OAAO,SAASA,OAAM,IAAIA,UAAS,QAAQ;AAChE,cAAM,UAAU,iBAAiB,YAAY;AAC7C,eAAO,SAAS,gBAAgB,SAAS,GAAG,MAAM;AAAA,MACtD;AACA,eAAS,gBAAgB,KAAK;AAC1B,YAAII,YAAW;AACf,cAAM,UAAU,SAAS,wBAAwB,UAAU,gBAAgB,OAAO,QAAQ,KAAK;AAC/F,iBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACrC,gBAAM,uBAAuB,UAAU,MAAM,QAAQ,CAAC,CAAC,KAAK,CAAC;AAC7D,gBAAM,eAAe,SAAS,gBAAgB,sBAAsB,GAAG;AACvE,cAAI,gBAAgB,MAAM;AACtB,YAAAA,YAAW;AACX;AAAA,UACJ;AAAA,QACJ;AACA,eAAOA;AAAA,MACX;AAEA,eAAS,GAAG,KAAK;AACb,cAAMA,YAAW,gBAAgB,GAAG;AAEpC,eAAOA,aAAY,OACbA,YACA,SACI,OAAO,GAAG,GAAG,KAAK,CAAC,IACnB,CAAC;AAAA,MACf;AAEA,eAAS,iBAAiBJ,SAAQ;AAC9B,eAAQ,UAAU,MAAMA,OAAM,KAAK,CAAC;AAAA,MACxC;AAEA,eAAS,iBAAiBA,SAAQ,SAAS;AACvC,kBAAU,MAAMA,OAAM,IAAI;AAC1B,iBAAS,WAAW,UAAU;AAAA,MAClC;AAEA,eAAS,mBAAmBA,SAAQ,SAAS;AACzC,kBAAU,MAAMA,OAAM,IAAI,UAAU,MAAMA,OAAM,KAAK,CAAC;AACtD,iBAAS,SAAS,UAAU,MAAMA,OAAM,CAAC;AACzC,iBAAS,WAAW,UAAU;AAAA,MAClC;AAEA,eAAS,kBAAkBA,SAAQ;AAC/B,eAAO,iBAAiB,MAAMA,OAAM,KAAK,CAAC;AAAA,MAC9C;AAEA,eAAS,kBAAkBA,SAAQ,QAAQ;AACvC,yBAAiB,MAAMA,OAAM,IAAI;AACjC,iBAAS,kBAAkB,iBAAiB;AAC5C,iBAAS,oBAAoB,UAAUA,SAAQ,MAAM;AAAA,MACzD;AAEA,eAAS,oBAAoBA,SAAQ,QAAQ;AACzC,yBAAiB,MAAMA,OAAM,IAAI,OAAO,OAAO,iBAAiB,MAAMA,OAAM,KAAK,CAAC,GAAG,MAAM;AAC3F,iBAAS,kBAAkB,iBAAiB;AAC5C,iBAAS,oBAAoB,UAAUA,SAAQ,MAAM;AAAA,MACzD;AAEA,eAAS,gBAAgBA,SAAQ;AAC7B,eAAO,eAAe,MAAMA,OAAM,KAAK,CAAC;AAAA,MAC5C;AAEA,eAAS,gBAAgBA,SAAQ,QAAQ;AACrC,uBAAe,MAAMA,OAAM,IAAI;AAC/B,iBAAS,gBAAgB,eAAe;AACxC,iBAAS,kBAAkB,UAAUA,SAAQ,MAAM;AAAA,MACvD;AAEA,eAAS,kBAAkBA,SAAQ,QAAQ;AACvC,uBAAe,MAAMA,OAAM,IAAI,OAAO,OAAO,eAAe,MAAMA,OAAM,KAAK,CAAC,GAAG,MAAM;AACvF,iBAAS,gBAAgB,eAAe;AACxC,iBAAS,kBAAkB,UAAUA,SAAQ,MAAM;AAAA,MACvD;AAEA;AAEA,UAAI,UAAU,OAAO,WAAW;AAC5B,YAAI,MAAM,OAAO,QAAQ,CAAC,QAAQ;AAC9B,cAAI,gBAAgB;AAChB,oBAAQ,QAAQ;AAChB,qBAAS,SAAS;AAClB,qBAAS,qBAAqB,UAAU,QAAQ,OAAO,gBAAgB,KAAK;AAAA,UAChF;AAAA,QACJ,CAAC;AACD,YAAI,MAAM,OAAO,gBAAgB,CAAC,QAAQ;AACtC,cAAI,gBAAgB;AAChB,4BAAgB,QAAQ;AACxB,qBAAS,iBAAiB;AAC1B,qBAAS,qBAAqB,UAAU,QAAQ,OAAO,gBAAgB,KAAK;AAAA,UAChF;AAAA,QACJ,CAAC;AAAA,MACL;AAEA,YAAM,WAAW;AAAA,QACb,IAAI;AAAA,QACJ;AAAA,QACA;AAAA,QACA,IAAI,gBAAgB;AAChB,iBAAO;AAAA,QACX;AAAA,QACA,IAAI,cAAc,KAAK;AACnB,2BAAiB;AACjB,cAAI,OAAO,QAAQ;AACf,oBAAQ,QAAQ,OAAO,OAAO;AAC9B,4BAAgB,QAAQ,OAAO,eAAe;AAC9C,qBAAS,qBAAqB,UAAU,QAAQ,OAAO,gBAAgB,KAAK;AAAA,UAChF;AAAA,QACJ;AAAA,QACA,IAAI,mBAAmB;AACnB,iBAAO,OAAO,KAAK,UAAU,KAAK,EAAE,KAAK;AAAA,QAC7C;AAAA,QACA;AAAA,QACA,IAAI,YAAY;AACZ,iBAAO;AAAA,QACX;AAAA,QACA,IAAI,cAAc;AACd,iBAAO,gBAAgB,CAAC;AAAA,QAC5B;AAAA,QACA,IAAI,WAAW;AACX,iBAAO;AAAA,QACX;AAAA,QACA,IAAI,cAAc;AACd,iBAAO;AAAA,QACX;AAAA,QACA,IAAI,YAAY,KAAK;AACjB,yBAAe;AACf,mBAAS,cAAc;AAAA,QAC3B;AAAA,QACA,IAAI,eAAe;AACf,iBAAO;AAAA,QACX;AAAA,QACA,IAAI,aAAa,KAAK;AAClB,0BAAgB;AAChB,mBAAS,eAAe;AAAA,QAC5B;AAAA,QACA,IAAI,eAAe;AACf,iBAAO;AAAA,QACX;AAAA,QACA,IAAI,aAAa,KAAK;AAClB,0BAAgB;AAAA,QACpB;AAAA,QACA,IAAI,iBAAiB;AACjB,iBAAO;AAAA,QACX;AAAA,QACA,IAAI,eAAe,KAAK;AACpB,4BAAkB;AAClB,mBAAS,iBAAiB;AAAA,QAC9B;AAAA,QACA,IAAI,kBAAkB;AAClB,iBAAO;AAAA,QACX;AAAA,QACA,IAAI,gBAAgB,KAAK;AACrB,6BAAmB;AACnB,mBAAS,kBAAkB;AAAA,QAC/B;AAAA,QACA,IAAI,kBAAkB;AAClB,iBAAO;AAAA,QACX;AAAA,QACA,IAAI,gBAAgB,KAAK;AACrB,6BAAmB;AACnB,mBAAS,kBAAkB;AAAA,QAC/B;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,CAAC,oBAAoB,GAAG;AAAA,MAC5B;AACA;AACI,iBAAS,kBAAkB;AAC3B,iBAAS,gBAAgB;AACzB,iBAAS,KAAK;AACd,iBAAS,KAAK;AACd,iBAAS,KAAK;AACd,iBAAS,IAAI;AACb,iBAAS,IAAI;AACb,iBAAS,oBAAoB;AAC7B,iBAAS,oBAAoB;AAC7B,iBAAS,sBAAsB;AAC/B,iBAAS,kBAAkB;AAC3B,iBAAS,kBAAkB;AAC3B,iBAAS,oBAAoB;AAC7B,iBAAS,gBAAgB,IAAI,QAAQ;AACrC,iBAAS,oBAAoB,IAAI;AACjC,iBAAS,mBAAmB,IAAI;AAChC,iBAAS,iBAAiB,IAAI;AAAA,MAClC;AAEA;AACI,iBAAS,aAAa,IAAI,CAAC,YAAY;AACnC,mBAAS,cAAc;AAAA,QAC3B;AACA,iBAAS,cAAc,IAAI,MAAM;AAC7B,mBAAS,cAAc;AAAA,QAC3B;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AASA,aAAS,uBAAuB,SAAS;AACrC,YAAM,SAAS,OAAO,SAAS,QAAQ,MAAM,IAAI,QAAQ,SAAS,SAAS;AAC3E,YAAM,iBAAiB,OAAO,SAAS,QAAQ,cAAc,KACzD,OAAO,QAAQ,QAAQ,cAAc,KACrC,OAAO,cAAc,QAAQ,cAAc,KAC3C,QAAQ,mBAAmB,QACzB,QAAQ,iBACR;AACN,YAAM,UAAU,OAAO,WAAW,QAAQ,OAAO,IAAI,QAAQ,UAAU;AACvE,YAAM,cAAc,OAAO,UAAU,QAAQ,qBAAqB,KAC9D,OAAO,SAAS,QAAQ,qBAAqB,IAC3C,CAAC,QAAQ,wBACT;AACN,YAAM,eAAe,OAAO,UAAU,QAAQ,kBAAkB,KAC5D,OAAO,SAAS,QAAQ,kBAAkB,IACxC,CAAC,QAAQ,qBACT;AACN,YAAM,eAAe,OAAO,UAAU,QAAQ,YAAY,IACpD,QAAQ,eACR;AACN,YAAM,iBAAiB,CAAC,CAAC,QAAQ;AACjC,YAAM,YAAY,OAAO,cAAc,QAAQ,SAAS,IAAI,QAAQ,YAAY,CAAC;AACjF,YAAM,qBAAqB,QAAQ;AACnC,YAAM,kBAAkB,OAAO,WAAW,QAAQ,eAAe,IAC3D,QAAQ,kBACR;AACN,YAAM,kBAAkB,OAAO,SAAS,QAAQ,iBAAiB,IAC3D,QAAQ,sBAAsB,QAC9B;AACN,YAAM,kBAAkB,CAAC,CAAC,QAAQ;AAClC,YAAM,gBAAgB,OAAO,UAAU,QAAQ,IAAI,IAAI,QAAQ,OAAO;AACtE,UAAI,QAAQ,WAAW;AACnB,eAAO,KAAK,eAAe,cAAc,uBAAuB,CAAC;AAAA,MACrE;AACA,UAAI,QAAQ,0BAA0B;AAClC,eAAO,KAAK,eAAe,cAAc,gCAAgC,CAAC;AAAA,MAC9E;AACA,UAAI,WAAW,QAAQ;AACvB,UAAI,OAAO,cAAc,QAAQ,cAAc,GAAG;AAC9C,cAAM,iBAAiB,QAAQ;AAC/B,cAAM,UAAU,OAAO,KAAK,cAAc;AAC1C,mBAAW,QAAQ,OAAO,CAACI,WAAUJ,YAAW;AAC5C,gBAAM,UAAUI,UAASJ,OAAM,MAAMI,UAASJ,OAAM,IAAI,CAAC;AACzD,iBAAO,OAAO,SAAS,eAAeA,OAAM,CAAC;AAC7C,iBAAOI;AAAA,QACX,GAAI,YAAY,CAAC,CAAE;AAAA,MACvB;AACA,YAAM,EAAE,QAAQ,QAAQ,mBAAmB,IAAI;AAC/C,YAAM,kBAAkB,QAAQ;AAChC,YAAM,gBAAgB,QAAQ;AAC9B,YAAM,WAAW,QAAQ;AACzB,aAAO;AAAA,QACH;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,aAAa;AAAA,QACb;AAAA,QACA;AAAA,QACA;AAAA,QACA,iBAAiB,QAAQ;AAAA,QACzB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,IACJ;AAOA,aAAS,cAAc,UAAU,CAAC,GAAG,eAAe;AAChD;AACI,cAAM,WAAW,eAAe,uBAAuB,OAAO,CAAC;AAE/D,cAAM,UAAU;AAAA;AAAA,UAEZ,IAAI,SAAS;AAAA;AAAA,UAEb,IAAI,SAAS;AACT,mBAAO,SAAS,OAAO;AAAA,UAC3B;AAAA,UACA,IAAI,OAAO,KAAK;AACZ,qBAAS,OAAO,QAAQ;AAAA,UAC5B;AAAA;AAAA,UAEA,IAAI,iBAAiB;AACjB,mBAAO,SAAS,eAAe;AAAA,UACnC;AAAA,UACA,IAAI,eAAe,KAAK;AACpB,qBAAS,eAAe,QAAQ;AAAA,UACpC;AAAA;AAAA,UAEA,IAAI,WAAW;AACX,mBAAO,SAAS,SAAS;AAAA,UAC7B;AAAA;AAAA,UAEA,IAAI,kBAAkB;AAClB,mBAAO,SAAS,gBAAgB;AAAA,UACpC;AAAA;AAAA,UAEA,IAAI,gBAAgB;AAChB,mBAAO,SAAS,cAAc;AAAA,UAClC;AAAA;AAAA,UAEA,IAAI,mBAAmB;AACnB,mBAAO,SAAS;AAAA,UACpB;AAAA;AAAA,UAEA,IAAI,YAAY;AACZ,mBAAO,KAAK,eAAe,cAAc,uBAAuB,CAAC;AAEjE,mBAAO;AAAA,cACH,cAAc;AACV,uBAAO,CAAC;AAAA,cACZ;AAAA,YACJ;AAAA,UACJ;AAAA,UACA,IAAI,UAAU,KAAK;AACf,mBAAO,KAAK,eAAe,cAAc,uBAAuB,CAAC;AAAA,UACrE;AAAA;AAAA,UAEA,IAAI,UAAU;AACV,mBAAO,SAAS,kBAAkB;AAAA,UACtC;AAAA,UACA,IAAI,QAAQ,SAAS;AACjB,qBAAS,kBAAkB,OAAO;AAAA,UACtC;AAAA;AAAA,UAEA,IAAI,wBAAwB;AACxB,mBAAO,OAAO,UAAU,SAAS,WAAW,IACtC,CAAC,SAAS,cACV,SAAS;AAAA,UACnB;AAAA,UACA,IAAI,sBAAsB,KAAK;AAC3B,qBAAS,cAAc,OAAO,UAAU,GAAG,IAAI,CAAC,MAAM;AAAA,UAC1D;AAAA;AAAA,UAEA,IAAI,qBAAqB;AACrB,mBAAO,OAAO,UAAU,SAAS,YAAY,IACvC,CAAC,SAAS,eACV,SAAS;AAAA,UACnB;AAAA,UACA,IAAI,mBAAmB,KAAK;AACxB,qBAAS,eAAe,OAAO,UAAU,GAAG,IAAI,CAAC,MAAM;AAAA,UAC3D;AAAA;AAAA,UAEA,IAAI,YAAY;AACZ,mBAAO,SAAS;AAAA,UACpB;AAAA;AAAA,UAEA,IAAI,yBAAyB;AACzB,mBAAO,SAAS;AAAA,UACpB;AAAA,UACA,IAAI,uBAAuB,KAAK;AAC5B,qBAAS,iBAAiB;AAAA,UAC9B;AAAA;AAAA,UAEA,IAAI,kBAAkB;AAClB,mBAAO,SAAS,0BAA0B;AAAA,UAC9C;AAAA,UACA,IAAI,gBAAgB,SAAS;AACzB,qBAAS,0BAA0B,OAAO;AAAA,UAC9C;AAAA;AAAA,UAEA,IAAI,OAAO;AACP,mBAAO,SAAS;AAAA,UACpB;AAAA,UACA,IAAI,KAAK,KAAK;AACV,qBAAS,gBAAgB;AAAA,UAC7B;AAAA;AAAA,UAEA,IAAI,oBAAoB;AACpB,mBAAO,SAAS,kBAAkB,SAAS;AAAA,UAC/C;AAAA,UACA,IAAI,kBAAkB,KAAK;AACvB,qBAAS,kBAAkB,QAAQ;AAAA,UACvC;AAAA;AAAA,UAEA,IAAI,sBAAsB;AACtB,mBAAO,SAAS;AAAA,UACpB;AAAA,UACA,IAAI,oBAAoB,KAAK;AACzB,qBAAS,kBAAkB;AAAA,UAC/B;AAAA;AAAA,UAEA,IAAI,2BAA2B;AAC3B,mBAAO,KAAK,eAAe,cAAc,gCAAgC,CAAC;AAC1E,mBAAO;AAAA,UACX;AAAA,UACA,IAAI,yBAAyB,KAAK;AAC9B,mBAAO,KAAK,eAAe,cAAc,gCAAgC,CAAC;AAAA,UAC9E;AAAA;AAAA,UAEA,IAAI,qBAAqB;AACrB,mBAAO,SAAS,eAAe,CAAC;AAAA,UACpC;AAAA;AAAA,UAEA,YAAY;AAAA;AAAA,UAEZ,KAAK,MAAM;AACP,kBAAM,CAAC,MAAM,MAAM,IAAI,IAAI;AAC3B,kBAAMC,WAAU,CAAC;AACjB,gBAAI,OAAO;AACX,gBAAI,QAAQ;AACZ,gBAAI,CAAC,OAAO,SAAS,IAAI,GAAG;AACxB,oBAAM,gBAAgB,eAAe,gBAAgB;AAAA,YACzD;AACA,kBAAM,MAAM;AACZ,gBAAI,OAAO,SAAS,IAAI,GAAG;AACvB,cAAAA,SAAQ,SAAS;AAAA,YACrB,WACS,OAAO,QAAQ,IAAI,GAAG;AAC3B,qBAAO;AAAA,YACX,WACS,OAAO,cAAc,IAAI,GAAG;AACjC,sBAAQ;AAAA,YACZ;AACA,gBAAI,OAAO,QAAQ,IAAI,GAAG;AACtB,qBAAO;AAAA,YACX,WACS,OAAO,cAAc,IAAI,GAAG;AACjC,sBAAQ;AAAA,YACZ;AAEA,mBAAO,QAAQ,MAAM,SAAS,GAAG,UAAU;AAAA,cACvC;AAAA,cACC,QAAQ,SAAS,CAAC;AAAA,cACnBA;AAAA,YACJ,CAAC;AAAA,UACL;AAAA,UACA,MAAM,MAAM;AACR,mBAAO,QAAQ,MAAM,SAAS,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC;AAAA,UACzD;AAAA;AAAA,UAEA,MAAM,MAAM;AACR,kBAAM,CAAC,MAAM,MAAM,IAAI,IAAI;AAC3B,kBAAMA,WAAU,EAAE,QAAQ,EAAE;AAC5B,gBAAI,OAAO;AACX,gBAAI,QAAQ;AACZ,gBAAI,CAAC,OAAO,SAAS,IAAI,GAAG;AACxB,oBAAM,gBAAgB,eAAe,gBAAgB;AAAA,YACzD;AACA,kBAAM,MAAM;AACZ,gBAAI,OAAO,SAAS,IAAI,GAAG;AACvB,cAAAA,SAAQ,SAAS;AAAA,YACrB,WACS,OAAO,SAAS,IAAI,GAAG;AAC5B,cAAAA,SAAQ,SAAS;AAAA,YACrB,WACS,OAAO,QAAQ,IAAI,GAAG;AAC3B,qBAAO;AAAA,YACX,WACS,OAAO,cAAc,IAAI,GAAG;AACjC,sBAAQ;AAAA,YACZ;AACA,gBAAI,OAAO,SAAS,IAAI,GAAG;AACvB,cAAAA,SAAQ,SAAS;AAAA,YACrB,WACS,OAAO,QAAQ,IAAI,GAAG;AAC3B,qBAAO;AAAA,YACX,WACS,OAAO,cAAc,IAAI,GAAG;AACjC,sBAAQ;AAAA,YACZ;AAEA,mBAAO,QAAQ,MAAM,SAAS,GAAG,UAAU;AAAA,cACvC;AAAA,cACC,QAAQ,SAAS,CAAC;AAAA,cACnBA;AAAA,YACJ,CAAC;AAAA,UACL;AAAA;AAAA,UAEA,GAAG,KAAK,QAAQ;AACZ,mBAAO,SAAS,GAAG,KAAK,MAAM;AAAA,UAClC;AAAA;AAAA,UAEA,GAAG,KAAK;AACJ,mBAAO,SAAS,GAAG,GAAG;AAAA,UAC1B;AAAA;AAAA,UAEA,iBAAiB,QAAQ;AACrB,mBAAO,SAAS,iBAAiB,MAAM;AAAA,UAC3C;AAAA;AAAA,UAEA,iBAAiB,QAAQ,SAAS;AAC9B,qBAAS,iBAAiB,QAAQ,OAAO;AAAA,UAC7C;AAAA;AAAA,UAEA,mBAAmB,QAAQ,SAAS;AAChC,qBAAS,mBAAmB,QAAQ,OAAO;AAAA,UAC/C;AAAA;AAAA,UAEA,KAAK,MAAM;AACP,mBAAO,QAAQ,MAAM,SAAS,GAAG,UAAU,CAAC,GAAG,IAAI,CAAC;AAAA,UACxD;AAAA;AAAA,UAEA,kBAAkB,QAAQ;AACtB,mBAAO,SAAS,kBAAkB,MAAM;AAAA,UAC5C;AAAA;AAAA,UAEA,kBAAkB,QAAQ,QAAQ;AAC9B,qBAAS,kBAAkB,QAAQ,MAAM;AAAA,UAC7C;AAAA;AAAA,UAEA,oBAAoB,QAAQ,QAAQ;AAChC,qBAAS,oBAAoB,QAAQ,MAAM;AAAA,UAC/C;AAAA;AAAA,UAEA,KAAK,MAAM;AACP,mBAAO,QAAQ,MAAM,SAAS,GAAG,UAAU,CAAC,GAAG,IAAI,CAAC;AAAA,UACxD;AAAA;AAAA,UAEA,gBAAgB,QAAQ;AACpB,mBAAO,SAAS,gBAAgB,MAAM;AAAA,UAC1C;AAAA;AAAA,UAEA,gBAAgB,QAAQ,QAAQ;AAC5B,qBAAS,gBAAgB,QAAQ,MAAM;AAAA,UAC3C;AAAA;AAAA,UAEA,kBAAkB,QAAQ,QAAQ;AAC9B,qBAAS,kBAAkB,QAAQ,MAAM;AAAA,UAC7C;AAAA;AAAA;AAAA,UAGA,eAAe,QAAQ,eAAe;AAClC,mBAAO,KAAK,eAAe,cAAc,8BAA8B,CAAC;AACxE,mBAAO;AAAA,UACX;AAAA;AAAA,UAEA,6BAA6B,QAAQ;AACjC,kBAAM,EAAE,iCAAiC,IAAI;AAC7C,gBAAI,kCAAkC;AAClC,+CAAiC,QAAQ,OAAO;AAAA,YACpD;AAAA,UACJ;AAAA,QACJ;AAEA;AACI,kBAAQ,kBAAkB,CAAC,YAAY;AACnC,kBAAM,aAAa;AACnB,uBAAW,aAAa,KAAK,WAAW,aAAa,EAAE,OAAO;AAAA,UAClE;AACA,kBAAQ,mBAAmB,MAAM;AAC7B,kBAAM,aAAa;AACnB,uBAAW,cAAc,KAAK,WAAW,cAAc,EAAE;AAAA,UAC7D;AAAA,QACJ;AACA,eAAO;AAAA,MACX;AAAA,IACJ;AAGA,QAAM,kBAAkB;AAAA,MACpB,KAAK;AAAA,QACD,MAAM,CAAC,QAAQ,MAAM;AAAA,MACzB;AAAA,MACA,QAAQ;AAAA,QACJ,MAAM;AAAA,MACV;AAAA,MACA,OAAO;AAAA,QACH,MAAM;AAAA;AAAA,QAEN,WAAW,CAAC,QAAgC,QAAQ,YAAY,QAAQ;AAAA,QACxE,SAAS;AAAA;AAAA,MACb;AAAA,MACA,MAAM;AAAA,QACF,MAAM;AAAA,MACV;AAAA,IACJ;AAEA,aAAS,kBAET,EAAE,MAAM,GACR,MAAM;AACF,UAAI,KAAK,WAAW,KAAK,KAAK,CAAC,MAAM,WAAW;AAE5C,cAAM,MAAM,MAAM,UAAU,MAAM,QAAQ,IAAI,CAAC;AAE/C,eAAO,IAAI,OAAO,CAAC,MAAM,YAAY;AACjC,iBAAQ,OAAO;AAAA,YACX,GAAG;AAAA,YACH,GAAI,OAAO,QAAQ,QAAQ,QAAQ,IAAI,QAAQ,WAAW,CAAC,OAAO;AAAA,UACtE;AAAA,QACJ,GAAG,CAAC,CAAC;AAAA,MACT,OACK;AAED,eAAO,KAAK,OAAO,CAAC,KAAK,QAAQ;AAC7B,gBAAM,OAAO,MAAM,GAAG;AACtB,cAAI,MAAM;AACN,gBAAI,GAAG,IAAI,KAAK;AAAA,UACpB;AACA,iBAAO;AAAA,QACX,GAAG,CAAC,CAAC;AAAA,MACT;AAAA,IACJ;AAEA,aAAS,mBAAmB,KAAK;AAC7B,aAAO,IAAI;AAAA,IACf;AAmDA,QAAM;AAAA;AAAA,MAAqC;AAAA;AAAA,QAEvC,MAAM;AAAA,QACN,OAAO,OAAO,OAAO;AAAA,UACjB,SAAS;AAAA,YACL,MAAM;AAAA,YACN,UAAU;AAAA,UACd;AAAA,UACA,QAAQ;AAAA,YACJ,MAAM,CAAC,QAAQ,MAAM;AAAA;AAAA,YAErB,WAAW,CAAC,QAAQ,OAAO,SAAS,GAAG,KAAK,CAAC,MAAM,GAAG;AAAA,UAC1D;AAAA,QACJ,GAAG,eAAe;AAAA;AAAA;AAAA,QAGlB,MAAM,OAAO,SAAS;AAClB,gBAAM,EAAE,OAAO,MAAM,IAAI;AAEzB,gBAAM,OAAO,MAAM,QACf,QAAQ;AAAA,YACJ,UAAU,MAAM;AAAA,YAChB,gBAAgB;AAAA,UACpB,CAAC;AACL,iBAAO,MAAM;AACT,kBAAM,OAAO,OAAO,KAAK,KAAK,EAAE,OAAO,SAAO,QAAQ,GAAG;AACzD,kBAAM,UAAU,CAAC;AACjB,gBAAI,MAAM,QAAQ;AACd,sBAAQ,SAAS,MAAM;AAAA,YAC3B;AACA,gBAAI,MAAM,WAAW,QAAW;AAC5B,sBAAQ,SAAS,OAAO,SAAS,MAAM,MAAM,IAAI,CAAC,MAAM,SAAS,MAAM;AAAA,YAC3E;AACA,kBAAM,MAAM,kBAAkB,SAAS,IAAI;AAE3C,kBAAM,WAAW,KAAK,oBAAoB,EAAE,MAAM,SAAS,KAAK,OAAO;AACvE,kBAAM,gBAAgB,OAAO,OAAO,CAAC,GAAG,KAAK;AAC7C,kBAAM,MAAM,OAAO,SAAS,MAAM,GAAG,KAAK,OAAO,SAAS,MAAM,GAAG,IAC7D,MAAM,MACN,mBAAmB;AACzB,mBAAO,IAAI,EAAE,KAAK,eAAe,QAAQ;AAAA,UAC7C;AAAA,QACJ;AAAA,MACJ;AAAA;AAEA,aAAS,QAAQ,QAAQ;AACrB,aAAO,OAAO,QAAQ,MAAM,KAAK,CAAC,OAAO,SAAS,OAAO,CAAC,CAAC;AAAA,IAC/D;AACA,aAAS,gBAAgB,OAAO,SAAS,UAAU,eAAe;AAC9D,YAAM,EAAE,OAAO,MAAM,IAAI;AACzB,aAAO,MAAM;AACT,cAAM,UAAU,EAAE,MAAM,KAAK;AAC7B,YAAI,YAAY,CAAC;AACjB,YAAI,MAAM,QAAQ;AACd,kBAAQ,SAAS,MAAM;AAAA,QAC3B;AACA,YAAI,OAAO,SAAS,MAAM,MAAM,GAAG;AAC/B,kBAAQ,MAAM,MAAM;AAAA,QACxB,WACS,OAAO,SAAS,MAAM,MAAM,GAAG;AAEpC,cAAI,OAAO,SAAS,MAAM,OAAO,GAAG,GAAG;AAEnC,oBAAQ,MAAM,MAAM,OAAO;AAAA,UAC/B;AAEA,sBAAY,OAAO,KAAK,MAAM,MAAM,EAAE,OAAO,CAACA,UAAS,SAAS;AAC5D,mBAAO,SAAS,SAAS,IAAI,IACvB,OAAO,OAAO,CAAC,GAAGA,UAAS,EAAE,CAAC,IAAI,GAAG,MAAM,OAAO,IAAI,EAAE,CAAC,IACzDA;AAAA,UACV,GAAG,CAAC,CAAC;AAAA,QACT;AACA,cAAM,QAAQ,cAAc,GAAG,CAAC,MAAM,OAAO,SAAS,SAAS,CAAC;AAChE,YAAI,WAAW,CAAC,QAAQ,GAAG;AAC3B,YAAI,OAAO,QAAQ,KAAK,GAAG;AACvB,qBAAW,MAAM,IAAI,CAAC,MAAM,UAAU;AAClC,kBAAM,OAAO,MAAM,KAAK,IAAI;AAC5B,kBAAM,OAAO,OACP,KAAK,EAAE,CAAC,KAAK,IAAI,GAAG,KAAK,OAAO,OAAO,MAAM,CAAC,IAC9C,CAAC,KAAK,KAAK;AACjB,gBAAI,QAAQ,IAAI,GAAG;AACf,mBAAK,CAAC,EAAE,MAAM,GAAG,KAAK,IAAI,IAAI,KAAK;AAAA,YACvC;AACA,mBAAO;AAAA,UACX,CAAC;AAAA,QACL,WACS,OAAO,SAAS,KAAK,GAAG;AAC7B,qBAAW,CAAC,KAAK;AAAA,QACrB;AACA,cAAM,gBAAgB,OAAO,OAAO,CAAC,GAAG,KAAK;AAC7C,cAAM,MAAM,OAAO,SAAS,MAAM,GAAG,KAAK,OAAO,SAAS,MAAM,GAAG,IAC7D,MAAM,MACN,mBAAmB;AACzB,eAAO,IAAI,EAAE,KAAK,eAAe,QAAQ;AAAA,MAC7C;AAAA,IACJ;AAmBA,QAAM;AAAA;AAAA,MAAsC;AAAA;AAAA,QAExC,MAAM;AAAA,QACN,OAAO,OAAO,OAAO;AAAA,UACjB,OAAO;AAAA,YACH,MAAM;AAAA,YACN,UAAU;AAAA,UACd;AAAA,UACA,QAAQ;AAAA,YACJ,MAAM,CAAC,QAAQ,MAAM;AAAA,UACzB;AAAA,QACJ,GAAG,eAAe;AAAA;AAAA;AAAA,QAGlB,MAAM,OAAO,SAAS;AAClB,gBAAM,OAAO,MAAM,QACf,QAAQ,EAAE,UAAU,UAAU,gBAAgB,KAAK,CAAC;AACxD,iBAAO,gBAAgB,OAAO,SAAS,SAAS,4BAA4B,IAAI;AAAA;AAAA,YAEhF,KAAK,iBAAiB,EAAE,GAAG,IAAI;AAAA,WAAC;AAAA,QACpC;AAAA,MACJ;AAAA;AAmBA,QAAM;AAAA;AAAA,MAAuC;AAAA;AAAA,QAEzC,MAAM;AAAA,QACN,OAAO,OAAO,OAAO;AAAA,UACjB,OAAO;AAAA,YACH,MAAM,CAAC,QAAQ,IAAI;AAAA,YACnB,UAAU;AAAA,UACd;AAAA,UACA,QAAQ;AAAA,YACJ,MAAM,CAAC,QAAQ,MAAM;AAAA,UACzB;AAAA,QACJ,GAAG,eAAe;AAAA;AAAA;AAAA,QAGlB,MAAM,OAAO,SAAS;AAClB,gBAAM,OAAO,MAAM,QACf,QAAQ,EAAE,UAAU,UAAU,gBAAgB,KAAK,CAAC;AACxD,iBAAO,gBAAgB,OAAO,SAAS,SAAS,8BAA8B,IAAI;AAAA;AAAA,YAElF,KAAK,mBAAmB,EAAE,GAAG,IAAI;AAAA,WAAC;AAAA,QACtC;AAAA,MACJ;AAAA;AAEA,aAAS,cAAc,MAAM,UAAU;AACnC,YAAM,eAAe;AACrB,UAAI,KAAK,SAAS,eAAe;AAC7B,eAAQ,aAAa,cAAc,QAAQ,KAAK,KAAK;AAAA,MACzD,OACK;AACD,cAAM,UAAU,aAAa,cAAc,QAAQ;AACnD,eAAO,WAAW,OACZ,QAAQ,aACR,KAAK,OAAO;AAAA,MACtB;AAAA,IACJ;AACA,aAAS,YAAY,MAAM;AACvB,YAAM,WAAW,CAAC,YAAY;AAC1B,cAAM,EAAE,UAAU,WAAW,MAAM,IAAI;AAEvC,YAAI,CAAC,YAAY,CAAC,SAAS,GAAG;AAC1B,gBAAM,gBAAgB,eAAe,gBAAgB;AAAA,QACzD;AACA,cAAM,WAAW,cAAc,MAAM,SAAS,CAAC;AAC/C,YAAI,UAAU,UAAU;AACpB,iBAAO,KAAK,eAAe,cAAc,sBAAsB,CAAC;AAAA,QACpE;AACA,cAAM,cAAc,WAAW,KAAK;AACpC,eAAO;AAAA,UACH,QAAQ,MAAM,SAAS,GAAG,UAAU,CAAC,GAAG,WAAW,WAAW,CAAC,CAAC;AAAA,UAChE;AAAA,QACJ;AAAA,MACJ;AACA,YAAM,WAAW,CAAC,IAAI,YAAY;AAC9B,cAAM,CAAC,aAAa,QAAQ,IAAI,SAAS,OAAO;AAChD,YAAI,OAAO,aAAa,KAAK,WAAW,UAAU;AAE9C,aAAG,gBAAgB,IAAI,MAAM,SAAS,QAAQ,MAAM;AAChD,oBAAQ,YAAY,QAAQ,SAAS,aAAa;AAAA,UACtD,CAAC;AAAA,QACL;AACA,WAAG,aAAa;AAChB,WAAG,cAAc;AAAA,MACrB;AACA,YAAM,aAAa,CAAC,OAAO;AACvB,YAAI,OAAO,aAAa,GAAG,eAAe;AACtC,aAAG,cAAc;AACjB,aAAG,gBAAgB;AACnB,iBAAO,GAAG;AAAA,QACd;AACA,YAAI,GAAG,YAAY;AACf,aAAG,aAAa;AAChB,iBAAO,GAAG;AAAA,QACd;AAAA,MACJ;AACA,YAAM,SAAS,CAAC,IAAI,EAAE,MAAM,MAAM;AAC9B,YAAI,GAAG,YAAY;AACf,gBAAM,WAAW,GAAG;AACpB,gBAAM,cAAc,WAAW,KAAK;AACpC,aAAG,cAAc,QAAQ,MAAM,SAAS,GAAG,UAAU;AAAA,YACjD,GAAG,WAAW,WAAW;AAAA,UAC7B,CAAC;AAAA,QACL;AAAA,MACJ;AACA,YAAM,cAAc,CAAC,YAAY;AAC7B,cAAM,CAAC,WAAW,IAAI,SAAS,OAAO;AACtC,eAAO,EAAE,YAAY;AAAA,MACzB;AACA,aAAO;AAAA,QACH,SAAS;AAAA,QACT,WAAW;AAAA,QACX,cAAc;AAAA,QACd;AAAA,MACJ;AAAA,IACJ;AACA,aAAS,WAAW,OAAO;AACvB,UAAI,OAAO,SAAS,KAAK,GAAG;AACxB,eAAO,EAAE,MAAM,MAAM;AAAA,MACzB,WACS,OAAO,cAAc,KAAK,GAAG;AAClC,YAAI,EAAE,UAAU,QAAQ;AACpB,gBAAM,gBAAgB,eAAe,gBAAgB,MAAM;AAAA,QAC/D;AACA,eAAO;AAAA,MACX,OACK;AACD,cAAM,gBAAgB,eAAe,aAAa;AAAA,MACtD;AAAA,IACJ;AACA,aAAS,WAAW,OAAO;AACvB,YAAM,EAAE,MAAM,QAAQ,MAAM,QAAQ,OAAO,IAAI;AAC/C,YAAM,UAAU,CAAC;AACjB,YAAM,QAAQ,QAAQ,CAAC;AACvB,UAAI,OAAO,SAAS,MAAM,GAAG;AACzB,gBAAQ,SAAS;AAAA,MACrB;AACA,UAAI,OAAO,SAAS,MAAM,GAAG;AACzB,gBAAQ,SAAS;AAAA,MACrB;AACA,UAAI,OAAO,SAAS,MAAM,GAAG;AACzB,gBAAQ,SAAS;AAAA,MACrB;AACA,aAAO,CAAC,MAAM,OAAO,OAAO;AAAA,IAChC;AAEA,aAAS,MAAM,KAAK,SAAS,SAAS;AAClC,YAAM,gBAAgB,OAAO,cAAc,QAAQ,CAAC,CAAC,IAC/C,QAAQ,CAAC,IACT,CAAC;AACP,YAAM,uBAAuB,CAAC,CAAC,cAAc;AAC7C,YAAM,gBAAgB,OAAO,UAAU,cAAc,aAAa,IAC5D,cAAc,gBACd;AACN,UAAI,iBAAiB,sBAAsB;AACvC,eAAO,KAAK,eAAe,cAAc,kCAAkC;AAAA,UACvE,MAAM,YAAY;AAAA,QACtB,CAAC,CAAC;AAAA,MACN;AACA,UAAI,eAAe;AAEf,YAAI,UAAU,CAAC,uBAAuB,YAAY,OAAO,QAAQ,WAAW;AAC5E,YAAI,UAAU,aAAa,MAAM,YAAY;AAC7C,YAAI,UAAU,eAAe,MAAM,cAAc;AAAA,MACrD;AAEA;AACI,YAAI,UAAU,KAAK,YAAY,IAAI,CAAC;AAAA,MACxC;AAAA,IACJ;AAMA,aAAS,YAAY,SAAS,UAAU,MAAM;AAC1C,aAAO;AAAA,QACH,eAAe;AACX,gBAAM,WAAW,IAAI,mBAAmB;AAExC,cAAI,CAAC,UAAU;AACX,kBAAM,gBAAgB,eAAe,gBAAgB;AAAA,UACzD;AACA,gBAAM,UAAU,KAAK;AACrB,cAAI,QAAQ,MAAM;AACd,kBAAM,cAAc,QAAQ;AAC5B,gBAAI,QAAQ,QAAQ;AAChB,0BAAY,SAAS,QAAQ;AAAA,YACjC;AACA,wBAAY,SAAS;AACrB,gBAAI,SAAS,KAAK,OAAO;AACrB,mBAAK,QAAQ,YAAY,SAAS,WAAW;AAAA,YACjD,OACK;AACD,0BAAY,qBAAqB;AACjC,mBAAK,QAAQ,cAAc,WAAW;AAAA,YAC1C;AAAA,UACJ,WACS,QAAQ,QAAQ;AACrB,gBAAI,SAAS,KAAK,OAAO;AACrB,mBAAK,QAAQ,YAAY,SAAS,OAAO;AAAA,YAC7C,OACK;AACD,mBAAK,QAAQ,cAAc;AAAA,gBACvB,QAAQ,QAAQ;AAAA,gBAChB,oBAAoB;AAAA,gBACpB,QAAQ;AAAA,cACZ,CAAC;AAAA,YACL;AAAA,UACJ,OACK;AAED,iBAAK,QAAQ;AAAA,UACjB;AACA,cAAI,QAAQ,cAAc;AACtB,gCAAoB,UAAU,SAAS,OAAO;AAAA,UAClD;AACA,kBAAQ,6BAA6B,KAAK,KAAK;AAC/C,eAAK,cAAc,UAAU,KAAK,KAAK;AAEvC,eAAK,KAAK,IAAI,SAAS,KAAK,MAAM,EAAE,GAAG,IAAI;AAC3C,eAAK,MAAM,IAAI,SAAS,KAAK,MAAM,GAAG,GAAG,IAAI;AAC7C,eAAK,MAAM,IAAI,SAAS,KAAK,MAAM,GAAG,GAAG,IAAI;AAC7C,eAAK,MAAM,CAAC,KAAK,WAAW,KAAK,MAAM,GAAG,KAAK,MAAM;AACrD,eAAK,KAAK,IAAI,SAAS,KAAK,MAAM,EAAE,GAAG,IAAI;AAC3C,eAAK,KAAK,IAAI,SAAS,KAAK,MAAM,EAAE,GAAG,IAAI;AAC3C,eAAK,MAAM,CAAC,QAAQ,KAAK,MAAM,GAAG,GAAG;AAAA,QACzC;AAAA,QACA,UAAU;AAAA,QACV;AAAA,QACA,YAAY;AACR,gBAAM,WAAW,IAAI,mBAAmB;AAExC,cAAI,CAAC,UAAU;AACX,kBAAM,gBAAgB,eAAe,gBAAgB;AAAA,UACzD;AACA,iBAAO,KAAK;AACZ,iBAAO,KAAK;AACZ,iBAAO,KAAK;AACZ,iBAAO,KAAK;AACZ,iBAAO,KAAK;AACZ,iBAAO,KAAK;AACZ,iBAAO,KAAK;AACZ,eAAK,iBAAiB,QAAQ;AAC9B,iBAAO,KAAK;AAAA,QAChB;AAAA,MACJ;AAAA,IACJ;AACA,aAAS,YAAY,MAAM,SAAS;AAChC,WAAK,SAAS,QAAQ,UAAU,KAAK;AACrC,WAAK,iBAAiB,QAAQ,kBAAkB,KAAK;AACrD,WAAK,UAAU,QAAQ,WAAW,KAAK;AACvC,WAAK,wBACD,QAAQ,yBAAyB,KAAK;AAC1C,WAAK,qBACD,QAAQ,sBAAsB,KAAK;AACvC,WAAK,yBACD,QAAQ,0BAA0B,KAAK;AAC3C,WAAK,kBAAkB,QAAQ,mBAAmB,KAAK;AACvD,WAAK,oBAAoB,QAAQ,qBAAqB,KAAK;AAC3D,WAAK,sBACD,QAAQ,uBAAuB,KAAK;AACxC,WAAK,OAAO,QAAQ,QAAQ,KAAK;AACjC,WAAK,WAAW,oBAAoB,EAAE,QAAQ,sBAAsB,KAAK,kBAAkB;AAC3F,YAAM,WAAW,kBAAkB,KAAK,QAAQ;AAAA,QAC5C,UAAU,QAAQ;AAAA,QAClB,QAAQ,QAAQ;AAAA,MACpB,CAAC;AACD,aAAO,KAAK,QAAQ,EAAE,QAAQ,YAAU,KAAK,mBAAmB,QAAQ,SAAS,MAAM,CAAC,CAAC;AACzF,UAAI,QAAQ,iBAAiB;AACzB,eAAO,KAAK,QAAQ,eAAe,EAAE,QAAQ,YAAU,KAAK,oBAAoB,QAAQ,QAAQ,gBAAgB,MAAM,CAAC,CAAC;AAAA,MAC5H;AACA,UAAI,QAAQ,eAAe;AACvB,eAAO,KAAK,QAAQ,aAAa,EAAE,QAAQ,YAAU,KAAK,kBAAkB,QAAQ,QAAQ,cAAc,MAAM,CAAC,CAAC;AAAA,MACtH;AACA,aAAO;AAAA,IACX;AAWA,QAAM,mBACS,OAAO,WAAW,iBAAiB;AAElD,aAAS,WAAW,UAAU,CAAC,GAAG,eAAe;AAE7C,YAAM,eAAe,OAAO,UAAU,QAAQ,MAAM,IAC1C,QAAQ,SACR;AAEV,YAAM,oBAAoB,OAAO,UAAU,QAAQ,eAAe,IAC5D,QAAQ,kBACR;AAEN,YAAM,qBAAqB,eACjB,CAAC,CAAC,QAAQ,mBACV;AACV,YAAM,cAAc,oBAAI,IAAI;AAC5B,YAAM,CAAC,aAAa,QAAQ,IAAI,aAAa,SAAS,YAAY;AAClE,YAAM,SAAS,OAAO,WAAW,UAAW;AAC5C,eAAS,cAAc,WAAW;AAC9B,eAAO,YAAY,IAAI,SAAS,KAAK;AAAA,MACzC;AACA,eAAS,cAAc,WAAW,UAAU;AACxC,oBAAY,IAAI,WAAW,QAAQ;AAAA,MACvC;AACA,eAAS,iBAAiB,WAAW;AACjC,oBAAY,OAAO,SAAS;AAAA,MAChC;AACA;AACI,cAAM,OAAO;AAAA;AAAA,UAET,IAAI,OAAO;AACP,mBAAO,eACD,WACA;AAAA,UACV;AAAA;AAAA,UAEA,IAAI,mBAAmB;AACnB,mBAAO;AAAA,UACX;AAAA;AAAA,UAEA,MAAM,QAAQ,QAAQA,UAAS;AAE3B,gBAAI,sBAAsB;AAC1B,gBAAI,QAAQ,IAAI,qBAAqB,IAAI;AAEzC,gBAAI,CAAC,gBAAgB,mBAAmB;AACpC,iCAAmB,KAAK,KAAK,MAAM;AAAA,YACvC;AAEA;AACI,oBAAM,KAAK,MAAM,GAAGA,QAAO;AAAA,YAC/B;AAEA,gBAAI,cAAc;AACd,kBAAI,MAAM,YAAY,UAAU,SAAS,YAAY,IAAI,CAAC;AAAA,YAC9D;AAEA,kBAAM,aAAa,IAAI;AACvB,gBAAI,UAAU,MAAM;AAChB,mBAAK,QAAQ;AACb,yBAAW;AAAA,YACf;AAAA,UACJ;AAAA;AAAA,UAEA,IAAI,SAAS;AACT,mBAAO;AAAA,UACX;AAAA,UACA,UAAU;AACN,wBAAY,KAAK;AAAA,UACrB;AAAA;AAAA,UAEA;AAAA;AAAA,UAEA;AAAA;AAAA,UAEA;AAAA;AAAA,UAEA;AAAA,QACJ;AACA,eAAO;AAAA,MACX;AAAA,IACJ;AAEA,aAAS,QAAQ,UAAU,CAAC,GAAG;AAC3B,YAAM,WAAW,IAAI,mBAAmB;AACxC,UAAI,YAAY,MAAM;AAClB,cAAM,gBAAgB,eAAe,sBAAsB;AAAA,MAC/D;AACA,UAAI,CAAC,SAAS,QACV,SAAS,WAAW,OAAO,QAC3B,CAAC,SAAS,WAAW,IAAI,qBAAqB;AAC9C,cAAM,gBAAgB,eAAe,aAAa;AAAA,MACtD;AACA,YAAM,OAAO,gBAAgB,QAAQ;AACrC,YAAMJ,UAAS,kBAAkB,IAAI;AACrC,YAAM,mBAAmB,oBAAoB,QAAQ;AACrD,YAAM,QAAQ,SAAS,SAAS,gBAAgB;AAChD;AAEI,YAAI,KAAK,SAAS,YAAY,CAAC,QAAQ,gBAAgB;AACnD,cAAI,CAAC,KAAK,kBAAkB;AACxB,kBAAM,gBAAgB,eAAe,4BAA4B;AAAA,UACrE;AACA,iBAAO,iBAAiB,UAAU,OAAOA,SAAQ,OAAO;AAAA,QAC5D;AAAA,MACJ;AACA,UAAI,UAAU,UAAU;AACpB,4BAAoBA,SAAQ,SAAS,gBAAgB;AACrD,eAAOA;AAAA,MACX;AACA,UAAI,UAAU,UAAU;AAEpB,YAAIK,YAAW,YAAY,MAAM,UAAU,QAAQ,cAAc;AACjE,YAAIA,aAAY,MAAM;AAClB;AACI,mBAAO,KAAK,eAAe,cAAc,sBAAsB,CAAC;AAAA,UACpE;AACA,UAAAA,YAAWL;AAAA,QACf;AACA,eAAOK;AAAA,MACX;AACA,YAAM,eAAe;AACrB,UAAI,WAAW,aAAa,cAAc,QAAQ;AAClD,UAAI,YAAY,MAAM;AAClB,cAAM,kBAAkB,OAAO,OAAO,CAAC,GAAG,OAAO;AACjD,YAAI,YAAY,kBAAkB;AAC9B,0BAAgB,SAAS,iBAAiB;AAAA,QAC9C;AACA,YAAIL,SAAQ;AACR,0BAAgB,SAASA;AAAA,QAC7B;AACA,mBAAW,eAAe,eAAe;AACzC,uBAAe,cAAc,QAAQ;AACrC,qBAAa,cAAc,UAAU,QAAQ;AAAA,MACjD;AACA,aAAO;AAAA,IACX;AAiBA,QAAM,gBAAiB,CAAC,SAEnB;AACD,UAAI,EAAE,uBAAuB,OAAO;AAChC,cAAM,gBAAgB,eAAe,8BAA8B;AAAA,MACvE;AACA,aAAO;AAAA,IACX;AACA,aAAS,aAAa,SAAS,YAAY,eACzC;AACE,YAAM,QAAQ,IAAI,YAAY;AAC9B;AACI,cAAM,MAAM,aACN,MAAM,IAAI,MAAM,cAAc,OAAO,CAAC,IACtC,MAAM,IAAI,MAAM,eAAe,OAAO,CAAC;AAC7C,YAAI,OAAO,MAAM;AACb,gBAAM,gBAAgB,eAAe,gBAAgB;AAAA,QACzD;AACA,eAAO,CAAC,OAAO,GAAG;AAAA,MACtB;AAAA,IACJ;AACA,aAAS,gBAAgB,UAAU;AAC/B;AACI,cAAM,OAAO,IAAI,OAAO,CAAC,SAAS,OAC5B,SAAS,WAAW,IAAI,sBACxB,gBAAgB;AAEtB,YAAI,CAAC,MAAM;AACP,gBAAM,gBAAgB,CAAC,SAAS,OAC1B,eAAe,mBACf,eAAe,0BAA0B;AAAA,QACnD;AACA,eAAO;AAAA,MACX;AAAA,IACJ;AAEA,aAAS,SAAS,SAAS,kBAAkB;AAEzC,aAAO,OAAO,cAAc,OAAO,IAC5B,YAAY,mBACT,UACA,WACJ,CAAC,QAAQ,WACL,UACA,QAAQ;AAAA,IACtB;AACA,aAAS,kBAAkB,MAAM;AAE7B,aAAO,KAAK,SAAS,gBACX,KAAK,SACL,KAAK,OAAO;AAAA,IAE1B;AACA,aAAS,YAAY,MAAM,QAAQ,eAAe,OAAO;AACrD,UAAI,WAAW;AACf,YAAM,OAAO,OAAO;AACpB,UAAI,UAAU,OAAO;AACrB,aAAO,WAAW,MAAM;AACpB,cAAM,eAAe;AACrB,YAAI,KAAK,SAAS,eAAe;AAC7B,qBAAW,aAAa,cAAc,OAAO;AAAA,QACjD,OACK;AACD;AACI,kBAAM,UAAU,aAAa,cAAc,OAAO;AAClD,gBAAI,WAAW,MAAM;AACjB,yBAAW,QACN;AACL,kBAAI,gBACA,YACA,CAAC,SAAS,gBAAgB,GAC5B;AACE,2BAAW;AAAA,cACf;AAAA,YACJ;AAAA,UACJ;AAAA,QACJ;AACA,YAAI,YAAY,MAAM;AAClB;AAAA,QACJ;AACA,YAAI,SAAS,SAAS;AAClB;AAAA,QACJ;AACA,kBAAU,QAAQ;AAAA,MACtB;AACA,aAAO;AAAA,IACX;AACA,aAAS,eAAe,MAAM,QAAQ,UAAU;AAC5C;AACI,YAAI,UAAU,MAAM;AAAA,QACpB,GAAG,MAAM;AACT,YAAI,YAAY,MAAM;AAClB,eAAK,iBAAiB,MAAM;AAAA,QAChC,GAAG,MAAM;AAAA,MACb;AAAA,IACJ;AACA,aAAS,iBAAiB,UAAU,OAAO,MAAM,UAAU,CAAC,GAC1D;AACE,YAAM,WAAW,UAAU;AAC3B,YAAM,YAAY,IAAI,WAAW,IAAI;AACrC,UAAI,YACA,SAAS,SACT,EAAE,SAAS,MAAM,SAAS,QAAQ,SAAS,MAAM,SAAS,SAAS;AACnE,cAAM,gBAAgB,eAAe,4CAA4C;AAAA,MACrF;AACA,YAAM,iBAAiB,OAAO,UAAU,QAAQ,aAAa,IACvD,QAAQ,gBACR;AACN,YAAM,UAAU,IAAI;AAAA;AAAA,QAEpB,YAAY,iBACN,KAAK,OAAO,QACZ,OAAO,SAAS,QAAQ,MAAM,IAC1B,QAAQ,SACR,SAAS;AAAA,MAAc;AACjC,YAAM,kBAAkB,IAAI;AAAA;AAAA,QAE5B,YAAY,iBACN,KAAK,eAAe,QACpB,OAAO,SAAS,QAAQ,cAAc,KACpC,OAAO,QAAQ,QAAQ,cAAc,KACrC,OAAO,cAAc,QAAQ,cAAc,KAC3C,QAAQ,mBAAmB,QACzB,QAAQ,iBACR,QAAQ;AAAA,MAAK;AACvB,YAAM,YAAY,IAAI,IAAI,kBAAkB,QAAQ,OAAO,OAAO,CAAC;AAEnE,YAAM,mBAAmB,IAAI,IAAI,OAAO,cAAc,QAAQ,eAAe,IACvE,QAAQ,kBACR,EAAE,CAAC,QAAQ,KAAK,GAAG,CAAC,EAAE,CAAC;AAE7B,YAAM,iBAAiB,IAAI,IAAI,OAAO,cAAc,QAAQ,aAAa,IACnE,QAAQ,gBACR,EAAE,CAAC,QAAQ,KAAK,GAAG,CAAC,EAAE,CAAC;AAE7B,YAAM,eAAe,WACf,KAAK,cACL,OAAO,UAAU,QAAQ,WAAW,KAAK,OAAO,SAAS,QAAQ,WAAW,IACxE,QAAQ,cACR;AAEV,YAAM,gBAAgB,WAChB,KAAK,eACL,OAAO,UAAU,QAAQ,YAAY,KAAK,OAAO,SAAS,QAAQ,YAAY,IAC1E,QAAQ,eACR;AAEV,YAAM,gBAAgB,WAChB,KAAK,eACL,OAAO,UAAU,QAAQ,YAAY,IACjC,QAAQ,eACR;AAEV,YAAM,kBAAkB,CAAC,CAAC,QAAQ;AAElC,YAAM,WAAW,OAAO,WAAW,QAAQ,OAAO,IAAI,QAAQ,UAAU;AAExE,YAAM,mBAAmB,OAAO,WAAW,QAAQ,eAAe,IAC5D,QAAQ,kBACR;AAEN,YAAM,mBAAmB,WACnB,KAAK,kBACL,OAAO,UAAU,QAAQ,eAAe,IACpC,QAAQ,kBACR;AACV,YAAM,mBAAmB,CAAC,CAAC,QAAQ;AAEnC,YAAM,aAAa,WACb,KAAK,YACL,OAAO,cAAc,QAAQ,SAAS,IAClC,QAAQ,YACR,CAAC;AAEX,YAAM,eAAe,QAAQ,eAAgB,YAAY,KAAK;AAE9D,eAAS,wBAAwB;AAC7B,eAAO;AAAA,UACH,QAAQ;AAAA,UACR,gBAAgB;AAAA,UAChB,UAAU;AAAA,UACV,iBAAiB;AAAA,UACjB,eAAe;AAAA,QACnB;AAAA,MACJ;AAEA,YAAM,SAAS,IAAI,SAAS;AAAA,QACxB,KAAK,MAAM;AACP,iBAAO,UAAU,QAAQ,UAAU,MAAM,OAAO,QAAQ,QAAQ;AAAA,QACpE;AAAA,QACA,KAAK,SAAO;AACR,cAAI,UAAU,OAAO;AACjB,sBAAU,MAAM,OAAO,QAAQ;AAAA,UACnC;AACA,kBAAQ,QAAQ;AAAA,QACpB;AAAA,MACJ,CAAC;AAED,YAAM,iBAAiB,IAAI,SAAS;AAAA,QAChC,KAAK,MAAM;AACP,iBAAO,UAAU,QACX,UAAU,MAAM,eAAe,QAC/B,gBAAgB;AAAA,QAC1B;AAAA,QACA,KAAK,SAAO;AACR,cAAI,UAAU,OAAO;AACjB,sBAAU,MAAM,eAAe,QAAQ;AAAA,UAC3C;AACA,0BAAgB,QAAQ;AAAA,QAC5B;AAAA,MACJ,CAAC;AAED,YAAM,WAAW,IAAI,SAAS,MAAM;AAChC,YAAI,UAAU,OAAO;AAEjB,iBAAO,UAAU,MAAM,SAAS;AAAA,QACpC,OACK;AAED,iBAAO,UAAU;AAAA,QACrB;AAAA,MACJ,CAAC;AACD,YAAM,kBAAkB,IAAI,SAAS,MAAM,iBAAiB,KAAK;AACjE,YAAM,gBAAgB,IAAI,SAAS,MAAM,eAAe,KAAK;AAC7D,eAAS,4BAA4B;AACjC,eAAO,UAAU,QACX,UAAU,MAAM,0BAA0B,IAC1C;AAAA,MACV;AACA,eAAS,0BAA0B,SAAS;AACxC,YAAI,UAAU,OAAO;AACjB,oBAAU,MAAM,0BAA0B,OAAO;AAAA,QACrD;AAAA,MACJ;AACA,eAAS,oBAAoB;AACzB,eAAO,UAAU,QAAQ,UAAU,MAAM,kBAAkB,IAAI;AAAA,MACnE;AACA,eAAS,kBAAkB,SAAS;AAChC,YAAI,UAAU,OAAO;AACjB,oBAAU,MAAM,kBAAkB,OAAO;AAAA,QAC7C;AAAA,MACJ;AACA,eAAS,aAAa,IAAI;AACtB,8BAAsB;AACtB,eAAO,GAAG;AAAA,MACd;AACA,eAAS,KAAK,MAAM;AAChB,eAAO,UAAU,QACX,aAAa,MAAM,QAAQ,MAAM,UAAU,MAAM,GAAG,MAAM,CAAC,GAAG,IAAI,CAAC,CAAC,IACpE,aAAa,MAAM,EAAE;AAAA,MAC/B;AACA,eAAS,MAAM,MAAM;AACjB,eAAO,UAAU,QACX,QAAQ,MAAM,UAAU,MAAM,IAAI,MAAM,CAAC,GAAG,IAAI,CAAC,IACjD;AAAA,MACV;AACA,eAAS,KAAK,MAAM;AAChB,eAAO,UAAU,QACX,aAAa,MAAM,QAAQ,MAAM,UAAU,MAAM,GAAG,MAAM,CAAC,GAAG,IAAI,CAAC,CAAC,IACpE,aAAa,MAAM,EAAE;AAAA,MAC/B;AACA,eAAS,KAAK,MAAM;AAChB,eAAO,UAAU,QACX,aAAa,MAAM,QAAQ,MAAM,UAAU,MAAM,GAAG,MAAM,CAAC,GAAG,IAAI,CAAC,CAAC,IACpE,aAAa,MAAM,EAAE;AAAA,MAC/B;AACA,eAAS,GAAG,KAAK;AACb,eAAO,UAAU,QAAQ,UAAU,MAAM,GAAG,GAAG,IAAI,CAAC;AAAA,MACxD;AACA,eAAS,GAAG,KAAKD,SAAQ;AACrB,eAAO,UAAU,QAAQ,UAAU,MAAM,GAAG,KAAKA,OAAM,IAAI;AAAA,MAC/D;AACA,eAAS,iBAAiBA,SAAQ;AAC9B,eAAO,UAAU,QAAQ,UAAU,MAAM,iBAAiBA,OAAM,IAAI,CAAC;AAAA,MACzE;AACA,eAAS,iBAAiBA,SAAQ,SAAS;AACvC,YAAI,UAAU,OAAO;AACjB,oBAAU,MAAM,iBAAiBA,SAAQ,OAAO;AAChD,oBAAU,MAAMA,OAAM,IAAI;AAAA,QAC9B;AAAA,MACJ;AACA,eAAS,mBAAmBA,SAAQ,SAAS;AACzC,YAAI,UAAU,OAAO;AACjB,oBAAU,MAAM,mBAAmBA,SAAQ,OAAO;AAAA,QACtD;AAAA,MACJ;AACA,eAAS,kBAAkBA,SAAQ;AAC/B,eAAO,UAAU,QAAQ,UAAU,MAAM,kBAAkBA,OAAM,IAAI,CAAC;AAAA,MAC1E;AACA,eAAS,kBAAkBA,SAAQ,QAAQ;AACvC,YAAI,UAAU,OAAO;AACjB,oBAAU,MAAM,kBAAkBA,SAAQ,MAAM;AAChD,2BAAiB,MAAMA,OAAM,IAAI;AAAA,QACrC;AAAA,MACJ;AACA,eAAS,oBAAoBA,SAAQ,QAAQ;AACzC,YAAI,UAAU,OAAO;AACjB,oBAAU,MAAM,oBAAoBA,SAAQ,MAAM;AAAA,QACtD;AAAA,MACJ;AACA,eAAS,gBAAgBA,SAAQ;AAC7B,eAAO,UAAU,QAAQ,UAAU,MAAM,gBAAgBA,OAAM,IAAI,CAAC;AAAA,MACxE;AACA,eAAS,gBAAgBA,SAAQ,QAAQ;AACrC,YAAI,UAAU,OAAO;AACjB,oBAAU,MAAM,gBAAgBA,SAAQ,MAAM;AAC9C,yBAAe,MAAMA,OAAM,IAAI;AAAA,QACnC;AAAA,MACJ;AACA,eAAS,kBAAkBA,SAAQ,QAAQ;AACvC,YAAI,UAAU,OAAO;AACjB,oBAAU,MAAM,kBAAkBA,SAAQ,MAAM;AAAA,QACpD;AAAA,MACJ;AACA,YAAM,UAAU;AAAA,QACZ,IAAI,KAAK;AACL,iBAAO,UAAU,QAAQ,UAAU,MAAM,KAAK;AAAA,QAClD;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,IAAI,gBAAgB;AAChB,iBAAO,UAAU,QAAQ,UAAU,MAAM,gBAAgB;AAAA,QAC7D;AAAA,QACA,IAAI,cAAc,KAAK;AACnB,cAAI,UAAU,OAAO;AACjB,sBAAU,MAAM,gBAAgB;AAAA,UACpC;AAAA,QACJ;AAAA,QACA,IAAI,mBAAmB;AACnB,iBAAO,UAAU,QACX,UAAU,MAAM,mBAChB,OAAO,KAAK,UAAU,KAAK;AAAA,QACrC;AAAA,QACA,IAAI,YAAY;AACZ,iBAAQ,UAAU,QAAQ,UAAU,MAAM,YAAY;AAAA,QAC1D;AAAA,QACA,IAAI,cAAc;AACd,iBAAQ,UAAU,QAAQ,UAAU,MAAM,cAAc;AAAA,QAC5D;AAAA,QACA,IAAI,WAAW;AACX,iBAAO,UAAU,QAAQ,UAAU,MAAM,WAAW;AAAA,QACxD;AAAA,QACA,IAAI,cAAc;AACd,iBAAO,UAAU,QAAQ,UAAU,MAAM,cAAc;AAAA,QAC3D;AAAA,QACA,IAAI,YAAY,KAAK;AACjB,cAAI,UAAU,OAAO;AACjB,sBAAU,MAAM,cAAc;AAAA,UAClC;AAAA,QACJ;AAAA,QACA,IAAI,eAAe;AACf,iBAAO,UAAU,QAAQ,UAAU,MAAM,eAAe;AAAA,QAC5D;AAAA,QACA,IAAI,aAAa,KAAK;AAClB,cAAI,UAAU,OAAO;AACjB,sBAAU,MAAM,cAAc;AAAA,UAClC;AAAA,QACJ;AAAA,QACA,IAAI,eAAe;AACf,iBAAO,UAAU,QAAQ,UAAU,MAAM,eAAe;AAAA,QAC5D;AAAA,QACA,IAAI,aAAa,KAAK;AAClB,cAAI,UAAU,OAAO;AACjB,sBAAU,MAAM,eAAe;AAAA,UACnC;AAAA,QACJ;AAAA,QACA,IAAI,iBAAiB;AACjB,iBAAO,UAAU,QAAQ,UAAU,MAAM,iBAAiB;AAAA,QAC9D;AAAA,QACA,IAAI,eAAe,KAAK;AACpB,cAAI,UAAU,OAAO;AACjB,sBAAU,MAAM,iBAAiB;AAAA,UACrC;AAAA,QACJ;AAAA,QACA,IAAI,kBAAkB;AAClB,iBAAO,UAAU,QACX,UAAU,MAAM,kBAChB;AAAA,QACV;AAAA,QACA,IAAI,gBAAgB,KAAK;AACrB,cAAI,UAAU,OAAO;AACjB,sBAAU,MAAM,kBAAkB;AAAA,UACtC;AAAA,QACJ;AAAA,QACA,IAAI,kBAAkB;AAClB,iBAAO,UAAU,QACX,UAAU,MAAM,kBAChB;AAAA,QACV;AAAA,QACA,IAAI,gBAAgB,KAAK;AACrB,cAAI,UAAU,OAAO;AACjB,sBAAU,MAAM,kBAAkB;AAAA,UACtC;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AACA,eAAS,KAAK,UAAU;AACpB,iBAAS,OAAO,QAAQ,QAAQ;AAChC,iBAAS,eAAe,QAAQ,gBAAgB;AAChD,eAAO,KAAK,UAAU,KAAK,EAAE,QAAQ,CAAAA,YAAU;AAC3C,mBAAS,mBAAmBA,SAAQ,UAAU,MAAMA,OAAM,CAAC;AAAA,QAC/D,CAAC;AACD,eAAO,KAAK,iBAAiB,KAAK,EAAE,QAAQ,CAAAA,YAAU;AAClD,mBAAS,oBAAoBA,SAAQ,iBAAiB,MAAMA,OAAM,CAAC;AAAA,QACvE,CAAC;AACD,eAAO,KAAK,eAAe,KAAK,EAAE,QAAQ,CAAAA,YAAU;AAChD,mBAAS,kBAAkBA,SAAQ,eAAe,MAAMA,OAAM,CAAC;AAAA,QACnE,CAAC;AACD,iBAAS,kBAAkB;AAC3B,iBAAS,iBAAiB;AAC1B,iBAAS,eAAe;AACxB,iBAAS,eAAe;AACxB,iBAAS,cAAc;AACvB,iBAAS,kBAAkB;AAAA,MAC/B;AACA,UAAI,cAAc,MAAM;AACpB,YAAI,SAAS,SAAS,QAAQ,SAAS,MAAM,SAAS,MAAM;AACxD,gBAAM,gBAAgB,eAAe,mCAAmC;AAAA,QAC5E;AAEA,cAAM,WAAY,UAAU,QAAQ,SAAS,MAAM,MAC9C;AACL,YAAI,UAAU,UAAU;AACpB,kBAAQ,QAAQ,SAAS,OAAO;AAChC,0BAAgB,QAAQ,SAAS,eAAe;AAChD,oBAAU,QAAQ,SAAS,SAAS;AACpC,2BAAiB,QAAQ,SAAS,gBAAgB;AAClD,yBAAe,QAAQ,SAAS,cAAc;AAAA,QAClD,WACS,UAAU;AACf,eAAK,QAAQ;AAAA,QACjB;AAAA,MACJ,CAAC;AACD,aAAO;AAAA,IACX;AACA,QAAM,oBAAoB;AAAA,MACtB;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AACA,QAAM,sBAAsB,CAAC,KAAK,MAAM,KAAK,KAAK,IAAI;AACtD,aAAS,mBAAmB,KAAK,UAAU;AACvC,YAAM,OAAO,uBAAO,OAAO,IAAI;AAC/B,wBAAkB,QAAQ,UAAQ;AAC9B,cAAM,OAAO,OAAO,yBAAyB,UAAU,IAAI;AAC3D,YAAI,CAAC,MAAM;AACP,gBAAM,gBAAgB,eAAe,gBAAgB;AAAA,QACzD;AACA,cAAM,OAAO,IAAI,MAAM,KAAK,KAAK,IAC3B;AAAA,UACE,MAAM;AACF,mBAAO,KAAK,MAAM;AAAA,UACtB;AAAA;AAAA,UAEA,IAAI,KAAK;AACL,iBAAK,MAAM,QAAQ;AAAA,UACvB;AAAA,QACJ,IACE;AAAA,UACE,MAAM;AACF,mBAAO,KAAK,OAAO,KAAK,IAAI;AAAA,UAChC;AAAA,QACJ;AACJ,eAAO,eAAe,MAAM,MAAM,IAAI;AAAA,MAC1C,CAAC;AACD,UAAI,OAAO,iBAAiB,QAAQ;AACpC,0BAAoB,QAAQ,YAAU;AAClC,cAAM,OAAO,OAAO,yBAAyB,UAAU,MAAM;AAC7D,YAAI,CAAC,QAAQ,CAAC,KAAK,OAAO;AACtB,gBAAM,gBAAgB,eAAe,gBAAgB;AAAA,QACzD;AACA,eAAO,eAAe,IAAI,OAAO,kBAAkB,IAAI,MAAM,IAAI,IAAI;AAAA,MACzE,CAAC;AAAA,IACL;AAGA,aAAS,wBAAwB,SAAS,iBAAiB;AAE3D,aAAS,wBAAwB,SAAS,YAAY;AAEtD,aAAS,yBAAyB,SAAS,uBAAuB;AAElE;AACI,YAAM,SAAS,OAAO,cAAc;AACpC,aAAO,cAAc;AACrB,eAAS,gBAAgB,OAAO,gCAAgC;AAAA,IACpE;AAEA,YAAQ,iBAAiB;AACzB,YAAQ,mBAAmB;AAC3B,YAAQ,eAAe;AACvB,YAAQ,cAAc;AACtB,YAAQ,UAAU;AAClB,YAAQ,gBAAgB;AACxB,YAAQ,aAAa;AACrB,YAAQ,UAAU;AAClB,YAAQ,cAAc;AAAA;AAAA;", "names": ["sourceFile", "needle", "section", "index", "context", "ch", "sourceMap", "code", "type", "messageCompiler", "resolveValue", "msg", "source", "message", "code", "locale", "global", "locales", "_context", "messages", "options", "composer"]}