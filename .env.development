# port 端口号
VITE_PORT = 8888

#浏览器自动打开
VITE_OPEN = true

# 本地环境
ENV = 'development'

# 代码生成服务地址 (单体架构有效)
VITE_GEN_PROXY_PATH = http://127.0.0.1:5003

#百度地图的 key
VITE_BAIDU_MAP_AK = 'EqVT3rPsm5I7vhWZmEN4tk7AwMRIn6Mh'
# ADMIN 接口服务地址
## 本地-127
#VITE_ADMIN_PROXY_PATH = http://127.0.0.1:9999

## 测试-138【备用测试环境138】
#VITE_ADMIN_PROXY_PATH = http://192.168.0.138:9999

## 测试-146【备用测试环境146】
#VITE_ADMIN_PROXY_PATH = http://192.168.0.146:9999

## 测试-24【当前可用测试环境】
VITE_ADMIN_PROXY_PATH = http://192.168.0.24:8098/api

## 正式-盘锦【当前可用正式环境】
# VITE_ADMIN_PROXY_PATH = https://www.ctltrans.cn:8098/api
